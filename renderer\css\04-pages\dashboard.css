/* =================================
   Dashboard Page Styles
   ================================= */

/* =================================
   Dashboard Top Bar
   ================================= */
.dashboard-top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.dashboard-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.dashboard-title h1 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
}

.dashboard-icon {
  width: 32px;
  height: 32px;
  color: var(--interactive-primary);
}

.dashboard-quick-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

/* =================================
   Dashboard Main Content Layout
   ================================= */
.dashboard-main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.dashboard-left-section {
  min-width: 0; /* Prevents grid overflow */
}

.dashboard-right-section {
  min-width: 0; /* Prevents grid overflow */
}

/* =================================
   Dashboard Layout
   ================================= */
.dashboard-section {
  margin-bottom: var(--space-8);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-6) 0;
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
}

/* =================================
   Dashboard Cards
   ================================= */
.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.herd-cards {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.accounting-cards {
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
}

/* =================================
   Herd Cards - Accounting Style
   ================================= */
.herd-cards .accounting-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.herd-cards .accounting-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--border-secondary);
}

.herd-cards .accounting-card .card-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: var(--primary-50);
  color: var(--primary-600);
}

.herd-cards .accounting-card .card-icon svg {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

.herd-cards .accounting-card .card-content {
  flex: 1;
  min-width: 0;
}

.herd-cards .accounting-card .card-title {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.herd-cards .accounting-card .card-value {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  line-height: var(--leading-tight);
}

.herd-cards .accounting-card .card-subtitle {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  line-height: var(--leading-tight);
}

/* Specific herd card types */
.herd-cards .accounting-card.total .card-icon {
  background: var(--info-50);
  color: var(--info-600);
}

.herd-cards .accounting-card.female .card-icon {
  background: var(--pink-50);
  color: var(--pink-600);
}

.herd-cards .accounting-card.male .card-icon {
  background: var(--blue-50);
  color: var(--blue-600);
}

.herd-cards .accounting-card.pregnant .card-icon {
  background: var(--purple-50);
  color: var(--purple-600);
}

.herd-cards .accounting-card.births .card-icon {
  background: var(--orange-50);
  color: var(--orange-600);
}

.herd-cards .accounting-card.milking .card-icon {
  background: var(--cyan-50);
  color: var(--cyan-600);
}

.herd-cards .accounting-card.daily-milk .card-icon {
  background: var(--teal-50);
  color: var(--teal-600);
}

.herd-cards .accounting-card.alerts .card-icon {
  background: var(--error-50);
  color: var(--error-600);
}

/* =================================
   Financial Cards Styling
   ================================= */
.income-card.positive .value {
  color: var(--status-success);
}

.expense-card.negative .value {
  color: var(--status-error);
}

.profit-loss-card.positive .value {
  color: var(--status-success);
}

.profit-loss-card.negative .value {
  color: var(--status-error);
}

.financial-position-card.positive .value {
  color: var(--status-success);
}

.financial-position-card.negative .value {
  color: var(--status-error);
}

.receivables-card .value {
  color: var(--status-info);
}

.payables-card .value {
  color: var(--status-warning);
}

/* =================================
   Dashboard Charts
   ================================= */
.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.herd-charts {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.chart-container {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.chart-container:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.chart-container h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-align: center;
}

.chart-container canvas {
  max-height: 300px;
  width: 100%;
  border-radius: var(--radius-lg);
}

/* =================================
   Quick Actions
   ================================= */
.quick-actions {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.quick-actions h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  text-decoration: none;
  transition: all var(--transition-fast);
  cursor: pointer;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.quick-action-btn:hover {
  background: var(--primary-50);
  border-color: var(--interactive-primary);
  color: var(--text-primary);
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.quick-action-btn svg {
  width: 20px;
  height: 20px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

/* =================================
   Recent Activities
   ================================= */
.recent-activities {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.recent-activities h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recent-activities .view-all-link {
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  color: var(--text-link);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.recent-activities .view-all-link:hover {
  color: var(--text-link-hover);
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.activity-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.activity-item:hover {
  background: var(--primary-50);
  border-color: var(--interactive-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.activity-main {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-lg);
  background: var(--primary-50);
  color: var(--interactive-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon svg {
  width: 16px;
  height: 16px;
}

.activity-details {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-subtitle {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-1);
  flex-shrink: 0;
}

.activity-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  white-space: nowrap;
}

/* =================================
   Dashboard Stats
   ================================= */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.stat-item {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  text-align: center;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.stat-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  background: var(--primary-50);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-3);
}

.stat-icon svg {
  width: 24px;
  height: 24px;
}

.stat-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
  line-height: var(--leading-tight);
}

.stat-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin: 0;
}

.stat-change {
  font-size: var(--text-xs);
  margin-top: var(--space-1);
}

.stat-change.positive {
  color: var(--status-success);
}

.stat-change.negative {
  color: var(--status-error);
}

/* =================================
   Alerts Table Styles
   ================================= */
.alerts-table-container {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.alerts-table-container .modern-table {
  font-size: var(--text-xs);
}

.alert-row {
  cursor: pointer;
  transition: all var(--transition-fast);
}

.alert-row:hover {
  background: var(--interactive-secondary);
}

.alert-row.alert-high {
  border-left: 3px solid var(--status-error);
}

.alert-row.alert-medium {
  border-left: 3px solid var(--status-warning);
}

.alert-row.alert-low {
  border-left: 3px solid var(--status-info);
}

.alert-row.alert-unread {
  background: rgba(251, 191, 36, 0.1); /* yellow tint for unread */
}

.alert-row.alert-read {
  opacity: 0.7;
}

.alert-message {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Alert type badges */
.badge-vaccination {
  background: var(--status-info);
  color: white;
}

.badge-health {
  background: var(--status-error);
  color: white;
}

.badge-payment {
  background: var(--status-warning);
  color: white;
}

.badge-breeding {
  background: var(--status-success);
  color: white;
}

.badge-milk {
  background: var(--primary-600);
  color: white;
}

/* =================================
   Cariler Tables Styles
   ================================= */
.cariler-tables-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
  margin-top: var(--space-6);
}

.cariler-table-container {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.cariler-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  flex-wrap: wrap;
  gap: var(--space-3);
}

.cariler-table-container h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.table-controls {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.search-input {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  min-width: 150px;
}

.search-input:focus {
  outline: none;
  border-color: var(--interactive-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.filter-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: var(--interactive-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Sortable table headers */
.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background-color var(--transition-fast);
}

.sortable:hover {
  background: var(--interactive-secondary);
}

.sort-icon {
  margin-left: var(--space-1);
  transition: transform var(--transition-fast);
  opacity: 0.5;
}

.sortable.sort-asc .sort-icon {
  transform: rotate(180deg);
  opacity: 1;
}

.sortable.sort-desc .sort-icon {
  transform: rotate(0deg);
  opacity: 1;
}

.cariler-table-container .modern-table {
  font-size: var(--text-sm);
}

.amount.positive {
  color: var(--status-success);
  font-weight: var(--font-semibold);
}

.amount.negative {
  color: var(--status-error);
  font-weight: var(--font-semibold);
}

.company-name {
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.receivable-row:hover,
.payable-row:hover {
  background: var(--interactive-secondary);
}

/* =================================
   Bottom Section Layout
   ================================= */
.dashboard-bottom-section {
  margin-top: var(--space-8);
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 1024px) {
  .dashboard-main-content {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .cariler-tables-section {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
}

@media (max-width: 768px) {
  .dashboard-top-bar {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
    padding: var(--space-4);
  }

  .dashboard-title h1 {
    font-size: var(--text-xl);
  }

  .dashboard-quick-actions {
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--space-2);
  }

  .dashboard-quick-actions .btn {
    flex: 1;
    min-width: 120px;
    font-size: var(--text-sm);
    padding: var(--space-2) var(--space-3);
  }

  .dashboard-cards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }

  .dashboard-charts {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .chart-container {
    padding: var(--space-4);
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .quick-action-btn {
    padding: var(--space-3);
    gap: var(--space-2);
  }

  .activity-item {
    padding: var(--space-2);
  }

  .activity-main {
    gap: var(--space-2);
  }

  .activity-icon {
    width: 28px;
    height: 28px;
  }

  .activity-icon svg {
    width: 14px;
    height: 14px;
  }

  .dashboard-stats {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-3);
  }

  .stat-item {
    padding: var(--space-4);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    margin-bottom: var(--space-2);
  }

  .stat-icon svg {
    width: 20px;
    height: 20px;
  }

  .stat-value {
    font-size: var(--text-2xl);
  }

  .alert-message {
    max-width: 120px;
  }

  .cariler-table-container {
    padding: var(--space-4);
  }

  .cariler-table-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .table-controls {
    flex-direction: column;
    gap: var(--space-2);
  }

  .search-input,
  .filter-select {
    width: 100%;
    min-width: auto;
  }

  .alerts-table-container .modern-table,
  .cariler-table-container .modern-table {
    font-size: var(--text-xs);
  }

  .alerts-table-container .modern-table th,
  .alerts-table-container .modern-table td,
  .cariler-table-container .modern-table th,
  .cariler-table-container .modern-table td {
    padding: var(--space-2);
  }

  .btn-sm {
    padding: var(--space-1) var(--space-2);
    font-size: var(--text-xs);
  }

  .btn-sm svg {
    width: 12px;
    height: 12px;
  }
}

@media (max-width: 480px) {
  .dashboard-main-content {
    gap: var(--space-3);
  }

  .dashboard-top-bar {
    padding: var(--space-3);
  }

  .dashboard-title h1 {
    font-size: var(--text-lg);
  }

  .dashboard-quick-actions .btn {
    min-width: 100px;
    padding: var(--space-2);
  }

  .accounting-cards {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: var(--space-3);
  }

  .stat-card .value {
    font-size: var(--text-lg);
  }

  .alert-message {
    max-width: 100px;
  }

  .company-name {
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .amount {
    font-size: var(--text-sm);
  }
}

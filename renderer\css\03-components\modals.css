/* =================================
   Modern Modal System
   ================================= */

/* =================================
   Base Modal
   ================================= */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  padding: var(--space-4);
  box-sizing: border-box;
}

.modal.show,
.modal:not(.hidden) {
  opacity: 1;
  visibility: visible;
}

.modal.hidden {
  display: none !important;
}

/* =================================
   Modal Content
   ================================= */
.modal-content {
  background: var(--modal-bg);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 600px;
  width: 90vw;
  max-height: calc(100vh - 2 * var(--space-4));
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.95);
  transition: transform var(--transition-base);
}

/* Ensure all modal variants have proper height constraints */
.modal-content.large {
  max-width: 900px;
  width: 95vw;
  max-height: calc(100vh - 2 * var(--space-4));
}

.modal-content.small {
  max-width: 400px;
  width: 80vw;
  max-height: 80vh;
}

.modal-content.compact {
  max-width: 500px;
  width: 85vw;
  max-height: 85vh;
}

.modal.show .modal-content,
.modal:not(.hidden) .modal-content {
  transform: scale(1);
}

/* Modal Size Variants */
.modal-content.large {
  max-width: 800px;
  width: 95vw;
}

.modal-content.small {
  max-width: 400px;
  width: 80vw;
}

/* =================================
   Modal Header
   ================================= */
.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-elevated);
  flex-shrink: 0;
}

.modal-header h2 {
  margin: 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.modal-header .close-btn {
  background: none;
  border: none;
  font-size: var(--text-xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.modal-header .close-btn:hover {
  background: var(--interactive-secondary);
  color: var(--text-primary);
}

/* =================================
   Modal Body
   ================================= */
.modal-body {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--space-6);
  background: var(--modal-bg);
  min-height: 0; /* Important for flex child to allow scrolling */
}

/* Ensure modal body scrolling works properly */
.modal-content .modal-body {
  max-height: calc(90vh - 160px); /* Account for header and footer */
}

.modal-content.large .modal-body {
  max-height: calc(90vh - 160px);
}

.modal-content.small .modal-body {
  max-height: calc(80vh - 140px);
}

.modal-content.compact .modal-body {
  max-height: calc(85vh - 150px);
}

/* =================================
   Modal Footer
   ================================= */
.modal-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-elevated);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  flex-shrink: 0;
  position: sticky;
  bottom: 0;
  z-index: 10;
  margin-top: auto;
}

/* =================================
   Form Modal Styles
   ================================= */
.modal-body .form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

.modal-body .form-row {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.modal-body .form-row label {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.modal-body .form-row input,
.modal-body .form-row select,
.modal-body .form-row textarea {
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.modal-body .form-row input:focus,
.modal-body .form-row select:focus,
.modal-body .form-row textarea:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-body .form-row textarea {
  resize: vertical;
  min-height: 80px;
}

/* =================================
   Detail Modal Styles
   ================================= */
.detail-modal-content {
  max-width: 900px;
  width: 95vw;
  max-height: 90vh;
}

.detail-modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-elevated);
}

.detail-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.detail-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--space-3) 0;
}

.detail-badges {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.detail-close-btn {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: none;
  border: none;
  font-size: var(--text-xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.detail-close-btn:hover {
  background: var(--interactive-secondary);
  color: var(--text-primary);
}

/* =================================
   Modal Tabs
   ================================= */
.detail-modal-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-elevated);
  overflow-x: auto;
}

.detail-tab-link {
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-bottom: 2px solid transparent;
  white-space: nowrap;
  font-size: var(--text-sm);
}

.detail-tab-link:hover {
  color: var(--text-primary);
  background: var(--interactive-secondary);
}

.detail-tab-link.active {
  color: var(--interactive-primary);
  border-bottom-color: var(--interactive-primary);
  background: var(--primary-50);
}

.detail-modal-body {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-primary);
}

.detail-tab-content {
  padding: var(--space-6);
  display: none;
}

.detail-tab-content.active {
  display: block;
}

/* =================================
   Info Sections
   ================================= */
.detail-info-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.info-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
}

.info-section h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-primary);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3) var(--space-4);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.info-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-value {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

.info-value.empty {
  color: var(--text-tertiary);
  font-style: italic;
}

/* =================================
   Legacy Modal Support
   ================================= */

/* Animal Detail Modal */
#animal-detail-modal .animal-detail-modal-content {
  background: var(--modal-bg);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 900px;
  width: 95vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Account Detail Modal */
#account-detail-modal .account-detail-modal-content,
#cari-detail-modal .cari-detail-modal-content {
  background: var(--modal-bg);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 900px;
  width: 95vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  .modal {
    padding: var(--space-4);
  }

  .modal-content {
    width: 95vw;
    max-height: 95vh;
    border-radius: var(--radius-xl);
  }

  .modal-content.large {
    width: 98vw;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--space-4);
  }

  .detail-modal-header {
    padding: var(--space-4);
  }

  .detail-tab-content {
    padding: var(--space-4);
  }

  .detail-info-sections {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .modal-body .form-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
}

/* =================================
   AI Optimization Results Modal
   ================================= */
.optimization-results-modal {
  max-width: 900px;
  max-height: 85vh;
}

.optimization-summary {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.score-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.score-card h4 {
  margin: 0 0 20px 0;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
}

.score-breakdown {
  display: grid;
  gap: 16px;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.score-item span:first-child {
  min-width: 180px;
  font-weight: 500;
}

.score-bar {
  flex: 1;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, #10b981, #34d399);
  border-radius: 12px;
  transition: width 0.8s ease-out;
}

.score-bar span {
  position: relative;
  z-index: 1;
  font-weight: 600;
  font-size: 0.875rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.cost-summary {
  background: #f8fafc;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.cost-summary h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 1.125rem;
}

.cost-summary p {
  margin: 8px 0;
  color: #4b5563;
  display: flex;
  justify-content: space-between;
}

.feeds-summary {
  background: #fefefe;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.feeds-summary h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 1.125rem;
}

.feeds-list {
  display: grid;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.feed-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.feed-name {
  font-weight: 500;
  color: #1f2937;
  flex: 1;
}

.feed-amount {
  font-weight: 600;
  color: #059669;
  margin: 0 12px;
}

.feed-type {
  font-size: 0.75rem;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  text-transform: uppercase;
}

.badge-roughage {
  background: #dcfce7;
  color: #166534;
}

.badge-concentrate {
  background: #fef3c7;
  color: #92400e;
}

.badge-protein {
  background: #dbeafe;
  color: #1e40af;
}

.badge-mineral {
  background: #f3e8ff;
  color: #7c3aed;
}



/* Animate spin for loading */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design for optimization modal */
@media (max-width: 768px) {
  .optimization-results-modal {
    max-width: 95vw;
    margin: 10px;
  }

  .score-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .score-item span:first-child {
    min-width: auto;
    text-align: center;
  }

  .feed-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .cost-summary p {
    flex-direction: column;
    gap: 4px;
  }
}

/* =================================
   AI Optimization Controls
   ================================= */
.ai-optimization-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.ai-optimization-controls .form-select {
  min-width: 200px;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 6px 12px;
  background: white;
  color: #374151;
  transition: all 0.2s ease;
}

.ai-optimization-controls .form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ai-optimization-controls .form-select option {
  padding: 8px 12px;
  font-size: 0.875rem;
}

/* Responsive design for AI controls */
@media (max-width: 768px) {
  .ai-optimization-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .ai-optimization-controls .form-select {
    min-width: auto;
    width: 100%;
  }

  .ai-optimization-controls .btn {
    width: 100%;
    justify-content: center;
  }
}

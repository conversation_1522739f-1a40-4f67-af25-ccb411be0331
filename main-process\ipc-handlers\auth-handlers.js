const { ipcMain } = require('electron');
const { getDb } = require('../services/database.js');
const bcrypt = require('bcryptjs');

let currentUser = null;
let currentProfile = null;

/**
 * Authentication and User Management Handlers
 * Handles user login, registration, session management, and profile operations
 */

// --- USER AUTHENTICATION ---

// Register new user
ipcMain.handle('auth:register', async (event, userData) => {
  return new Promise(async (resolve, reject) => {
    try {
      const db = getDb();
      const { kullaniciAdi, email, sifre, adSoyad } = userData;

      // Check if username or email already exists
      db.get('SELECT Id FROM Kullanicilar WHERE KullaniciAdi = ? OR Email = ?', 
        [kullaniciAdi, email], async (err, existingUser) => {
        if (err) {
          reject(err);
          return;
        }

        if (existingUser) {
          reject(new Error('Kullanıcı adı veya e-posta zaten kullanımda'));
          return;
        }

        try {
          // Hash password
          const saltRounds = 12;
          const sifreHash = await bcrypt.hash(sifre, saltRounds);

          // Insert new user
          const stmt = db.prepare(`INSERT INTO Kullanicilar (
            KullaniciAdi, Email, SifreHash, AdSoyad, Rol, AktifMi, OlusturmaTarihi
          ) VALUES (?, ?, ?, ?, ?, ?, ?)`);

          stmt.run([
            kullaniciAdi, 
            email, 
            sifreHash, 
            adSoyad, 
            'User', 
            1, 
            new Date().toISOString()
          ], function(err) {
            stmt.finalize();
            if (err) {
              reject(err);
            } else {
              // Create default profile for new user
              const profileStmt = db.prepare(`INSERT INTO Profiller (
                KullaniciId, ProfilAdi, Aciklama, VarsayilanMi, AktifMi, OlusturmaTarihi
              ) VALUES (?, ?, ?, ?, ?, ?)`);

              profileStmt.run([
                this.lastID,
                'Varsayılan Profil',
                'Otomatik oluşturulan varsayılan profil',
                1,
                1,
                new Date().toISOString()
              ], function(profileErr) {
                profileStmt.finalize();
                if (profileErr) {
                  reject(profileErr);
                } else {
                  resolve({ 
                    userId: this.lastID, 
                    profileId: this.lastID,
                    message: 'Kullanıcı başarıyla oluşturuldu' 
                  });
                }
              });
            }
          });
        } catch (hashError) {
          reject(hashError);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
});

// User login
ipcMain.handle('auth:login', async (event, credentials) => {
  return new Promise(async (resolve, reject) => {
    try {
      const db = getDb();
      const { kullaniciAdi, sifre } = credentials;

      // Get user by username or email
      db.get(`SELECT * FROM Kullanicilar WHERE 
        (KullaniciAdi = ? OR Email = ?) AND AktifMi = 1`, 
        [kullaniciAdi, kullaniciAdi], async (err, user) => {
        if (err) {
          reject(err);
          return;
        }

        if (!user) {
          reject(new Error('Kullanıcı bulunamadı veya hesap aktif değil'));
          return;
        }

        try {
          // Verify password
          const isValidPassword = await bcrypt.compare(sifre, user.SifreHash);
          
          if (!isValidPassword) {
            reject(new Error('Geçersiz şifre'));
            return;
          }

          // Set current user with previous login time
          currentUser = {
            id: user.Id,
            kullaniciAdi: user.KullaniciAdi,
            email: user.Email,
            adSoyad: user.AdSoyad,
            rol: user.Rol,
            sonGirisTarihi: user.SonGirisTarihi // Önceki giriş tarihi
          };

          // Update last login time after setting current user
          db.run('UPDATE Kullanicilar SET SonGirisTarihi = ? WHERE Id = ?',
            [new Date().toISOString(), user.Id], (updateErr) => {
            if (updateErr) {
              console.error('Son giriş tarihi güncellenemedi:', updateErr);
            }
          });

          // Get user's default profile
          db.get('SELECT * FROM Profiller WHERE KullaniciId = ? AND VarsayilanMi = 1 AND AktifMi = 1', 
            [user.Id], (profileErr, profile) => {
            if (profileErr) {
              reject(profileErr);
              return;
            }

            if (profile) {
              currentProfile = {
                Id: profile.Id,
                id: profile.Id,
                profilAdi: profile.ProfilAdi,
                aciklama: profile.Aciklama
              };
            }

            resolve({
              user: currentUser,
              profile: currentProfile,
              message: 'Giriş başarılı'
            });
          });

        } catch (compareError) {
          reject(compareError);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
});

// User logout
ipcMain.handle('auth:logout', async (event, clearRemembered = false) => {
  currentUser = null;
  currentProfile = null;
  return {
    message: 'Çıkış başarılı',
    clearRemembered: clearRemembered
  };
});

// Get current session
ipcMain.handle('auth:getCurrentSession', async () => {
  return {
    user: currentUser,
    profile: currentProfile,
    isAuthenticated: !!currentUser
  };
});

// Check if user is authenticated
ipcMain.handle('auth:isAuthenticated', async () => {
  return !!currentUser;
});

// --- PROFILE MANAGEMENT ---

// Get user profiles
ipcMain.handle('profiles:list', async () => {
  return new Promise((resolve, reject) => {
    if (!currentUser) {
      reject(new Error('Kullanıcı oturumu bulunamadı'));
      return;
    }

    const db = getDb();
    db.all('SELECT * FROM Profiller WHERE KullaniciId = ? AND AktifMi = 1 ORDER BY VarsayilanMi DESC, ProfilAdi', 
      [currentUser.id], (err, profiles) => {
      if (err) reject(err);
      else resolve(profiles);
    });
  });
});

// Create new profile
ipcMain.handle('profiles:create', async (event, profileData) => {
  return new Promise((resolve, reject) => {
    if (!currentUser) {
      reject(new Error('Kullanıcı oturumu bulunamadı'));
      return;
    }

    const db = getDb();
    const { profilAdi, aciklama } = profileData;

    // Check if profile name already exists for this user
    db.get('SELECT Id FROM Profiller WHERE KullaniciId = ? AND ProfilAdi = ?', 
      [currentUser.id, profilAdi], (err, existing) => {
      if (err) {
        reject(err);
        return;
      }

      if (existing) {
        reject(new Error('Bu profil adı zaten kullanımda'));
        return;
      }

      // Create new profile
      const stmt = db.prepare(`INSERT INTO Profiller (
        KullaniciId, ProfilAdi, Aciklama, VarsayilanMi, AktifMi, OlusturmaTarihi
      ) VALUES (?, ?, ?, ?, ?, ?)`);

      stmt.run([
        currentUser.id,
        profilAdi,
        aciklama || '',
        0, // Not default
        1, // Active
        new Date().toISOString()
      ], function(err) {
        stmt.finalize();
        if (err) {
          reject(err);
        } else {
          resolve({ 
            id: this.lastID, 
            message: 'Profil başarıyla oluşturuldu' 
          });
        }
      });
    });
  });
});

// Switch to profile
ipcMain.handle('profiles:switch', async (event, profileId) => {
  return new Promise((resolve, reject) => {
    if (!currentUser) {
      reject(new Error('Kullanıcı oturumu bulunamadı'));
      return;
    }

    const db = getDb();
    db.get('SELECT * FROM Profiller WHERE Id = ? AND KullaniciId = ? AND AktifMi = 1', 
      [profileId, currentUser.id], (err, profile) => {
      if (err) {
        reject(err);
        return;
      }

      if (!profile) {
        reject(new Error('Profil bulunamadı'));
        return;
      }

      // Set current profile
      currentProfile = {
        Id: profile.Id,
        id: profile.Id,
        profilAdi: profile.ProfilAdi,
        aciklama: profile.Aciklama
      };

      resolve({
        profile: currentProfile,
        message: 'Profil değiştirildi'
      });
    });
  });
});

// Delete profile
ipcMain.handle('profiles:delete', async (event, profileId) => {
  return new Promise((resolve, reject) => {
    if (!currentUser) {
      reject(new Error('Kullanıcı oturumu bulunamadı'));
      return;
    }

    const db = getDb();
    
    // Check if it's the default profile
    db.get('SELECT VarsayilanMi FROM Profiller WHERE Id = ? AND KullaniciId = ?', 
      [profileId, currentUser.id], (err, profile) => {
      if (err) {
        reject(err);
        return;
      }

      if (!profile) {
        reject(new Error('Profil bulunamadı'));
        return;
      }

      if (profile.VarsayilanMi) {
        reject(new Error('Varsayılan profil silinemez'));
        return;
      }

      // Delete profile (CASCADE will handle related data)
      db.run('DELETE FROM Profiller WHERE Id = ? AND KullaniciId = ?', 
        [profileId, currentUser.id], function(err) {
        if (err) {
          reject(err);
        } else {
          // If current profile was deleted, switch to default
          if (currentProfile && currentProfile.id === profileId) {
            db.get('SELECT * FROM Profiller WHERE KullaniciId = ? AND VarsayilanMi = 1', 
              [currentUser.id], (defaultErr, defaultProfile) => {
              if (!defaultErr && defaultProfile) {
                currentProfile = {
                  Id: defaultProfile.Id,
                  id: defaultProfile.Id,
                  profilAdi: defaultProfile.ProfilAdi,
                  aciklama: defaultProfile.Aciklama
                };
              }
            });
          }

          resolve({ 
            changes: this.changes,
            message: 'Profil başarıyla silindi' 
          });
        }
      });
    });
  });
});

// Get current profile context
ipcMain.handle('profiles:getCurrentProfile', async () => {
  return currentProfile;
});

module.exports = {
  getCurrentUser: () => currentUser,
  getCurrentProfile: () => currentProfile,
  setCurrentUser: (user) => { currentUser = user; },
  setCurrentProfile: (profile) => { currentProfile = profile; }
};

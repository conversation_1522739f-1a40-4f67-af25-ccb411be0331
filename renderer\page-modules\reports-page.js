import { calculateAge as calculateAgeUtil } from '../utils/ui-helpers.js'; // calculateAge fonksiyonunu import et

// Expects contentArea, i18n to be passed
export async function renderReportsPage(contentArea, i18n) {
  const t = i18n.t;
  // calculateAge fonksiyonunu i18n objesiyle birlikte kullanıma hazır hale getir
  const calculateAge = (dateStr) => calculateAgeUtil(dateStr, t);

  contentArea.innerHTML = `
    <!-- Reports Header Section -->
    <div class="reports-header-section">
      <div class="reports-header-content">
        <h2 class="reports-page-title">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
            <polyline points="14,2 14,8 20,8"/>
            <line x1="16" y1="13" x2="8" y2="13"/>
            <line x1="16" y1="17" x2="8" y2="17"/>
            <polyline points="10,9 9,9 8,9"/>
          </svg>
          ${t('reports_management') || 'Raporlama'}
        </h2>
      </div>
    </div>

    <!-- Report Criteria Section -->
    <div class="reports-criteria-section">
      <div class="reports-criteria-header">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"/>
          <path d="M21 21l-4.35-4.35"/>
        </svg>
        <h3>${t('report_criteria') || 'Rapor Kriterleri'}</h3>
      </div>
      <div class="reports-criteria-content">
        <div class="reports-form-grid">
          <div class="form-group">
            <label for="report-type">${t('label_report_type') || 'Rapor Türü'}</label>
            <select id="report-type" name="report-type">
              <option value="milk_yield_summary">${t('report_milk_yield_summary') || 'Süt Verimi Özeti'}</option>
              <option value="animal_list">${t('report_animal_list') || 'Hayvan Listesi'}</option>
              <option value="health_summary">${t('report_health_summary') || 'Sağlık Özeti'}</option>
              <option value="reproduction_summary">${t('report_reproduction_summary') || 'Üreme Özeti'}</option>
              <option value="financial_summary">${t('report_financial_summary') || 'Mali Özet'}</option>
            </select>
          </div>
          <div class="form-group">
            <label for="report-start-date">${t('label_start_date') || 'Başlangıç Tarihi'}</label>
            <input type="date" id="report-start-date" name="report-start-date">
          </div>
          <div class="form-group">
            <label for="report-end-date">${t('label_end_date') || 'Bitiş Tarihi'}</label>
            <input type="date" id="report-end-date" name="report-end-date">
          </div>
          <div class="form-group">
            <label>&nbsp;</label>
            <button id="generate-report-btn" class="btn btn-primary">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              ${t('btn_generate_report') || 'Rapor Oluştur'}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Report Output Section -->
    <div class="reports-output-section">
      <div class="reports-output-header">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 11H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2h-4"/>
          <polyline points="9,11 12,14 15,11"/>
          <line x1="12" y1="2" x2="12" y2="14"/>
        </svg>
        <h3>${t('report_output') || 'Rapor Çıktısı'}</h3>
        <div id="report-actions" class="report-export-actions" style="display: none;">
          <button id="export-pdf-btn" class="btn btn-secondary">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
            </svg>
            ${t('btn_export_pdf') || 'PDF Aktar'}
          </button>
          <button id="export-excel-btn" class="btn btn-secondary">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <rect x="8" y="13" width="8" height="7"/>
            </svg>
            ${t('btn_export_excel') || 'Excel Aktar'}
          </button>
        </div>
      </div>
      <div id="report-output-area" class="reports-output-content">
        <div class="empty-message">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
            <polyline points="14,2 14,8 20,8"/>
            <line x1="16" y1="13" x2="8" y2="13"/>
            <line x1="16" y1="17" x2="8" y2="17"/>
            <polyline points="10,9 9,9 8,9"/>
          </svg>
          <p>${t('report_select_criteria_generate') || 'Lütfen rapor kriterlerini seçip "Rapor Oluştur" butonuna tıklayın.'}</p>
        </div>
      </div>
    </div>
  `;

  // Add event listeners and logic for report generation and export here
  const generateReportBtn = contentArea.querySelector('#generate-report-btn');
  const reportOutputArea = contentArea.querySelector('#report-output-area');
  const reportActions = contentArea.querySelector('#report-actions');

  generateReportBtn.addEventListener('click', async () => {
    const reportType = contentArea.querySelector('#report-type').value;
    const startDate = contentArea.querySelector('#report-start-date').value;
    const endDate = contentArea.querySelector('#report-end-date').value;

    // Basic validation
    if (reportType === 'milk_yield_summary' && (!startDate || !endDate)) {
      reportOutputArea.innerHTML = `<p style="color: red;">${t('error_date_range_required') || 'Süt verimi özeti için tarih aralığı zorunludur.'}</p>`;
      reportActions.style.display = 'none';
      return;
    }

    reportOutputArea.innerHTML = `<p>${t('report_generating') || 'Rapor oluşturuluyor...'}</p>`;
    reportActions.style.display = 'none';

    try {
      if (reportType === 'milk_yield_summary') {
        const summaryData = await window.api.invoke('reports:milk-yield-summary', { startDate, endDate });
        if (summaryData && summaryData.length > 0) {
          reportOutputArea.innerHTML = `
            <h3>${t('report_milk_yield_summary')} (${startDate} - ${endDate})</h3>
            <table class="modern-table">
              <thead>
                <tr>
                  <th class="cell-text-left">${t('label_animal_tag')}</th>
                  <th class="cell-text-right">${t('label_total_milk')}</th>
                  <th class="cell-text-right">${t('label_average_daily_milk')}</th>
                  <th class="cell-text-right">${t('label_milking_days') || 'Sağım Yapılan Gün'}</th>
                </tr>
              </thead>
              <tbody>
                ${summaryData.map(row => `
                  <tr>
                    <td class="cell-text-left">${row.HayvanKupeNo}</td>
                    <td class="cell-text-right">${row.ToplamSut.toFixed(2)}</td>
                    <td class="cell-text-right">${row.OrtalamaGunlukSut.toFixed(2)}</td>
                    <td class="cell-text-right">${row.SagimYapilanGunSayisi}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>`;
        } else {
          reportOutputArea.innerHTML = `<p>${t('no_data_for_report')}</p>`;
        }
      } else if (reportType === 'animal_list') {
        const animalData = await window.api.invoke('reports:animal-list');
        if (animalData && animalData.length > 0) {
          reportOutputArea.innerHTML = `
            <h3>${t('report_animal_list')}</h3>
            <table class="modern-table">
              <thead>
                <tr>
                  <th class="cell-text-left">${t('label_kupe_no')}</th>
                  <th class="cell-text-left">${t('label_isim')}</th>
                  <th class="cell-text-left">${t('label_tur')}</th>
                  <th class="cell-text-left">${t('label_irk')}</th>
                  <th class="cell-text-left">${t('label_cinsiyet')}</th>
                  <th class="cell-text-center">${t('label_age')}</th>
                </tr>
              </thead>
              <tbody>
                ${animalData.map(animal => `
                  <tr>
                    <td class="cell-text-left">${animal.KupeNo}</td>
                    <td class="cell-text-left">${animal.Isim || '-'}</td>
                    <td class="cell-text-left">${animal.Tur || '-'}</td>
                    <td class="cell-text-left">${animal.Irk || '-'}</td>
                    <td class="cell-text-left">${animal.Cinsiyet ? t('option_' + animal.Cinsiyet.toLowerCase()) : '-'}</td>
                    <td class="cell-text-center">${animal.DogumTarihi ? calculateAge(animal.DogumTarihi) : '-'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>`;
        } else {
          reportOutputArea.innerHTML = `<p>${t('no_data_for_report')}</p>`;
        }
      }
      if (reportOutputArea.querySelector('table')) { // Eğer bir tablo oluşturulduysa export butonlarını göster
        reportActions.style.display = 'flex';
      }
    } catch (error) {
      console.error("Error generating report:", error);
      reportOutputArea.innerHTML = `<p style="color: red;">${t('error_generating_report')} ${error.message}</p>`;
      reportActions.style.display = 'none';
    }
  });

  // --- EXPORT FUNCTIONS ---
  const exportPdfBtn = contentArea.querySelector('#export-pdf-btn');
  const exportExcelBtn = contentArea.querySelector('#export-excel-btn');

  function getReportDataFromTable(tableElement) {
    const headers = Array.from(tableElement.querySelectorAll('thead th')).map(th => th.textContent);
    const rows = Array.from(tableElement.querySelectorAll('tbody tr')).map(tr =>
      Array.from(tr.querySelectorAll('td')).map(td => td.textContent)
    );
    return { headers, rows };
  }

  exportPdfBtn.addEventListener('click', () => {
    const table = reportOutputArea.querySelector('table.modern-table');
    const reportTitleElement = reportOutputArea.querySelector('h3');
    const reportTitle = reportTitleElement ? reportTitleElement.textContent : t('report_title_default') || "Rapor";

    if (!table) {
      window.toast?.warning(t('no_report_to_export') || 'Dışa aktarılacak rapor bulunamadı.');
      return;
    }
    if (typeof pdfMake === 'undefined') {
      window.toast?.error(t('pdf_library_not_loaded') || 'PDF kütüphanesi yüklenemedi. Lütfen internet bağlantınızı kontrol edin veya sayfayı yenileyin.');
      return;
    }

    const { headers, rows } = getReportDataFromTable(table);

    if (rows.length === 0 || (rows.length === 1 && rows[0].length === 1 && rows[0][0] === t('no_data_for_report'))) {
        window.toast?.warning(t('no_data_to_export') || 'Dışa aktarılacak veri bulunamadı.');
        return;
    }

    const tableBody = [headers, ...rows];

    const documentDefinition = {
      content: [
        { text: reportTitle, style: 'header' },
        {
          table: {
            headerRows: 1,
            widths: headers.map(() => '*'), // Auto-size columns
            body: tableBody
          },
          layout: 'lightHorizontalLines' // optional
        }
      ],
      styles: {
        header: {
          fontSize: 18,
          bold: true,
          margin: [0, 0, 0, 10] // [left, top, right, bottom]
        }
      },
      defaultStyle: {
        // font: 'Roboto' // Ensure vfs_fonts.js includes Roboto or specify a default like Helvetica
      }
    };

    // pdfMake.fonts = { // If using custom fonts bundled with vfs_fonts.js
    //   Roboto: {
    //     normal: 'Roboto-Regular.ttf',
    //     bold: 'Roboto-Medium.ttf',
    //     italics: 'Roboto-Italic.ttf',
    //     bolditalics: 'Roboto-MediumItalic.ttf'
    //   }
    // };

    pdfMake.createPdf(documentDefinition).download(`${reportTitle.replace(/\s+/g, '_').toLowerCase()}_${new Date().toISOString().slice(0,10)}.pdf`);
  });

  exportExcelBtn.addEventListener('click', () => {
    const table = reportOutputArea.querySelector('table.modern-table');
    const reportTitleElement = reportOutputArea.querySelector('h3');
    const reportTitle = reportTitleElement ? reportTitleElement.textContent : t('report_title_default') || "Rapor";
    const sheetName = reportTitle.substring(0, 30); // Excel sheet names have a length limit

    if (!table) {
      window.toast?.warning(t('no_report_to_export') || 'Dışa aktarılacak rapor bulunamadı.');
      return;
    }
    if (typeof XLSX === 'undefined') {
      window.toast?.error(t('excel_library_not_loaded') || 'Excel kütüphanesi yüklenemedi. Lütfen internet bağlantınızı kontrol edin veya sayfayı yenileyin.');
      return;
    }

    const { headers, rows } = getReportDataFromTable(table);

    if (rows.length === 0 || (rows.length === 1 && rows[0].length === 1 && rows[0][0] === t('no_data_for_report'))) {
        window.toast?.warning(t('no_data_to_export') || 'Dışa aktarılacak veri bulunamadı.');
        return;
    }

    // Create worksheet
    const ws_data = [headers, ...rows];
    const ws = XLSX.utils.aoa_to_sheet(ws_data);

    // Create workbook
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, sheetName);

    // Generate Excel file and trigger download
    XLSX.writeFile(wb, `${reportTitle.replace(/\s+/g, '_').toLowerCase()}_${new Date().toISOString().slice(0,10)}.xlsx`);
  });
}

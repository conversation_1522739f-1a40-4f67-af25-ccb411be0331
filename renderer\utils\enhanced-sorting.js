/**
 * Enhanced Sorting System
 * Provides comprehensive sorting functionality for all data types with proper null handling
 * and locale-aware string comparison
 */

export class EnhancedSorting {
  /**
   * Sort an array of objects by a specified column with advanced type handling
   * @param {Array} data - Array of objects to sort
   * @param {string} column - Column key to sort by (supports nested keys with dot notation)
   * @param {number} direction - Sort direction (1 for ascending, -1 for descending)
   * @param {Object} options - Additional sorting options
   * @returns {Array} Sorted array
   */
  static sortData(data, column, direction = 1, options = {}) {
    const {
      dataType = 'auto',
      locale = 'en-US',
      nullsLast = true,
      caseSensitive = false,
      customComparator = null
    } = options;

    return [...data].sort((a, b) => {
      let valA = EnhancedSorting.getNestedValue(a, column);
      let valB = EnhancedSorting.getNestedValue(b, column);

      // Handle null/undefined values
      const nullResult = EnhancedSorting.handleNullValues(valA, valB, nullsLast);
      if (nullResult !== null) {
        return nullResult * direction;
      }

      // Use custom comparator if provided
      if (customComparator && typeof customComparator === 'function') {
        return customComparator(valA, valB) * direction;
      }

      // Auto-detect data type if not specified
      const detectedType = dataType === 'auto' ? 
        EnhancedSorting.detectDataType(valA, valB) : dataType;

      // Perform type-specific comparison
      const result = EnhancedSorting.compareByType(valA, valB, detectedType, {
        locale,
        caseSensitive
      });

      return result * direction;
    });
  }

  /**
   * Get nested value from object using dot notation
   * @param {Object} obj - Object to extract value from
   * @param {string} path - Dot-separated path to the value
   * @returns {*} The value at the specified path
   */
  static getNestedValue(obj, path) {
    if (!obj || !path) return undefined;
    
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Handle null and undefined values in sorting
   * @param {*} valA - First value
   * @param {*} valB - Second value
   * @param {boolean} nullsLast - Whether to put nulls at the end
   * @returns {number|null} Comparison result or null if both values are valid
   */
  static handleNullValues(valA, valB, nullsLast = true) {
    const aIsNull = valA === null || valA === undefined || valA === '';
    const bIsNull = valB === null || valB === undefined || valB === '';

    if (aIsNull && bIsNull) return 0;
    if (aIsNull) return nullsLast ? 1 : -1;
    if (bIsNull) return nullsLast ? -1 : 1;
    
    return null; // Both values are valid
  }

  /**
   * Auto-detect the data type of values for appropriate sorting
   * @param {*} valA - First value
   * @param {*} valB - Second value
   * @returns {string} Detected data type
   */
  static detectDataType(valA, valB) {
    // Check if both values are numbers
    if (EnhancedSorting.isNumeric(valA) && EnhancedSorting.isNumeric(valB)) {
      return 'number';
    }

    // Check if both values are dates
    if (EnhancedSorting.isDate(valA) && EnhancedSorting.isDate(valB)) {
      return 'date';
    }

    // Check if both values are booleans
    if (typeof valA === 'boolean' && typeof valB === 'boolean') {
      return 'boolean';
    }

    // Default to string
    return 'string';
  }

  /**
   * Check if a value is numeric
   * @param {*} value - Value to check
   * @returns {boolean} True if numeric
   */
  static isNumeric(value) {
    if (typeof value === 'number') return !isNaN(value);
    if (typeof value === 'string') {
      return !isNaN(value) && !isNaN(parseFloat(value)) && value.trim() !== '';
    }
    return false;
  }

  /**
   * Check if a value is a valid date
   * @param {*} value - Value to check
   * @returns {boolean} True if valid date
   */
  static isDate(value) {
    if (value instanceof Date) return !isNaN(value.getTime());
    if (typeof value === 'string') {
      // Check for common date formats
      const dateRegex = /^\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4}|\d{2}-\d{2}-\d{4}/;
      if (dateRegex.test(value)) {
        const date = new Date(value);
        return !isNaN(date.getTime());
      }
    }
    return false;
  }

  /**
   * Compare two values based on their data type
   * @param {*} valA - First value
   * @param {*} valB - Second value
   * @param {string} dataType - Data type for comparison
   * @param {Object} options - Comparison options
   * @returns {number} Comparison result (-1, 0, 1)
   */
  static compareByType(valA, valB, dataType, options = {}) {
    const { locale = 'en-US', caseSensitive = false } = options;

    switch (dataType) {
      case 'number':
        return EnhancedSorting.compareNumbers(valA, valB);
      
      case 'date':
        return EnhancedSorting.compareDates(valA, valB);
      
      case 'boolean':
        return EnhancedSorting.compareBooleans(valA, valB);
      
      case 'string':
      default:
        return EnhancedSorting.compareStrings(valA, valB, locale, caseSensitive);
    }
  }

  /**
   * Compare two numeric values
   * @param {*} valA - First value
   * @param {*} valB - Second value
   * @returns {number} Comparison result
   */
  static compareNumbers(valA, valB) {
    const numA = typeof valA === 'number' ? valA : parseFloat(valA);
    const numB = typeof valB === 'number' ? valB : parseFloat(valB);
    
    if (isNaN(numA) && isNaN(numB)) return 0;
    if (isNaN(numA)) return 1;
    if (isNaN(numB)) return -1;
    
    return numA - numB;
  }

  /**
   * Compare two date values
   * @param {*} valA - First value
   * @param {*} valB - Second value
   * @returns {number} Comparison result
   */
  static compareDates(valA, valB) {
    const dateA = valA instanceof Date ? valA : new Date(valA);
    const dateB = valB instanceof Date ? valB : new Date(valB);
    
    if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0;
    if (isNaN(dateA.getTime())) return 1;
    if (isNaN(dateB.getTime())) return -1;
    
    return dateA.getTime() - dateB.getTime();
  }

  /**
   * Compare two boolean values
   * @param {*} valA - First value
   * @param {*} valB - Second value
   * @returns {number} Comparison result
   */
  static compareBooleans(valA, valB) {
    const boolA = Boolean(valA);
    const boolB = Boolean(valB);
    
    if (boolA === boolB) return 0;
    return boolA ? 1 : -1;
  }

  /**
   * Compare two string values with locale support
   * @param {*} valA - First value
   * @param {*} valB - Second value
   * @param {string} locale - Locale for comparison
   * @param {boolean} caseSensitive - Whether comparison is case sensitive
   * @returns {number} Comparison result
   */
  static compareStrings(valA, valB, locale = 'en-US', caseSensitive = false) {
    const strA = String(valA);
    const strB = String(valB);
    
    const sensitivity = caseSensitive ? 'case' : 'base';
    
    return strA.localeCompare(strB, locale, {
      sensitivity,
      numeric: true, // Enable natural sorting (e.g., "item2" before "item10")
      ignorePunctuation: false
    });
  }

  /**
   * Create a multi-column sort function
   * @param {Array} sortCriteria - Array of sort criteria objects
   * @returns {Function} Sort function for array.sort()
   */
  static createMultiColumnSort(sortCriteria) {
    return (a, b) => {
      for (const criteria of sortCriteria) {
        const { column, direction = 1, dataType = 'auto', options = {} } = criteria;
        
        let valA = EnhancedSorting.getNestedValue(a, column);
        let valB = EnhancedSorting.getNestedValue(b, column);
        
        // Handle null values
        const nullResult = EnhancedSorting.handleNullValues(valA, valB, options.nullsLast);
        if (nullResult !== null) {
          const result = nullResult * direction;
          if (result !== 0) return result;
          continue;
        }
        
        // Detect type if auto
        const detectedType = dataType === 'auto' ? 
          EnhancedSorting.detectDataType(valA, valB) : dataType;
        
        // Compare values
        const result = EnhancedSorting.compareByType(valA, valB, detectedType, options) * direction;
        if (result !== 0) return result;
      }
      
      return 0;
    };
  }

  /**
   * Sort table data with enhanced options
   * @param {Array} data - Data to sort
   * @param {string} column - Column to sort by
   * @param {number} direction - Sort direction
   * @param {Object} columnConfig - Column configuration object
   * @returns {Array} Sorted data
   */
  static sortTableData(data, column, direction, columnConfig = {}) {
    const options = {
      dataType: columnConfig.dataType || 'auto',
      locale: columnConfig.locale || 'en-US',
      nullsLast: columnConfig.nullsLast !== false,
      caseSensitive: columnConfig.caseSensitive === true,
      customComparator: columnConfig.customComparator
    };

    return EnhancedSorting.sortData(data, column, direction, options);
  }

  /**
   * Get sort indicator class for UI
   * @param {string} column - Column name
   * @param {string} currentSortColumn - Currently sorted column
   * @param {number} sortDirection - Current sort direction
   * @returns {string} CSS class for sort indicator
   */
  static getSortIndicatorClass(column, currentSortColumn, sortDirection) {
    if (column !== currentSortColumn) return '';
    return sortDirection === 1 ? 'sorted-asc' : 'sorted-desc';
  }

  /**
   * Toggle sort direction for a column
   * @param {string} column - Column to toggle
   * @param {string} currentSortColumn - Currently sorted column
   * @param {number} currentDirection - Current sort direction
   * @returns {Object} New sort state
   */
  static toggleSortDirection(column, currentSortColumn, currentDirection) {
    if (column === currentSortColumn) {
      return {
        column,
        direction: currentDirection * -1
      };
    } else {
      return {
        column,
        direction: 1
      };
    }
  }
}

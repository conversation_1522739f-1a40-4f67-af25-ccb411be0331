/**
 * Modal Utilities for Enhanced Livestock Management Forms
 * Provides dynamic behavior for improved modal forms
 */

// Quarantine date visibility toggle for treatment modal
function toggleQuarantineDates(value) {
  const quarantineDates = document.querySelectorAll('.quarantine-dates');
  const isVisible = value === 'Var';
  
  quarantineDates.forEach(element => {
    element.style.display = isVisible ? 'flex' : 'none';
    
    // Clear values when hiding
    if (!isVisible) {
      const inputs = element.querySelectorAll('input');
      inputs.forEach(input => input.value = '');
    }
  });
}

// Pregnancy outcome sections toggle
function togglePregnancyOutcome(value) {
  const birthDetails = document.querySelector('.birth-details');
  const failureDetails = document.querySelector('.failure-details');
  
  // Hide all sections first
  if (birthDetails) birthDetails.style.display = 'none';
  if (failureDetails) failureDetails.style.display = 'none';
  
  // Show relevant section
  if (value === 'Başarılı' && birthDetails) {
    birthDetails.style.display = 'block';
  } else if (value === 'Başarısız' && failureDetails) {
    failureDetails.style.display = 'block';
  }
  
  // Clear hidden section values
  if (value !== 'Başarılı' && birthDetails) {
    const inputs = birthDetails.querySelectorAll('input, select');
    inputs.forEach(input => input.value = '');
  }
  
  if (value !== 'Başarısız' && failureDetails) {
    const inputs = failureDetails.querySelectorAll('input, select');
    inputs.forEach(input => input.value = '');
  }
}

// Insemination type toggle for semen information
function toggleInseminationType() {
  const inseminationForm = document.getElementById('insemination-form');
  if (!inseminationForm) return;
  
  const typeRadios = inseminationForm.querySelectorAll('input[name="TohumlamaTipi"]');
  const semenInfo = inseminationForm.querySelector('.semen-info');
  
  typeRadios.forEach(radio => {
    radio.addEventListener('change', function() {
      if (semenInfo) {
        semenInfo.style.display = this.value === 'Suni Tohumlama' ? 'block' : 'none';
        
        // Clear semen info when hiding
        if (this.value !== 'Suni Tohumlama') {
          const inputs = semenInfo.querySelectorAll('input, select');
          inputs.forEach(input => input.value = '');
        }
      }
    });
  });
}

// Calculate due date for pregnancy (cattle gestation ~280 days)
function calculateDueDate() {
  const startDateInput = document.querySelector('input[name="BaslangicTarihi"]');
  const dueDateInput = document.querySelector('input[name="BeklenenDogumTarihi"]');
  const durationInput = document.querySelector('input[name="GebelikSuresi"]');
  
  if (!startDateInput || !dueDateInput) return;
  
  const startDate = new Date(startDateInput.value);
  if (isNaN(startDate.getTime())) return;
  
  // Add 280 days for cattle gestation
  const dueDate = new Date(startDate);
  dueDate.setDate(dueDate.getDate() + 280);
  
  dueDateInput.value = dueDate.toISOString().split('T')[0];
  
  // Calculate current duration if end date exists
  if (durationInput) {
    const today = new Date();
    const diffTime = Math.abs(today - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    durationInput.value = diffDays;
  }
}

// Calculate lactation duration
function calculateLactationDuration() {
  const startDateInput = document.querySelector('input[name="BaslangicTarihi"]');
  const endDateInput = document.querySelector('input[name="BitisTarihi"]');
  const durationInput = document.querySelector('input[name="LaktasyonSuresi"]');
  
  if (!startDateInput || !durationInput) return;
  
  const startDate = new Date(startDateInput.value);
  const endDate = endDateInput && endDateInput.value ? new Date(endDateInput.value) : new Date();
  
  if (isNaN(startDate.getTime())) return;
  
  const diffTime = Math.abs(endDate - startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  durationInput.value = diffDays;
}

// Calculate milking rate
function calculateMilkingRate() {
  const milkingForm = document.getElementById('milking-form');
  if (!milkingForm) return;
  
  const quantityInput = milkingForm.querySelector('input[name="Miktar"]');
  const durationInput = milkingForm.querySelector('input[name="SagimSuresi"]');
  const rateInput = milkingForm.querySelector('input[name="SagimHizi"]');
  
  if (!quantityInput || !durationInput || !rateInput) return;
  
  quantityInput.addEventListener('input', updateRate);
  durationInput.addEventListener('input', updateRate);
  
  function updateRate() {
    const quantity = parseFloat(quantityInput.value) || 0;
    const duration = parseFloat(durationInput.value) || 0;
    
    if (quantity > 0 && duration > 0) {
      const rate = (quantity / duration).toFixed(2);
      rateInput.value = rate;
    } else {
      rateInput.value = '';
    }
  }
}

// Enhanced form validation
async function validateModalForm(formId) {
  const form = document.getElementById(formId);
  if (!form) return true;

  let isValid = true;
  const errors = [];

  // Clear previous errors
  clearFormErrors(form);

  // Check required fields
  const requiredFields = form.querySelectorAll('[required]');
  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      isValid = false;
      const message = window.i18n.t('validation_field_required', { field: getFieldLabel(field) });
      addFieldError(field, message);
      errors.push(message);
    }
  });

  // Validate number ranges
  const numberInputs = form.querySelectorAll('input[type="number"]');
  numberInputs.forEach(input => {
    const value = parseFloat(input.value);
    const min = parseFloat(input.min);
    const max = parseFloat(input.max);

    if (input.value && !isNaN(value)) {
      if (!isNaN(min) && value < min) {
        isValid = false;
        const message = window.i18n.t('validation_min_value', { min: min });
        addFieldError(input, message);
        errors.push(message);
      } else if (!isNaN(max) && value > max) {
        isValid = false;
        const message = window.i18n.t('validation_max_value', { max: max });
        addFieldError(input, message);
        errors.push(message);
      }
    }
  });

  // Validate email fields
  const emailInputs = form.querySelectorAll('input[type="email"]');
  emailInputs.forEach(input => {
    if (input.value && !isValidEmail(input.value)) {
      isValid = false;
      const message = window.i18n.t('validation_invalid_email');
      addFieldError(input, message);
      errors.push(message);
    }
  });

  // Validate date ranges
  const dateInputs = form.querySelectorAll('input[type="date"]');
  dateInputs.forEach(input => {
    if (input.value) {
      const date = new Date(input.value);
      const today = new Date();

      // Check for future dates where not appropriate
      // Gelecek tarih olabilir: BeklenenDogumTarihi, BitisTarihi
      // Gelecek tarih olamaz: DogumTarihi, TohumlamaTarihi, BaslangicTarihi, Tarih (sağım)
      const futureNotAllowedFields = ['DogumTarihi', 'TohumlamaTarihi', 'BaslangicTarihi', 'Tarih'];
      const futureAllowedFields = ['BeklenenDogumTarihi', 'BitisTarihi'];

      if (futureNotAllowedFields.includes(input.name)) {
        if (date > today) {
          isValid = false;
          const message = window.i18n.t('validation_future_date_not_allowed', { field: getFieldLabel(input) });
          addFieldError(input, message);
          errors.push(message);
        }
      }

      // Check for very old dates (more than 50 years ago)
      const fiftyYearsAgo = new Date();
      fiftyYearsAgo.setFullYear(fiftyYearsAgo.getFullYear() - 50);
      if (date < fiftyYearsAgo) {
        isValid = false;
        const message = window.i18n.t('validation_date_too_old', { field: getFieldLabel(input) });
        addFieldError(input, message);
        errors.push(message);
      }
    }
  });

  // Validate business logic
  const businessLogicValid = await validateBusinessLogic(form);
  isValid = businessLogicValid && isValid;

  // Show errors if any
  if (!isValid && errors.length > 0) {
    showValidationErrors(errors);
  }

  return isValid;
}

// Helper functions for validation
function getFieldLabel(field) {
  const label = field.closest('.form-row')?.querySelector('label');
  return label?.textContent?.replace(':', '') || field.name || 'Field';
}

function addFieldError(field, message) {
  field.classList.add('error');

  // Add error message below field
  let errorDiv = field.parentNode.querySelector('.error-message');
  if (!errorDiv) {
    errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    field.parentNode.appendChild(errorDiv);
  }
  errorDiv.textContent = message;
}

function clearFormErrors(form) {
  // Remove error classes
  form.querySelectorAll('.error').forEach(el => el.classList.remove('error'));

  // Remove error messages
  form.querySelectorAll('.error-message').forEach(el => el.remove());
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

async function validateBusinessLogic(form) {
  let isValid = true;

  // Validate pregnancy dates
  const startDate = form.querySelector('input[name="BaslangicTarihi"]');
  const endDate = form.querySelector('input[name="BeklenenDogumTarihi"]');

  if (startDate && endDate && startDate.value && endDate.value) {
    const start = new Date(startDate.value);
    const end = new Date(endDate.value);

    if (end <= start) {
      isValid = false;
      const message = window.i18n.t('validation_due_date_after_start');
      addFieldError(endDate, message);
    }
  }

  // Validate milk yield values
  const milkQuantity = form.querySelector('input[name="Miktar"]');
  if (milkQuantity && milkQuantity.value) {
    const quantity = parseFloat(milkQuantity.value);
    if (quantity > 100) { // Unrealistic daily milk yield
      const message = window.i18n.t('validation_milk_yield_too_high');
      addFieldError(milkQuantity, message);
    }
  }

  // İş kuralları validation'ı
  const animalIdField = form.querySelector('input[name="HayvanId"]');
  if (animalIdField && animalIdField.value) {
    const animalId = parseInt(animalIdField.value);
    const formId = form.id;

    // Form tipini belirle
    let operation = null;
    if (formId.includes('pregnancy') || formId.includes('gebelik')) {
      operation = 'pregnancy';
    } else if (formId.includes('insemination') || formId.includes('tohumlama')) {
      operation = 'insemination';
    } else if (formId.includes('lactation') || formId.includes('laktasyon')) {
      operation = 'lactation';
    } else if (formId.includes('milking') || formId.includes('sagim')) {
      operation = 'milking';
    }

    if (operation) {
      try {
        const validationData = {
          animalId: animalId,
          operation: operation,
          startDate: startDate ? startDate.value : null,
          endDate: endDate ? endDate.value : null,
          excludeId: form.querySelector('input[name="Id"]')?.value || null
        };

        const validationResult = await window.api.invoke('validation:validateBusinessRules', validationData);

        if (!validationResult.isValid) {
          isValid = false;
          validationResult.errors.forEach(error => {
            // Translate the error message if it's an i18n key
            const translatedError = error.startsWith('validation_') ? window.i18n.t(error) : error;
            // İlk uygun field'a error ekle
            const targetField = animalIdField || startDate || form.querySelector('input[type="text"]');
            if (targetField) {
              addFieldError(targetField, translatedError);
            }
          });
        }
      } catch (error) {
        console.error('Business rule validation error:', error);
        // Validation hatası durumunda işleme devam et ama uyar
        const message = window.i18n.t('validation_check_failed');
        addFieldError(animalIdField, message);
      }
    }
  }

  return isValid;
}

function showValidationErrors(errors) {
  // Create a more user-friendly error display
  const errorContainer = document.createElement('div');
  errorContainer.className = 'validation-errors';
  errorContainer.innerHTML = `
    <div class="error-header">
      <strong>Please fix the following issues:</strong>
    </div>
    <ul>
      ${errors.map(error => `<li>${error}</li>`).join('')}
    </ul>
  `;

  // Show as toast notification
  window.toast.warning('Please fix the following errors:\n\n' + errors.join('\n'));
}

// Initialize all modal enhancements
function initializeModalEnhancements() {
  // Set up insemination type toggle
  toggleInseminationType();

  // Set up milking rate calculation
  calculateMilkingRate();

  // Set up accounting modal enhancements
  initializeAccountingModals();

  // Set up real-time validation
  setupRealTimeValidation();

  // Set up accessibility features
  setupAccessibility();

  // Enhance form submission
  enhanceFormSubmission();

  // Add enhanced error styling to CSS if not already present
  if (!document.querySelector('#modal-error-styles')) {
    const style = document.createElement('style');
    style.id = 'modal-error-styles';
    style.textContent = `
      .error {
        border-color: #ef4444 !important;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
        animation: shake 0.3s ease-in-out;
      }

      .error:focus {
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
      }

      .error-message {
        color: #ef4444;
        font-size: 0.8rem;
        margin-top: 4px;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .error-message::before {
        content: '⚠';
        font-size: 0.9rem;
      }

      @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-4px); }
        75% { transform: translateX(4px); }
      }

      .validation-errors {
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        color: #991b1b;
      }

      .validation-errors .error-header {
        margin-bottom: 8px;
        font-weight: 600;
      }

      .validation-errors ul {
        margin: 0;
        padding-left: 20px;
      }

      .validation-errors li {
        margin-bottom: 4px;
      }

      /* Success states */
      .success {
        border-color: #10b981 !important;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
      }

      .success-message {
        color: #10b981;
        font-size: 0.8rem;
        margin-top: 4px;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .success-message::before {
        content: '✓';
        font-size: 0.9rem;
      }
    `;
    document.head.appendChild(style);
  }


}

// Accessibility and focus management
function setupAccessibility() {
  // Add ARIA attributes to modals (skip unified modals)
  document.querySelectorAll('.modal').forEach(modal => {
    // Skip unified modals
    const content = modal.querySelector('.modal-content');
    if (content && (
        content.classList.contains('detail-modal-content') ||
        modal.id === 'animal-detail-modal' ||
        modal.id === 'cari-detail-modal' ||
        modal.id === 'account-detail-modal'
    )) {
      return;
    }

    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-modal', 'true');
    modal.setAttribute('aria-hidden', 'true');

    // Add aria-labelledby to modal content
    const title = modal.querySelector('h2, h3');
    if (title && !title.id) {
      title.id = `modal-title-${Math.random().toString(36).substring(2, 11)}`;
    }
    if (title) {
      modal.setAttribute('aria-labelledby', title.id);
    }
  });

  // Set up focus management
  setupFocusManagement();
}

function setupFocusManagement() {
  let lastFocusedElement = null;

  // When modal opens, trap focus
  document.addEventListener('click', function(e) {
    if (e.target.matches('.close-btn') || e.target.closest('.modal') === e.target) {
      const modal = e.target.closest('.modal');
      if (modal && !modal.classList.contains('hidden')) {
        closeModal(modal);
      }
    }
  });

  // Trap focus within modal
  document.addEventListener('keydown', function(e) {
    const openModal = document.querySelector('.modal:not(.hidden)');
    if (!openModal) return;

    if (e.key === 'Escape') {
      closeModal(openModal);
      return;
    }

    if (e.key === 'Tab') {
      trapFocus(e, openModal);
    }
  });

  function trapFocus(e, modal) {
    const focusableElements = modal.querySelectorAll(
      'input, select, textarea, button, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  }

  function closeModal(modal) {
    modal.classList.add('hidden');
    modal.setAttribute('aria-hidden', 'true');

    // Return focus to the element that opened the modal
    if (lastFocusedElement) {
      lastFocusedElement.focus();
      lastFocusedElement = null;
    }
  }

  // Store the element that opened the modal
  document.addEventListener('click', function(e) {
    if (e.target.matches('[data-opens-modal]') ||
        e.target.closest('button')?.textContent.includes('Add') ||
        e.target.closest('button')?.textContent.includes('Edit')) {
      lastFocusedElement = e.target;
    }
  });
}

// Form submission with loading states
function enhanceFormSubmission() {
  document.addEventListener('submit', function(e) {
    if (e.target.closest('.modal')) {
      const submitButton = e.target.querySelector('button[type="submit"]');
      if (submitButton) {
        submitButton.classList.add('loading');
        submitButton.disabled = true;

        // Re-enable after a delay (in real app, this would be after API response)
        setTimeout(() => {
          submitButton.classList.remove('loading');
          submitButton.disabled = false;
        }, 2000);
      }
    }
  });
}

// Convert existing modals to new structure
function convertModalsToNewStructure() {
  const modals = document.querySelectorAll('.modal');

  modals.forEach(modal => {
    const content = modal.querySelector('.modal-content');
    if (!content) return;

    // Skip if special modals or unified modals
    if (content.classList.contains('animal-detail-modal-content') ||
        content.classList.contains('account-detail-modal-content') ||
        content.classList.contains('cari-detail-modal-content') ||
        content.classList.contains('detail-modal-content') ||
        modal.id === 'animal-detail-modal' ||
        modal.id === 'cari-detail-modal' ||
        modal.id === 'account-detail-modal') {

      return;
    }

    // Check if already converted but force re-processing for footer issues
    const hasHeader = content.querySelector('.modal-header');
    const hasFooter = content.querySelector('.modal-footer');
    const hasForm = content.querySelector('form');

    if (hasHeader && hasFooter && hasForm) {

      // Still apply compact class and footer fixes
      content.classList.add('modal-compact');
      const footer = content.querySelector('.modal-footer');
      if (footer) {
        footer.style.flexShrink = '0';
        footer.style.minHeight = '60px';
      }
      return;
    }



    // Get existing elements
    const closeBtn = content.querySelector('.close-btn');
    const title = content.querySelector('h2, h3');
    const form = content.querySelector('form');
    const formActions = form?.querySelector('.form-actions');

    // Handle modals without forms (like detail modals)
    if (!form) {
      // Create header structure for non-form modals
      const header = document.createElement('div');
      header.className = 'modal-header';

      const body = document.createElement('div');
      body.className = 'modal-body';

      // Move close button and title to header
      if (closeBtn) {
        header.appendChild(closeBtn);
      }
      if (title) {
        header.appendChild(title);
      }

      // Move remaining content to body
      const remainingElements = Array.from(content.children).filter(child =>
        child !== closeBtn && child !== title
      );

      remainingElements.forEach(element => {
        body.appendChild(element);
      });

      // Clear content and rebuild
      content.innerHTML = '';
      content.appendChild(header);
      content.appendChild(body);

      content.classList.add('modal-compact');
      return;
    }

    // Create new structure for form modals
    const header = document.createElement('div');
    header.className = 'modal-header';

    const body = document.createElement('div');
    body.className = 'modal-body';

    const footer = document.createElement('div');
    footer.className = 'modal-footer';

    // Move close button and title to header
    if (closeBtn) {
      header.appendChild(closeBtn);
    }
    if (title) {
      header.appendChild(title);
    }

    // Move form content to body (except form actions)
    const formElements = Array.from(form.children).filter(child =>
      !child.classList.contains('form-actions')
    );

    formElements.forEach(element => {
      body.appendChild(element);
    });

    // Move form actions to footer
    if (formActions) {
      // Remove any existing margins from form actions
      formActions.style.margin = '0';
      formActions.style.padding = '0';
      footer.appendChild(formActions);
    } else {
      // Create default form actions if none exist
      const defaultActions = document.createElement('div');
      defaultActions.className = 'form-actions';
      const modalId = modal.id || 'modal';
      defaultActions.innerHTML = `
        <button type="button" class="btn btn-secondary" onclick="document.getElementById('${modalId}').classList.add('hidden')">İptal</button>
        <button type="submit" class="btn btn-primary">Kaydet</button>
      `;
      footer.appendChild(defaultActions);
    }

    // Clear content and rebuild
    content.innerHTML = '';
    content.appendChild(header);

    // Wrap body in form
    form.innerHTML = '';
    form.appendChild(body);
    form.appendChild(footer);

    content.appendChild(form);

    // Add compact class for better styling
    content.classList.add('modal-compact');

    // Ensure footer is always visible
    footer.style.flexShrink = '0';
    footer.style.minHeight = '60px';


  });
}

// Optimize modal scrolling
function optimizeModalScrolling() {
  // Add smooth scrolling to modal bodies (skip unified modals)
  const modalBodies = document.querySelectorAll('.modal-body');
  modalBodies.forEach(body => {
    // Skip unified modal bodies
    const modal = body.closest('.modal');
    if (modal && (
        modal.id === 'animal-detail-modal' ||
        modal.id === 'cari-detail-modal' ||
        modal.id === 'account-detail-modal' ||
        modal.querySelector('.detail-modal-content')
    )) {
      return;
    }

    body.style.scrollBehavior = 'smooth';

    // Add scroll indicators
    const scrollIndicator = document.createElement('div');
    scrollIndicator.className = 'scroll-indicator';
    scrollIndicator.innerHTML = '↓ Scroll for more';
    scrollIndicator.style.cssText = `
      position: absolute;
      bottom: 60px;
      right: 20px;
      background: var(--accent);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.7rem;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
      z-index: 10;
    `;

    body.parentElement.appendChild(scrollIndicator);

    // Show/hide scroll indicator
    body.addEventListener('scroll', function() {
      const isScrollable = this.scrollHeight > this.clientHeight;
      const isAtBottom = this.scrollTop + this.clientHeight >= this.scrollHeight - 10;

      if (isScrollable && !isAtBottom) {
        scrollIndicator.style.opacity = '0.8';
      } else {
        scrollIndicator.style.opacity = '0';
      }
    });

    // Initial check
    setTimeout(() => {
      const isScrollable = body.scrollHeight > body.clientHeight;
      if (isScrollable) {
        scrollIndicator.style.opacity = '0.8';
        setTimeout(() => {
          scrollIndicator.style.opacity = '0';
        }, 3000);
      }
    }, 100);
  });
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      convertModalsToNewStructure();
      initializeModalEnhancements();
      optimizeModalScrolling();

    }, 100); // Small delay to ensure all content is loaded
  });
} else {
  setTimeout(() => {
    convertModalsToNewStructure();
    initializeModalEnhancements();
    optimizeModalScrolling();

  }, 100);
}

// Also run conversion when new content is dynamically loaded
function reinitializeModals() {
  convertModalsToNewStructure();
  initializeModalEnhancements();
  optimizeModalScrolling();

}

// Export for manual triggering
window.reinitializeModals = reinitializeModals;

// Debug function for unified modals
window.debugUnifiedModals = function() {


  const unifiedModals = ['animal-detail-modal', 'cari-detail-modal', 'account-detail-modal'];

  unifiedModals.forEach(modalId => {
    const modal = document.getElementById(modalId);
    if (modal) {
      const isHidden = modal.classList.contains('hidden');
      const content = modal.querySelector('.detail-modal-content');
      const hasContent = !!content;







      if (content) {
  
      }
    } else {

    }
  });
};

// Debug function to check modal status
window.debugModals = function() {
  const modals = document.querySelectorAll('.modal');


  modals.forEach(modal => {
    const content = modal.querySelector('.modal-content');
    const hasHeader = content?.querySelector('.modal-header');
    const hasFooter = content?.querySelector('.modal-footer');
    const hasForm = modal.querySelector('form');
    const hasFormActions = modal.querySelector('.form-actions');








    // Check footer visibility
    if (hasFooter) {
      const footerRect = hasFooter.getBoundingClientRect();
      const contentRect = content.getBoundingClientRect();
      const isFooterVisible = footerRect.height > 0 && footerRect.bottom <= contentRect.bottom + 5;


    }
  });
};

// Test modal functionality
window.testModalFooters = function() {


  const modals = document.querySelectorAll('.modal');
  modals.forEach(modal => {
    const content = modal.querySelector('.modal-content');
    const footer = modal.querySelector('.modal-footer');
    const buttons = modal.querySelectorAll('.form-actions .btn');

    if (footer && buttons.length > 0) {
      // Temporarily show modal to test
      const wasHidden = modal.classList.contains('hidden');
      modal.classList.remove('hidden');

      setTimeout(() => {
        const footerRect = footer.getBoundingClientRect();
        const contentRect = content.getBoundingClientRect();
        const isFooterVisible = footerRect.height > 0 && footerRect.bottom <= contentRect.bottom + 5;



        buttons.forEach((btn, index) => {
          const btnRect = btn.getBoundingClientRect();
          const isBtnVisible = btnRect.height > 0 && btnRect.width > 0;

        });

        // Hide modal again if it was hidden
        if (wasHidden) {
          modal.classList.add('hidden');
        }
      }, 100);
    }
  });
};

// Watch for dynamically added modals (skip unified modals)
const modalObserver = new MutationObserver((mutations) => {
  let shouldReinitialize = false;

  mutations.forEach((mutation) => {
    mutation.addedNodes.forEach((node) => {
      if (node.nodeType === Node.ELEMENT_NODE) {
        // Check if the added node is a modal or contains modals
        if (node.classList?.contains('modal') || node.querySelector?.('.modal')) {
          // Skip if it's a unified modal
          if (node.id === 'animal-detail-modal' ||
              node.id === 'cari-detail-modal' ||
              node.id === 'account-detail-modal' ||
              node.querySelector?.('.detail-modal-content')) {
            return;
          }
          shouldReinitialize = true;
        }
      }
    });
  });

  if (shouldReinitialize) {
    setTimeout(() => {
      convertModalsToNewStructure();

    }, 50);
  }
});

// Start observing
if (document.body) {
  modalObserver.observe(document.body, {
    childList: true,
    subtree: true
  });
} else {
  document.addEventListener('DOMContentLoaded', () => {
    modalObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  });
}

// Global tab system for unified modals
document.addEventListener('click', function(e) {
  if (e.target.classList.contains('detail-tab-link') || e.target.closest('.detail-tab-link')) {
    const tabLink = e.target.classList.contains('detail-tab-link') ? e.target : e.target.closest('.detail-tab-link');
    const modal = tabLink.closest('.modal');
    const targetTab = tabLink.dataset.tab;

    if (!modal || !targetTab) return;

    // Remove active class from all tabs and contents in this modal
    modal.querySelectorAll('.detail-tab-link').forEach(link => link.classList.remove('active'));
    modal.querySelectorAll('.detail-tab-content').forEach(content => content.classList.remove('active'));

    // Add active class to clicked tab and corresponding content
    tabLink.classList.add('active');
    const targetContent = modal.querySelector(`#${targetTab}`);
    if (targetContent) {
      targetContent.classList.add('active');
    }
  }
});

// Initialize unified modals when they are opened
function initializeUnifiedModal(modal) {
  if (!modal || !modal.querySelector('.detail-modal-content')) return;

  // Add accessibility attributes
  modal.setAttribute('role', 'dialog');
  modal.setAttribute('aria-modal', 'true');
  modal.setAttribute('aria-hidden', 'false');

  // Add aria-labelledby
  const title = modal.querySelector('.detail-title');
  if (title && !title.id) {
    title.id = `unified-modal-title-${Date.now()}`;
  }
  if (title) {
    modal.setAttribute('aria-labelledby', title.id);
  }

  // Setup smooth scrolling for modal body
  const modalBody = modal.querySelector('.detail-modal-body');
  if (modalBody) {
    modalBody.style.scrollBehavior = 'smooth';
  }

  // Setup keyboard navigation
  setupUnifiedModalKeyboard(modal);
}

// Keyboard navigation for unified modals
function setupUnifiedModalKeyboard(modal) {
  modal.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      const closeBtn = modal.querySelector('.detail-close-btn');
      if (closeBtn) {
        closeBtn.click();
      }
    }

    // Tab navigation within modal
    if (e.key === 'Tab') {
      const focusableElements = modal.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      } else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  });
}

// Auto-initialize unified modals when they become visible
const unifiedModalObserver = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    mutation.target.classList?.forEach?.((className) => {
      if (className === 'modal' && !mutation.target.classList.contains('hidden')) {
        const modal = mutation.target;
        if (modal.querySelector('.detail-modal-content')) {
          initializeUnifiedModal(modal);
        }
      }
    });
  });
});

// Observe class changes on modals
document.querySelectorAll('.modal').forEach(modal => {
  if (modal.querySelector('.detail-modal-content')) {
    unifiedModalObserver.observe(modal, {
      attributes: true,
      attributeFilter: ['class']
    });
  }
});

// Toggle due date field visibility for sales/purchases
function toggleDueDate(paymentStatus) {
  const dueDateField = document.querySelector('.due-date-field');
  if (dueDateField) {
    dueDateField.style.display = paymentStatus === 'Vadeli' ? 'flex' : 'none';

    // Clear due date when hiding
    if (paymentStatus !== 'Vadeli') {
      const dueDateInput = dueDateField.querySelector('input[type="date"]');
      if (dueDateInput) dueDateInput.value = '';
    }
  }
}

// Calculate total amount for sales/purchases
function calculateTotalAmount() {
  const quantityInput = document.querySelector('input[name="Miktar"]');
  const priceInput = document.querySelector('input[name="BirimFiyat"]');
  const totalInput = document.querySelector('input[name="ToplamTutar"]');

  if (!quantityInput || !priceInput || !totalInput) return;

  quantityInput.addEventListener('input', updateTotal);
  priceInput.addEventListener('input', updateTotal);

  function updateTotal() {
    const quantity = parseFloat(quantityInput.value) || 0;
    const price = parseFloat(priceInput.value) || 0;
    const total = quantity * price;

    totalInput.value = total.toFixed(2);
  }
}

// Initialize accounting modal enhancements
function initializeAccountingModals() {
  // Set up total amount calculation
  calculateTotalAmount();

  // Set up payment status change handlers
  const paymentStatusSelects = document.querySelectorAll('select[name="OdemeDurumu"]');
  paymentStatusSelects.forEach(select => {
    select.addEventListener('change', function() {
      toggleDueDate(this.value);
    });
  });
}

// Real-time validation feedback
function setupRealTimeValidation() {
  // Add event listeners to all form inputs for real-time validation
  document.addEventListener('input', function(e) {
    if (e.target.matches('input, select, textarea')) {
      validateField(e.target);
    }
  });

  document.addEventListener('blur', function(e) {
    if (e.target.matches('input, select, textarea')) {
      validateField(e.target);
    }
  });
}

function validateField(field) {
  // Clear previous validation state
  field.classList.remove('error', 'success');
  const errorMsg = field.parentNode.querySelector('.error-message');
  const successMsg = field.parentNode.querySelector('.success-message');
  if (errorMsg) errorMsg.remove();
  if (successMsg) successMsg.remove();

  let isValid = true;
  let message = '';

  // Required field validation
  if (field.hasAttribute('required') && !field.value.trim()) {
    isValid = false;
    message = `${getFieldLabel(field)} is required`;
  }

  // Email validation
  if (field.type === 'email' && field.value && !isValidEmail(field.value)) {
    isValid = false;
    message = 'Please enter a valid email address';
  }

  // Number validation
  if (field.type === 'number' && field.value) {
    const value = parseFloat(field.value);
    const min = parseFloat(field.min);
    const max = parseFloat(field.max);

    if (isNaN(value)) {
      isValid = false;
      message = 'Please enter a valid number';
    } else if (!isNaN(min) && value < min) {
      isValid = false;
      message = `Value must be at least ${min}`;
    } else if (!isNaN(max) && value > max) {
      isValid = false;
      message = `Value must be at most ${max}`;
    }
  }

  // Date validation
  if (field.type === 'date' && field.value) {
    const date = new Date(field.value);
    const today = new Date();

    // Gelecek tarih olabilir: BeklenenDogumTarihi, BitisTarihi
    // Gelecek tarih olamaz: DogumTarihi, TohumlamaTarihi, BaslangicTarihi, Tarih (sağım)
    const futureNotAllowedFields = ['DogumTarihi', 'TohumlamaTarihi', 'BaslangicTarihi', 'Tarih'];

    if (futureNotAllowedFields.includes(field.name)) {
      if (date > today) {
        isValid = false;
        message = window.i18n.t('validation_future_date_not_allowed', { field: getFieldLabel(field) });
      }
    }
  }

  // Apply validation result
  if (!isValid && message) {
    addFieldError(field, message);
  } else if (field.value && field.hasAttribute('required')) {
    // Show success for required fields that are valid
    addFieldSuccess(field);
  }
}

function addFieldSuccess(field) {
  field.classList.add('success');

  // Add success message below field
  let successDiv = field.parentNode.querySelector('.success-message');
  if (!successDiv) {
    successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    field.parentNode.appendChild(successDiv);
  }
  successDiv.textContent = 'Valid';
}

// Export functions for global use
window.toggleQuarantineDates = toggleQuarantineDates;
window.togglePregnancyOutcome = togglePregnancyOutcome;
window.calculateDueDate = calculateDueDate;
window.calculateLactationDuration = calculateLactationDuration;
window.validateModalForm = validateModalForm;
window.toggleDueDate = toggleDueDate;
window.calculateTotalAmount = calculateTotalAmount;

const { ipcMain } = require('electron');
const { getDb, createGenericCrudHandlers } = require('../services/database.js');
const { getCurrentProfile } = require('./auth-handlers.js');

const db = getDb(); // Ensure db is available, though generic handlers use getDb() internally too

// --- AŞILAMALAR CRUD ---
const asilamalarColumns = ['AsiAdi', 'AsilamaTarihi', 'Doz', 'UygulayanVeteriner', 'Notlar'];
const asilamalarCrud = createGenericCrudHandlers('Asilamalar', asilamalarColumns, { hasHayvanId: true, requiresProfile: true });

ipcMain.handle('asilamalar:list', asilamalarCrud.listByHayvanId);
ipcMain.handle('asilamalar:add', asilamalarCrud.add);
ipcMain.handle('asilamalar:update', asilamalarCrud.update);
ipcMain.handle('asilamalar:delete', asilamalarCrud.delete);

// Add listAll method for asilamalar with profile context
ipcMain.handle('asilamalar:listAll', async () => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.all('SELECT * FROM Asilamalar WHERE ProfilId = ? ORDER BY AsilamaTarihi DESC, Id DESC', [currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
});

// --- TEDAVİLER CRUD ---
const tedavilerColumns = ['Teshis', 'TedaviBaslangicTarihi', 'TedaviBitisTarihi', 'KullanilanIlaclar', 'KarantinaDurumu', 'Notlar'];
const tedavilerCrud = createGenericCrudHandlers('Tedaviler', tedavilerColumns, { hasHayvanId: true, booleanFields: ['KarantinaDurumu'], requiresProfile: true });

ipcMain.handle('tedaviler:list', tedavilerCrud.listByHayvanId);
ipcMain.handle('tedaviler:add', tedavilerCrud.add);
ipcMain.handle('tedaviler:update', tedavilerCrud.update);
ipcMain.handle('tedaviler:delete', tedavilerCrud.delete);

// Add listAll method for tedaviler with profile context
ipcMain.handle('tedaviler:listAll', async () => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.all('SELECT * FROM Tedaviler WHERE ProfilId = ? ORDER BY TedaviBaslangicTarihi DESC, Id DESC', [currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
});

// --- AŞI ŞABLONLARI (VACCINATION TEMPLATES) CRUD ---
const asiSablonlariColumns = ['SablonAdi', 'HayvanTuru', 'AsiAdi', 'Periyot', 'PeriyotTipi', 'IlkAsiYasi', 'TekrarSayisi', 'Aktif', 'Aciklama'];
const asiSablonlariCrud = createGenericCrudHandlers('AsiSablonlari', asiSablonlariColumns, { requiresProfile: true, booleanFields: ['Aktif'], hasHayvanId: false });

ipcMain.handle('asiSablonlari:list', async (event) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const db = getDb();
    db.all('SELECT * FROM AsiSablonlari WHERE ProfilId = ? ORDER BY SablonAdi', [currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows || []);
    });
  });
});

ipcMain.handle('asiSablonlari:add', asiSablonlariCrud.add);
ipcMain.handle('asiSablonlari:update', asiSablonlariCrud.update);
ipcMain.handle('asiSablonlari:delete', asiSablonlariCrud.delete);

// Get active templates for specific animal type
ipcMain.handle('asiSablonlari:getByAnimalType', async (event, animalType) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const db = getDb();
    const query = animalType
      ? 'SELECT * FROM AsiSablonlari WHERE ProfilId = ? AND (HayvanTuru = ? OR HayvanTuru IS NULL) AND Aktif = 1 ORDER BY SablonAdi'
      : 'SELECT * FROM AsiSablonlari WHERE ProfilId = ? AND Aktif = 1 ORDER BY SablonAdi';

    const params = animalType ? [currentProfile.id, animalType] : [currentProfile.id];

    db.all(query, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows || []);
    });
  });
});

// --- AŞI TAKVİMİ (VACCINATION SCHEDULE) CRUD ---
const asiTakvimiColumns = ['HayvanId', 'SablonId', 'PlanlananTarih', 'Durum', 'TamamlanmaTarihi', 'AsilamaId', 'Notlar'];
const asiTakvimiCrud = createGenericCrudHandlers('AsiTakvimi', asiTakvimiColumns, { requiresProfile: true });

ipcMain.handle('asiTakvimi:list', async (event, hayvanId = null) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const db = getDb();
    let query = `
      SELECT at.*, ast.SablonAdi, ast.AsiAdi, ast.Periyot, ast.PeriyotTipi,
             h.KupeNo, h.Isim as HayvanIsim
      FROM AsiTakvimi at
      JOIN AsiSablonlari ast ON at.SablonId = ast.Id
      JOIN Hayvanlar h ON at.HayvanId = h.Id
      WHERE at.ProfilId = ?
    `;

    const params = [currentProfile.id];

    if (hayvanId) {
      query += ' AND at.HayvanId = ?';
      params.push(hayvanId);
    }

    query += ' ORDER BY at.PlanlananTarih ASC';

    db.all(query, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows || []);
    });
  });
});

ipcMain.handle('asiTakvimi:add', asiTakvimiCrud.add);
ipcMain.handle('asiTakvimi:update', asiTakvimiCrud.update);
ipcMain.handle('asiTakvimi:delete', asiTakvimiCrud.delete);

// Get upcoming vaccinations (next 30 days)
ipcMain.handle('asiTakvimi:getUpcoming', async (event, days = 30) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const db = getDb();
    const today = new Date().toISOString().split('T')[0];
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);
    const futureDateStr = futureDate.toISOString().split('T')[0];

    const query = `
      SELECT at.*, ast.SablonAdi, ast.AsiAdi, ast.Periyot, ast.PeriyotTipi,
             h.KupeNo, h.Isim as HayvanIsim,
             CASE
               WHEN at.PlanlananTarih < ? THEN 'Gecikti'
               WHEN at.PlanlananTarih = ? THEN 'Bugun'
               ELSE 'Yaklasan'
             END as UyariTipi
      FROM AsiTakvimi at
      JOIN AsiSablonlari ast ON at.SablonId = ast.Id
      JOIN Hayvanlar h ON at.HayvanId = h.Id
      WHERE at.ProfilId = ?
        AND at.Durum = 'Bekliyor'
        AND at.PlanlananTarih <= ?
        AND h.AktifMi = 1
      ORDER BY at.PlanlananTarih ASC
    `;

    db.all(query, [today, today, currentProfile.id, futureDateStr], (err, rows) => {
      if (err) reject(err);
      else resolve(rows || []);
    });
  });
});

// Get overdue vaccinations
ipcMain.handle('asiTakvimi:getOverdue', async (event) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const db = getDb();
    const today = new Date().toISOString().split('T')[0];

    const query = `
      SELECT at.*, ast.SablonAdi, ast.AsiAdi, ast.Periyot, ast.PeriyotTipi,
             h.KupeNo, h.Isim as HayvanIsim,
             julianday(?) - julianday(at.PlanlananTarih) as GecikmeGunu
      FROM AsiTakvimi at
      JOIN AsiSablonlari ast ON at.SablonId = ast.Id
      JOIN Hayvanlar h ON at.HayvanId = h.Id
      WHERE at.ProfilId = ?
        AND at.Durum = 'Bekliyor'
        AND at.PlanlananTarih < ?
        AND h.AktifMi = 1
      ORDER BY at.PlanlananTarih ASC
    `;

    db.all(query, [today, currentProfile.id, today], (err, rows) => {
      if (err) reject(err);
      else resolve(rows || []);
    });
  });
});

// Get schedule by ID
ipcMain.handle('asiTakvimi:getById', async (event, scheduleId) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const db = getDb();
    const query = `
      SELECT at.*, ast.SablonAdi, ast.AsiAdi, ast.Periyot, ast.PeriyotTipi,
             h.KupeNo, h.Isim as HayvanIsim
      FROM AsiTakvimi at
      JOIN AsiSablonlari ast ON at.SablonId = ast.Id
      JOIN Hayvanlar h ON at.HayvanId = h.Id
      WHERE at.Id = ? AND at.ProfilId = ?
    `;

    db.get(query, [scheduleId, currentProfile.id], (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
});

// --- BULK VACCINATION OPERATIONS ---

// Add vaccination from schedule completion
ipcMain.handle('asilamalar:addFromSchedule', async (event, data) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const { vaccinationData, scheduleId } = data;
    const db = getDb();

    db.serialize(() => {
      db.run('BEGIN TRANSACTION');

      try {
        // Insert vaccination record
        const vaccinationStmt = db.prepare(`
          INSERT INTO Asilamalar (ProfilId, HayvanId, AsiAdi, AsilamaTarihi, Doz, UygulayanVeteriner, Notlar, SablonId, TakvimId)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const vaccinationResult = vaccinationStmt.run(
          currentProfile.id,
          vaccinationData.HayvanId,
          vaccinationData.AsiAdi,
          vaccinationData.AsilamaTarihi,
          vaccinationData.Doz || null,
          vaccinationData.UygulayanVeteriner || null,
          vaccinationData.Notlar || null,
          vaccinationData.SablonId || null,
          scheduleId
        );

        const vaccinationId = vaccinationResult.lastID;
        vaccinationStmt.finalize();

        // Update schedule status
        const scheduleStmt = db.prepare(`
          UPDATE AsiTakvimi
          SET Durum = 'Tamamlandi', TamamlanmaTarihi = ?, AsilamaId = ?
          WHERE Id = ? AND ProfilId = ?
        `);

        scheduleStmt.run(
          vaccinationData.AsilamaTarihi,
          vaccinationId,
          scheduleId,
          currentProfile.id
        );

        scheduleStmt.finalize();

        // Get template info for potential next vaccination scheduling
        const templateQuery = `
          SELECT ast.*, at.HayvanId
          FROM AsiTakvimi at
          JOIN AsiSablonlari ast ON at.SablonId = ast.Id
          WHERE at.Id = ? AND at.ProfilId = ?
        `;

        db.get(templateQuery, [scheduleId, currentProfile.id], (err, template) => {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          // If template has unlimited repeats or more repeats, schedule next vaccination
          if (template && (template.TekrarSayisi === -1 || template.TekrarSayisi > 1)) {
            try {
              const nextDate = new Date(vaccinationData.AsilamaTarihi);

              // Calculate next vaccination date based on template period
              if (template.PeriyotTipi === 'ay') {
                nextDate.setMonth(nextDate.getMonth() + template.Periyot);
              } else if (template.PeriyotTipi === 'yil') {
                nextDate.setFullYear(nextDate.getFullYear() + template.Periyot);
              } else { // gun
                nextDate.setDate(nextDate.getDate() + template.Periyot);
              }

              // Insert next vaccination schedule
              const nextScheduleStmt = db.prepare(`
                INSERT INTO AsiTakvimi (ProfilId, HayvanId, SablonId, PlanlananTarih, Durum)
                VALUES (?, ?, ?, ?, 'Bekliyor')
              `);

              nextScheduleStmt.run(
                currentProfile.id,
                template.HayvanId,
                template.Id,
                nextDate.toISOString().split('T')[0]
              );

              nextScheduleStmt.finalize();
            } catch (nextScheduleError) {
              console.warn('Warning: Could not schedule next vaccination:', nextScheduleError);
              // Don't fail the transaction for this
            }
          }

          db.run('COMMIT');
          resolve({
            success: true,
            vaccinationId: vaccinationId,
            nextScheduled: template && (template.TekrarSayisi === -1 || template.TekrarSayisi > 1)
          });
        });

      } catch (error) {
        db.run('ROLLBACK');
        reject(error);
      }
    });
  });
});

// Bulk add vaccinations for multiple animals
ipcMain.handle('asilamalar:bulkAdd', async (event, data) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const { hayvanIds, asiAdi, asilamaTarihi, doz, uygulayanVeteriner, notlar } = data;

    if (!hayvanIds || !Array.isArray(hayvanIds) || hayvanIds.length === 0) {
      reject(new Error('En az bir hayvan seçilmelidir'));
      return;
    }

    const db = getDb();
    const stmt = db.prepare(`
      INSERT INTO Asilamalar (ProfilId, HayvanId, AsiAdi, AsilamaTarihi, Doz, UygulayanVeteriner, Notlar)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    db.serialize(() => {
      db.run('BEGIN TRANSACTION');

      try {
        const results = [];
        hayvanIds.forEach(hayvanId => {
          const result = stmt.run(currentProfile.id, hayvanId, asiAdi, asilamaTarihi, doz, uygulayanVeteriner, notlar);
          results.push({ hayvanId, asilamaId: result.lastID });
        });

        db.run('COMMIT', (err) => {
          stmt.finalize();
          if (err) {
            reject(err);
          } else {
            resolve({ success: true, results, count: results.length });
          }
        });
      } catch (error) {
        db.run('ROLLBACK');
        stmt.finalize();
        reject(error);
      }
    });
  });
});

// Generate vaccination schedule for an animal based on templates
ipcMain.handle('asiTakvimi:generateSchedule', async (event, data) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const { hayvanId, sablonIds, baslangicTarihi } = data;

    if (!hayvanId || !sablonIds || !Array.isArray(sablonIds) || sablonIds.length === 0) {
      reject(new Error('Hayvan ve şablon seçimi gereklidir'));
      return;
    }

    const db = getDb();

    // First get the templates
    const templateQuery = `SELECT * FROM AsiSablonlari WHERE Id IN (${sablonIds.map(() => '?').join(',')}) AND ProfilId = ?`;

    db.all(templateQuery, [...sablonIds, currentProfile.id], (err, templates) => {
      if (err) {
        reject(err);
        return;
      }

      if (templates.length === 0) {
        reject(new Error('Geçerli şablon bulunamadı'));
        return;
      }

      const stmt = db.prepare(`
        INSERT INTO AsiTakvimi (ProfilId, HayvanId, SablonId, PlanlananTarih, Durum)
        VALUES (?, ?, ?, ?, 'Bekliyor')
      `);

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        try {
          const results = [];
          const startDate = new Date(baslangicTarihi);

          templates.forEach(template => {
            let currentDate = new Date(startDate);

            // Add initial vaccination if IlkAsiYasi is specified
            if (template.IlkAsiYasi && template.IlkAsiYasi > 0) {
              currentDate.setDate(currentDate.getDate() + template.IlkAsiYasi);
            }

            // Generate schedule based on repeat count
            const maxRepeats = template.TekrarSayisi === -1 ? 10 : template.TekrarSayisi; // Limit to 10 if unlimited

            for (let i = 0; i < maxRepeats; i++) {
              const result = stmt.run(currentProfile.id, hayvanId, template.Id, currentDate.toISOString().split('T')[0]);
              results.push({
                sablonId: template.Id,
                takvimId: result.lastID,
                planlananTarih: currentDate.toISOString().split('T')[0]
              });

              // Calculate next vaccination date
              if (template.PeriyotTipi === 'ay') {
                currentDate.setMonth(currentDate.getMonth() + template.Periyot);
              } else if (template.PeriyotTipi === 'yil') {
                currentDate.setFullYear(currentDate.getFullYear() + template.Periyot);
              } else { // gun
                currentDate.setDate(currentDate.getDate() + template.Periyot);
              }
            }
          });

          db.run('COMMIT', (err) => {
            stmt.finalize();
            if (err) {
              reject(err);
            } else {
              resolve({ success: true, results, count: results.length });
            }
          });
        } catch (error) {
          db.run('ROLLBACK');
          stmt.finalize();
          reject(error);
        }
      });
    });
  });
});

// Mark vaccination as completed and update schedule
ipcMain.handle('asiTakvimi:markCompleted', async (event, data) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const { takvimId, asilamaId, tamamlanmaTarihi } = data;

    const db = getDb();
    const query = `
      UPDATE AsiTakvimi
      SET Durum = 'Tamamlandi', TamamlanmaTarihi = ?, AsilamaId = ?
      WHERE Id = ? AND ProfilId = ?
    `;

    db.run(query, [tamamlanmaTarihi, asilamaId, takvimId, currentProfile.id], function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ success: true, changes: this.changes });
      }
    });
  });
});

// --- AŞI HATIRLATICILAR (VACCINATION REMINDERS) ---

// Get active reminders
ipcMain.handle('asiHatirlaticlari:getActive', async (event) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const db = getDb();
    const query = `
      SELECT ah.*, at.PlanlananTarih, ast.SablonAdi, ast.AsiAdi,
             h.KupeNo, h.Isim as HayvanIsim
      FROM AsiHatirlaticlari ah
      JOIN AsiTakvimi at ON ah.TakvimId = at.Id
      JOIN AsiSablonlari ast ON at.SablonId = ast.Id
      JOIN Hayvanlar h ON ah.HayvanId = h.Id
      WHERE ah.ProfilId = ? AND ah.Okundu = 0
      ORDER BY ah.HatirlaticiTarihi ASC
    `;

    db.all(query, [currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows || []);
    });
  });
});

// Mark reminder as read
ipcMain.handle('asiHatirlaticlari:markRead', async (event, reminderId) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const db = getDb();
    const query = 'UPDATE AsiHatirlaticlari SET Okundu = 1 WHERE Id = ? AND ProfilId = ?';

    db.run(query, [reminderId, currentProfile.id], function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ success: true, changes: this.changes });
      }
    });
  });
});

// Generate reminders for upcoming vaccinations
ipcMain.handle('asiHatirlaticlari:generate', async (event) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const db = getDb();
    const today = new Date().toISOString().split('T')[0];

    // Get upcoming vaccinations that need reminders
    const upcomingQuery = `
      SELECT at.*, h.KupeNo, h.Isim as HayvanIsim, ast.AsiAdi
      FROM AsiTakvimi at
      JOIN Hayvanlar h ON at.HayvanId = h.Id
      JOIN AsiSablonlari ast ON at.SablonId = ast.Id
      WHERE at.ProfilId = ?
        AND at.Durum = 'Bekliyor'
        AND h.AktifMi = 1
        AND at.Id NOT IN (
          SELECT TakvimId FROM AsiHatirlaticlari
          WHERE ProfilId = ? AND HatirlaticiTarihi = ?
        )
    `;

    db.all(upcomingQuery, [currentProfile.id, currentProfile.id, today], (err, schedules) => {
      if (err) {
        reject(err);
        return;
      }

      if (schedules.length === 0) {
        resolve({ success: true, generated: 0 });
        return;
      }

      const stmt = db.prepare(`
        INSERT INTO AsiHatirlaticlari (ProfilId, HayvanId, TakvimId, HatirlaticiTipi, HatirlaticiTarihi, Mesaj)
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        try {
          let generated = 0;
          const todayDate = new Date(today);

          schedules.forEach(schedule => {
            const plannedDate = new Date(schedule.PlanlananTarih);
            const daysDiff = Math.ceil((plannedDate - todayDate) / (1000 * 60 * 60 * 24));

            let reminderType = '';
            let message = '';

            if (daysDiff < 0) {
              reminderType = 'Geciken';
              message = `${schedule.KupeNo || schedule.HayvanIsim} için ${schedule.AsiAdi} aşısı ${Math.abs(daysDiff)} gün gecikti`;
            } else if (daysDiff <= 7) {
              reminderType = 'Yaklasan';
              message = `${schedule.KupeNo || schedule.HayvanIsim} için ${schedule.AsiAdi} aşısı ${daysDiff} gün içinde yapılmalı`;
            } else if (daysDiff <= 30) {
              reminderType = 'Yaklasan';
              message = `${schedule.KupeNo || schedule.HayvanIsim} için ${schedule.AsiAdi} aşısı ${daysDiff} gün içinde yapılmalı`;
            }

            if (reminderType) {
              stmt.run(currentProfile.id, schedule.HayvanId, schedule.Id, reminderType, today, message);
              generated++;
            }
          });

          db.run('COMMIT', (err) => {
            stmt.finalize();
            if (err) {
              reject(err);
            } else {
              resolve({ success: true, generated });
            }
          });
        } catch (error) {
          db.run('ROLLBACK');
          stmt.finalize();
          reject(error);
        }
      });
    });
  });
});

console.log('Health (Asilamalar, Tedaviler) IPC handlers registered.');

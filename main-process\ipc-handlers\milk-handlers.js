const { ipcMain } = require('electron');
const { getDb, createGenericCrudHandlers } = require('../services/database.js');
const { getCurrentProfile } = require('./auth-handlers.js');

// --- LAKTASYONLAR CRUD ---
// Note: Laktasyonlar list is by HayvanId, but add/update might not strictly follow the generic pattern
// if more complex logic were involved. For now, using generic for add. List is custom.
const laktasyonlarColumns = ['HayvanId', 'GebelikId', 'BaslangicTarihi', 'BitisTarihi'];
const laktasyonlarCrud = createGenericCrudHandlers('Laktasyonlar', laktasyonlarColumns, {hasHayvanId: false, requiresProfile: true}); // hasHayvanId is false because HayvanId is part of the columns for add.

ipcMain.handle('laktasyonlar:list', async (event, hayvanId) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    getDb().all('SELECT * FROM Laktasyonlar WHERE HayvanId = ? AND ProfilId = ? ORDER BY BaslangicTarihi DESC', [hayvanId, currentProfile.id], (err, rows) => {
      if (err) reject(err); else resolve(rows);
    });
  });
});
ipcMain.handle('laktasyonlar:add', laktasyonlarCrud.add); // Uses generic add
// ipcMain.handle('laktasyonlar:delete', laktasyonlarCrud.delete); // Replaced with custom handler below

ipcMain.handle('laktasyonlar:delete', async (event, laktasyonId) => {
  const db = getDb();
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.serialize(() => {
      db.run('BEGIN TRANSACTION;', (err) => {
        if (err) return reject(err);
      });

      // Önce ilişkili SagimVerileri'ni sil (profile context ile)
      db.run('DELETE FROM SagimVerileri WHERE LaktasyonId = ? AND ProfilId = ?', [laktasyonId, currentProfile.id], function(err) {
        if (err) {
          db.run('ROLLBACK;', () => reject(err));
          return;
        }
        console.log(`Deleted ${this.changes} milking records for lactation ID ${laktasyonId}`);

        // Sonra Laktasyonlar kaydını sil (profile context ile)
        db.run('DELETE FROM Laktasyonlar WHERE Id = ? AND ProfilId = ?', [laktasyonId, currentProfile.id], function(err) {
          if (err) {
            db.run('ROLLBACK;', () => reject(err));
            return;
          }
          if (this.changes === 0) {
             db.run('ROLLBACK;', () => reject(new Error(`Lactation record with ID ${laktasyonId} not found or access denied.`)));
             return;
          }
          console.log(`Deleted lactation record with ID ${laktasyonId}`);
          db.run('COMMIT;', (err) => {
            if (err) reject(err); else resolve({ id: laktasyonId, changes: 1 }); // Mimic generic handler response
          });
        });
      });
    });
  });
});
ipcMain.handle('laktasyonlar:update', laktasyonlarCrud.update); // Uses generic update

// Add listAll method for laktasyonlar
ipcMain.handle('laktasyonlar:listAll', async () => {
  return new Promise((resolve, reject) => {
    getDb().all('SELECT * FROM Laktasyonlar ORDER BY BaslangicTarihi DESC, Id DESC', [], (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
});

// --- SAĞIM VERİLERİ CRUD ---
// SagimVerileri list is by LaktasyonId, not HayvanId directly. Add/Update/Delete are standard.
const sagimVerileriColumns = ['LaktasyonId', 'Miktar', 'Tarih', 'Saat', 'YagOrani', 'ProteinOrani'];
const sagimVerileriCrud = createGenericCrudHandlers('SagimVerileri', sagimVerileriColumns, {hasHayvanId: false, requiresProfile: true}); // LaktasyonId is the parent key here

ipcMain.handle('sagimverileri:list', async (event, laktasyonId) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    getDb().all('SELECT * FROM SagimVerileri WHERE LaktasyonId = ? AND ProfilId = ? ORDER BY Tarih DESC, Saat DESC', [laktasyonId, currentProfile.id], (err, rows) => {
      if (err) reject(err); else resolve(rows);
    });
  });
});
ipcMain.handle('sagimverileri:add', sagimVerileriCrud.add);
ipcMain.handle('sagimverileri:update', sagimVerileriCrud.update);
ipcMain.handle('sagimverileri:delete', sagimVerileriCrud.delete);

// Add listAll method for sagimverileri with HayvanId from Laktasyonlar and profile context
ipcMain.handle('sagimverileri:listAll', async () => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const query = `
      SELECT sv.*, l.HayvanId
      FROM SagimVerileri sv
      JOIN Laktasyonlar l ON sv.LaktasyonId = l.Id
      WHERE sv.ProfilId = ? AND l.ProfilId = ?
      ORDER BY sv.Tarih DESC, sv.Saat DESC, sv.Id DESC
    `;
    getDb().all(query, [currentProfile.id, currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
});

// Get animals with active lactation for bulk milking entry with profile context
ipcMain.handle('hayvanlar:getActiveLactating', async () => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const query = `
      SELECT
        H.Id as HayvanId,
        H.KupeNo,
        H.Isim,
        L.Id as LaktasyonId,
        L.BaslangicTarihi
      FROM Hayvanlar H
      JOIN Laktasyonlar L ON H.Id = L.HayvanId
      WHERE H.AktifMi = 1
        AND H.ProfilId = ?
        AND L.ProfilId = ?
        AND (L.BitisTarihi IS NULL OR L.BitisTarihi >= date('now'))
      ORDER BY H.KupeNo
    `;
    getDb().all(query, [currentProfile.id, currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
});

// Bulk insert milking data with profile context
ipcMain.handle('sagimverileri:bulkAdd', async (event, milkingDataArray) => {
  const db = getDb();
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.serialize(() => {
      db.run('BEGIN TRANSACTION;', (err) => {
        if (err) return reject(err);
      });

      let completed = 0;
      let hasError = false;

      if (milkingDataArray.length === 0) {
        db.run('COMMIT;', (commitErr) => {
          if (commitErr) reject(commitErr);
          else resolve({ success: true, insertedCount: 0 });
        });
        return;
      }

      milkingDataArray.forEach((data, index) => {
        const stmt = db.prepare(`
          INSERT INTO SagimVerileri (ProfilId, LaktasyonId, Miktar, Tarih, Saat, YagOrani, ProteinOrani)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `);

        stmt.run([
          currentProfile.id,
          data.LaktasyonId,
          data.Miktar,
          data.Tarih,
          data.Saat,
          data.YagOrani || null,
          data.ProteinOrani || null
        ], function(err) {
          stmt.finalize();

          if (err && !hasError) {
            hasError = true;
            db.run('ROLLBACK;', () => reject(err));
            return;
          }

          completed++;
          if (completed === milkingDataArray.length && !hasError) {
            db.run('COMMIT;', (commitErr) => {
              if (commitErr) reject(commitErr);
              else resolve({ success: true, insertedCount: milkingDataArray.length });
            });
          }
        });
      });
    });
  });
});

console.log('Milk (Laktasyonlar, SagimVerileri) IPC handlers registered.');

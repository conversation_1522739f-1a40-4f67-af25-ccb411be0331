{"name": "livestock_management", "version": "1.0.0", "description": "Canlı Hayvan Yönetimi Masaüstü Uygulaması", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder --dir", "dist": "electron-builder"}, "build": {"appId": "com.livestock.management", "productName": "Livestock Management", "win": {"target": ["nsis"], "icon": "renderer/assets/icons/app-icon.ico"}, "extraResources": [{"from": "db", "to": "db", "filter": ["**/*"]}]}, "dependencies": {"bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "sqlite3": "^5.1.6"}, "devDependencies": {"electron": "^28.3.3", "electron-builder": "^26.0.12"}, "author": "", "license": "MIT"}
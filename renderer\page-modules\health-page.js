import { IconSystem } from '../utils/icon-system.js';
import { BadgeSystem } from '../utils/badge-system.js';
import { RecentActivities, formatActivityDate, truncateText } from '../components/recent-activities.js';
import { TableAnimations } from '../utils/table-animations.js';

// Globals for this module
let passedI18nHealth;
let passedDefaultAvatarSVGHealth;

// State for Vaccines Tab
let allVaccinesForAnimal = [];
let currentVaccinesForAnimal = [];
const itemsPerPageVaccines = 5; // Example, can be adjusted
let currentPageVaccines = 1;
let sortColumnVaccines = 'AsilamaTarihi';
let sortDirectionVaccines = -1; // Default sort by date descending

// State for Treatments Tab
let allTreatmentsForAnimal = [];
let currentTreatmentsForAnimal = [];
const itemsPerPageTreatments = 5; // Example
let currentPageTreatments = 1;
let sortColumnTreatments = 'TedaviBaslangicTarihi';
let sortDirectionTreatments = -1;

// Common elements
let animalSelectElHealth;
let healthContentEl;
let healthRecentActivities;

export async function renderHealthPage(contentArea, i18n, defaultAvatarSVG) {
  passedI18nHealth = i18n;
  passedDefaultAvatarSVGHealth = defaultAvatarSVG;
  const t = passedI18nHealth.t;

  contentArea.innerHTML = `
    <!-- Quick Actions Header -->
    <div class="health-quick-actions-section">
      <div class="health-quick-actions-header">
        <h2 class="health-page-title">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
          </svg>
          ${t('health_management')}
        </h2>
        <div class="health-quick-actions">
          <button class="btn btn-primary" id="header-bulk-vaccination-btn">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
              <line x1="12" y1="8" x2="12" y2="16"/>
              <line x1="8" y1="12" x2="16" y2="12"/>
            </svg>
            <span>${t('bulk_vaccination')}</span>
          </button>

          <button class="btn btn-secondary" id="header-generate-schedule-btn">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
              <line x1="16" y1="2" x2="16" y2="6"/>
              <line x1="8" y1="2" x2="8" y2="6"/>
              <line x1="3" y1="10" x2="21" y2="10"/>
            </svg>
            <span>${t('generate_schedule')}</span>
          </button>

          <button class="btn btn-secondary" id="header-view-reminders-btn">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
            </svg>
            <span>${t('view_reminders')}</span>
          </button>

          <button class="btn btn-secondary" id="header-manage-templates-btn">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
            <span>${t('templates')}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="health-navigation-section">
      <div id="health-tabs" class="health-nav-tabs">
        <button class="health-tab active" data-tab="asi-takvimi">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
            <line x1="16" y1="2" x2="16" y2="6"/>
            <line x1="8" y1="2" x2="8" y2="6"/>
            <line x1="3" y1="10" x2="21" y2="10"/>
          </svg>
          <span>${t('vaccination_schedule')}</span>
        </button>
        <button class="health-tab" data-tab="asi-yonetimi">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
            <polyline points="14,2 14,8 20,8"/>
            <line x1="16" y1="13" x2="8" y2="13"/>
            <line x1="16" y1="17" x2="8" y2="17"/>
            <polyline points="10,9 9,9 8,9"/>
          </svg>
          <span>${t('vaccination_management')}</span>
        </button>
        <button class="health-tab" data-tab="asilamalar">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
          </svg>
          <span>${t('health_vaccinations')}</span>
        </button>
        <button class="health-tab" data-tab="tedaviler">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"/>
          </svg>
          <span>${t('health_treatments')}</span>
        </button>
      </div>

      <!-- Animal Selection Bar - Only shown for individual animal tabs -->
      <div id="animal-selection-bar" class="animal-selection-bar" style="display: none;">
        <div class="animal-selection-bar-content">
          <div class="animal-selection-info">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M19 7.5C19 11.09 16.09 14 12.5 14S6 11.09 6 7.5 8.91 1 12.5 1s6.5 2.91 6.5 6.5z"/>
              <path d="M12.5 14v7"/>
              <path d="M8 21h9"/>
            </svg>
            <span>${t('select_animal')}</span>
          </div>
          <select id="health-animal-select" data-placeholder="${t('please_select_animal')}">
            <option value="">${t('please_select_animal')}</option>
          </select>
        </div>
      </div>
    </div>

    <div id="health-content">
      <div class='empty-message'><svg width='24' height='24' fill='none' viewBox='0 0 24 24'><circle cx='12' cy='12' r='12' fill='#e0e7ef'/><ellipse cx='12' cy='11' rx='5' ry='4' fill='#fff'/><ellipse cx='9' cy='10' rx='1.5' ry='1' fill='#b6c3d1' opacity='0.7'/><ellipse cx='15' cy='10' rx='1.5' ry='1' fill='#b6c3d1' opacity='0.7'/><ellipse cx='10.5' cy='12.5' rx='1' ry='1.2' fill='#222c37'/><ellipse cx='13.5' cy='12.5' rx='1' ry='1.2' fill='#222c37'/><ellipse cx='12' cy='15' rx='2' ry='0.8' fill='#b6c3d1'/></svg><br>${t('please_select_animal')}</div>
    </div>

    <div id="health-recent-activities"></div>
  `;

  animalSelectElHealth = contentArea.querySelector('#health-animal-select');
  healthContentEl = contentArea.querySelector('#health-content');
  const tabs = contentArea.querySelectorAll('.health-tab');

  const animals = await window.api.invoke('hayvanlar:list');
  animalSelectElHealth.innerHTML = `<option value="" selected disabled>${t('please_select_animal')}</option>`;
  animals.forEach(a => {
    const option = document.createElement('option');
    option.value = a.Id;
    option.textContent = `${a.KupeNo} ${a.Isim ? ' - ' + a.Isim : ''}`;
    animalSelectElHealth.appendChild(option);
  });

  tabs.forEach(tab => {
    tab.addEventListener('click', async () => {
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');
      // Reset sort column when changing tabs to avoid applying unrelated sort
      const tabName = tab.getAttribute('data-tab');
      if (tabName === 'asilamalar') {
          sortColumnVaccines = 'AsilamaTarihi'; sortDirectionVaccines = -1;
      } else if (tabName === 'tedaviler') {
          sortColumnTreatments = 'TedaviBaslangicTarihi'; sortDirectionTreatments = -1;
      } else if (tabName === 'asi-takvimi') {
          sortColumnSchedule = 'PlanlananTarih'; sortDirectionSchedule = 1;
      } else if (tabName === 'asi-yonetimi') {
          sortColumnTemplates = 'SablonAdi'; sortDirectionTemplates = 1;
      }

      // Show/hide animal selection bar based on tab
      const animalBar = document.getElementById('animal-selection-bar');
      if (animalBar) {
        if (tabName === 'asilamalar' || tabName === 'tedaviler') {
          animalBar.style.display = 'block';
        } else {
          animalBar.style.display = 'none';
        }
      }

      await renderActiveHealthTab();
    });
  });

  animalSelectElHealth.addEventListener('change', async () => {
    await renderActiveHealthTab();
  });

  // Header quick action buttons
  document.getElementById('header-bulk-vaccination-btn')?.addEventListener('click', () => openBulkVaccinationModal());
  document.getElementById('header-generate-schedule-btn')?.addEventListener('click', () => openGenerateScheduleModal());
  document.getElementById('header-view-reminders-btn')?.addEventListener('click', () => openRemindersModal());
  document.getElementById('header-manage-templates-btn')?.addEventListener('click', () => {
    // Switch to vaccination management tab
    const managementTab = document.querySelector('[data-tab="asi-yonetimi"]');
    if (managementTab) {
      document.querySelectorAll('.health-tab').forEach(t => t.classList.remove('active'));
      managementTab.classList.add('active');

      // Hide animal selection card for management tab
      const animalCard = document.getElementById('animal-selection-card');
      if (animalCard) {
        animalCard.style.display = 'none';
      }

      renderActiveHealthTab();
    }
  });

  // Set initial animal card visibility (default tab is asi-takvimi, so hide it)
  const animalCard = document.getElementById('animal-selection-card');
  if (animalCard) {
    animalCard.style.display = 'none';
  }

  // Initialize Recent Activities
  healthRecentActivities = new RecentActivities({
    containerId: 'health-recent-activities',
    title: t('recent_health_activities') || 'Recent Health Activities',
    maxItems: 5,
    onViewAll: () => {
      // Scroll to main table
      healthContentEl.scrollIntoView({ behavior: 'smooth' });
    },
    getRecords: async () => {
      try {
        // Get all animals first to map names
        const animals = await window.api.invoke('hayvanlar:list');
        const animalMap = {};
        animals.forEach(animal => {
          animalMap[animal.Id] = animal.KupeNo || animal.Isim || `#${animal.Id}`;
        });

        // Get all recent vaccinations and treatments (not filtered by animal)
        const [allVaccinations, allTreatments] = await Promise.all([
          window.api.invoke('asilamalar:listAll') || [],
          window.api.invoke('tedaviler:listAll') || []
        ]);

        // Combine and sort by date
        const allRecords = [
          ...allVaccinations.map(v => ({ ...v, type: 'vaccination', animalName: animalMap[v.HayvanId] })),
          ...allTreatments.map(t => ({ ...t, type: 'treatment', animalName: animalMap[t.HayvanId] }))
        ];

        return allRecords.sort((a, b) => {
          const dateA = new Date(a.AsilamaTarihi || a.TedaviBaslangicTarihi);
          const dateB = new Date(b.AsilamaTarihi || b.TedaviBaslangicTarihi);
          return dateB - dateA;
        });
      } catch (error) {
        console.error('Error fetching health records:', error);
        return [];
      }
    },
    formatRecord: (record) => {
      const isVaccination = record.type === 'vaccination';
      const date = record.AsilamaTarihi || record.TedaviBaslangicTarihi;

      return {
        title: isVaccination ?
          (record.AsiAdi || t('vaccination')) :
          (record.Teshis || t('treatment')),
        subtitle: `${record.animalName || t('unknown_animal') || 'Bilinmeyen Hayvan'} - ${isVaccination ?
          t('vaccination_record') :
          t('treatment_record')}`,
        date: formatActivityDate(date),
        badge: isVaccination ?
          BadgeSystem.createBadge(t('vaccination') || 'Vaccination', BadgeSystem.TYPES.SUCCESS) :
          BadgeSystem.createBadge(t('treatment') || 'Treatment', BadgeSystem.TYPES.WARNING)
      };
    },
    emptyMessage: t('no_recent_health_activities') || 'No recent health activities'
  });

  // Initialize recent activities
  healthRecentActivities.init();

  // Initialize modals (assuming they are in index.html or similar global scope)
  initializeVaccineModal();
  initializeTreatmentModal();

  // Initial render for the active tab (which will be empty until animal selected)
  renderActiveHealthTab();
}

// --- MODAL HANDLING (Adapted from original, assuming modals exist globally) ---
let vaccineModal, vaccineForm, vaccineCloseBtn, vaccineModalTitle, editingVaccineId;
function initializeVaccineModal() {
  vaccineModal = document.getElementById('vaccine-modal');
  vaccineForm = document.getElementById('vaccine-form');
  vaccineCloseBtn = vaccineModal.querySelector('.close-btn');
  vaccineModalTitle = document.getElementById('vaccine-modal-title');
  if (!vaccineForm.elements['HayvanId']) { /* Add HayvanId if missing */ }
  vaccineCloseBtn.onclick = closeVaccineModal;
  vaccineModal.onclick = e => { if (e.target === vaccineModal) closeVaccineModal(); };
  vaccineForm.onsubmit = submitVaccineForm;

  // Setup vaccine name dropdown change handler
  const vaccineNameSelect = document.getElementById('vaccine-name-select');
  const vaccineNameOther = document.getElementById('vaccine-name-other');

  if (vaccineNameSelect && vaccineNameOther) {
    vaccineNameSelect.addEventListener('change', function() {
      if (this.value === 'Diğer') {
        vaccineNameOther.style.display = 'block';
        vaccineNameOther.required = true;
      } else {
        vaccineNameOther.style.display = 'none';
        vaccineNameOther.required = false;
        vaccineNameOther.value = '';
      }
    });
  }
}
function openVaccineModal(vaccine = null) {
    const t = passedI18nHealth.t;
    vaccineModal.classList.remove('hidden');
    vaccineForm.reset();
    editingVaccineId = vaccine ? vaccine.Id : null;
    vaccineForm.elements['HayvanId'].value = animalSelectElHealth.value;

    // Reset vaccine name dropdown and other input
    const vaccineNameSelect = document.getElementById('vaccine-name-select');
    const vaccineNameOther = document.getElementById('vaccine-name-other');

    if (vaccineNameOther) {
        vaccineNameOther.style.display = 'none';
        vaccineNameOther.required = false;
        vaccineNameOther.value = '';
    }

    if (vaccine) {
        vaccineModalTitle.textContent = t('vaccine_modal_title_edit');

        // Handle vaccine name - check if it's a predefined option
        const predefinedOptions = ['Şap Aşısı', 'Şarbon Aşısı', 'Enterotoksemi Aşısı', 'Brucella Aşısı', 'IBR/IPV Aşısı', 'BVD Aşısı', 'Pastörella Aşısı'];
        if (vaccine.AsiAdi && predefinedOptions.includes(vaccine.AsiAdi)) {
            vaccineNameSelect.value = vaccine.AsiAdi;
        } else if (vaccine.AsiAdi) {
            // Custom vaccine name
            vaccineNameSelect.value = 'Diğer';
            vaccineNameOther.style.display = 'block';
            vaccineNameOther.required = true;
            vaccineNameOther.value = vaccine.AsiAdi;
        }

        // Set other fields
        Object.keys(vaccine).forEach(key => {
            if (vaccineForm.elements[key] && key !== 'AsiAdi') {
                vaccineForm.elements[key].value = vaccine[key] ?? '';
            }
        });

        if(vaccineForm.elements['Id'] && vaccine.Id) vaccineForm.elements['Id'].value = vaccine.Id;
    } else {
        vaccineModalTitle.textContent = t('vaccine_modal_title_add');
        if(vaccineForm.elements['Id']) vaccineForm.elements['Id'].value = '';
    }
}
function closeVaccineModal() {
  vaccineModal.classList.add('hidden');
  editingVaccineId = null;
  // Reset completion mode
  isCompletingFromSchedule = false;
  currentScheduleData = null;
}
async function submitVaccineForm(e) {
  e.preventDefault();
  const t = passedI18nHealth.t;

  try {
    const formData = Object.fromEntries(new FormData(vaccineForm).entries());
    if (!formData.HayvanId) formData.HayvanId = animalSelectElHealth.value;
    formData.HayvanId = parseInt(formData.HayvanId);

    // Handle "Diğer" vaccine name selection
    if (formData.AsiAdi === 'Diğer' && formData.AsiAdiDiger) {
      formData.AsiAdi = formData.AsiAdiDiger;
    }
    // Remove the temporary field
    delete formData.AsiAdiDiger;

    if (editingVaccineId) {
      formData.Id = parseInt(editingVaccineId);
      await window.api.invoke('asilamalar:update', formData);
      window.toast.success(t('toast_success_update'));
    } else {
      // Remove Id field if it's empty, backend might auto-generate
      if (formData.Id === '' || formData.Id === null || formData.Id === undefined) delete formData.Id;

      // Check if this is a completion from schedule
      if (isCompletingFromSchedule && currentScheduleData) {
        // Add schedule reference to vaccination data
        formData.TakvimId = currentScheduleData.Id;
        formData.SablonId = currentScheduleData.SablonId;

        // Use the completion API that handles both vaccination and schedule update
        const result = await window.api.invoke('asilamalar:addFromSchedule', {
          vaccinationData: formData,
          scheduleId: currentScheduleData.Id
        });

        if (result.success) {
          window.toast.success(t('vaccination_completed_successfully') || 'Aşı başarıyla tamamlandı');

          // Reset completion mode
          isCompletingFromSchedule = false;
          currentScheduleData = null;

          // Refresh reminders if modal is open
          const remindersModal = document.getElementById('reminders-modal');
          if (remindersModal && !remindersModal.classList.contains('hidden')) {
            await refreshReminders();
          }

          // Refresh schedule if on schedule tab
          const activeTab = document.querySelector('#health-tabs .health-tab.active');
          if (activeTab && activeTab.getAttribute('data-tab') === 'asiTakvimi') {
            await loadVaccinationSchedule(animalSelectElHealth.value || null);
          }
        }
      } else {
        // Regular vaccination add
        await window.api.invoke('asilamalar:add', formData);
        window.toast.success(t('toast_success_save'));
      }
    }
    closeVaccineModal();
    await loadAndRenderActiveTabData(); // Re-fetch and re-render
    // Refresh recent activities
    if (healthRecentActivities) {
      healthRecentActivities.refresh();
    }
  } catch (error) {
    console.error('Error saving vaccine data:', error);
    window.toast.error(t('toast_error_save') + ': ' + error.message);
  }
}

let treatmentModal, treatmentForm, treatmentCloseBtn, treatmentModalTitle, editingTreatmentId;
function initializeTreatmentModal() {
  treatmentModal = document.getElementById('treatment-modal');
  treatmentForm = document.getElementById('treatment-form');
  treatmentCloseBtn = treatmentModal.querySelector('.close-btn');
  treatmentModalTitle = document.getElementById('treatment-modal-title');
  if (!treatmentForm.elements['HayvanId']) { /* Add HayvanId if missing */ }
  treatmentCloseBtn.onclick = closeTreatmentModal;
  treatmentModal.onclick = e => { if (e.target === treatmentModal) closeTreatmentModal(); };
  treatmentForm.onsubmit = submitTreatmentForm;

  // Setup diagnosis dropdown change handler
  const diagnosisSelect = document.getElementById('diagnosis-select');
  const diagnosisOther = document.getElementById('diagnosis-other');

  if (diagnosisSelect && diagnosisOther) {
    diagnosisSelect.addEventListener('change', function() {
      if (this.value === 'Diğer') {
        diagnosisOther.style.display = 'block';
        diagnosisOther.required = true;
      } else {
        diagnosisOther.style.display = 'none';
        diagnosisOther.required = false;
        diagnosisOther.value = '';
      }
    });
  }
}
function openTreatmentModal(treatment = null) {
    const t = passedI18nHealth.t;
    treatmentModal.classList.remove('hidden');
    treatmentForm.reset();
    editingTreatmentId = treatment ? treatment.Id : null;
    treatmentForm.elements['HayvanId'].value = animalSelectElHealth.value;

    // Reset diagnosis dropdown and other input
    const diagnosisSelect = document.getElementById('diagnosis-select');
    const diagnosisOther = document.getElementById('diagnosis-other');

    if (diagnosisOther) {
        diagnosisOther.style.display = 'none';
        diagnosisOther.required = false;
        diagnosisOther.value = '';
    }

    if (treatment) {
        treatmentModalTitle.textContent = t('treatment_modal_title_edit');

        // Handle diagnosis - check if it's a predefined option
        const predefinedDiagnoses = ['Mastitis', 'Laminitis', 'Pneumonia', 'Diarrhea', 'Ketosis', 'Milk Fever', 'Retained Placenta', 'Metritis', 'Bloat'];
        if (treatment.Teshis && predefinedDiagnoses.includes(treatment.Teshis)) {
            diagnosisSelect.value = treatment.Teshis;
        } else if (treatment.Teshis) {
            // Custom diagnosis
            diagnosisSelect.value = 'Diğer';
            diagnosisOther.style.display = 'block';
            diagnosisOther.required = true;
            diagnosisOther.value = treatment.Teshis;
        }

        // Set other fields
        Object.keys(treatment).forEach(key => {
            if (treatmentForm.elements[key] && key !== 'Teshis') {
                if (key === 'KarantinaDurumu') {
                    treatmentForm.elements[key].value = treatment[key] ? 'Var' : 'Yok';
                } else {
                    treatmentForm.elements[key].value = treatment[key] ?? '';
                }
            }
        });

        if(treatmentForm.elements['Id'] && treatment.Id) treatmentForm.elements['Id'].value = treatment.Id;
    } else {
        treatmentModalTitle.textContent = t('treatment_modal_title_add');
        if(treatmentForm.elements['Id']) treatmentForm.elements['Id'].value = '';
    }
}
function closeTreatmentModal() { treatmentModal.classList.add('hidden'); editingTreatmentId = null; }
async function submitTreatmentForm(e) {
  e.preventDefault();
  const t = passedI18nHealth.t;

  try {
    const formData = Object.fromEntries(new FormData(treatmentForm).entries());
    if (!formData.HayvanId) formData.HayvanId = animalSelectElHealth.value;
    formData.HayvanId = parseInt(formData.HayvanId);

    // Handle "Diğer" diagnosis selection
    if (formData.Teshis === 'Diğer' && formData.TeshisDiger) {
      formData.Teshis = formData.TeshisDiger;
    }
    // Remove the temporary field
    delete formData.TeshisDiger;

    // Convert karantina durumu to boolean
    formData.KarantinaDurumu = formData.KarantinaDurumu === 'Var';

    // Remove unused karantina date fields
    delete formData.KarantinaBaslangicTarihi;
    delete formData.KarantinaBitisTarihi;

    if (editingTreatmentId) {
      formData.Id = parseInt(editingTreatmentId);
      await window.api.invoke('tedaviler:update', formData);
      window.toast.success(t('toast_success_update'));
    } else {
      if (formData.Id === '' || formData.Id === null || formData.Id === undefined) delete formData.Id;
      await window.api.invoke('tedaviler:add', formData);
      window.toast.success(t('toast_success_save'));
    }
    closeTreatmentModal();
    await loadAndRenderActiveTabData(); // Re-fetch and re-render
    // Refresh recent activities
    if (healthRecentActivities) {
      healthRecentActivities.refresh();
    }
  } catch (error) {
    console.error('Error saving treatment data:', error);
    window.toast.error(t('toast_error_save') + ': ' + error.message);
  }
}
// --- END MODAL HANDLING ---

// --- VACCINATION MANAGEMENT PAGE ---
let allVaccinationTemplates = [];
let currentVaccinationTemplates = [];
const itemsPerPageTemplates = 10;
let currentPageTemplates = 1;
let sortColumnTemplates = 'SablonAdi';
let sortDirectionTemplates = 1;

function renderVaccinationManagementPage() {
  const t = passedI18nHealth.t;

  healthContentEl.innerHTML = `
    <div class="vaccination-management-container">
      <!-- Templates Section -->
      <div class="management-section">
        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
          <h3 style="margin:0;font-size:1.2rem;">${t('vaccination_templates')}</h3>
          <button class="btn btn-primary" id="add-template-btn">
            <svg viewBox='0 0 20 20' fill='none'>
              <circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/>
              <path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/>
            </svg>
            ${t('btn_add_template')}
          </button>
        </div>

        <table id="templates-table" class="modern-table">
          <thead>
            <tr>
              <th data-sort="SablonAdi">${t('template_name')}</th>
              <th data-sort="HayvanTuru">${t('animal_type')}</th>
              <th data-sort="AsiAdi">${t('vaccine_name')}</th>
              <th data-sort="Periyot">${t('interval')}</th>
              <th data-sort="Aktif">${t('status')}</th>
              <th>${t('actions')}</th>
            </tr>
            <tr class="filter-row">
              <th><input type="text" id="filter-template-name" class="filter-input" placeholder="${t('template_name')}"></th>
              <th><input type="text" id="filter-animal-type" class="filter-input" placeholder="${t('animal_type')}"></th>
              <th><input type="text" id="filter-vaccine-name" class="filter-input" placeholder="${t('vaccine_name')}"></th>
              <th></th>
              <th>
                <select id="filter-template-status" class="filter-input">
                  <option value="">${t('option_all')}</option>
                  <option value="1">${t('option_active')}</option>
                  <option value="0">${t('option_inactive')}</option>
                </select>
              </th>
              <th></th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>

        <div id="templates-pagination-controls" class="pagination-controls">
          <button id="prev-page-templates" disabled>&laquo; ${t('btn_previous')}</button>
          <span id="page-info-templates"></span>
          <button id="next-page-templates" disabled>&raquo; ${t('btn_next')}</button>
        </div>
      </div>


    </div>
  `;

  // Event listeners
  healthContentEl.querySelector('#add-template-btn').onclick = () => openTemplateModal();

  // Setup table functionality
  setupTableSortingHealth('#templates-table', ['SablonAdi', 'HayvanTuru', 'AsiAdi', 'Periyot', 'Aktif'], 'template');
  setupFilterListenersHealth('template');
  setupPaginationListenersHealth('template');

  // Tablo stillerini zorla
  forceHealthTableStyles('#templates-table');

  // Initialize table animations
  setTimeout(() => {
    TableAnimations.initializeTable('#templates-table');
  }, 100);

  // Load and render templates
  loadVaccinationTemplates();
}

async function loadVaccinationTemplates() {
  try {
    allVaccinationTemplates = await window.api.invoke('asiSablonlari:list');
    currentVaccinationTemplates = [...allVaccinationTemplates];
    currentPageTemplates = 1;
    updateTemplatesTableBody();
    updateTemplatesPaginationControls();
  } catch (error) {
    console.error('Error loading vaccination templates:', error);
    window.toast.error(passedI18nHealth.t('toast_error_load') + ': ' + error.message);
  }
}

function updateTemplatesTableBody() {
  const t = passedI18nHealth.t;
  const tbody = document.querySelector('#templates-table tbody');
  if (!tbody) return;

  tbody.innerHTML = '';

  if (currentVaccinationTemplates.length === 0) {
    tbody.innerHTML = `<tr><td colspan="6" class="no-data">${t('no_records_found')}</td></tr>`;
    return;
  }

  const startIndex = (currentPageTemplates - 1) * itemsPerPageTemplates;
  const endIndex = Math.min(startIndex + itemsPerPageTemplates, currentVaccinationTemplates.length);
  const pageTemplates = currentVaccinationTemplates.slice(startIndex, endIndex);

  pageTemplates.forEach(template => {
    const row = tbody.insertRow();

    // Template Name
    row.insertCell().textContent = template.SablonAdi || '-';

    // Animal Type
    row.insertCell().textContent = template.HayvanTuru || t('option_all');

    // Vaccine Name
    row.insertCell().textContent = template.AsiAdi || '-';

    // Interval
    const intervalCell = row.insertCell();
    let intervalText = '';
    if (template.Periyot) {
      const periodType = template.PeriyotTipi === 'ay' ? t('month_unit') :
                        template.PeriyotTipi === 'yil' ? t('year_unit') : t('day_unit');
      intervalText = `${template.Periyot} ${periodType}`;
    }
    intervalCell.textContent = intervalText || '-';

    // Status
    const statusCell = row.insertCell();
    const statusBadge = BadgeSystem.createBadge(
      template.Aktif ? t('option_active') : t('option_inactive'),
      template.Aktif ? 'success' : 'secondary'
    );
    statusCell.innerHTML = statusBadge;

    // Actions
    const actionsCell = row.insertCell();
    actionsCell.className = 'actions-cell';
    actionsCell.innerHTML = `
      <div class="actions-wrapper">
        ${IconSystem.createActionButton('edit', template.Id, t('btn_edit'))}
        ${IconSystem.createActionButton('delete', template.Id, t('btn_delete'))}
      </div>
    `;

    // Event listeners for actions
    row.querySelector('.edit-btn').onclick = () => openTemplateModal(template);
    row.querySelector('.delete-btn').onclick = async () => {
      const confirmed = await window.toast.confirm(t('confirm_delete_template'), {
        confirmText: t('toast_confirm'),
        cancelText: t('toast_cancel')
      });

      if (confirmed) {
        try {
          await window.api.invoke('asiSablonlari:delete', template.Id);
          await loadVaccinationTemplates();
          window.toast.success(t('toast_success_delete'));
        } catch (error) {
          console.error('Error deleting template:', error);
          window.toast.error(t('toast_error_delete') + ': ' + error.message);
        }
      }
    };
  });
}

function updateTemplatesPaginationControls() {
  const t = passedI18nHealth.t;
  const totalPages = Math.ceil(currentVaccinationTemplates.length / itemsPerPageTemplates);

  const prevBtn = document.getElementById('prev-page-templates');
  const nextBtn = document.getElementById('next-page-templates');
  const pageInfo = document.getElementById('page-info-templates');

  if (!prevBtn || !nextBtn || !pageInfo) return;

  prevBtn.disabled = currentPageTemplates <= 1;
  nextBtn.disabled = currentPageTemplates >= totalPages;

  pageInfo.textContent = `${t('page')} ${currentPageTemplates} / ${totalPages} (${currentVaccinationTemplates.length} ${t('records')})`;
}

// Modal handlers
function openTemplateModal(template = null) {
  const t = passedI18nHealth.t;

  // Create modal
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.id = 'template-modal';

  modal.innerHTML = `
    <div class="modal-content" style="max-width: 600px;">
      <div class="modal-header">
        <span class="close-btn">&times;</span>
        <h2>${template ? t('edit_template') : t('add_template')}</h2>
      </div>
      <form id="template-form">
        <div class="modal-body">
          <div class="form-section">
            <h3 class="section-title">${t('template_information')}</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="template-name">${t('template_name')} *</label>
                <input type="text" id="template-name" name="sablonAdi" required
                       value="${template?.SablonAdi || ''}" placeholder="${t('enter_template_name')}">
              </div>
              <div class="form-group">
                <label for="animal-type">${t('animal_type')}</label>
                <select id="animal-type" name="hayvanTuru">
                  <option value="">${t('option_all')}</option>
                  <option value="Sığır" ${template?.HayvanTuru === 'Sığır' ? 'selected' : ''}>Sığır</option>
                  <option value="Koyun" ${template?.HayvanTuru === 'Koyun' ? 'selected' : ''}>Koyun</option>
                  <option value="Keçi" ${template?.HayvanTuru === 'Keçi' ? 'selected' : ''}>Keçi</option>
                </select>
              </div>
              <div class="form-group">
                <label for="template-vaccine">${t('vaccine_name')} *</label>
                <input type="text" id="template-vaccine" name="asiAdi" required
                       value="${template?.AsiAdi || ''}" placeholder="${t('enter_vaccine_name')}">
              </div>
              <div class="form-group">
                <label for="interval-value">${t('interval_value')} *</label>
                <input type="number" id="interval-value" name="periyot" required min="1"
                       value="${template?.Periyot || ''}" placeholder="${t('enter_interval')}">
              </div>
              <div class="form-group">
                <label for="interval-type">${t('interval_type')} *</label>
                <select id="interval-type" name="periyotTipi" required>
                  <option value="gun" ${template?.PeriyotTipi === 'gun' ? 'selected' : ''}>${t('days')}</option>
                  <option value="ay" ${template?.PeriyotTipi === 'ay' ? 'selected' : ''}>${t('months')}</option>
                  <option value="yil" ${template?.PeriyotTipi === 'yil' ? 'selected' : ''}>${t('years')}</option>
                </select>
              </div>
              <div class="form-group">
                <label for="first-age">${t('first_vaccination_age')}</label>
                <input type="number" id="first-age" name="ilkAsiYasi" min="0"
                       value="${template?.IlkAsiYasi || ''}" placeholder="${t('days_after_birth')}">
              </div>
              <div class="form-group">
                <label for="repeat-count">${t('repeat_count')}</label>
                <input type="number" id="repeat-count" name="tekrarSayisi" min="-1"
                       value="${template?.TekrarSayisi || ''}" placeholder="${t('unlimited_if_negative')}">
              </div>
              <div class="form-group">
                <label for="template-active">${t('status')}</label>
                <select id="template-active" name="aktif">
                  <option value="1" ${template?.Aktif !== 0 ? 'selected' : ''}>${t('option_active')}</option>
                  <option value="0" ${template?.Aktif === 0 ? 'selected' : ''}>${t('option_inactive')}</option>
                </select>
              </div>
              <div class="form-group full-width">
                <label for="template-description">${t('description')}</label>
                <textarea id="template-description" name="aciklama" rows="3"
                          placeholder="${t('enter_description')}">${template?.Aciklama || ''}</textarea>
              </div>
            </div>
          </div>
          <input type="hidden" name="id" value="${template?.Id || ''}">
        </div>
        <div class="modal-footer">
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" id="template-cancel-btn">
              ${t('btn_cancel')}
            </button>
            <button type="submit" class="btn btn-primary">
              ${t('btn_save')}
            </button>
          </div>
        </div>
      </form>
    </div>
  `;

  document.body.appendChild(modal);
  modal.style.display = 'flex';

  // Wait for DOM to be ready, then add event listeners
  setTimeout(() => {
    // Event listeners
    const closeBtn = modal.querySelector('.close-btn');
    if (closeBtn) {
      closeBtn.onclick = closeTemplateModal;
      console.log('Template modal close button event listener added');
    } else {
      console.warn('Close button not found in template modal');
    }

    const cancelBtn = modal.querySelector('#template-cancel-btn');
    if (cancelBtn) {
      cancelBtn.onclick = closeTemplateModal;
      console.log('Template modal cancel button event listener added');
    }

    modal.onclick = (e) => { if (e.target === modal) closeTemplateModal(); };

    const form = modal.querySelector('#template-form');
    if (form) {
      form.onsubmit = submitTemplateForm;
    }

    // Escape key listener
    const escapeHandler = (e) => {
      if (e.key === 'Escape') {
        closeTemplateModal();
        document.removeEventListener('keydown', escapeHandler);
      }
    };
    document.addEventListener('keydown', escapeHandler);
  }, 0);
}

function closeTemplateModal() {
  console.log('closeTemplateModal called'); // Debug
  const modal = document.getElementById('template-modal');
  if (modal) {
    // Remove any event listeners
    document.removeEventListener('keydown', closeTemplateModal);
    modal.remove();
    console.log('Template modal removed'); // Debug
  } else {
    console.warn('Template modal not found when trying to close');
  }
}

async function submitTemplateForm(e) {
  e.preventDefault();
  const t = passedI18nHealth.t;

  try {
    const formData = new FormData(e.target);
    const rawData = Object.fromEntries(formData.entries());

    // Convert field names to match database columns
    const templateData = {
      SablonAdi: rawData.sablonAdi,
      HayvanTuru: rawData.hayvanTuru || null,
      AsiAdi: rawData.asiAdi,
      Periyot: parseInt(rawData.periyot),
      PeriyotTipi: rawData.periyotTipi,
      IlkAsiYasi: rawData.ilkAsiYasi ? parseInt(rawData.ilkAsiYasi) : null,
      TekrarSayisi: rawData.tekrarSayisi ? parseInt(rawData.tekrarSayisi) : -1,
      Aktif: parseInt(rawData.aktif),
      Aciklama: rawData.aciklama || null
    };

    if (templateData.id) {
      // Update existing template
      templateData.Id = parseInt(templateData.id);
      await window.api.invoke('asiSablonlari:update', templateData);
      window.toast.success(t('toast_success_update'));
    } else {
      // Add new template
      delete templateData.id;
      await window.api.invoke('asiSablonlari:add', templateData);
      window.toast.success(t('toast_success_save'));
    }

    // Close modal first
    closeTemplateModal();
    await loadVaccinationTemplates();

  } catch (error) {
    console.error('Error saving template:', error);
    window.toast.error(t('toast_error_save') + ': ' + error.message);
  }
}

async function openBulkVaccinationModal() {
  const t = passedI18nHealth.t;

  try {
    // Get all active animals
    const animals = await window.api.invoke('hayvanlar:list');
    const activeAnimals = animals.filter(animal => animal.AktifMi);

    if (activeAnimals.length === 0) {
      window.toast.warning(t('no_active_animals_found'));
      return;
    }

    // Create modal
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'bulk-vaccination-modal';

    modal.innerHTML = `
      <div class="modal-content" style="max-width: 800px;">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2>${t('bulk_vaccination')}</h2>
        </div>
        <form id="bulk-vaccination-form">
          <div class="modal-body">
            <!-- Vaccination Information -->
            <div class="form-section">
              <h3 class="section-title">${t('vaccination_information')}</h3>
              <div class="form-grid">
                <div class="form-group">
                  <label for="bulk-vaccine-name">${t('label_vaccine_name')} *</label>
                  <select id="bulk-vaccine-name" name="asiAdi" required>
                    <option value="">${t('select_option')}</option>
                    <option value="Şap Aşısı">Şap Aşısı</option>
                    <option value="Şarbon Aşısı">Şarbon Aşısı</option>
                    <option value="Enterotoksemi Aşısı">Enterotoksemi Aşısı</option>
                    <option value="Brucella Aşısı">Brucella Aşısı</option>
                    <option value="IBR/IPV Aşısı">IBR/IPV Aşısı</option>
                    <option value="BVD Aşısı">BVD Aşısı</option>
                    <option value="Pastörella Aşısı">Pastörella Aşısı</option>
                    <option value="Diğer">${t('option_other')}</option>
                  </select>
                </div>
                <div class="form-group" id="bulk-vaccine-other-group" style="display: none;">
                  <label for="bulk-vaccine-other">${t('label_vaccine_name')} (${t('option_other')})</label>
                  <input type="text" id="bulk-vaccine-other" name="asiAdiDiger" placeholder="${t('enter_vaccine_name')}">
                </div>
                <div class="form-group">
                  <label for="bulk-vaccination-date">${t('label_vaccination_date')} *</label>
                  <input type="date" id="bulk-vaccination-date" name="asilamaTarihi" required>
                </div>
                <div class="form-group">
                  <label for="bulk-dose">${t('label_dose')}</label>
                  <input type="text" id="bulk-dose" name="doz" placeholder="${t('enter_dose')}">
                </div>
                <div class="form-group">
                  <label for="bulk-veterinarian">${t('label_vet')}</label>
                  <input type="text" id="bulk-veterinarian" name="uygulayanVeteriner" placeholder="${t('enter_veterinarian')}">
                </div>
                <div class="form-group full-width">
                  <label for="bulk-notes">${t('label_notes')}</label>
                  <textarea id="bulk-notes" name="notlar" rows="3" placeholder="${t('enter_notes')}"></textarea>
                </div>
              </div>
            </div>

            <!-- Animal Selection -->
            <div class="form-section">
              <h3 class="section-title">${t('select_animals')}</h3>
              <div class="animal-selection-controls">
                <button type="button" class="btn btn-sm btn-secondary" id="select-all-animals-btn">
                  ${t('select_all')}
                </button>
                <button type="button" class="btn btn-sm btn-secondary" id="deselect-all-animals-btn">
                  ${t('deselect_all')}
                </button>
                <span class="selected-count">
                  ${t('selected')}: <span id="selected-animal-count">0</span> / ${activeAnimals.length}
                </span>
              </div>
              <div class="animal-selection-list">
                ${activeAnimals.map(animal => `
                  <div class="animal-selection-row">
                    <label class="animal-checkbox-row">
                      <input type="checkbox" name="selectedAnimals" value="${animal.Id}">
                      <span class="animal-info-compact">
                        <strong>${animal.KupeNo || `#${animal.Id}`}</strong>
                        ${animal.Isim ? ` - ${animal.Isim}` : ''}
                        ${animal.Tur ? ` (${animal.Tur})` : ''}
                      </span>
                    </label>
                  </div>
                `).join('')}
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" id="bulk-cancel-btn">
                ${t('btn_cancel')}
              </button>
              <button type="submit" class="btn btn-primary">
                ${t('btn_save_vaccinations')}
              </button>
            </div>
          </div>
        </form>
      </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';

    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    modal.querySelector('#bulk-vaccination-date').value = today;

    // Wait for DOM to be ready, then add event listeners
    setTimeout(() => {
      // Event listeners
      const closeBtn = modal.querySelector('.close-btn');
      if (closeBtn) {
        closeBtn.onclick = closeBulkVaccinationModal;
        console.log('Bulk vaccination modal close button event listener added');
      } else {
        console.warn('Close button not found in bulk vaccination modal');
      }

      const cancelBtn = modal.querySelector('#bulk-cancel-btn');
      if (cancelBtn) {
        cancelBtn.onclick = closeBulkVaccinationModal;
        console.log('Bulk vaccination modal cancel button event listener added');
      }

      modal.onclick = (e) => { if (e.target === modal) closeBulkVaccinationModal(); };

      // Escape key listener
      const escapeHandler = (e) => {
        if (e.key === 'Escape') {
          closeBulkVaccinationModal();
          document.removeEventListener('keydown', escapeHandler);
        }
      };
      document.addEventListener('keydown', escapeHandler);
    }, 0);

    // Vaccine name change handler
    modal.querySelector('#bulk-vaccine-name').onchange = function() {
      const otherGroup = modal.querySelector('#bulk-vaccine-other-group');
      const otherInput = modal.querySelector('#bulk-vaccine-other');

      if (this.value === 'Diğer') {
        otherGroup.style.display = 'block';
        otherInput.required = true;
      } else {
        otherGroup.style.display = 'none';
        otherInput.required = false;
        otherInput.value = '';
      }
    };

    // Animal selection change handler
    modal.querySelectorAll('input[name="selectedAnimals"]').forEach(checkbox => {
      checkbox.onchange = updateSelectedCount;
    });

    // Select/Deselect all buttons
    modal.querySelector('#select-all-animals-btn').onclick = selectAllAnimals;
    modal.querySelector('#deselect-all-animals-btn').onclick = deselectAllAnimals;

    // Form submit handler
    modal.querySelector('#bulk-vaccination-form').onsubmit = submitBulkVaccination;

  } catch (error) {
    console.error('Error opening bulk vaccination modal:', error);
    window.toast.error(t('toast_error_load') + ': ' + error.message);
  }
}

function closeBulkVaccinationModal() {
  console.log('closeBulkVaccinationModal called'); // Debug
  const modal = document.getElementById('bulk-vaccination-modal');
  if (modal) {
    // Remove any event listeners
    document.removeEventListener('keydown', closeBulkVaccinationModal);
    modal.remove();
    console.log('Bulk vaccination modal removed'); // Debug
  } else {
    console.warn('Bulk vaccination modal not found when trying to close');
  }
}

function selectAllAnimals() {
  const modal = document.getElementById('bulk-vaccination-modal');
  if (!modal) return;

  modal.querySelectorAll('input[name="selectedAnimals"]').forEach(checkbox => {
    checkbox.checked = true;
  });
  updateSelectedCount();
}

function deselectAllAnimals() {
  const modal = document.getElementById('bulk-vaccination-modal');
  if (!modal) return;

  modal.querySelectorAll('input[name="selectedAnimals"]').forEach(checkbox => {
    checkbox.checked = false;
  });
  updateSelectedCount();
}

function updateSelectedCount() {
  const modal = document.getElementById('bulk-vaccination-modal');
  if (!modal) return;

  const selectedCount = modal.querySelectorAll('input[name="selectedAnimals"]:checked').length;
  const countElement = modal.querySelector('#selected-animal-count');
  if (countElement) {
    countElement.textContent = selectedCount;
  }
}

async function submitBulkVaccination(e) {
  e.preventDefault();
  const t = passedI18nHealth.t;

  try {
    const modal = document.getElementById('bulk-vaccination-modal');
    const formData = new FormData(e.target);

    // Get selected animals
    const selectedAnimals = Array.from(modal.querySelectorAll('input[name="selectedAnimals"]:checked'))
      .map(checkbox => parseInt(checkbox.value));

    if (selectedAnimals.length === 0) {
      window.toast.warning(t('please_select_animals'));
      return;
    }

    // Prepare vaccination data
    const vaccinationData = {
      hayvanIds: selectedAnimals,
      asiAdi: formData.get('asiAdi') === 'Diğer' ? formData.get('asiAdiDiger') : formData.get('asiAdi'),
      asilamaTarihi: formData.get('asilamaTarihi'),
      doz: formData.get('doz') || '',
      uygulayanVeteriner: formData.get('uygulayanVeteriner') || '',
      notlar: formData.get('notlar') || ''
    };

    // Submit bulk vaccination
    const result = await window.api.invoke('asilamalar:bulkAdd', vaccinationData);

    if (result.success) {
      window.toast.success(t('bulk_vaccination_success').replace('{count}', result.count));

      // Close modal first
      closeBulkVaccinationModal();

      // Refresh current tab if on vaccinations
      const activeTab = document.querySelector('#health-tabs .health-tab.active');
      if (activeTab && activeTab.getAttribute('data-tab') === 'asilamalar') {
        await loadAndRenderActiveTabData('asilamalar');
      }

      // Refresh recent activities
      if (healthRecentActivities) {
        healthRecentActivities.refresh();
      }
    }

  } catch (error) {
    console.error('Error submitting bulk vaccination:', error);
    window.toast.error(t('toast_error_save') + ': ' + error.message);
  }
}

async function openGenerateScheduleModal() {
  const t = passedI18nHealth.t;

  try {
    // Get active animals and templates
    const [animals, templates] = await Promise.all([
      window.api.invoke('hayvanlar:list'),
      window.api.invoke('asiSablonlari:list')
    ]);

    const activeAnimals = animals.filter(animal => animal.AktifMi);
    const activeTemplates = templates.filter(template => template.Aktif);

    if (activeAnimals.length === 0) {
      window.toast.warning(t('no_active_animals_found'));
      return;
    }

    if (activeTemplates.length === 0) {
      window.toast.warning(t('no_active_templates_found'));
      return;
    }

    // Create modal
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'generate-schedule-modal';

    modal.innerHTML = `
      <div class="modal-content" style="max-width: 700px;">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2>${t('generate_schedule')}</h2>
        </div>
        <form id="generate-schedule-form">
          <div class="modal-body">
            <div class="form-section">
              <h3 class="section-title">${t('select_animals')}</h3>
              <div class="schedule-animal-selection">
                <div class="form-group">
                  <label>
                    <input type="radio" name="animalSelection" value="all" checked>
                    ${t('all_animals')}
                  </label>
                </div>
                <div class="form-group">
                  <label>
                    <input type="radio" name="animalSelection" value="single">
                    ${t('single_animal')}
                  </label>
                  <select id="schedule-animal" name="hayvanId" disabled style="margin-top: 8px;">
                    <option value="">${t('please_select_animal')}</option>
                    ${activeAnimals.map(animal => `
                      <option value="${animal.Id}">
                        ${animal.KupeNo || `#${animal.Id}`} - ${animal.Isim || ''} (${animal.Tur || ''})
                      </option>
                    `).join('')}
                  </select>
                </div>
              </div>
            </div>

            <div class="form-section">
              <h3 class="section-title">${t('select_templates')}</h3>
              <div class="template-selection-grid">
                ${activeTemplates.map(template => `
                  <div class="template-selection-item">
                    <label class="template-checkbox">
                      <input type="checkbox" name="selectedTemplates" value="${template.Id}">
                      <div class="template-info">
                        <div class="template-name">${template.SablonAdi}</div>
                        <div class="template-vaccine">${template.AsiAdi}</div>
                        <div class="template-interval">
                          ${template.Periyot} ${template.PeriyotTipi === 'ay' ? t('months') :
                            template.PeriyotTipi === 'yil' ? t('years') : t('days')}
                        </div>
                      </div>
                    </label>
                  </div>
                `).join('')}
              </div>
            </div>

            <div class="form-section">
              <h3 class="section-title">${t('schedule_settings')}</h3>
              <div class="form-group">
                <label for="start-date">${t('start_date')} *</label>
                <input type="date" id="start-date" name="baslangicTarihi" required>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" id="schedule-cancel-btn">
                ${t('btn_cancel')}
              </button>
              <button type="submit" class="btn btn-primary">
                ${t('btn_generate')}
              </button>
            </div>
          </div>
        </form>
      </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';

    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    modal.querySelector('#start-date').value = today;

    // Wait for DOM to be ready, then add event listeners
    setTimeout(() => {
      // Event listeners
      const closeBtn = modal.querySelector('.close-btn');
      if (closeBtn) {
        closeBtn.onclick = closeGenerateScheduleModal;
      }

      const cancelBtn = modal.querySelector('#schedule-cancel-btn');
      if (cancelBtn) {
        cancelBtn.onclick = closeGenerateScheduleModal;
      }

      modal.onclick = (e) => { if (e.target === modal) closeGenerateScheduleModal(); };

      // Escape key listener
      const escapeHandler = (e) => {
        if (e.key === 'Escape') {
          closeGenerateScheduleModal();
          document.removeEventListener('keydown', escapeHandler);
        }
      };
      document.addEventListener('keydown', escapeHandler);
    }, 0);

    // Animal selection radio buttons
    modal.querySelectorAll('input[name="animalSelection"]').forEach(radio => {
      radio.onchange = function() {
        const animalSelect = modal.querySelector('#schedule-animal');
        if (this.value === 'single') {
          animalSelect.disabled = false;
          animalSelect.required = true;
        } else {
          animalSelect.disabled = true;
          animalSelect.required = false;
          animalSelect.value = '';
        }
      };
    });

    modal.querySelector('#generate-schedule-form').onsubmit = submitGenerateSchedule;

  } catch (error) {
    console.error('Error opening generate schedule modal:', error);
    window.toast.error(t('toast_error_load') + ': ' + error.message);
  }
}

function closeGenerateScheduleModal() {
  const modal = document.getElementById('generate-schedule-modal');
  if (modal) {
    // Remove any event listeners
    document.removeEventListener('keydown', closeGenerateScheduleModal);
    modal.remove();
  }
}

async function submitGenerateSchedule(e) {
  e.preventDefault();
  const t = passedI18nHealth.t;

  try {
    const modal = document.getElementById('generate-schedule-modal');
    const formData = new FormData(e.target);

    // Get selected templates
    const selectedTemplates = Array.from(modal.querySelectorAll('input[name="selectedTemplates"]:checked'))
      .map(checkbox => parseInt(checkbox.value));

    if (selectedTemplates.length === 0) {
      window.toast.warning(t('please_select_templates'));
      return;
    }

    // Check animal selection type
    const animalSelection = formData.get('animalSelection');
    let targetAnimals = [];

    if (animalSelection === 'all') {
      // Get all active animals
      const animals = await window.api.invoke('hayvanlar:list');
      targetAnimals = animals.filter(animal => animal.AktifMi).map(animal => animal.Id);
    } else {
      // Single animal
      const hayvanId = formData.get('hayvanId');
      if (!hayvanId) {
        window.toast.warning(t('please_select_animal'));
        return;
      }
      targetAnimals = [parseInt(hayvanId)];
    }

    if (targetAnimals.length === 0) {
      window.toast.warning(t('no_active_animals_found'));
      return;
    }

    // Generate schedules for all target animals
    let totalGenerated = 0;
    const startDate = formData.get('baslangicTarihi');

    for (const animalId of targetAnimals) {
      const scheduleData = {
        hayvanId: animalId,
        sablonIds: selectedTemplates,
        baslangicTarihi: startDate
      };

      const result = await window.api.invoke('asiTakvimi:generateSchedule', scheduleData);
      if (result.success) {
        totalGenerated += result.count;
      }
    }

    if (totalGenerated > 0) {
      window.toast.success(t('schedule_generated_success').replace('{count}', totalGenerated));

      // Close modal first
      closeGenerateScheduleModal();

      // Refresh schedule if on schedule tab
      const activeTab = document.querySelector('#health-tabs .health-tab.active');
      if (activeTab && activeTab.getAttribute('data-tab') === 'asi-takvimi') {
        await loadVaccinationSchedule();
        await loadScheduleOverview();
      }
    }

  } catch (error) {
    console.error('Error generating schedule:', error);
    window.toast.error(t('toast_error_save') + ': ' + error.message);
  }
}

async function openRemindersModal() {
  const t = passedI18nHealth.t;

  try {
    // Get upcoming and overdue vaccinations
    const [upcoming, overdue] = await Promise.all([
      window.api.invoke('asiTakvimi:getUpcoming', 30),
      window.api.invoke('asiTakvimi:getOverdue')
    ]);

    // Create modal
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'reminders-modal';

    modal.innerHTML = `
      <div class="modal-content" style="max-width: 900px;">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2>${t('vaccination_reminders')}</h2>
        </div>
        <div class="modal-body">
          <!-- Overdue Section -->
          <div class="reminders-section">
            <h3 style="color: var(--danger-color); margin-bottom: 12px;">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 8px;">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                <line x1="12" y1="9" x2="12" y2="13"/>
                <line x1="12" y1="17" x2="12.01" y2="17"/>
              </svg>
              ${t('overdue_vaccinations')} (${overdue.length})
            </h3>
            <div class="reminders-list">
              ${overdue.length === 0 ?
                `<div class="no-reminders">${t('no_overdue_vaccinations')}</div>` :
                overdue.map(item => `
                  <div class="reminder-item overdue">
                    <div class="reminder-info">
                      <div class="animal-info">
                        <strong>${item.KupeNo || item.HayvanIsim}</strong>
                        <span class="vaccine-name">${item.AsiAdi}</span>
                      </div>
                      <div class="reminder-details">
                        <span class="planned-date">${t('planned_date')}: ${formatDate(item.PlanlananTarih)}</span>
                        <span class="overdue-days">${Math.floor(item.GecikmeGunu)} ${t('days_overdue')}</span>
                      </div>
                    </div>
                    <div class="reminder-actions">
                      <button class="btn btn-sm btn-success" onclick="markVaccinationCompleted(${item.Id})" title="${t('mark_completed')}">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="width: 16px; height: 16px;">
                          <path d="M9 12l2 2 4-4"/>
                          <circle cx="12" cy="12" r="10"/>
                        </svg>
                        ${t('mark_completed')}
                      </button>
                    </div>
                  </div>
                `).join('')
              }
            </div>
          </div>

          <!-- Upcoming Section -->
          <div class="reminders-section" style="margin-top: 24px;">
            <h3 style="color: var(--warning-color); margin-bottom: 12px;">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 8px;">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
              ${t('upcoming_vaccinations')} (${upcoming.filter(u => u.UyariTipi !== 'Gecikti').length})
            </h3>
            <div class="reminders-list">
              ${upcoming.filter(u => u.UyariTipi !== 'Gecikti').length === 0 ?
                `<div class="no-reminders">${t('no_upcoming_vaccinations')}</div>` :
                upcoming.filter(u => u.UyariTipi !== 'Gecikti').map(item => `
                  <div class="reminder-item ${item.UyariTipi.toLowerCase()}">
                    <div class="reminder-info">
                      <div class="animal-info">
                        <strong>${item.KupeNo || item.HayvanIsim}</strong>
                        <span class="vaccine-name">${item.AsiAdi}</span>
                      </div>
                      <div class="reminder-details">
                        <span class="planned-date">${t('planned_date')}: ${formatDate(item.PlanlananTarih)}</span>
                        <span class="reminder-type ${item.UyariTipi.toLowerCase()}">${getWarningTypeText(item.UyariTipi, t)}</span>
                      </div>
                    </div>
                    <div class="reminder-actions">
                      <button class="btn btn-sm btn-success" onclick="markVaccinationCompleted(${item.Id})" title="${t('mark_completed')}">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="width: 16px; height: 16px;">
                          <path d="M9 12l2 2 4-4"/>
                          <circle cx="12" cy="12" r="10"/>
                        </svg>
                        ${t('mark_completed')}
                      </button>
                    </div>
                  </div>
                `).join('')
              }
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" id="reminders-close-btn">
            ${t('btn_close')}
          </button>
          <button type="button" class="btn btn-primary" onclick="refreshReminders()">
            ${t('btn_refresh')}
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';

    // Wait for DOM to be ready, then add event listeners
    setTimeout(() => {
      // Event listeners
      const closeBtn = modal.querySelector('.close-btn');
      if (closeBtn) {
        closeBtn.onclick = closeRemindersModal;
      }

      const cancelBtn = modal.querySelector('#reminders-close-btn');
      if (cancelBtn) {
        cancelBtn.onclick = closeRemindersModal;
      }

      modal.onclick = (e) => { if (e.target === modal) closeRemindersModal(); };

      // Escape key listener
      const escapeHandler = (e) => {
        if (e.key === 'Escape') {
          closeRemindersModal();
          document.removeEventListener('keydown', escapeHandler);
        }
      };
      document.addEventListener('keydown', escapeHandler);
    }, 0);

  } catch (error) {
    console.error('Error loading vaccination reminders:', error);
    window.toast.error(t('toast_error_load') + ': ' + error.message);
  }
}

function closeRemindersModal() {
  const modal = document.getElementById('reminders-modal');
  if (modal) {
    // Remove any event listeners
    document.removeEventListener('keydown', closeRemindersModal);
    modal.remove();
  }
}

function formatDate(dateStr) {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleDateString('tr-TR');
}

function getWarningTypeText(type, t) {
  switch (type) {
    case 'Bugun': return t('due_today');
    case 'Yaklasan': return t('due_soon');
    default: return type;
  }
}

async function markVaccinationCompleted(scheduleId) {
  const t = passedI18nHealth.t;

  try {
    // Get schedule details first
    const scheduleDetails = await window.api.invoke('asiTakvimi:getById', scheduleId);
    if (!scheduleDetails) {
      window.toast.error(t('schedule_not_found') || 'Takvim kaydı bulunamadı');
      return;
    }

    // Open vaccination completion modal with pre-filled data
    await openVaccineCompletionModal(scheduleDetails);

  } catch (error) {
    console.error('Error marking vaccination as completed:', error);
    window.toast.error(t('toast_error_save') + ': ' + error.message);
  }
}

async function refreshReminders() {
  closeRemindersModal();
  await openRemindersModal();
}

// Global variable to track if we're completing from schedule
let isCompletingFromSchedule = false;
let currentScheduleData = null;

async function openVaccineCompletionModal(scheduleData) {
  const t = passedI18nHealth.t;

  try {
    // Set completion mode
    isCompletingFromSchedule = true;
    currentScheduleData = scheduleData;

    // Get animal details for the schedule
    const animal = await window.api.invoke('hayvanlar:getById', scheduleData.HayvanId);
    if (!animal) {
      window.toast.error(t('animal_not_found') || 'Hayvan bulunamadı');
      return;
    }

    // Open the existing vaccine modal but with pre-filled data
    vaccineModal.classList.remove('hidden');
    vaccineForm.reset();
    editingVaccineId = null; // This is a new vaccination record

    // Update modal title to indicate completion
    vaccineModalTitle.textContent = t('complete_vaccination') || 'Aşıyı Tamamla';

    // Pre-fill form with schedule data
    vaccineForm.elements['HayvanId'].value = scheduleData.HayvanId;

    // Set vaccine name from template
    const vaccineNameSelect = document.getElementById('vaccine-name-select');
    const vaccineNameOther = document.getElementById('vaccine-name-other');

    if (scheduleData.AsiAdi) {
      const predefinedOptions = ['Şap Aşısı', 'Şarbon Aşısı', 'Enterotoksemi Aşısı', 'Brucella Aşısı', 'IBR/IPV Aşısı', 'BVD Aşısı', 'Pastörella Aşısı'];
      if (predefinedOptions.includes(scheduleData.AsiAdi)) {
        vaccineNameSelect.value = scheduleData.AsiAdi;
      } else {
        vaccineNameSelect.value = 'Diğer';
        vaccineNameOther.style.display = 'block';
        vaccineNameOther.required = true;
        vaccineNameOther.value = scheduleData.AsiAdi;
      }
    }

    // Set today's date as default vaccination date
    const today = new Date().toISOString().split('T')[0];
    vaccineForm.elements['AsilamaTarihi'].value = today;

    // Add a note about the animal and planned date
    const notesField = vaccineForm.elements['Notlar'];
    const plannedDateText = t('planned_date') || 'Planlanan Tarih';
    const animalText = t('animal') || 'Hayvan';
    notesField.value = `${animalText}: ${animal.KupeNo || animal.Isim}\n${plannedDateText}: ${formatDate(scheduleData.PlanlananTarih)}`;

  } catch (error) {
    console.error('Error opening vaccine completion modal:', error);
    window.toast.error(t('toast_error_load') + ': ' + error.message);
  }
}

// --- VACCINATION SCHEDULE PAGE ---
let allVaccinationSchedule = [];
let currentVaccinationSchedule = [];
const itemsPerPageSchedule = 10;
let currentPageSchedule = 1;
let sortColumnSchedule = 'PlanlananTarih';
let sortDirectionSchedule = 1;

async function renderVaccinationSchedulePage(animalId = null) {
  const t = passedI18nHealth.t;

  healthContentEl.innerHTML = `
    <div class="vaccination-schedule-container">
      <!-- Schedule Overview Cards -->
      <div class="schedule-overview">
        <div class="overview-card overdue">
          <div class="card-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
              <line x1="12" y1="9" x2="12" y2="13"/>
              <line x1="12" y1="17" x2="12.01" y2="17"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-value" id="overdue-count">-</div>
            <div class="card-label">${t('overdue_vaccinations')}</div>
          </div>
        </div>

        <div class="overview-card due-today">
          <div class="card-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-value" id="due-today-count">-</div>
            <div class="card-label">${t('due_today')}</div>
          </div>
        </div>

        <div class="overview-card upcoming">
          <div class="card-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-value" id="upcoming-count">-</div>
            <div class="card-label">${t('upcoming_30_days')}</div>
          </div>
        </div>

        <div class="overview-card completed">
          <div class="card-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
              <polyline points="22,4 12,14.01 9,11.01"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-value" id="completed-count">-</div>
            <div class="card-label">${t('completed_this_month')}</div>
          </div>
        </div>
      </div>

      <!-- Schedule Table -->
      <div class="schedule-section">
        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
          <h3 style="margin:0;font-size:1.2rem;">${t('vaccination_schedule')}</h3>
          <div class="schedule-actions">
            <button class="btn btn-secondary" id="generate-schedule-btn">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="width: 16px; height: 16px;">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                <line x1="16" y1="2" x2="16" y2="6"/>
                <line x1="8" y1="2" x2="8" y2="6"/>
                <line x1="3" y1="10" x2="21" y2="10"/>
              </svg>
              ${t('generate_schedule')}
            </button>
            <button class="btn btn-primary" id="view-reminders-btn">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="width: 16px; height: 16px;">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
              </svg>
              ${t('view_reminders')}
            </button>
          </div>
        </div>

        <!-- Quick Animal Filter -->
        <div class="quick-filter-section" style="margin-bottom: 16px;">
          <div class="filter-group">
            <label for="quick-animal-filter">${t('filter_by_animal')}:</label>
            <select id="quick-animal-filter" class="filter-input" style="width: 250px;">
              <option value="">${t('all_animals')}</option>
            </select>
          </div>
        </div>

        <table id="schedule-table" class="modern-table">
          <thead>
            <tr>
              <th data-sort="KupeNo">${t('animal')}</th>
              <th data-sort="AsiAdi">${t('vaccine_name')}</th>
              <th data-sort="PlanlananTarih">${t('planned_date')}</th>
              <th data-sort="Durum">${t('status')}</th>
              <th data-sort="TamamlanmaTarihi">${t('completion_date')}</th>
              <th>${t('actions')}</th>
            </tr>
            <tr class="filter-row">
              <th><input type="text" id="filter-animal" class="filter-input" placeholder="${t('animal')}"></th>
              <th><input type="text" id="filter-vaccine" class="filter-input" placeholder="${t('vaccine_name')}"></th>
              <th><input type="date" id="filter-date" class="filter-input"></th>
              <th>
                <select id="filter-status" class="filter-input">
                  <option value="">${t('option_all')}</option>
                  <option value="Bekliyor">${t('status_pending')}</option>
                  <option value="Tamamlandi">${t('status_completed')}</option>
                  <option value="Gecikti">${t('status_overdue')}</option>
                  <option value="Iptal">${t('status_cancelled')}</option>
                </select>
              </th>
              <th></th>
              <th></th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>

        <div id="schedule-pagination-controls" class="pagination-controls">
          <button id="prev-page-schedule" disabled>&laquo; ${t('btn_previous')}</button>
          <span id="page-info-schedule"></span>
          <button id="next-page-schedule" disabled>&raquo; ${t('btn_next')}</button>
        </div>
      </div>
    </div>
  `;

  // Event listeners
  healthContentEl.querySelector('#generate-schedule-btn').onclick = () => openGenerateScheduleModal();
  healthContentEl.querySelector('#view-reminders-btn').onclick = () => openRemindersModal();

  // Setup table functionality
  setupTableSortingHealth('#schedule-table', ['KupeNo', 'AsiAdi', 'PlanlananTarih', 'Durum', 'TamamlanmaTarihi'], 'schedule');
  setupFilterListenersHealth('schedule');
  setupPaginationListenersHealth('schedule');

  // Tablo stillerini zorla
  forceHealthTableStyles('#schedule-table');

  // Initialize table animations
  setTimeout(() => {
    TableAnimations.initializeTable('#schedule-table');
  }, 100);

  // Load animals for quick filter
  await loadAnimalsForQuickFilter();

  // Setup quick animal filter
  const quickAnimalFilter = healthContentEl.querySelector('#quick-animal-filter');
  if (quickAnimalFilter) {
    quickAnimalFilter.addEventListener('change', () => {
      const selectedAnimalId = quickAnimalFilter.value;
      loadVaccinationSchedule(selectedAnimalId || null);
    });

    // Set initial value if animalId is provided
    if (animalId) {
      quickAnimalFilter.value = animalId;
    }
  }

  // Load and render schedule
  await loadVaccinationSchedule(animalId);
  await loadScheduleOverview();
}

async function loadVaccinationSchedule(animalId = null) {
  try {
    allVaccinationSchedule = await window.api.invoke('asiTakvimi:list', animalId);
    currentVaccinationSchedule = [...allVaccinationSchedule];
    currentPageSchedule = 1;
    updateScheduleTableBody();
    updateSchedulePaginationControls();
  } catch (error) {
    console.error('Error loading vaccination schedule:', error);
    window.toast.error(passedI18nHealth.t('toast_error_load') + ': ' + error.message);
  }
}

async function loadScheduleOverview() {
  try {
    const [upcoming, overdue] = await Promise.all([
      window.api.invoke('asiTakvimi:getUpcoming', 30),
      window.api.invoke('asiTakvimi:getOverdue')
    ]);

    // Calculate counts
    const overdueCount = overdue.length;
    const dueTodayCount = upcoming.filter(item => item.UyariTipi === 'Bugun').length;
    const upcomingCount = upcoming.filter(item => item.UyariTipi === 'Yaklasan').length;

    // Get completed this month
    const thisMonth = new Date();
    const firstDay = new Date(thisMonth.getFullYear(), thisMonth.getMonth(), 1).toISOString().split('T')[0];
    const lastDay = new Date(thisMonth.getFullYear(), thisMonth.getMonth() + 1, 0).toISOString().split('T')[0];

    const completedThisMonth = allVaccinationSchedule.filter(item =>
      item.Durum === 'Tamamlandi' &&
      item.TamamlanmaTarihi >= firstDay &&
      item.TamamlanmaTarihi <= lastDay
    ).length;

    // Update overview cards (with null checks)
    const overdueCountEl = document.getElementById('overdue-count');
    const dueTodayCountEl = document.getElementById('due-today-count');
    const upcomingCountEl = document.getElementById('upcoming-count');
    const completedCountEl = document.getElementById('completed-count');
    
    if (overdueCountEl) overdueCountEl.textContent = overdueCount;
    if (dueTodayCountEl) dueTodayCountEl.textContent = dueTodayCount;
    if (upcomingCountEl) upcomingCountEl.textContent = upcomingCount;
    if (completedCountEl) completedCountEl.textContent = completedThisMonth;

  } catch (error) {
    console.error('Error loading schedule overview:', error);
  }
}

async function loadAnimalsForQuickFilter() {
  try {
    const animals = await window.api.invoke('hayvanlar:list');
    const activeAnimals = animals.filter(animal => animal.AktifMi);

    const quickAnimalFilter = healthContentEl.querySelector('#quick-animal-filter');
    if (quickAnimalFilter) {
      // Clear existing options except the first one
      while (quickAnimalFilter.children.length > 1) {
        quickAnimalFilter.removeChild(quickAnimalFilter.lastChild);
      }

      // Add animal options
      activeAnimals.forEach(animal => {
        const option = document.createElement('option');
        option.value = animal.Id;
        option.textContent = `${animal.KupeNo || `#${animal.Id}`} - ${animal.Isim || ''} (${animal.Tur || ''})`;
        quickAnimalFilter.appendChild(option);
      });
    }
  } catch (error) {
    console.error('Error loading animals for quick filter:', error);
  }
}

// Function to refresh animal lists in all modals
async function refreshAnimalLists() {
  try {
    // Refresh quick filter in schedule page
    if (document.querySelector('#quick-animal-filter')) {
      await loadAnimalsForQuickFilter();
    }

    // Refresh bulk vaccination modal if open
    const bulkModal = document.getElementById('bulk-vaccination-modal');
    if (bulkModal) {
      closeBulkVaccinationModal();
      await openBulkVaccinationModal();
    }

    // Refresh generate schedule modal if open
    const scheduleModal = document.getElementById('generate-schedule-modal');
    if (scheduleModal) {
      closeGenerateScheduleModal();
      await openGenerateScheduleModal();
    }

  } catch (error) {
    console.error('Error refreshing animal lists:', error);
  }
}

// Make this function globally available
window.refreshHealthAnimalLists = refreshAnimalLists;

function updateScheduleTableBody() {
  const t = passedI18nHealth.t;
  const tbody = document.querySelector('#schedule-table tbody');
  if (!tbody) return;

  tbody.innerHTML = '';

  if (currentVaccinationSchedule.length === 0) {
    tbody.innerHTML = `<tr><td colspan="6" class="no-data">${t('no_records_found')}</td></tr>`;
    return;
  }

  const startIndex = (currentPageSchedule - 1) * itemsPerPageSchedule;
  const endIndex = Math.min(startIndex + itemsPerPageSchedule, currentVaccinationSchedule.length);
  const pageSchedule = currentVaccinationSchedule.slice(startIndex, endIndex);

  pageSchedule.forEach(schedule => {
    const row = tbody.insertRow();

    // Animal
    const animalCell = row.insertCell();
    animalCell.innerHTML = `
      <div class="animal-cell">
        <strong>${schedule.KupeNo || `#${schedule.HayvanId}`}</strong>
        ${schedule.HayvanIsim ? `<br><small>${schedule.HayvanIsim}</small>` : ''}
      </div>
    `;

    // Vaccine Name
    row.insertCell().textContent = schedule.AsiAdi || '-';

    // Planned Date
    const plannedDateCell = row.insertCell();
    plannedDateCell.textContent = formatDate(schedule.PlanlananTarih);

    // Add warning indicator for overdue/due soon
    const today = new Date().toISOString().split('T')[0];
    const plannedDate = schedule.PlanlananTarih;

    if (schedule.Durum === 'Bekliyor') {
      if (plannedDate < today) {
        plannedDateCell.classList.add('overdue-date');
        plannedDateCell.innerHTML += ' <span class="warning-indicator overdue">!</span>';
      } else if (plannedDate === today) {
        plannedDateCell.classList.add('due-today-date');
        plannedDateCell.innerHTML += ' <span class="warning-indicator due-today">!</span>';
      }
    }

    // Status
    const statusCell = row.insertCell();
    let statusText = schedule.Durum;
    let statusClass = 'secondary';

    switch (schedule.Durum) {
      case 'Bekliyor':
        statusText = t('status_pending');
        if (plannedDate < today) {
          statusText = t('status_overdue');
          statusClass = 'danger';
        } else if (plannedDate === today) {
          statusClass = 'warning';
        } else {
          statusClass = 'info';
        }
        break;
      case 'Tamamlandi':
        statusText = t('status_completed');
        statusClass = 'success';
        break;
      case 'Iptal':
        statusText = t('status_cancelled');
        statusClass = 'secondary';
        break;
    }

    statusCell.innerHTML = BadgeSystem.createBadge(statusText, statusClass);

    // Completion Date
    row.insertCell().textContent = schedule.TamamlanmaTarihi ? formatDate(schedule.TamamlanmaTarihi) : '-';

    // Actions
    const actionsCell = row.insertCell();
    actionsCell.className = 'actions-cell';

    let actionsHtml = '';
    if (schedule.Durum === 'Bekliyor') {
      actionsHtml = `
        <div class="actions-wrapper">
          ${IconSystem.createActionButton('complete', schedule.Id, t('mark_completed'))}
          ${IconSystem.createActionButton('edit', schedule.Id, t('btn_edit'))}
          ${IconSystem.createActionButton('delete', schedule.Id, t('btn_delete'))}
        </div>
      `;
    } else {
      actionsHtml = `
        <div class="actions-wrapper">
          ${IconSystem.createActionButton('edit', schedule.Id, t('btn_edit'))}
          ${IconSystem.createActionButton('delete', schedule.Id, t('btn_delete'))}
        </div>
      `;
    }

    actionsCell.innerHTML = actionsHtml;

    // Event listeners for actions
    if (schedule.Durum === 'Bekliyor') {
      const completeBtn = row.querySelector('.complete-btn');
      if (completeBtn) {
        completeBtn.onclick = () => markScheduleCompleted(schedule);
      }
    }

    const editBtn = row.querySelector('.edit-btn');
    if (editBtn) {
      editBtn.onclick = () => editScheduleItem(schedule);
    }

    const deleteBtn = row.querySelector('.delete-btn');
    if (deleteBtn) {
      deleteBtn.onclick = async () => {
        const confirmed = await window.toast.confirm(t('confirm_delete_schedule'), {
          confirmText: t('toast_confirm'),
          cancelText: t('toast_cancel')
        });

        if (confirmed) {
          try {
            await window.api.invoke('asiTakvimi:delete', schedule.Id);
            await loadVaccinationSchedule();
            await loadScheduleOverview();
            window.toast.success(t('toast_success_delete'));
          } catch (error) {
            console.error('Error deleting schedule:', error);
            window.toast.error(t('toast_error_delete') + ': ' + error.message);
          }
        }
      };
    }
  });
}

function updateSchedulePaginationControls() {
  const t = passedI18nHealth.t;
  const totalPages = Math.ceil(currentVaccinationSchedule.length / itemsPerPageSchedule);

  const prevBtn = document.getElementById('prev-page-schedule');
  const nextBtn = document.getElementById('next-page-schedule');
  const pageInfo = document.getElementById('page-info-schedule');

  if (!prevBtn || !nextBtn || !pageInfo) return;

  prevBtn.disabled = currentPageSchedule <= 1;
  nextBtn.disabled = currentPageSchedule >= totalPages;

  pageInfo.textContent = `${t('page')} ${currentPageSchedule} / ${totalPages} (${currentVaccinationSchedule.length} ${t('records')})`;
}

async function markScheduleCompleted(schedule) {
  const t = passedI18nHealth.t;

  try {
    // Open vaccination completion modal with pre-filled data from schedule
    await openVaccineCompletionModal(schedule);

  } catch (error) {
    console.error('Error marking schedule as completed:', error);
    window.toast.error(t('toast_error_save') + ': ' + error.message);
  }
}

function editScheduleItem(schedule) {
  const t = passedI18nHealth.t;
  window.toast.info(t('feature_not_implemented_yet') || 'Edit schedule functionality not implemented yet');
}

async function renderActiveHealthTab() {
    const activeTabElement = document.querySelector('#health-tabs .health-tab.active');
    if (activeTabElement) {
        const tabName = activeTabElement.getAttribute('data-tab');
        await loadAndRenderActiveTabData(tabName);
    } else {
         // Default to vaccination schedule if no tab is active (should not happen with current setup)
        await loadAndRenderActiveTabData('asi-takvimi');
    }
}

async function loadAndRenderActiveTabData(tabNameOverride = null) {
  const t = passedI18nHealth.t;
  const selectedAnimalId = animalSelectElHealth.value;
  const activeTab = tabNameOverride || document.querySelector('#health-tabs .health-tab.active').getAttribute('data-tab');

  // Only require animal selection for individual animal records (vaccinations and treatments)
  if ((!selectedAnimalId || selectedAnimalId === "") && (activeTab === 'asilamalar' || activeTab === 'tedaviler')) {
    healthContentEl.innerHTML = `<div class='empty-message'><svg width='24' height='24' fill='none' viewBox='0 0 24 24'><circle cx='12' cy='12' r='12' fill='#e0e7ef'/><ellipse cx='12' cy='11' rx='5' ry='4' fill='#fff'/><ellipse cx='9' cy='10' rx='1.5' ry='1' fill='#b6c3d1' opacity='0.7'/><ellipse cx='15' cy='10' rx='1.5' ry='1' fill='#b6c3d1' opacity='0.7'/><ellipse cx='10.5' cy='12.5' rx='1' ry='1.2' fill='#222c37'/><ellipse cx='13.5' cy='12.5' rx='1' ry='1.2' fill='#222c37'/><ellipse cx='12' cy='15' rx='2' ry='0.8' fill='#b6c3d1'/></svg><br>${t('please_select_animal')}</div>`;
    return;
  }

  if (activeTab === 'asi-takvimi') {
    // Vaccination schedule can show all animals or filtered by selected animal
    renderVaccinationSchedulePage(selectedAnimalId);
  } else if (activeTab === 'asi-yonetimi') {
    // Vaccination management doesn't require animal selection
    renderVaccinationManagementPage();
  } else if (activeTab === 'asilamalar') {
    allVaccinesForAnimal = await window.api.invoke('asilamalar:list', selectedAnimalId);
    // First render the full table structure, then apply filters
    renderVaccinesTablePage();
  } else if (activeTab === 'tedaviler') {
    allTreatmentsForAnimal = await window.api.invoke('tedaviler:list', selectedAnimalId);
    // First render the full table structure, then apply filters
    renderTreatmentsTablePage();
  }
}

// --- VACCINES TAB SPECIFIC FUNCTIONS ---
function applyFiltersAndSortVaccines() {
  const t = passedI18nHealth.t;
  currentVaccinesForAnimal = [...allVaccinesForAnimal]; // Start with all data for the animal

  const nameFilter = healthContentEl.querySelector('#filter-vaccine-name')?.value.trim().toLowerCase();
  const dateFilter = healthContentEl.querySelector('#filter-vaccine-date')?.value;
  const vetFilter = healthContentEl.querySelector('#filter-vaccine-vet')?.value.trim().toLowerCase();

  if (nameFilter) currentVaccinesForAnimal = currentVaccinesForAnimal.filter(v => (v.AsiAdi || '').toLowerCase().includes(nameFilter));
  if (dateFilter) currentVaccinesForAnimal = currentVaccinesForAnimal.filter(v => v.AsilamaTarihi === dateFilter);
  if (vetFilter) currentVaccinesForAnimal = currentVaccinesForAnimal.filter(v => (v.UygulayanVeteriner || '').toLowerCase().includes(vetFilter));

  if (sortColumnVaccines) {
    currentVaccinesForAnimal = sortHealthList(currentVaccinesForAnimal, sortColumnVaccines, sortDirectionVaccines, 'vaccine');
  }
  currentPageVaccines = 1;

  // Only update table body, not the entire table structure
  updateVaccinesTableBody();
  updateVaccinesPaginationControls();
}

function renderVaccinesTablePage() {
  const t = passedI18nHealth.t;
  // Generate HTML for vaccine table, filter row, and pagination controls
  // This structure will be injected into healthContentEl
  healthContentEl.innerHTML = `
    <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
      <h3 style="margin:0;font-size:1.2rem;">${t('health_vaccinations')}</h3>
      <button class="btn btn-primary" id="add-vaccine-btn"><svg viewBox='0 0 20 20' fill='none'><circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/><path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/></svg> ${t('btn_add_vaccine')}</button>
    </div>
    <table id="vaccine-table" class="modern-table">
      <thead>
        <tr>
          <th data-sort="AsiAdi" class="sortable">${t('label_vaccine_name')}</th>
          <th data-sort="AsilamaTarihi" class="col-date sortable">${t('label_date')}</th>
          <th data-sort="Doz" class="sortable">${t('label_dose')}</th>
          <th data-sort="UygulayanVeteriner" class="sortable">${t('label_vet')}</th>
          <th data-sort="Notlar" class="sortable">${t('label_notlar')}</th>
          <th class="col-actions">${t('label_actions')}</th>
        </tr>
        <tr class="filter-row">
          <th><input type="text" id="filter-vaccine-name" class="filter-input" placeholder="${t('label_vaccine_name')}"></th>
          <th><input type="date" id="filter-vaccine-date" class="filter-input"></th>
          <th></th> <!-- Dose filter not added for simplicity -->
          <th><input type="text" id="filter-vaccine-vet" class="filter-input" placeholder="${t('label_vet')}"></th>
          <th></th> <!-- Notes filter not added -->
          <th></th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>
    <div id="vaccines-pagination-controls" class="pagination-controls">
      <button id="prev-page-vaccines" disabled>&laquo; ${t('btn_previous')}</button>
      <span id="page-info-vaccines"></span>
      <button id="next-page-vaccines" disabled>&raquo; ${t('btn_next')}</button>
    </div>
  `;

  healthContentEl.querySelector('#add-vaccine-btn').onclick = () => openVaccineModal();
  setupTableSortingHealth('#vaccine-table', ['AsiAdi', 'AsilamaTarihi', 'Doz', 'UygulayanVeteriner', 'Notlar'], 'vaccine');
  setupFilterListenersHealth('vaccine');
  setupPaginationListenersHealth('vaccine');

  // Initialize table animations
  setTimeout(() => {
    TableAnimations.initializeTable('#vaccine-table');
  }, 100);

  // Reset filters and prepare data for display
  currentVaccinesForAnimal = [...allVaccinesForAnimal];
  currentPageVaccines = 1;

  // Update table body and pagination
  updateVaccinesTableBody();
  updateVaccinesPaginationControls();
}

function updateVaccinesTableBody() {
  const t = passedI18nHealth.t;
  const tableBody = healthContentEl.querySelector('#vaccine-table tbody');
  if (!tableBody) return;

  const totalPages = Math.ceil(currentVaccinesForAnimal.length / itemsPerPageVaccines);
  currentPageVaccines = Math.max(1, Math.min(currentPageVaccines, totalPages || 1));
  const startIndex = (currentPageVaccines - 1) * itemsPerPageVaccines;
  const endIndex = startIndex + itemsPerPageVaccines;
  const pageItems = currentVaccinesForAnimal.slice(startIndex, endIndex);

  // Clear existing rows
  tableBody.innerHTML = '';

  if (pageItems.length === 0) {
    tableBody.innerHTML = `<tr><td colspan="6"><div class="empty-state-container"><div class="empty-state-icon">${passedDefaultAvatarSVGHealth(48)}</div><p>${t('no_records_found')}</p></div></td></tr>`;
  } else {
    pageItems.forEach(v => {
      const row = tableBody.insertRow();
      row.innerHTML = `
        <td title="${v.AsiAdi || ''}">${v.AsiAdi || ''}</td>
        <td class="col-date">${v.AsilamaTarihi || ''}</td>
        <td title="${v.Doz || ''}">${v.Doz || ''}</td>
        <td title="${v.UygulayanVeteriner || ''}">${v.UygulayanVeteriner || ''}</td>
        <td title="${v.Notlar || ''}">${v.Notlar || ''}</td>
        <td class="col-actions">
          <div class="actions-wrapper">
            ${IconSystem.createActionButton('edit', v.Id, t('btn_edit'))}
            ${IconSystem.createActionButton('delete', v.Id, t('btn_delete'))}
          </div>
        </td>`;
      row.querySelector('.edit-btn').onclick = () => openVaccineModal(v);
      row.querySelector('.delete-btn').onclick = async () => {
        const confirmed = await window.toast.confirm(t('confirm_delete_vaccine'), {
          confirmText: t('toast_confirm'),
          cancelText: t('toast_cancel')
        });

        if (confirmed) {
          try {
            await window.api.invoke('asilamalar:delete', v.Id);
            await loadAndRenderActiveTabData();
            window.toast.success(t('toast_success_delete'));
          } catch (error) {
            console.error('Error deleting vaccine:', error);
            window.toast.error(t('toast_error_delete') + ': ' + error.message);
          }
        }
      };
    });
  }
}

function updateVaccinesPaginationControls() {
  const t = passedI18nHealth.t;
  const pageInfoEl = healthContentEl.querySelector('#page-info-vaccines');
  const prevBtn = healthContentEl.querySelector('#prev-page-vaccines');
  const nextBtn = healthContentEl.querySelector('#next-page-vaccines');

  const totalItems = currentVaccinesForAnimal.length;
  const totalPages = Math.ceil(totalItems / itemsPerPageVaccines) || 1;
  if (pageInfoEl) pageInfoEl.textContent = t('page_info_text', { currentPage: currentPageVaccines, totalPages, totalItems });
  if (prevBtn) prevBtn.disabled = currentPageVaccines === 1;
  if (nextBtn) nextBtn.disabled = currentPageVaccines === totalPages;
}

// --- TREATMENTS TAB SPECIFIC FUNCTIONS ---
function applyFiltersAndSortTreatments() {
  const t = passedI18nHealth.t;
  currentTreatmentsForAnimal = [...allTreatmentsForAnimal];

  const diagnosisFilter = healthContentEl.querySelector('#filter-treatment-diagnosis')?.value.trim().toLowerCase();
  const startDateFilter = healthContentEl.querySelector('#filter-treatment-start-date')?.value;
  const endDateFilter = healthContentEl.querySelector('#filter-treatment-end-date')?.value; // Added this line
  const medicationsFilter = healthContentEl.querySelector('#filter-treatment-medications')?.value.trim().toLowerCase();
  const quarantineFilter = healthContentEl.querySelector('#filter-treatment-quarantine')?.value;


  if (diagnosisFilter) currentTreatmentsForAnimal = currentTreatmentsForAnimal.filter(tr => (tr.Teshis || '').toLowerCase().includes(diagnosisFilter));
  if (startDateFilter) currentTreatmentsForAnimal = currentTreatmentsForAnimal.filter(tr => tr.TedaviBaslangicTarihi >= startDateFilter); // Use >= for start date
  if (endDateFilter) currentTreatmentsForAnimal = currentTreatmentsForAnimal.filter(tr => tr.TedaviBitisTarihi && tr.TedaviBitisTarihi <= endDateFilter); // Use <= for end date, ensure BitisTarihi exists
  if (medicationsFilter) currentTreatmentsForAnimal = currentTreatmentsForAnimal.filter(tr => (tr.KullanilanIlaclar || '').toLowerCase().includes(medicationsFilter));
  if (quarantineFilter !== "" && quarantineFilter !== undefined) currentTreatmentsForAnimal = currentTreatmentsForAnimal.filter(tr => tr.KarantinaDurumu == (quarantineFilter === '1'));


  if (sortColumnTreatments) {
    currentTreatmentsForAnimal = sortHealthList(currentTreatmentsForAnimal, sortColumnTreatments, sortDirectionTreatments, 'treatment');
  }
  currentPageTreatments = 1;

  // Only update table body, not the entire table structure
  updateTreatmentsTableBody();
  updateTreatmentsPaginationControls();
}

function renderTreatmentsTablePage() {
  const t = passedI18nHealth.t;
  healthContentEl.innerHTML = `
    <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
      <h3 style="margin:0;font-size:1.2rem;">${t('health_treatments')}</h3>
      <button class="btn btn-primary" id="add-treatment-btn"><svg viewBox='0 0 20 20' fill='none'><circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/><path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/></svg> ${t('btn_add_treatment')}</button>
    </div>
    <table id="treatment-table" class="modern-table">
      <thead>
        <tr>
          <th data-sort="Teshis" class="sortable">${t('label_teshis')}</th>
          <th data-sort="TedaviBaslangicTarihi" class="col-date sortable">${t('label_tedavi_baslangic')}</th>
          <th data-sort="TedaviBitisTarihi" class="col-date sortable">${t('label_tedavi_bitis')}</th>
          <th data-sort="KullanilanIlaclar" class="sortable">${t('label_kullanilan_ilaclar')}</th>
          <th data-sort="KarantinaDurumu" class="sortable">${t('label_karantina_durumu')}</th>
          <th data-sort="Notlar" class="sortable">${t('label_notlar')}</th>
          <th class="col-actions">${t('label_actions')}</th>
        </tr>
        <tr class="filter-row">
          <th><input type="text" id="filter-treatment-diagnosis" class="filter-input" placeholder="${t('label_teshis')}"></th>
          <th><input type="date" id="filter-treatment-start-date" class="filter-input" placeholder="${t('label_start_date_short')}"></th>
          <th><input type="date" id="filter-treatment-end-date" class="filter-input" placeholder="${t('label_end_date_short')}"></th>
          <th><input type="text" id="filter-treatment-medications" class="filter-input" placeholder="${t('label_kullanilan_ilaclar')}"></th>
          <th>
            <select id="filter-treatment-quarantine" class="filter-input">
              <option value="">${t('option_all')}</option>
              <option value="1">${t('option_yes')}</option>
              <option value="0">${t('option_no')}</option>
            </select>
          </th>
          <th></th> <!-- Notes filter -->
          <th></th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>
    <div id="treatments-pagination-controls" class="pagination-controls">
      <button id="prev-page-treatments" disabled>&laquo; ${t('btn_previous')}</button>
      <span id="page-info-treatments"></span>
      <button id="next-page-treatments" disabled>&raquo; ${t('btn_next')}</button>
    </div>
  `;

  healthContentEl.querySelector('#add-treatment-btn').onclick = () => openTreatmentModal();
  setupTableSortingHealth('#treatment-table', ['Teshis', 'TedaviBaslangicTarihi', 'TedaviBitisTarihi', 'KullanilanIlaclar', 'KarantinaDurumu', 'Notlar'], 'treatment');
  setupFilterListenersHealth('treatment');
  setupPaginationListenersHealth('treatment');

  // Force header styles for health tables
  forceHealthTableStyles('#treatment-table');

  // Initialize table animations
  setTimeout(() => {
    TableAnimations.initializeTable('#treatment-table');
  }, 100);

  // Reset filters and prepare data for display
  currentTreatmentsForAnimal = [...allTreatmentsForAnimal];
  currentPageTreatments = 1;

  // Update table body and pagination
  updateTreatmentsTableBody();
  updateTreatmentsPaginationControls();
}

function updateTreatmentsTableBody() {
  const t = passedI18nHealth.t;
  const tableBody = healthContentEl.querySelector('#treatment-table tbody');
  if (!tableBody) return;

  const totalPages = Math.ceil(currentTreatmentsForAnimal.length / itemsPerPageTreatments);
  currentPageTreatments = Math.max(1, Math.min(currentPageTreatments, totalPages || 1));
  const startIndex = (currentPageTreatments - 1) * itemsPerPageTreatments;
  const endIndex = startIndex + itemsPerPageTreatments;
  const pageItems = currentTreatmentsForAnimal.slice(startIndex, endIndex);

  // Clear existing rows
  tableBody.innerHTML = '';

  if (pageItems.length === 0) {
    tableBody.innerHTML = `<tr><td colspan="7"><div class="empty-state-container"><div class="empty-state-icon">${passedDefaultAvatarSVGHealth(48)}</div><p>${t('no_records_found')}</p></div></td></tr>`;
  } else {
    pageItems.forEach(tr => {
      const row = tableBody.insertRow();
      row.innerHTML = `
        <td title="${tr.Teshis || ''}">${tr.Teshis || ''}</td>
        <td class="col-date">${tr.TedaviBaslangicTarihi || ''}</td>
        <td class="col-date">${tr.TedaviBitisTarihi || ''}</td>
        <td title="${tr.KullanilanIlaclar || ''}">${tr.KullanilanIlaclar || ''}</td>
        <td title="${tr.KarantinaDurumu == 1 ? t('option_yes') : t('option_no')}">${BadgeSystem.createActiveStatusBadge(tr.KarantinaDurumu == 1, { active: t('option_yes'), inactive: t('option_no') })}</td>
        <td title="${tr.Notlar || ''}">${tr.Notlar || ''}</td>
        <td class="col-actions">
          ${IconSystem.createActionsWrapper(tr.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
        </td>`;
      row.querySelector('.edit-btn').onclick = () => openTreatmentModal(tr);
      row.querySelector('.delete-btn').onclick = async () => {
        const confirmed = await window.toast.confirm(t('confirm_delete_treatment'), {
          confirmText: t('toast_confirm'),
          cancelText: t('toast_cancel')
        });

        if (confirmed) {
          try {
            await window.api.invoke('tedaviler:delete', tr.Id);
            await loadAndRenderActiveTabData();
            window.toast.success(t('toast_success_delete'));
          } catch (error) {
            console.error('Error deleting treatment:', error);
            window.toast.error(t('toast_error_delete') + ': ' + error.message);
          }
        }
      };
    });
  }
}

function updateTreatmentsPaginationControls() {
  const t = passedI18nHealth.t;
  const pageInfoEl = healthContentEl.querySelector('#page-info-treatments');
  const prevBtn = healthContentEl.querySelector('#prev-page-treatments');
  const nextBtn = healthContentEl.querySelector('#next-page-treatments');

  const totalItems = currentTreatmentsForAnimal.length;
  const totalPages = Math.ceil(totalItems / itemsPerPageTreatments) || 1;
  if (pageInfoEl) pageInfoEl.textContent = t('page_info_text', { currentPage: currentPageTreatments, totalPages, totalItems });
  if (prevBtn) prevBtn.disabled = currentPageTreatments === 1;
  if (nextBtn) nextBtn.disabled = currentPageTreatments === totalPages;
}

// --- COMMON HELPER FOR SORTING & EVENT LISTENERS ---
function sortHealthList(list, column, direction, type) { // type is 'vaccine' or 'treatment'
  return [...list].sort((a, b) => {
    let valA = a[column];
    let valB = b[column];
    const dateColumnsVaccine = ['AsilamaTarihi'];
    const dateColumnsTreatment = ['TedaviBaslangicTarihi', 'TedaviBitisTarihi'];
    const dateColumns = type === 'vaccine' ? dateColumnsVaccine : dateColumnsTreatment;

    if (dateColumns.includes(column)) {
      valA = new Date(valA); valB = new Date(valB);
    } else if (typeof valA === 'boolean' && typeof valB === 'boolean') {
      // no change
    } else if (!isNaN(parseFloat(valA)) && !isNaN(parseFloat(valB)) && column !== 'Id') { // Avoid treating ID as float if it's like "VET-001"
         valA = parseFloat(valA); valB = parseFloat(valB);
    } else {
      valA = (valA || '').toString().toLowerCase();
      valB = (valB || '').toString().toLowerCase();
    }
    if (valA < valB) return -1 * direction;
    if (valA > valB) return 1 * direction;
    return 0;
  });
}

// Force health table header styles with JavaScript
function forceHealthTableStyles(tableSelector) {
  setTimeout(() => {
    const table = document.querySelector(tableSelector);
    if (table) {
      const headers = table.querySelectorAll('thead th');
      headers.forEach(th => {
        th.style.padding = '8px 16px';
        th.style.height = '36px';
        th.style.lineHeight = '1.2';
        th.style.boxSizing = 'border-box';
        th.style.verticalAlign = 'middle';

        // First and last column adjustments
        if (th === headers[0]) {
          th.style.paddingLeft = '24px';
        }
        if (th === headers[headers.length - 1]) {
          th.style.paddingRight = '24px';
        }

        // Sortable column adjustments
        if (th.classList.contains('sortable')) {
          th.style.paddingRight = '32px';
        }
      });

      // Also fix filter row if exists
      const filterHeaders = table.querySelectorAll('.filter-row th');
      filterHeaders.forEach(th => {
        th.style.padding = '8px 16px';
        th.style.height = '40px';

        if (th === filterHeaders[0]) {
          th.style.paddingLeft = '24px';
        }
        if (th === filterHeaders[filterHeaders.length - 1]) {
          th.style.paddingRight = '24px';
        }

        // Fix input/select heights in filter row
        const inputs = th.querySelectorAll('input, select');
        inputs.forEach(input => {
          input.style.height = '28px';
          input.style.padding = '4px 8px';
          input.style.boxSizing = 'border-box';
        });
      });
    }
  }, 100);
}

function setupTableSortingHealth(tableSelector, columnKeys, type) {
  const ths = healthContentEl.querySelectorAll(`${tableSelector} thead th[data-sort]`);

  ths.forEach(th => {
    const columnKey = th.dataset.sort || th.dataset.columnKey;
    if (!columnKey) return;

    th.classList.add('sortable'); // Ensure sortable class for styling

    let currentSortCol, currentSortDir;
    if (type === 'vaccine') {
      currentSortCol = sortColumnVaccines;
      currentSortDir = sortDirectionVaccines;
    } else if (type === 'treatment') {
      currentSortCol = sortColumnTreatments;
      currentSortDir = sortDirectionTreatments;
    } else if (type === 'template') {
      currentSortCol = sortColumnTemplates;
      currentSortDir = sortDirectionTemplates;
    } else if (type === 'schedule') {
      currentSortCol = sortColumnSchedule;
      currentSortDir = sortDirectionSchedule;
    }

    // Set initial sorting state
    if (columnKey === currentSortCol) {
        th.classList.add(currentSortDir === 1 ? 'sorted-asc' : 'sorted-desc');
    }

    th.addEventListener('click', () => {
      // Clear all sorting classes first
      ths.forEach(headerTh => {
        headerTh.classList.remove('sorted-asc', 'sorted-desc');
      });

      if (type === 'vaccine') {
        if (sortColumnVaccines === columnKey) sortDirectionVaccines *= -1;
        else { sortColumnVaccines = columnKey; sortDirectionVaccines = 1; }
        th.classList.add(sortDirectionVaccines === 1 ? 'sorted-asc' : 'sorted-desc');
        applyFiltersAndSortVaccines();
      } else if (type === 'treatment') {
        if (sortColumnTreatments === columnKey) sortDirectionTreatments *= -1;
        else { sortColumnTreatments = columnKey; sortDirectionTreatments = 1; }
        th.classList.add(sortDirectionTreatments === 1 ? 'sorted-asc' : 'sorted-desc');
        applyFiltersAndSortTreatments();
      } else if (type === 'template') {
        if (sortColumnTemplates === columnKey) sortDirectionTemplates *= -1;
        else { sortColumnTemplates = columnKey; sortDirectionTemplates = 1; }
        th.classList.add(sortDirectionTemplates === 1 ? 'sorted-asc' : 'sorted-desc');
        applyFiltersAndSortTemplates();
      } else if (type === 'schedule') {
        if (sortColumnSchedule === columnKey) sortDirectionSchedule *= -1;
        else { sortColumnSchedule = columnKey; sortDirectionSchedule = 1; }
        th.classList.add(sortDirectionSchedule === 1 ? 'sorted-asc' : 'sorted-desc');
        applyFiltersAndSortSchedule();
      }
    });
  });
}

function setupFilterListenersHealth(type) {
    const t = passedI18nHealth.t;
    let filterSelectors = [];
    let applyFunc;

    if (type === 'vaccine') {
        filterSelectors = ['#filter-vaccine-name', '#filter-vaccine-date', '#filter-vaccine-vet'];
        applyFunc = applyFiltersAndSortVaccines;
    } else if (type === 'treatment') {
        filterSelectors = ['#filter-treatment-diagnosis', '#filter-treatment-start-date', '#filter-treatment-end-date', '#filter-treatment-medications', '#filter-treatment-quarantine'];
        applyFunc = applyFiltersAndSortTreatments;
    } else if (type === 'template') {
        filterSelectors = ['#filter-template-name', '#filter-animal-type', '#filter-vaccine-name', '#filter-template-status'];
        applyFunc = applyFiltersAndSortTemplates;
    } else if (type === 'schedule') {
        filterSelectors = ['#filter-animal', '#filter-vaccine', '#filter-date', '#filter-status'];
        applyFunc = applyFiltersAndSortSchedule;
    }

    filterSelectors.forEach(selector => {
        const inputElement = healthContentEl.querySelector(selector);
        if (inputElement) {
            // Remove any existing listeners first
            inputElement.removeEventListener('input', applyFunc);
            inputElement.removeEventListener('change', applyFunc);

            // Add new listeners using addEventListener
            inputElement.addEventListener('input', applyFunc);
            if (inputElement.tagName === 'SELECT' || inputElement.type === 'date') {
                inputElement.addEventListener('change', applyFunc);
            }
        }
    });
}

// Filter functions for new table types
function applyFiltersAndSortTemplates() {
  const nameFilter = document.getElementById('filter-template-name')?.value.toLowerCase() || '';
  const typeFilter = document.getElementById('filter-animal-type')?.value.toLowerCase() || '';
  const vaccineFilter = document.getElementById('filter-vaccine-name')?.value.toLowerCase() || '';
  const statusFilter = document.getElementById('filter-template-status')?.value || '';

  currentVaccinationTemplates = allVaccinationTemplates.filter(template => {
    const nameMatch = !nameFilter || (template.SablonAdi || '').toLowerCase().includes(nameFilter);
    const typeMatch = !typeFilter || (template.HayvanTuru || '').toLowerCase().includes(typeFilter);
    const vaccineMatch = !vaccineFilter || (template.AsiAdi || '').toLowerCase().includes(vaccineFilter);
    const statusMatch = !statusFilter || template.Aktif.toString() === statusFilter;

    return nameMatch && typeMatch && vaccineMatch && statusMatch;
  });

  // Apply sorting
  if (sortColumnTemplates && sortDirectionTemplates !== 0) {
    currentVaccinationTemplates.sort((a, b) => {
      let aVal = a[sortColumnTemplates] || '';
      let bVal = b[sortColumnTemplates] || '';

      if (typeof aVal === 'string') aVal = aVal.toLowerCase();
      if (typeof bVal === 'string') bVal = bVal.toLowerCase();

      if (aVal < bVal) return -1 * sortDirectionTemplates;
      if (aVal > bVal) return 1 * sortDirectionTemplates;
      return 0;
    });
  }

  currentPageTemplates = 1;
  updateTemplatesTableBody();
  updateTemplatesPaginationControls();
}

function applyFiltersAndSortSchedule() {
  const animalFilter = document.getElementById('filter-animal')?.value.toLowerCase() || '';
  const vaccineFilter = document.getElementById('filter-vaccine')?.value.toLowerCase() || '';
  const dateFilter = document.getElementById('filter-date')?.value || '';
  const statusFilter = document.getElementById('filter-status')?.value || '';

  currentVaccinationSchedule = allVaccinationSchedule.filter(schedule => {
    const animalMatch = !animalFilter ||
      (schedule.KupeNo || '').toLowerCase().includes(animalFilter) ||
      (schedule.HayvanIsim || '').toLowerCase().includes(animalFilter);
    const vaccineMatch = !vaccineFilter || (schedule.AsiAdi || '').toLowerCase().includes(vaccineFilter);
    const dateMatch = !dateFilter || schedule.PlanlananTarih === dateFilter;
    const statusMatch = !statusFilter || schedule.Durum === statusFilter;

    return animalMatch && vaccineMatch && dateMatch && statusMatch;
  });

  // Apply sorting
  if (sortColumnSchedule && sortDirectionSchedule !== 0) {
    currentVaccinationSchedule.sort((a, b) => {
      let aVal = a[sortColumnSchedule] || '';
      let bVal = b[sortColumnSchedule] || '';

      if (typeof aVal === 'string') aVal = aVal.toLowerCase();
      if (typeof bVal === 'string') bVal = bVal.toLowerCase();

      if (aVal < bVal) return -1 * sortDirectionSchedule;
      if (aVal > bVal) return 1 * sortDirectionSchedule;
      return 0;
    });
  }

  currentPageSchedule = 1;
  updateScheduleTableBody();
  updateSchedulePaginationControls();
}

function setupPaginationListenersHealth(type) {
    let prevBtn, nextBtn, renderFunc, updateControlsFunc, currentPageVar, setPageVar, itemsPerPageVar, currentDataArray;
    if (type === 'vaccine') {
        prevBtn = healthContentEl.querySelector('#prev-page-vaccines');
        nextBtn = healthContentEl.querySelector('#next-page-vaccines');
        renderFunc = renderVaccinesTablePage;
        updateControlsFunc = updateVaccinesPaginationControls;
        currentPageVar = () => currentPageVaccines;
        setPageVar = (val) => currentPageVaccines = val;
        itemsPerPageVar = itemsPerPageVaccines;
        currentDataArray = () => currentVaccinesForAnimal;

    } else if (type === 'treatment') {
        prevBtn = healthContentEl.querySelector('#prev-page-treatments');
        nextBtn = healthContentEl.querySelector('#next-page-treatments');
        renderFunc = renderTreatmentsTablePage;
        updateControlsFunc = updateTreatmentsPaginationControls;
        currentPageVar = () => currentPageTreatments;
        setPageVar = (val) => currentPageTreatments = val;
        itemsPerPageVar = itemsPerPageTreatments;
        currentDataArray = () => currentTreatmentsForAnimal;
    } else if (type === 'template') {
        prevBtn = healthContentEl.querySelector('#prev-page-templates');
        nextBtn = healthContentEl.querySelector('#next-page-templates');
        renderFunc = updateTemplatesTableBody;
        updateControlsFunc = updateTemplatesPaginationControls;
        currentPageVar = () => currentPageTemplates;
        setPageVar = (val) => currentPageTemplates = val;
        itemsPerPageVar = itemsPerPageTemplates;
        currentDataArray = () => currentVaccinationTemplates;
    } else if (type === 'schedule') {
        prevBtn = healthContentEl.querySelector('#prev-page-schedule');
        nextBtn = healthContentEl.querySelector('#next-page-schedule');
        renderFunc = updateScheduleTableBody;
        updateControlsFunc = updateSchedulePaginationControls;
        currentPageVar = () => currentPageSchedule;
        setPageVar = (val) => currentPageSchedule = val;
        itemsPerPageVar = itemsPerPageSchedule;
        currentDataArray = () => currentVaccinationSchedule;
    }

    if (prevBtn) prevBtn.onclick = () => {
        if (currentPageVar() > 1) {
            setPageVar(currentPageVar() - 1);
            renderFunc();
            updateControlsFunc();
        }
    };
    if (nextBtn) nextBtn.onclick = () => {
        const totalPages = Math.ceil(currentDataArray().length / itemsPerPageVar);
        if (currentPageVar() < totalPages) {
            setPageVar(currentPageVar() + 1);
            renderFunc();
            updateControlsFunc();
        }
    };
}

const { ipcMain } = require('electron');
const { getDb } = require('../services/database.js');
const accountingService = require('../services/accounting-service.js');

// Helper function to get current profile
function getCurrentProfile() {
  try {
    const authHandlers = require('./auth-handlers.js');
    return authHandlers.getCurrentProfile();
  } catch (error) {
    console.error('Error getting current profile:', error);
    return null;
  }
}

ipcMain.handle('dashboard:stats', async () => {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  const db = getDb();

  const runQuery = (sql, params = []) => {
    return new Promise((resolve, reject) => {
      db.get(sql, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  };

  try {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);
    const thirtyDaysFromNowStr = thirtyDaysFromNow.toISOString().split('T')[0];

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);
    const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0];

    // Ortalama günlük süt verimi ve aktif sağlık uyarıları için ek sorgular
    const averageDailyMilkQuery = `
      SELECT AVG(GunlukMiktar) as average FROM (
        SELECT SUM(sv.Miktar) as GunlukMiktar
        FROM SagimVerileri sv
        JOIN Laktasyonlar l ON sv.LaktasyonId = l.Id
        JOIN Hayvanlar h ON l.HayvanId = h.Id
        WHERE sv.Tarih >= date('now', '-30 days') AND h.ProfilId = ?
        GROUP BY l.HayvanId, sv.Tarih
      )
    `;
    // Aktif sağlık uyarıları (devam eden tedaviler + yaklaşan/geciken aşılar)
    const activeHealthAlertsQuery = `
      SELECT
        (SELECT COUNT(*) FROM Tedaviler t
         JOIN Hayvanlar h ON t.HayvanId = h.Id
         WHERE (t.TedaviBitisTarihi IS NULL OR t.TedaviBitisTarihi >= ?)
         AND h.AktifMi = 1 AND h.ProfilId = ?) +
        (SELECT COUNT(*) FROM AsiTakvimi at
         JOIN Hayvanlar h ON at.HayvanId = h.Id
         WHERE at.ProfilId = ? AND at.Durum = 'Bekliyor'
         AND at.PlanlananTarih <= date(?, '+30 days')
         AND h.AktifMi = 1) as count
    `;

    // Son 6 aylık süt üretimi için sorgu
    // Bu sorgu biraz daha karmaşık olacak ve her ay için toplam süt miktarını alacak.
    // SQLite'da dinamik olarak son 6 ayı almak için RECURSIVE CTE kullanabiliriz veya JS tarafında döngü ile ay ay sorgulayabiliriz.
    // Şimdilik JS tarafında manuel olarak son 6 ayın etiketlerini oluşturup, her biri için sorgu yapacağız.
    // Daha performanslı bir çözüm için tek bir SQL sorgusu daha iyi olabilir.
    // last6MonthsMilk verisini de Promise.all içine taşıyalım.
    const getLast6MonthsMilk = async () => {
      const milkData = { labels: [], data: [] };
      for (let i = 5; i >= 0; i--) {
        const d = new Date();
        d.setMonth(d.getMonth() - i);
        const year = d.getFullYear();
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const monthLabel = `${year}-${month}`;
        milkData.labels.push(monthLabel);
        const monthResult = await runQuery(`
          SELECT SUM(sv.Miktar) as total
          FROM SagimVerileri sv
          JOIN Laktasyonlar l ON sv.LaktasyonId = l.Id
          JOIN Hayvanlar h ON l.HayvanId = h.Id
          WHERE strftime('%Y-%m', sv.Tarih) = ? AND h.ProfilId = ?
        `, [monthLabel, currentProfile.id]);
        milkData.data.push(monthResult.total || 0);
      }
      return milkData;
    };

    const statsData = await Promise.all([
      runQuery('SELECT COUNT(*) as count FROM Hayvanlar WHERE AktifMi = 1 AND ProfilId = ?', [currentProfile.id]), // 0
      runQuery("SELECT COUNT(*) as count FROM Hayvanlar WHERE AktifMi = 1 AND Cinsiyet = 'Erkek' AND ProfilId = ?", [currentProfile.id]), // 1
      runQuery("SELECT COUNT(*) as count FROM Hayvanlar WHERE AktifMi = 1 AND Cinsiyet = 'Dişi' AND ProfilId = ?", [currentProfile.id]), // 2
      runQuery("SELECT COUNT(*) as count FROM Gebelikler g JOIN Hayvanlar h ON g.HayvanId = h.Id WHERE (g.GebelikSonucu IS NULL OR g.GebelikSonucu = '' OR g.GebelikSonucu = 'Devam Ediyor') AND h.AktifMi = 1 AND h.ProfilId = ?", [currentProfile.id]), // 3
      runQuery("SELECT COUNT(*) as count FROM Gebelikler g JOIN Hayvanlar h ON g.HayvanId = h.Id WHERE (g.GebelikSonucu IS NULL OR g.GebelikSonucu = '' OR g.GebelikSonucu = 'Devam Ediyor') AND g.BeklenenDogumTarihi BETWEEN ? AND ? AND h.AktifMi = 1 AND h.ProfilId = ?", [todayStr, thirtyDaysFromNowStr, currentProfile.id]), // 4
      runQuery("SELECT COUNT(DISTINCT l.HayvanId) as count FROM Laktasyonlar l JOIN Hayvanlar h ON h.Id = l.HayvanId WHERE h.AktifMi = 1 AND h.ProfilId = ? AND l.BaslangicTarihi <= ? AND (l.BitisTarihi IS NULL OR l.BitisTarihi >= ?)", [currentProfile.id, todayStr, todayStr]), // 5
      runQuery(averageDailyMilkQuery, [currentProfile.id]), // 6
      runQuery(activeHealthAlertsQuery, [todayStr, currentProfile.id, currentProfile.id, todayStr]), // 7
      getLast6MonthsMilk(), // 8
      accountingService.getAccountsNetBalanceDashboard(), // 9 - New function call
      accountingService.getMonthlyNetProfitLossTrend(12) // 10 (index shifted)
    ]);

    // thirtyDaysAgoStr ve todayStr artık borçlu/alacaklı cariler için kullanılmıyor.
    // Eğer başka bir yerde de kullanılmıyorsa (örneğin getLast6MonthsMilk farklı bir mantıkla çalışıyorsa)
    // bu değişkenler gereksiz olabilir. Şimdilik kalsınlar, getLast6MonthsMilk kendi tarih mantığına sahip.

    return {
      totalAnimals: statsData[0].count,
      maleAnimals: statsData[1].count,
      femaleAnimals: statsData[2].count,
      pregnantAnimals: statsData[3].count,
      upcomingBirths: statsData[4].count,
      milkingAnimals: statsData[5].count,
      averageDailyMilkYield: statsData[6].average || 0,
      activeHealthAlerts: statsData[7].count,
      last6MonthsMilk: statsData[8],
      carilerNetBakiye: statsData[9], // İsim değişikliği
      aylikKarZararTrendi: statsData[10], // İndeks değişikliği
    };
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return { error: error.message };
  }
  // No db.close() here, using shared connection from getDb()
});

console.log('Dashboard IPC handlers registered.');

/* =================================
   Modern Sidebar Component
   ================================= */

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: var(--sidebar-width);
  background: var(--sidebar-bg);
  border-right: 1px solid var(--sidebar-border);
  display: flex;
  flex-direction: column;
  z-index: var(--z-fixed);
  overflow-y: auto;
  scrollbar-width: none;
}

.sidebar::-webkit-scrollbar {
  display: none;
}

/* =================================
   Sidebar Logo
   ================================= */
.sidebar .logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-6);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--interactive-primary);
  border-bottom: 1px solid var(--sidebar-border);
  text-decoration: none;
}

.sidebar .logo:hover {
  color: var(--interactive-primary-hover);
}

/* =================================
   Sidebar Menu
   ================================= */
.sidebar .menu {
  list-style: none;
  padding: 0;
  margin: 0;
  padding-top: var(--space-4);
}

.sidebar .menu-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-6);
  color: var(--text-secondary);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  border-radius: 0;
  border-radius: var(--radius-lg);
}

.sidebar .menu-item:hover {
  background: var(--sidebar-item-hover);
  color: var(--text-primary);
}

.sidebar .menu-item.active {
  background: var(--sidebar-item-active);
  color: var(--sidebar-item-active-text);
  font-weight: var(--font-semibold);
}

.sidebar .menu-item svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* =================================
   Sidebar Accordion
   ================================= */
.sidebar .accordion-toggle {
  position: relative;
}

.sidebar .accordion-toggle .arrow {
  margin-left: auto;
  transition: transform var(--transition-fast);
  width: 16px;
  height: 16px;
}

.sidebar .accordion-toggle.expanded .arrow,
.sidebar .accordion-toggle.open .arrow {
  transform: rotate(90deg);
}

.sidebar .submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-base);
}

.sidebar .accordion-toggle.expanded + .submenu,
.sidebar .accordion-toggle.open + .submenu,
.sidebar .submenu.open {
  max-height: 400px; /* Increased from 200px to accommodate more items */
}

.sidebar .submenu .menu-item {
  padding: var(--space-2) var(--space-6);
  font-size: var(--text-sm);
  margin: 0 var(--space-2);
}

/* =================================
   Main Content Layout
   ================================= */
.main-content {
  margin-left: var(--sidebar-width);
  min-height: 100vh;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
}

#content-area {
  flex: 1;
  padding: var(--space-8);
  max-width: calc(100vw - var(--sidebar-width) - 2 * var(--space-8));
  width: 90%;
  margin: 0 auto;
}

/* =================================
   Breadcrumb Navigation
   ================================= */
.breadcrumb-nav {
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-3) var(--space-8);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(8px);
}

.breadcrumb-container {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  max-width: var(--content-max-width);
  margin: 0 auto;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  transition: color var(--transition-fast);
}

.breadcrumb-item.home {
  color: var(--text-link);
  cursor: pointer;
}

.breadcrumb-item.home:hover {
  color: var(--text-link-hover);
}

.breadcrumb-item.current {
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

.breadcrumb-separator {
  display: flex;
  align-items: center;
  color: var(--text-tertiary);
  opacity: 0.6;
}

/* =================================
   Page Actions
   ================================= */
.page-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  gap: var(--space-3);
  flex-wrap: wrap;
}

.page-actions > *:only-child {
  margin-left: auto;
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }

  .main-content {
    margin-left: 200px;
  }

  #content-area {
    padding: var(--space-4);
    max-width: calc(100vw - 200px - 2 * var(--space-4));
    width: 90%;
  }

  .breadcrumb-nav {
    padding: var(--space-2) var(--space-4);
  }

  .breadcrumb-container {
    font-size: var(--text-xs);
    gap: var(--space-1);
  }

  .breadcrumb-item span {
    display: none;
  }

  .breadcrumb-item.current span {
    display: inline;
  }

  .breadcrumb-item svg {
    width: 14px;
    height: 14px;
  }

  .page-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 180px;
  }

  .main-content {
    margin-left: 180px;
  }

  #content-area {
    padding: var(--space-3);
    max-width: calc(100vw - 180px - 2 * var(--space-3));
    width: 90%;
  }

  .breadcrumb-nav {
    padding: var(--space-2) var(--space-3);
  }
}

/* =================================
   Dark Theme Adjustments
   ================================= */
[data-theme="dark"] .breadcrumb-nav {
  backdrop-filter: blur(8px);
  background: var(--bg-elevated);
}

// This module will be called from database.js after db is initialized
function initializeSchema(db, callback) {

// Hayvanlar Tablosu
const createHayvanlar = `CREATE TABLE IF NOT EXISTS Hayvanlar (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  KupeNo TEXT,
  IsletmeNo TEXT,
  <PERSON><PERSON> TEXT,
  Tur TEXT,
  Irk TEXT,
  Cinsiyet TEXT,
  DogumTarihi DATE,
  DogumAgirl<PERSON> REAL,
  DogumTipi TEXT,
  AnneKupe TEXT,
  BabaKupe TEXT,
  BoynuzDurumu TEXT,
  IsletmeyeGirisTarihi DATE,
  IsletmedenCikisTarihi DATE,
  CikisSebebi TEXT,
  FotografUrl TEXT,
  Notlar TEXT,
  AktifMi BOOLEAN,
  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
  UNIQUE(ProfilId, KupeNo)
);`;

// Aşılamalar Tablosu
const createAsilamalar = `CREATE TABLE IF NOT EXISTS Asilamalar (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  HayvanId INTEGER,
  AsiAdi TEXT,
  AsilamaTarihi DATE,
  Doz TEXT,
  UygulayanVeteriner TEXT,
  Notlar TEXT,
  SablonId INTEGER, -- Reference to vaccination template if created from template
  TakvimId INTEGER, -- Reference to schedule entry if created from schedule
  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
  FOREIGN KEY (HayvanId) REFERENCES Hayvanlar(Id),
  FOREIGN KEY (SablonId) REFERENCES AsiSablonlari(Id),
  FOREIGN KEY (TakvimId) REFERENCES AsiTakvimi(Id)
);`;

// Aşı Şablonları Tablosu (Vaccination Templates)
const createAsiSablonlari = `CREATE TABLE IF NOT EXISTS AsiSablonlari (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  SablonAdi TEXT NOT NULL,
  HayvanTuru TEXT, -- Cattle, Sheep, etc.
  AsiAdi TEXT NOT NULL,
  Periyot INTEGER NOT NULL, -- Days between vaccinations
  PeriyotTipi TEXT DEFAULT 'gun', -- gun, ay, yil
  IlkAsiYasi INTEGER, -- Age in days for first vaccination
  TekrarSayisi INTEGER DEFAULT -1, -- -1 for unlimited, or specific number
  Aktif BOOLEAN DEFAULT 1,
  Aciklama TEXT,
  OlusturmaTarihi DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE
);`;

// Aşı Takvimi Tablosu (Vaccination Schedule)
const createAsiTakvimi = `CREATE TABLE IF NOT EXISTS AsiTakvimi (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  HayvanId INTEGER NOT NULL,
  SablonId INTEGER NOT NULL,
  PlanlananTarih DATE NOT NULL,
  Durum TEXT DEFAULT 'Bekliyor', -- Bekliyor, Tamamlandi, Gecikti, Iptal
  TamamlanmaTarihi DATE,
  AsilamaId INTEGER, -- Reference to actual vaccination record
  Notlar TEXT,
  OlusturmaTarihi DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
  FOREIGN KEY (HayvanId) REFERENCES Hayvanlar(Id) ON DELETE CASCADE,
  FOREIGN KEY (SablonId) REFERENCES AsiSablonlari(Id) ON DELETE CASCADE,
  FOREIGN KEY (AsilamaId) REFERENCES Asilamalar(Id)
);`;

// Aşı Hatırlatıcıları Tablosu (Vaccination Reminders)
const createAsiHatirlaticlari = `CREATE TABLE IF NOT EXISTS AsiHatirlaticlari (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  HayvanId INTEGER NOT NULL,
  TakvimId INTEGER NOT NULL,
  HatirlaticiTipi TEXT NOT NULL, -- Yaklasan, Geciken, Kritik
  HatirlaticiTarihi DATE NOT NULL,
  Mesaj TEXT,
  Okundu BOOLEAN DEFAULT 0,
  OlusturmaTarihi DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
  FOREIGN KEY (HayvanId) REFERENCES Hayvanlar(Id) ON DELETE CASCADE,
  FOREIGN KEY (TakvimId) REFERENCES AsiTakvimi(Id) ON DELETE CASCADE
);`;

// Tedaviler Tablosu
const createTedaviler = `CREATE TABLE IF NOT EXISTS Tedaviler (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  HayvanId INTEGER,
  Teshis TEXT,
  TedaviBaslangicTarihi DATE,
  TedaviBitisTarihi DATE,
  KullanilanIlaclar TEXT,
  KarantinaDurumu BOOLEAN,
  KarantinaBaslangicTarihi DATE,
  KarantinaBitisTarihi DATE,
  Notlar TEXT,
  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
  FOREIGN KEY (HayvanId) REFERENCES Hayvanlar(Id)
);`;

// Tohumlamalar Tablosu
const createTohumlamalar = `CREATE TABLE IF NOT EXISTS Tohumlamalar (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  HayvanId INTEGER,
  TohumlamaTarihi DATE,
  TohumlamaTipi TEXT,
  BogaId INTEGER,
  SpermaKodu TEXT,
  Sonuc TEXT,
  Notlar TEXT,
  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
  FOREIGN KEY (HayvanId) REFERENCES Hayvanlar(Id),
  FOREIGN KEY (BogaId) REFERENCES Hayvanlar(Id)
);`;

// Gebelikler Tablosu
const createGebelikler = `CREATE TABLE IF NOT EXISTS Gebelikler (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  HayvanId INTEGER,
  TohumlamaId INTEGER,
  BaslangicTarihi DATE,
  BeklenenDogumTarihi DATE,
  GebelikSonucu TEXT,
  DogumTarihi DATE,
  Notlar TEXT,
  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
  FOREIGN KEY (HayvanId) REFERENCES Hayvanlar(Id),
  FOREIGN KEY (TohumlamaId) REFERENCES Tohumlamalar(Id)
);`;

// Laktasyonlar Tablosu
const createLaktasyonlar = `CREATE TABLE IF NOT EXISTS Laktasyonlar (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  HayvanId INTEGER,
  GebelikId INTEGER,
  BaslangicTarihi DATE,
  BitisTarihi DATE,
  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
  FOREIGN KEY (HayvanId) REFERENCES Hayvanlar(Id),
  FOREIGN KEY (GebelikId) REFERENCES Gebelikler(Id)
);`;

// SagimVerileri Tablosu
const createSagimVerileri = `CREATE TABLE IF NOT EXISTS SagimVerileri (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  LaktasyonId INTEGER,
  Miktar REAL,
  Tarih DATE,
  Saat TEXT,
  YagOrani REAL,
  ProteinOrani REAL,
  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
  FOREIGN KEY (LaktasyonId) REFERENCES Laktasyonlar(Id)
);`;

// Kullanıcılar Tablosu - Enhanced for authentication
const createKullanicilar = `CREATE TABLE IF NOT EXISTS Kullanicilar (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  KullaniciAdi TEXT UNIQUE NOT NULL,
  Email TEXT UNIQUE,
  SifreHash TEXT NOT NULL,
  AdSoyad TEXT NOT NULL,
  Rol TEXT DEFAULT 'User',
  AktifMi BOOLEAN DEFAULT 1,
  OlusturmaTarihi DATETIME DEFAULT CURRENT_TIMESTAMP,
  SonGirisTarihi DATETIME,
  Notlar TEXT
);`;

// Profiller Tablosu - User profiles for data organization
const createProfiller = `CREATE TABLE IF NOT EXISTS Profiller (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  KullaniciId INTEGER NOT NULL,
  ProfilAdi TEXT NOT NULL,
  Aciklama TEXT,
  VarsayilanMi BOOLEAN DEFAULT 0,
  AktifMi BOOLEAN DEFAULT 1,
  OlusturmaTarihi DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (KullaniciId) REFERENCES Kullanicilar(Id) ON DELETE CASCADE,
  UNIQUE(KullaniciId, ProfilAdi)
);`;

// Sürü Profilleri Tablosu (ekstra) - Deprecated, will be replaced by Profiller
const createSuruler = `CREATE TABLE IF NOT EXISTS Suruler (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  Isim TEXT,
  Aciklama TEXT
);`;

// Hayvan-Sürü ilişkisi (çoklu sürü desteği için)
const createHayvanSuruler = `CREATE TABLE IF NOT EXISTS HayvanSuruler (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  HayvanId INTEGER,
  SürüId INTEGER,
  FOREIGN KEY (HayvanId) REFERENCES Hayvanlar(Id),
  FOREIGN KEY (SürüId) REFERENCES Suruler(Id)
);`;

const tables = [
  createKullanicilar,  // Create users first
  createProfiller,     // Then profiles (depends on users)
  createHayvanlar,     // Then animals (depends on profiles)
  createAsiSablonlari, // Vaccination templates (depends on profiles)
  createAsilamalar,    // Vaccinations (depends on animals and templates)
  createAsiTakvimi,    // Vaccination schedule (depends on animals and templates)
  createAsiHatirlaticlari, // Vaccination reminders (depends on schedule)
  createTedaviler,
  createTohumlamalar,
  createGebelikler,
  createLaktasyonlar,
  createSagimVerileri
];

// Muhasebe Tabloları
const createUrunler = `CREATE TABLE IF NOT EXISTS Urunler (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProfilId INTEGER NOT NULL,
    Ad TEXT NOT NULL,
    Kategori TEXT, -- Satış, Alış, vb.
    VarsayilanBirim TEXT, -- Kg, Adet, Litre
    Notlar TEXT,
    FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE
);`;

const createCariler = `CREATE TABLE IF NOT EXISTS Cariler (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProfilId INTEGER NOT NULL,
    Unvan TEXT NOT NULL,
    Tip TEXT CHECK(Tip IN ('Musteri', 'Tedarikci')) NOT NULL,
    Telefon TEXT,
    Email TEXT,
    Adres TEXT,
    VergiNo TEXT,
    Notlar TEXT,
    FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE
);`;

const createAlislar = `CREATE TABLE IF NOT EXISTS Alislar (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProfilId INTEGER NOT NULL,
    CariId INTEGER NOT NULL,
    UrunId INTEGER NOT NULL,
    Tarih DATE NOT NULL,
    FaturaNo TEXT,
    Miktar REAL NOT NULL,
    BirimFiyat REAL NOT NULL,
    ToplamTutar REAL, -- GENERATED ALWAYS AS (Miktar * BirimFiyat) STORED, -- Kaldırıldı, manuel hesaplanacak
    OdenenTutar REAL DEFAULT 0,
    KalanTutar REAL, -- GENERATED ALWAYS AS (ToplamTutar - OdenenTutar) STORED, -- Kaldırıldı, manuel hesaplanacak
    OdemeDurumu TEXT CHECK(OdemeDurumu IN ('Pesin', 'Vadeli')) NOT NULL,
    VadeTarihi DATE,
    IslemTamamlandi BOOLEAN DEFAULT 0, -- Bu satır zaten migration ile ekleniyordu, tanıma taşıdık
    Notlar TEXT,
    FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
    FOREIGN KEY (CariId) REFERENCES Cariler(Id) ON DELETE CASCADE,
    FOREIGN KEY (UrunId) REFERENCES Urunler(Id) ON DELETE CASCADE
);`;

const createSatislar = `CREATE TABLE IF NOT EXISTS Satislar (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProfilId INTEGER NOT NULL,
    CariId INTEGER NOT NULL,
    UrunId INTEGER NOT NULL,
    Tarih DATE NOT NULL,
    FaturaNo TEXT,
    Miktar REAL NOT NULL,
    BirimFiyat REAL NOT NULL,
    ToplamTutar REAL, -- GENERATED ALWAYS AS (Miktar * BirimFiyat) STORED, -- Kaldırıldı, manuel hesaplanacak
    OdenenTutar REAL DEFAULT 0,
    KalanTutar REAL, -- GENERATED ALWAYS AS (ToplamTutar - OdenenTutar) STORED, -- Kaldırıldı, manuel hesaplanacak
    OdemeDurumu TEXT CHECK(OdemeDurumu IN ('Pesin', 'Vadeli')) NOT NULL,
    VadeTarihi DATE,
    IslemTamamlandi BOOLEAN DEFAULT 0, -- Bu satır zaten migration ile ekleniyordu, tanıma taşıdık
    Notlar TEXT,
    FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
    FOREIGN KEY (CariId) REFERENCES Cariler(Id) ON DELETE CASCADE,
    FOREIGN KEY (UrunId) REFERENCES Urunler(Id) ON DELETE CASCADE
);`;

const createOdemeler = `CREATE TABLE IF NOT EXISTS Odemeler (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProfilId INTEGER NOT NULL,
    IslemTipi TEXT CHECK(IslemTipi IN ('Alis', 'Satis')) NOT NULL,
    IslemId INTEGER NOT NULL,
    OdemeTarihi DATE NOT NULL,
    Tutar REAL NOT NULL,
    Yontem TEXT, -- Nakit, EFT vb.
    Notlar TEXT,
    FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE
);`;

// Yem ve Rasyon Tabloları
// UNIT STANDARDIZATION (NRC 2001 Dairy Cattle Standards):
// - All nutritional values stored as per kg DM (dry matter)
// - Percentages: % DM (protein, fiber, minerals, anti-nutritional factors)
// - Energy: Mcal/kg DM for NEL/NEM/DE, MJ/kg DM for ME
// - Macro minerals: % DM (converted to g/day in calculations)
// - Micro minerals: mg/kg DM (equivalent to ppm, converted to mg/day in calculations)
// - Fat-soluble vitamins: IU/kg DM (converted to IU/day in calculations)
// - Water-soluble vitamins: mg/kg DM or μg/kg DM (converted to daily units in calculations)
const createYemler = `CREATE TABLE IF NOT EXISTS Yemler (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  YemAdi TEXT NOT NULL,
  YemTipi TEXT, -- Kaba Yem, Konsantre, Mineral vb.
  Aciklama TEXT,

  -- Temel Besin Değerleri (NRC 2001 Standardized Units) - Doğrudan Ölçülen
  KuruMadde REAL, -- % DM (Dry Matter Percentage)
  HamProtein REAL, -- % DM (Crude Protein as % of DM)
  RDP REAL, -- % CP (Rumen Degradable Protein as % of Crude Protein)
  RUP REAL, -- % CP (Rumen Undegradable Protein as % of Crude Protein)
  MetabolikEnerji REAL, -- MJ/kg DM (Metabolizable Energy)
  NEL REAL, -- Mcal/kg DM (Net Energy for Lactation)
  -- TDN REAL, -- REMOVED: Türetilen değer (NEL'den hesaplanır)
  HamYag REAL, -- % DM (Crude Fat as % of DM)

  -- Karbohidrat Fraksiyonları (3) - Doğrudan Ölçülen
  Nisasta REAL, -- % DM (Starch)
  Seker REAL, -- % DM (Sugar)
  Kul REAL, -- % DM (Ash)

  -- Fiber Fraksiyonları (NRC 2001 Standards)
  NDF REAL, -- % DM (Neutral Detergent Fiber)
  ADF REAL, -- % DM (Acid Detergent Fiber)
  Hemiseluloz REAL, -- % DM (Hemicellulose)
  Seluloz REAL, -- % DM (Cellulose)
  Lignin REAL, -- % DM (Lignin)

  -- Makro Mineraller (NRC 2001 Standards)
  Kalsiyum REAL, -- % DM (Calcium as % of DM)
  Fosfor REAL, -- % DM (Phosphorus as % of DM)
  Magnezyum REAL, -- % DM (Magnesium as % of DM)
  Potasyum REAL, -- % DM (Potassium as % of DM)
  Sodyum REAL, -- % DM (Sodium as % of DM)
  Klorur REAL, -- % DM (Chloride as % of DM)
  sulfur REAL, -- % DM (Sulfur as % of DM)

  -- Mikro Mineraller (NRC 2001 Standards)
  Demir REAL, -- mg/kg DM (Iron, equivalent to ppm)
  Cinko REAL, -- mg/kg DM (Zinc, equivalent to ppm)
  Bakir REAL, -- mg/kg DM (Copper, equivalent to ppm)
  Manganez REAL, -- mg/kg DM (Manganese, equivalent to ppm)
  Selenyum REAL, -- mg/kg DM (Selenium, equivalent to ppm)
  Iyot REAL, -- mg/kg DM (Iodine, equivalent to ppm)
  Kobalt REAL, -- mg/kg DM (Cobalt, equivalent to ppm)
  Molibden REAL, -- mg/kg DM (Molybdenum, equivalent to ppm)

  -- Vitaminler (NRC 2001 Standards)
  VitaminA REAL, -- IU/kg DM (Vitamin A)
  VitaminD REAL, -- IU/kg DM (Vitamin D)
  VitaminE REAL, -- IU/kg DM (Vitamin E)
  VitaminK REAL, -- mg/kg DM (Vitamin K)
  Thiamin REAL, -- mg/kg DM (Thiamin/B1)
  Riboflavin REAL, -- mg/kg DM (Riboflavin/B2)
  Niacin REAL, -- mg/kg DM (Niacin/B3)
  Biotin REAL, -- mg/kg DM (Biotin/B7)
  Folat REAL, -- mg/kg DM (Folate/B9)
  Cobalamin REAL, -- μg/kg DM (Cobalamin/B12)

  -- Anti-besin Faktörleri (NRC 2001 Standards)
  Tanen REAL, -- % DM (Tannins)
  Fitat REAL, -- % DM (Phytates)
  Saponin REAL, -- % DM (Saponins)
  Oksalat REAL, -- % DM (Oxalates)

  -- Diğer Bilgiler
  BirimFiyat REAL, -- TL/kg
  TedarikciId INTEGER, -- Cariler tablosundan
  AktifMi BOOLEAN DEFAULT 1,
  OlusturmaTarihi DATETIME DEFAULT CURRENT_TIMESTAMP,
  GuncellemeTarihi DATETIME DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE,
  FOREIGN KEY (TedarikciId) REFERENCES Cariler(Id)
);`;

const createRasyonlar = `CREATE TABLE IF NOT EXISTS Rasyonlar (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  ProfilId INTEGER NOT NULL,
  RasyonAdi TEXT NOT NULL,
  HayvanKategorisi TEXT, -- dairy-cow, calf, heifer, bull
  HedefCanlıAgirlik REAL, -- kg
  HedefSutVerimi REAL, -- L/gün
  HedefSutYagOrani REAL, -- %
  GebelikDurumu TEXT, -- not-pregnant, early, mid, late
  AktiviteSeviyesi TEXT, -- normal, high, low

  -- Hesaplanan İhtiyaçlar (referans için)
  HedefKuruMadde REAL, -- kg/gün
  HedefHamProtein REAL, -- kg/gün
  HedefMetabolikEnerji REAL, -- MJ/gün
  HedefNEL REAL, -- Mcal/gün
  HedefKalsiyum REAL, -- g/gün
  HedefFosfor REAL, -- g/gün

  -- Rasyon Durumu
  OnaylandiMi BOOLEAN DEFAULT 0,
  AktifMi BOOLEAN DEFAULT 1,
  Notlar TEXT,
  OlusturmaTarihi DATETIME DEFAULT CURRENT_TIMESTAMP,
  GuncellemeTarihi DATETIME DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (ProfilId) REFERENCES Profiller(Id) ON DELETE CASCADE
);`;

const createRasyonYemleri = `CREATE TABLE IF NOT EXISTS RasyonYemleri (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  RasyonId INTEGER NOT NULL,
  YemId INTEGER NOT NULL,
  Miktar REAL NOT NULL, -- kg/gün
  Yuzde REAL, -- Rasyondaki yüzdesi
  Sira INTEGER DEFAULT 1, -- Gösterim sırası

  FOREIGN KEY (RasyonId) REFERENCES Rasyonlar(Id) ON DELETE CASCADE,
  FOREIGN KEY (YemId) REFERENCES Yemler(Id) ON DELETE CASCADE,
  UNIQUE(RasyonId, YemId)
);`;

const createRasyonAnalizleri = `CREATE TABLE IF NOT EXISTS RasyonAnalizleri (
  Id INTEGER PRIMARY KEY AUTOINCREMENT,
  RasyonId INTEGER NOT NULL,

  -- Hesaplanan Toplam Değerler
  ToplamKuruMadde REAL, -- kg/gün
  ToplamHamProtein REAL, -- kg/gün
  ToplamMetabolikEnerji REAL, -- MJ/gün
  ToplamNEL REAL, -- Mcal/gün
  ToplamKalsiyum REAL, -- g/gün
  ToplamFosfor REAL, -- g/gün
  ToplamMagnezyum REAL, -- g/gün

  -- Karşılaştırma Sonuçları (İhtiyaç karşılama oranları %)
  KuruMaddeKarsilama REAL, -- %
  ProteinKarsilama REAL, -- %
  EnerjiKarsilama REAL, -- %
  KalsiyumKarsilama REAL, -- %
  FosforKarsilama REAL, -- %

  -- Rasyon Kalite Skorları
  GenelKaliteSkor REAL, -- 0-100
  BesinDengesiSkor REAL, -- 0-100
  MalivetSkor REAL, -- 0-100

  -- Öneriler ve Uyarılar (JSON formatında)
  Oneriler TEXT, -- JSON array
  Uyarilar TEXT, -- JSON array

  HesaplamaTarihi DATETIME DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (RasyonId) REFERENCES Rasyonlar(Id) ON DELETE CASCADE
);`;

const yemRasyonTables = [
  createYemler,
  createRasyonlar,
  createRasyonYemleri,
  createRasyonAnalizleri
];

const muhasebeTables = [
  createUrunler,
  createCariler,
  createAlislar,
  createSatislar,
  createOdemeler
];

// Migration: Add new columns to Alislar and Satislar if they don't exist
// Note: SQLite's ALTER TABLE has limitations. Adding columns with defaults is fine.
// If more complex migrations are needed (e.g., changing data types, removing columns),
// a more robust migration strategy (like creating a new table, copying data, dropping old, renaming new)
// or a dedicated migration library would be better.

const migrations = [
  // Existing accounting migrations
  `ALTER TABLE Alislar ADD COLUMN ToplamTutar REAL`,
  `ALTER TABLE Alislar ADD COLUMN OdenenTutar REAL DEFAULT 0`,
  `ALTER TABLE Alislar ADD COLUMN KalanTutar REAL`,
  `ALTER TABLE Satislar ADD COLUMN ToplamTutar REAL`,
  `ALTER TABLE Satislar ADD COLUMN OdenenTutar REAL DEFAULT 0`,
  `ALTER TABLE Satislar ADD COLUMN KalanTutar REAL`,

  // Multi-user system migrations - Add ProfileId to existing tables
  `ALTER TABLE Hayvanlar ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE Asilamalar ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE Asilamalar ADD COLUMN SablonId INTEGER`,
  `ALTER TABLE Asilamalar ADD COLUMN TakvimId INTEGER`,
  `ALTER TABLE Tedaviler ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE Tohumlamalar ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE Gebelikler ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE Laktasyonlar ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE SagimVerileri ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE Urunler ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE Cariler ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE Alislar ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE Satislar ADD COLUMN ProfilId INTEGER`,
  `ALTER TABLE Odemeler ADD COLUMN ProfilId INTEGER`,

  // Enhanced user table migrations
  `ALTER TABLE Kullanicilar ADD COLUMN Email TEXT`,
  `ALTER TABLE Kullanicilar ADD COLUMN OlusturmaTarihi DATETIME DEFAULT CURRENT_TIMESTAMP`,
  `ALTER TABLE Kullanicilar ADD COLUMN SonGirisTarihi DATETIME`,
  `ALTER TABLE Kullanicilar ADD COLUMN Notlar TEXT`
];


  db.serialize(() => {
    tables.forEach(sql => db.run(sql, err => {
      if (err) console.error(`Error creating table with SQL: ${sql}`, err.message);
    }));
    muhasebeTables.forEach(sql => db.run(sql, err => {
      if (err) console.error(`Error creating muhasebe table with SQL: ${sql}`, err.message);
    }));
    yemRasyonTables.forEach(sql => db.run(sql, err => {
      if (err) console.error(`Error creating yem/rasyon table with SQL: ${sql}`, err.message);
    }));

    // Run migrations
    // We check if the column exists before trying to add it to avoid errors on subsequent runs.
    // This is a basic way to handle migrations. For complex apps, a migration library is better.
    const runMigration = (alterSql, tableName, columnName) => {
      db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
        if (err) {
          console.error(`Error fetching table info for ${tableName}:`, err.message);
          return;
        }
        const columnExists = columns.some(col => col.name === columnName);
        if (!columnExists) {
          db.run(alterSql, migrateErr => {
            if (migrateErr) {
              console.error(`Migration error for ${tableName} adding ${columnName}:`, migrateErr.message);
            } else {
              console.log(`Successfully migrated ${tableName}: added ${columnName}`);
            }
          });
        }
      });
    };

    // Apply existing accounting migrations
    runMigration(`ALTER TABLE Alislar ADD COLUMN ToplamTutar REAL`, 'Alislar', 'ToplamTutar');
    runMigration(`ALTER TABLE Alislar ADD COLUMN OdenenTutar REAL DEFAULT 0`, 'Alislar', 'OdenenTutar');
    runMigration(`ALTER TABLE Alislar ADD COLUMN KalanTutar REAL`, 'Alislar', 'KalanTutar');
    runMigration(`ALTER TABLE Satislar ADD COLUMN ToplamTutar REAL`, 'Satislar', 'ToplamTutar');
    runMigration(`ALTER TABLE Satislar ADD COLUMN OdenenTutar REAL DEFAULT 0`, 'Satislar', 'OdenenTutar');
    runMigration(`ALTER TABLE Satislar ADD COLUMN KalanTutar REAL`, 'Satislar', 'KalanTutar');

    // Apply multi-user system migrations - Add ProfileId columns
    runMigration(`ALTER TABLE Hayvanlar ADD COLUMN ProfilId INTEGER`, 'Hayvanlar', 'ProfilId');
    runMigration(`ALTER TABLE Asilamalar ADD COLUMN ProfilId INTEGER`, 'Asilamalar', 'ProfilId');
    runMigration(`ALTER TABLE Asilamalar ADD COLUMN SablonId INTEGER`, 'Asilamalar', 'SablonId');
    runMigration(`ALTER TABLE Asilamalar ADD COLUMN TakvimId INTEGER`, 'Asilamalar', 'TakvimId');
    runMigration(`ALTER TABLE Tedaviler ADD COLUMN ProfilId INTEGER`, 'Tedaviler', 'ProfilId');
    runMigration(`ALTER TABLE Tohumlamalar ADD COLUMN ProfilId INTEGER`, 'Tohumlamalar', 'ProfilId');
    runMigration(`ALTER TABLE Gebelikler ADD COLUMN ProfilId INTEGER`, 'Gebelikler', 'ProfilId');
    runMigration(`ALTER TABLE Laktasyonlar ADD COLUMN ProfilId INTEGER`, 'Laktasyonlar', 'ProfilId');
    runMigration(`ALTER TABLE SagimVerileri ADD COLUMN ProfilId INTEGER`, 'SagimVerileri', 'ProfilId');
    runMigration(`ALTER TABLE Urunler ADD COLUMN ProfilId INTEGER`, 'Urunler', 'ProfilId');
    runMigration(`ALTER TABLE Cariler ADD COLUMN ProfilId INTEGER`, 'Cariler', 'ProfilId');
    runMigration(`ALTER TABLE Alislar ADD COLUMN ProfilId INTEGER`, 'Alislar', 'ProfilId');
    runMigration(`ALTER TABLE Satislar ADD COLUMN ProfilId INTEGER`, 'Satislar', 'ProfilId');
    runMigration(`ALTER TABLE Odemeler ADD COLUMN ProfilId INTEGER`, 'Odemeler', 'ProfilId');

    // Apply enhanced user table migrations
    runMigration(`ALTER TABLE Kullanicilar ADD COLUMN Email TEXT`, 'Kullanicilar', 'Email');
    runMigration(`ALTER TABLE Kullanicilar ADD COLUMN OlusturmaTarihi DATETIME DEFAULT CURRENT_TIMESTAMP`, 'Kullanicilar', 'OlusturmaTarihi');
    runMigration(`ALTER TABLE Kullanicilar ADD COLUMN SonGirisTarihi DATETIME`, 'Kullanicilar', 'SonGirisTarihi');
    runMigration(`ALTER TABLE Kullanicilar ADD COLUMN Notlar TEXT`, 'Kullanicilar', 'Notlar');

  });
  
  // Callback when all operations are complete
  if (callback) callback();
}

module.exports = { initializeSchema };

// db.close() çağrısını buradan kaldırıyoruz, çünkü database.js bu db örneğini kullanacak.
// Uygulama kapatılırken ana süreçte (main.js) kapatılmalı.
// db.close((err) => {
//   if (err) {
//     return console.error(err.message);
//   }
//   console.log('Database connection closed.');
// });
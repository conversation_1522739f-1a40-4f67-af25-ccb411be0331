/* =================================
   Reports Page Styles
   ================================= */

/* =================================
   Reports Header Section
   ================================= */
.reports-header-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.reports-header-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.reports-page-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.reports-page-title svg {
  width: 28px;
  height: 28px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

/* =================================
   Reports Criteria Section
   ================================= */
.reports-criteria-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.reports-criteria-section:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.reports-criteria-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
}

.reports-criteria-header svg {
  width: 20px;
  height: 20px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

.reports-criteria-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.reports-criteria-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

/* Reports Form Grid */
.reports-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.form-group input,
.form-group select {
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-group input:hover,
.form-group select:hover {
  border-color: var(--border-secondary);
}

.form-group .btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  justify-content: center;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.form-group .btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* =================================
   Reports Output Section
   ================================= */
.reports-output-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  min-height: 400px;
}

.reports-output-section:hover {
  box-shadow: var(--shadow-md);
}

.reports-output-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
  flex-wrap: wrap;
  gap: var(--space-3);
}

.reports-output-header > div:first-child {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.reports-output-header svg {
  width: 20px;
  height: 20px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

.reports-output-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

/* Report Export Actions */
.report-export-actions {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.report-export-actions .btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
  white-space: nowrap;
}

.report-export-actions .btn svg {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

/* Reports Output Content */
.reports-output-content {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  min-height: 300px;
  overflow-x: auto;
}

/* Empty Message */
.reports-output-content .empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-4);
  text-align: center;
  color: var(--text-secondary);
  height: 100%;
  min-height: 250px;
}

.reports-output-content .empty-message svg {
  width: 48px;
  height: 48px;
  margin-bottom: var(--space-4);
  opacity: 0.6;
  color: var(--text-tertiary);
}

.reports-output-content .empty-message p {
  margin: 0;
  font-size: var(--text-sm);
}

/* Report Tables */
.report-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: var(--space-4);
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.report-table th,
.report-table td {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
  font-size: var(--text-sm);
}

.report-table th {
  background: var(--bg-secondary);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.report-table td {
  color: var(--text-secondary);
}

.report-table tr:hover {
  background: var(--interactive-secondary);
}

.report-table tr:last-child td {
  border-bottom: none;
}

/* Report Summary Cards */
.report-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.report-summary-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  text-align: center;
  transition: all var(--transition-fast);
}

.report-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.report-summary-card .summary-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.report-summary-card .summary-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  .reports-header-section,
  .reports-criteria-section,
  .reports-output-section {
    padding: var(--space-4);
  }
  
  .reports-page-title {
    font-size: var(--text-xl);
  }
  
  .reports-page-title svg {
    width: 24px;
    height: 24px;
  }
  
  .reports-form-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .reports-output-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .report-export-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .report-summary-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }
  
  .report-table {
    font-size: var(--text-xs);
  }
  
  .report-table th,
  .report-table td {
    padding: var(--space-2) var(--space-3);
  }
}

@media (max-width: 480px) {
  .reports-header-section,
  .reports-criteria-section,
  .reports-output-section {
    padding: var(--space-3);
  }
  
  .reports-page-title {
    font-size: var(--text-lg);
  }
  
  .form-group .btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }
  
  .report-export-actions .btn {
    padding: var(--space-1) var(--space-2);
    font-size: var(--text-xs);
  }
  
  .reports-output-content .empty-message svg {
    width: 32px;
    height: 32px;
  }
}

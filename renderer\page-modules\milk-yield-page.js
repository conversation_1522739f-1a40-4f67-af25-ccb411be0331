// milk-yield-page.js

import { IconSystem } from '../utils/icon-system.js';
import { BadgeSystem } from '../utils/badge-system.js';
import { RecentActivities, formatActivityDate, truncateText } from '../components/recent-activities.js';
import { TableAnimations } from '../utils/table-animations.js';

// Globals for this module
let passedI18nMilkYield;
let passedDefaultAvatarSVGMilkYield;

// Lactations State
let allLactationsForAnimal = [];
let currentLactationsForAnimal = [];
const itemsPerPageLactations = 5;
let currentPageLactations = 1;
let sortColumnLactations = 'BaslangicTarihi';
let sortDirectionLactations = -1;

// Milking Data State
let allMilkingDataForLactation = [];
let currentMilkingDataForLactation = [];
const itemsPerPageMilking = 10;
let currentPageMilking = 1;
let sortColumnMilking = 'Tarih';
let sortDirectionMilking = -1;

// Common Elements
let animalSelectElMilkYield;
let lactationsContainerEl;
let milkingContainerEl;
let addLactationBtnEl;
let addMilkingBtnEl;
let selectedLactationIdMilkYield = null;

// Modal elements (assuming they exist globally in index.html)
let lactationModal, lactationForm, lactationModalTitle, editingLactationRecordId;
let milkingModal, milkingForm, milkingModalTitle, editingMilkingRecordId;
let milkRecentActivities;


export async function renderMilkYieldPage(contentArea, i18n, defaultAvatarSVG) {
    passedI18nMilkYield = i18n;
    passedDefaultAvatarSVGMilkYield = defaultAvatarSVG;
    const t = passedI18nMilkYield.t;

    contentArea.innerHTML = `
        <!-- Milk Yield Header Section -->
        <div class="milk-yield-header-section">
            <div class="milk-yield-header-content">
                <h2 class="milk-yield-page-title">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M8 3H5a2 2 0 0 0-2 2v3m2-5h10a2 2 0 0 1 2 2v3m0 0v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-8m18 0H2"/>
                        <circle cx="12" cy="14" r="3"/>
                    </svg>
                    ${t('milk_yield_management')}
                </h2>
                <div class="milk-yield-quick-actions">
                    <button class="btn btn-primary" id="bulk-milking-btn">
                        <svg viewBox='0 0 20 20' fill='none'>
                            <circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/>
                            <path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/>
                        </svg>
                        <span>${t('btn_bulk_milking') || 'Toplu Sağım Girdisi'}</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Animal Selection Bar -->
        <div class="animal-selection-bar">
            <div class="animal-selection-bar-content">
                <div class="animal-selection-info">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 7.5C19 11.09 16.09 14 12.5 14S6 11.09 6 7.5 8.91 1 12.5 1s6.5 2.91 6.5 6.5z"/>
                        <path d="M12.5 14v7"/>
                        <path d="M8 21h9"/>
                    </svg>
                    <span>${t('select_animal')}</span>
                </div>
                <select id="milk-animal-select" data-placeholder="${t('please_select_animal')}">
                    <option value="">${t('please_select_animal')}</option>
                </select>
            </div>
        </div>

        <div id="milk-content-wrapper">
            <div class="milk-yield-sections-grid">
                <div id="lactations-section" class="milk-yield-section">
                    <div class="section-header">
                        <h3>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                            ${t('label_laktasyonlar')}
                        </h3>
                        <button class="btn btn-primary" id="add-lactation-btn" disabled>
                            <svg viewBox='0 0 20 20' fill='none'>
                                <circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/>
                                <path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/>
                            </svg>
                            ${t('btn_add_lactation')}
                        </button>
                    </div>
                    <div id="lactations-table-container">
                        <div class="empty-message">${t('please_select_animal')}</div>
                    </div>
                </div>

                <div id="milking-section" class="milk-yield-section">
                    <div class="section-header">
                        <h3>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M8 3H5a2 2 0 0 0-2 2v3m2-5h10a2 2 0 0 1 2 2v3m0 0v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-8m18 0H2"/>
                                <circle cx="12" cy="14" r="3"/>
                            </svg>
                            ${t('label_sagim_verileri')}
                        </h3>
                        <button class="btn btn-primary" id="add-milking-btn" disabled>
                            <svg viewBox='0 0 20 20' fill='none'>
                                <circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/>
                                <path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/>
                            </svg>
                            ${t('btn_add_milking')}
                        </button>
                    </div>
                    <div id="milking-table-container">
                         <div class="empty-message">${t('label_laktasyon_secin')}</div>
                    </div>
                </div>
            </div>
        </div>

        <div id="milk-recent-activities"></div>
    `;

    animalSelectElMilkYield = contentArea.querySelector('#milk-animal-select');
    lactationsContainerEl = contentArea.querySelector('#lactations-table-container');
    milkingContainerEl = contentArea.querySelector('#milking-table-container');
    addLactationBtnEl = contentArea.querySelector('#add-lactation-btn');
    addMilkingBtnEl = contentArea.querySelector('#add-milking-btn');
    const bulkMilkingBtnEl = contentArea.querySelector('#bulk-milking-btn');

    initializeLactationModal();
    initializeMilkingModal();
    initializeBulkMilkingModal();

    const animals = await window.api.invoke('hayvanlar:list');
    animalSelectElMilkYield.innerHTML = `<option value="" selected disabled>${t('please_select_animal')}</option>`;
    animals.forEach(a => {
        const option = document.createElement('option');
        option.value = a.Id;
        option.textContent = `${a.KupeNo} ${a.Isim ? ' - ' + a.Isim : ''}`;
        animalSelectElMilkYield.appendChild(option);
    });

    animalSelectElMilkYield.addEventListener('change', async () => {
        const selectedAnimalId = animalSelectElMilkYield.value;
        if (selectedAnimalId) {
            addLactationBtnEl.disabled = false;
            allLactationsForAnimal = await window.api.invoke('laktasyonlar:list', selectedAnimalId);
            // First render the full table structure, then apply filters
            renderLactationsTablePage();
            milkingContainerEl.innerHTML = `<div class="empty-message">${t('label_laktasyon_secin')}</div>`;
            addMilkingBtnEl.disabled = true;
            selectedLactationIdMilkYield = null;

        } else {
            addLactationBtnEl.disabled = true;
            lactationsContainerEl.innerHTML = `<div class="empty-message">${t('please_select_animal')}</div>`;
            milkingContainerEl.innerHTML = `<div class="empty-message">${t('label_laktasyon_secin')}</div>`;
            addMilkingBtnEl.disabled = true;
        }
    });

    // Event listeners for buttons
    addLactationBtnEl.onclick = () => openLactationModal();
    addMilkingBtnEl.onclick = () => openMilkingModal();
    bulkMilkingBtnEl.onclick = () => {
        console.log('Bulk milking button clicked');
        openBulkMilkingModal();
    };

    // Initialize Recent Activities
    milkRecentActivities = new RecentActivities({
        containerId: 'milk-recent-activities',
        title: t('recent_milk_activities') || 'Recent Milk Activities',
        maxItems: 5,

        onViewAll: () => {
            // Scroll to lactations section
            lactationsContainerEl.scrollIntoView({ behavior: 'smooth' });
        },
        getRecords: async () => {
            try {
                // Get all animals first to map names
                const animals = await window.api.invoke('hayvanlar:list');
                const animalMap = {};
                animals.forEach(animal => {
                    animalMap[animal.Id] = animal.KupeNo || animal.Isim || `#${animal.Id}`;
                });

                // Get all recent lactations and milking data (not filtered by animal)
                const [allLactations, allMilkingData] = await Promise.all([
                    window.api.invoke('laktasyonlar:listAll') || [],
                    window.api.invoke('sagimverileri:listAll') || []
                ]);

                // Combine and sort by date
                const allRecords = [
                    ...allLactations.map(l => ({ ...l, type: 'lactation', animalName: animalMap[l.HayvanId] })),
                    ...allMilkingData.map(m => ({ ...m, type: 'milking', animalName: animalMap[m.HayvanId] }))
                ];

                return allRecords.sort((a, b) => {
                    const dateA = new Date(a.BaslangicTarihi || a.Tarih);
                    const dateB = new Date(b.BaslangicTarihi || b.Tarih);
                    return dateB - dateA;
                });
            } catch (error) {
                console.error('Error fetching milk records:', error);
                return [];
            }
        },
        formatRecord: (record) => {
            const isLactation = record.type === 'lactation';
            const date = record.BaslangicTarihi || record.Tarih;

            return {
                title: isLactation ?
                    t('lactation_period') || 'Lactation Period' :
                    `${record.Miktar || 0}L ${t('milk_yield') || 'Milk'}`,
                subtitle: `${record.animalName || t('unknown_animal') || 'Bilinmeyen Hayvan'} - ${isLactation ?
                    (record.BitisTarihi ? t('completed_lactation') : t('ongoing_lactation')) :
                    t('milking_record')}`,
                date: formatActivityDate(date),
                amount: !isLactation && record.Miktar ? `${record.Miktar}L` : null
            };
        },
        emptyMessage: t('no_recent_milk_activities') || 'No recent milk activities'
    });

    addLactationBtnEl.onclick = () => openLactationModal();
    addMilkingBtnEl.onclick = () => {
        const t = passedI18nMilkYield.t;
        if (!selectedLactationIdMilkYield) {
            window.toast.warning(t('please_select_lactation_for_milking'));
            return;
        }
        openMilkingModal();
    };

    // Initialize recent activities
    milkRecentActivities.init();
}


// --- LACTATION SPECIFIC FUNCTIONS ---
function applyFiltersAndSortLactations() {
    let filteredData = [...allLactationsForAnimal];
    const t = passedI18nMilkYield.t;

    const startDateFilterVal = lactationsContainerEl.querySelector('#filter-lactation-start-date')?.value;
    const endDateFilterVal = lactationsContainerEl.querySelector('#filter-lactation-end-date')?.value;

    if (startDateFilterVal) {
        filteredData = filteredData.filter(l => l.BaslangicTarihi >= startDateFilterVal);
    }
    if (endDateFilterVal) {
        // Filter lactations that started on or before the filter's end date.
        // This means BaslangicTarihi must be <= endDateFilterVal.
        filteredData = filteredData.filter(l => l.BaslangicTarihi <= endDateFilterVal);
    }

    currentLactationsForAnimal = filteredData;

    // Apply Sorting
    if (sortColumnLactations) {
        currentLactationsForAnimal = sortMilkYieldList(currentLactationsForAnimal, sortColumnLactations, sortDirectionLactations, 'lactation');
    }
    currentPageLactations = 1;

    // Only update table body, not the entire table structure
    updateLactationsTableBody();
}

async function loadAndRenderActiveTabDataMilkYield() {
    const selectedAnimalId = animalSelectElMilkYield.value;
    if (selectedAnimalId) {
        allLactationsForAnimal = await window.api.invoke('laktasyonlar:list', selectedAnimalId);
        renderLactationsTablePage();
    }
}

function updateLactationsTableBody() {
    const t = passedI18nMilkYield.t;
    const tableBody = lactationsContainerEl.querySelector('#lactations-table tbody');
    if (!tableBody) return;

    const totalPages = Math.ceil(currentLactationsForAnimal.length / itemsPerPageLactations);
    currentPageLactations = Math.max(1, Math.min(currentPageLactations, totalPages || 1));
    const startIndex = (currentPageLactations - 1) * itemsPerPageLactations;
    const endIndex = startIndex + itemsPerPageLactations;
    const pageItems = currentLactationsForAnimal.slice(startIndex, endIndex);

    // Clear existing rows
    tableBody.innerHTML = '';

    if (pageItems.length === 0) {
        tableBody.innerHTML = `<tr><td colspan="3"><div class="empty-state-container">${passedDefaultAvatarSVGMilkYield(48)}<p>${t('no_records_found')}</p></div></td></tr>`;
    } else {
        pageItems.forEach(l => {
            const row = tableBody.insertRow();
            row.dataset.id = l.Id;
            row.classList.add('clickable-row');
            row.innerHTML = `
                <td class="col-date">${l.BaslangicTarihi || ''}</td>
                <td class="col-date">${l.BitisTarihi || ''}</td>
                <td class="col-actions">
                    ${IconSystem.createActionsWrapper(l.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
                </td>`;
            row.onclick = async () => {
                lactationsContainerEl.querySelector('tr.selected')?.classList.remove('selected');
                row.classList.add('selected');
                selectedLactationIdMilkYield = row.dataset.id;
                addMilkingBtnEl.disabled = false;
                allMilkingDataForLactation = await window.api.invoke('sagimverileri:list', selectedLactationIdMilkYield);
                // Set current data and reset pagination
                currentMilkingDataForLactation = [...allMilkingDataForLactation];
                currentPageMilking = 1;
                // Create the full table structure and display data
                renderMilkingDataTablePage();
            };
            row.querySelector('.edit-btn').onclick = (e) => { e.stopPropagation(); openLactationModal(l); };
            row.querySelector('.delete-btn').onclick = async (e) => {
                e.stopPropagation();
                const confirmed = await window.toast.confirm(t('confirm_delete_lactation'), {
                    confirmText: t('toast_confirm'),
                    cancelText: t('toast_cancel')
                });

                if (confirmed) {
                    try {
                        await window.api.invoke('laktasyonlar:delete', l.Id);
                        if (selectedLactationIdMilkYield == l.Id) {
                            selectedLactationIdMilkYield = null;
                            milkingContainerEl.innerHTML = `<div class="empty-message">${t('label_laktasyon_secin')}</div>`;
                            addMilkingBtnEl.disabled = true;
                        }
                        await loadAndRenderActiveTabDataMilkYield();
                        window.toast.success(t('toast_success_delete'));
                    } catch (error) {
                        console.error('Error deleting lactation:', error);
                        window.toast.error(t('toast_error_delete') + ': ' + error.message);
                    }
                }
            };
        });

        // Auto-select first lactation if available and none selected
        if (pageItems.length > 0 && !selectedLactationIdMilkYield) {
            const firstRow = lactationsContainerEl.querySelector('tbody tr[data-id]');
            if (firstRow) firstRow.click();
        } else if (pageItems.length > 0 && selectedLactationIdMilkYield) {
            // Reselect if current selection is still valid
            const selectedRow = lactationsContainerEl.querySelector(`tbody tr[data-id='${selectedLactationIdMilkYield}']`);
            if (selectedRow) {
                selectedRow.classList.add('selected'); // Ensure it's visually selected
            } else { // Previously selected is gone, select first if any
                const firstRow = lactationsContainerEl.querySelector('tbody tr[data-id]');
                if (firstRow) firstRow.click();
            }
        }
    }
    updateLactationsPaginationControls();
}

function renderLactationsTablePage() {
    const t = passedI18nMilkYield.t;
    lactationsContainerEl.innerHTML = `
        <table id="lactations-table" class="modern-table">
            <thead>
                <tr>
                    <th data-column-key="BaslangicTarihi" class="sortable">${t('label_laktasyon_baslangic')}</th>
                    <th data-column-key="BitisTarihi" class="sortable">${t('label_laktasyon_bitis')}</th>
                    <th class="col-actions">${t('label_actions')}</th>
                </tr>
                <tr class="filter-row">
                    <th><input type="date" id="filter-lactation-start-date" class="filter-input"></th>
                    <th><input type="date" id="filter-lactation-end-date" class="filter-input"></th>
                    <th></th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
        <div id="lactations-pagination-controls" class="pagination-controls">
            <button id="prev-page-lactations" disabled>&laquo; ${t('btn_previous')}</button>
            <span id="page-info-lactations"></span>
            <button id="next-page-lactations" disabled>&raquo; ${t('btn_next')}</button>
        </div>
    `;

    setupTableSortingMilkYield('#lactations-table', ['BaslangicTarihi', 'BitisTarihi'], 'lactation', applyFiltersAndSortLactations);
    setupFilterListenersMilkYield('lactation', applyFiltersAndSortLactations);
    setupPaginationListenersMilkYield('lactation', renderLactationsTablePage, updateLactationsPaginationControls, () => currentPageLactations, val => currentPageLactations = val, itemsPerPageLactations, () => currentLactationsForAnimal);

    // Reset filters and prepare data for display
    currentLactationsForAnimal = [...allLactationsForAnimal];
    currentPageLactations = 1;

    // Update table body and pagination
    updateLactationsTableBody();
    updateLactationsPaginationControls();
    
    // Initialize table animations
    setTimeout(() => {
        TableAnimations.initializeTable('#lactations-table');
    }, 100);
}

function updateLactationsPaginationControls() {
    const t = passedI18nMilkYield.t;
    const pageInfoEl = lactationsContainerEl.querySelector('#page-info-lactations');
    const prevBtn = lactationsContainerEl.querySelector('#prev-page-lactations');
    const nextBtn = lactationsContainerEl.querySelector('#next-page-lactations');

    const totalItems = currentLactationsForAnimal.length;
    const totalPages = Math.ceil(totalItems / itemsPerPageLactations) || 1;
    if(pageInfoEl) pageInfoEl.textContent = t('page_info_text', { currentPage: currentPageLactations, totalPages, totalItems });
    if(prevBtn) prevBtn.disabled = currentPageLactations === 1;
    if(nextBtn) nextBtn.disabled = currentPageLactations === totalPages;
}


// --- MILKING DATA SPECIFIC FUNCTIONS ---
function applyFiltersAndSortMilkingData() {
    currentMilkingDataForLactation = [...allMilkingDataForLactation];
    const t = passedI18nMilkYield.t;

    const tarihFilter = milkingContainerEl.querySelector('#filter-milking-tarih')?.value;
    const saatFilter = milkingContainerEl.querySelector('#filter-milking-saat')?.value.trim().toLowerCase();
    const miktarMinFilter = milkingContainerEl.querySelector('#filter-milking-miktar-min')?.value;
    const miktarMaxFilter = milkingContainerEl.querySelector('#filter-milking-miktar-max')?.value;

    if (tarihFilter) currentMilkingDataForLactation = currentMilkingDataForLactation.filter(m => m.Tarih === tarihFilter);
    if (saatFilter) currentMilkingDataForLactation = currentMilkingDataForLactation.filter(m => (m.Saat || '').toLowerCase().includes(saatFilter));
    if (miktarMinFilter) currentMilkingDataForLactation = currentMilkingDataForLactation.filter(m => m.Miktar >= parseFloat(miktarMinFilter));
    if (miktarMaxFilter) currentMilkingDataForLactation = currentMilkingDataForLactation.filter(m => m.Miktar <= parseFloat(miktarMaxFilter));

    if (sortColumnMilking) {
        currentMilkingDataForLactation = sortMilkYieldList(currentMilkingDataForLactation, sortColumnMilking, sortDirectionMilking, 'milking');
    }
    currentPageMilking = 1;

    // Only update table body, not the entire table structure
    updateMilkingDataTableBody();
    updateMilkingPaginationControls();
}

function updateMilkingDataTableBody() {
    const t = passedI18nMilkYield.t;
    const tableBody = milkingContainerEl.querySelector('#milking-table tbody');
    if (!tableBody) return;

    const totalPages = Math.ceil(currentMilkingDataForLactation.length / itemsPerPageMilking);
    currentPageMilking = Math.max(1, Math.min(currentPageMilking, totalPages || 1));
    const startIndex = (currentPageMilking - 1) * itemsPerPageMilking;
    const endIndex = startIndex + itemsPerPageMilking;
    const pageItems = currentMilkingDataForLactation.slice(startIndex, endIndex);

    // Clear existing rows
    tableBody.innerHTML = '';

    if (pageItems.length === 0) {
        tableBody.innerHTML = `<tr><td colspan="6"><div class="empty-state-container">${passedDefaultAvatarSVGMilkYield(48)}<p>${t('no_records_found')}</p></div></td></tr>`;
    } else {
        pageItems.forEach(m => {
            const row = tableBody.insertRow();
            row.innerHTML = `
                <td class="col-date">${m.Tarih || ''}</td>
                <td>${m.Saat || ''}</td>
                <td>${m.Miktar || ''} L</td>
                <td>${m.YagOrani || ''}%</td>
                <td>${m.ProteinOrani || ''}%</td>
                <td class="col-actions">
                    ${IconSystem.createActionsWrapper(m.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
                </td>`;
            row.querySelector('.edit-btn').onclick = () => openMilkingModal(m);
            row.querySelector('.delete-btn').onclick = async () => {
                const confirmed = await window.toast.confirm(t('confirm_delete_milking'), {
                    confirmText: t('toast_confirm'),
                    cancelText: t('toast_cancel')
                });

                if (confirmed) {
                    try {
                        await window.api.invoke('sagimverileri:delete', m.Id);
                        await loadAndRenderActiveTabDataMilkYield();
                        window.toast.success(t('toast_success_delete'));
                    } catch (error) {
                        console.error('Error deleting milking record:', error);
                        window.toast.error(t('toast_error_delete') + ': ' + error.message);
                    }
                }
            };
        });
    }
}

function renderMilkingDataTablePage() {
    const t = passedI18nMilkYield.t;
    milkingContainerEl.innerHTML = `
        <table id="milking-table" class="modern-table">
            <thead>
                <tr>
                    <th data-column-key="Tarih" class="sortable">${t('label_sagim_tarih')}</th>
                    <th data-column-key="Saat" class="sortable">${t('label_sagim_saat')}</th>
                    <th data-column-key="Miktar" class="sortable">${t('label_sagim_miktar')}</th>
                    <th data-column-key="YagOrani" class="sortable">${t('label_yag_orani')}</th>
                    <th data-column-key="ProteinOrani" class="sortable">${t('label_protein_orani')}</th>
                    <th class="col-actions">${t('label_actions')}</th>
                </tr>
                <tr class="filter-row">
                    <th><input type="date" id="filter-milking-tarih" class="filter-input"></th>
                    <th><input type="text" id="filter-milking-saat" class="filter-input" placeholder="${t('label_sagim_saat')}"></th>
                    <th>
                        <input type="number" id="filter-milking-miktar-min" class="filter-input" placeholder="${t('label_min')}">
                        <input type="number" id="filter-milking-miktar-max" class="filter-input" placeholder="${t('label_max')}">
                    </th>
                    <th></th> <!-- Yag Orani Filter -->
                    <th></th> <!-- Protein Orani Filter -->
                    <th></th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
        <div id="milking-pagination-controls" class="pagination-controls">
            <button id="prev-page-milking" disabled>&laquo; ${t('btn_previous')}</button>
            <span id="page-info-milking"></span>
            <button id="next-page-milking" disabled>&raquo; ${t('btn_next')}</button>
        </div>
    `;

    const tableBody = milkingContainerEl.querySelector('#milking-table tbody');
    const totalPages = Math.ceil(currentMilkingDataForLactation.length / itemsPerPageMilking);
    currentPageMilking = Math.max(1, Math.min(currentPageMilking, totalPages || 1));
    const startIndex = (currentPageMilking - 1) * itemsPerPageMilking;
    const endIndex = startIndex + itemsPerPageMilking;
    const pageItems = currentMilkingDataForLactation.slice(startIndex, endIndex);

    if (pageItems.length === 0) {
        tableBody.innerHTML = `<tr><td colspan="6"><div class="empty-state-container"><div class="empty-state-icon">${passedDefaultAvatarSVGMilkYield(48)}</div><p>${t('no_records_found')}</p></div></td></tr>`;
    } else {
        pageItems.forEach(m => {
            const row = tableBody.insertRow();
            row.innerHTML = `
                <td class="col-date">${m.Tarih || ''}</td>
                <td>${m.Saat || ''}</td>
                <td>${m.Miktar !== null ? BadgeSystem.createMilkYieldBadge(m.Miktar, 20, { dailyMilk: t('label_sagim_miktar') || 'Milk Amount' }) : ''}</td>
                <td>${m.YagOrani !== null ? m.YagOrani.toFixed(1) : ''}</td>
                <td>${m.ProteinOrani !== null ? m.ProteinOrani.toFixed(1) : ''}</td>
                <td class="col-actions">
                    ${IconSystem.createActionsWrapper(m.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
                </td>`;
            row.querySelector('.edit-btn').onclick = () => openMilkingModal(m);
            row.querySelector('.delete-btn').onclick = async () => {
                const confirmed = await window.toast.confirm(t('confirm_delete_milking'), {
                    confirmText: t('toast_confirm'),
                    cancelText: t('toast_cancel')
                });

                if (confirmed) {
                    try {
                        await window.api.invoke('sagimverileri:delete', m.Id);
                        allMilkingDataForLactation = await window.api.invoke('sagimverileri:list', selectedLactationIdMilkYield);
                        applyFiltersAndSortMilkingData();
                        window.toast.success(t('toast_success_delete'));
                    } catch (error) {
                        console.error('Error deleting milking record:', error);
                        window.toast.error(t('toast_error_delete') + ': ' + error.message);
                    }
                }
            };
        });
    }
    setupTableSortingMilkYield('#milking-table', ['Tarih', 'Saat', 'Miktar', 'YagOrani', 'ProteinOrani'], 'milking', applyFiltersAndSortMilkingData);
    setupFilterListenersMilkYield('milking', applyFiltersAndSortMilkingData);
    setupPaginationListenersMilkYield('milking', renderMilkingDataTablePage, updateMilkingPaginationControls, () => currentPageMilking, val => currentPageMilking = val, itemsPerPageMilking, () => currentMilkingDataForLactation);
    updateMilkingPaginationControls();
    
    // Initialize table animations
    setTimeout(() => {
        TableAnimations.initializeTable('#milking-table');
    }, 100);
}

function updateMilkingPaginationControls() {
    const t = passedI18nMilkYield.t;
    const pageInfoEl = milkingContainerEl.querySelector('#page-info-milking');
    const prevBtn = milkingContainerEl.querySelector('#prev-page-milking');
    const nextBtn = milkingContainerEl.querySelector('#next-page-milking');

    const totalItems = currentMilkingDataForLactation.length;
    const totalPages = Math.ceil(totalItems / itemsPerPageMilking) || 1;
    if(pageInfoEl) pageInfoEl.textContent = t('page_info_text', { currentPage: currentPageMilking, totalPages, totalItems });
    if(prevBtn) prevBtn.disabled = currentPageMilking === 1;
    if(nextBtn) nextBtn.disabled = currentPageMilking === totalPages;
}


// --- MODAL HANDLING ---
function initializeLactationModal() {
    lactationModal = document.getElementById('lactation-modal');
    lactationForm = document.getElementById('lactation-form');
    lactationModalTitle = document.getElementById('lactation-modal-title');
    if (lactationModal) {
        lactationModal.querySelector('.close-btn').onclick = () => lactationModal.classList.add('hidden');
        lactationModal.onclick = (e) => { if (e.target === lactationModal) lactationModal.classList.add('hidden'); };
        lactationForm.onsubmit = submitLactationForm;
    }
}

function initializeMilkingModal() {
    milkingModal = document.getElementById('milking-modal');
    milkingForm = document.getElementById('milking-form');
    milkingModalTitle = document.getElementById('milking-modal-title');
     if (milkingModal) {
        milkingModal.querySelector('.close-btn').onclick = () => milkingModal.classList.add('hidden');
        milkingModal.onclick = (e) => { if (e.target === milkingModal) milkingModal.classList.add('hidden'); };
        milkingForm.onsubmit = submitMilkingForm;
    }
}

function openLactationModal(record = null) {
    const t = passedI18nMilkYield.t;
    editingLactationRecordId = record ? record.Id : null;
    lactationModal.classList.remove('hidden');
    lactationForm.reset();
    const selectedAnimalId = animalSelectElMilkYield.value;

    lactationForm.elements['HayvanId'].value = selectedAnimalId; // Always set for add/edit context
    if (record) {
        lactationModalTitle.textContent = t('lactation_modal_title_edit');
        lactationForm.elements['Id'].value = record.Id; // Set ID for editing
        lactationForm.elements['BaslangicTarihi'].value = record.BaslangicTarihi || '';
        lactationForm.elements['BitisTarihi'].value = record.BitisTarihi || '';
        lactationForm.elements['GebelikId'].value = record.GebelikId || ''; // Set GebelikId for editing
    } else {
        lactationModalTitle.textContent = t('lactation_modal_title_add');
        lactationForm.elements['Id'].value = ''; // Clear ID for adding
        lactationForm.elements['GebelikId'].value = ''; // Clear GebelikId for adding
        if (!selectedAnimalId) {
            window.toast.warning(t('please_select_animal_for_lactation'));
            lactationModal.classList.add('hidden');
            return;
        }
    }
}

async function submitLactationForm(e) {
    e.preventDefault();
    const t = passedI18nMilkYield.t;

    // Validate form before submission
    const isValid = await validateModalForm('lactation-form');
    if (!isValid) {
        return; // Stop submission if validation fails
    }

    const formData = Object.fromEntries(new FormData(lactationForm).entries());
    formData.HayvanId = parseInt(formData.HayvanId); // Ensure HayvanId is integer
    if (!formData.BaslangicTarihi) {
        window.toast.warning(t('start_date_required_for_lactation'));
        return;
    }
    if (formData.BitisTarihi === '') formData.BitisTarihi = null;

    // Handle GebelikId - set to null if empty to avoid foreign key constraint issues
    if (formData.GebelikId === '' || formData.GebelikId === undefined) {
        formData.GebelikId = null;
    } else {
        formData.GebelikId = parseInt(formData.GebelikId);
    }

    try {
        if (editingLactationRecordId) {
            formData.Id = parseInt(editingLactationRecordId);
            await window.api.invoke('laktasyonlar:update', formData);
            window.toast.success(t('toast_success_update'));
        } else {
            delete formData.Id; // Ensure no Id for add
            await window.api.invoke('laktasyonlar:add', formData);
            window.toast.success(t('toast_success_save'));
        }
        lactationModal.classList.add('hidden');
        editingLactationRecordId = null;
        await loadAndRenderActiveTabDataMilkYield();
        // Refresh recent activities
        if (milkRecentActivities) {
            milkRecentActivities.refresh();
        }
    } catch (error) {
        console.error("Error saving lactation:", error);
        window.toast.error(t('error_saving_lactation_data') + ': ' + (error.message || ''));
    }
}

function openMilkingModal(record = null) {
    const t = passedI18nMilkYield.t;
    editingMilkingRecordId = record ? record.Id : null;
    milkingModal.classList.remove('hidden');
    milkingForm.reset();

    milkingForm.elements['LaktasyonId'].value = selectedLactationIdMilkYield; // Crucial for add/edit context

    if (record) {
        milkingModalTitle.textContent = t('milking_modal_title_edit');
        milkingForm.elements['Id'].value = record.Id;
        milkingForm.elements['Tarih'].value = record.Tarih || '';
        milkingForm.elements['Saat'].value = record.Saat || '';
        milkingForm.elements['Miktar'].value = record.Miktar || '';
        milkingForm.elements['YagOrani'].value = record.YagOrani || '';
        milkingForm.elements['ProteinOrani'].value = record.ProteinOrani || '';
    } else {
        milkingModalTitle.textContent = t('milking_modal_title_add');
        milkingForm.elements['Id'].value = '';
        milkingForm.elements['Tarih'].valueAsDate = new Date(); // Default to today
    }
}

async function submitMilkingForm(e) {
    e.preventDefault();
    const t = passedI18nMilkYield.t;

    // Validate form before submission
    const isValid = await validateModalForm('milking-form');
    if (!isValid) {
        return; // Stop submission if validation fails
    }

    const formData = Object.fromEntries(new FormData(milkingForm).entries());
    formData.LaktasyonId = parseInt(formData.LaktasyonId);

    if (formData.Miktar === '' || isNaN(parseFloat(formData.Miktar)) || parseFloat(formData.Miktar) <= 0) {
        window.toast.warning(t('miktar_positive_error'));
        return;
    }
    formData.Miktar = parseFloat(formData.Miktar);
    formData.YagOrani = formData.YagOrani === '' ? null : parseFloat(formData.YagOrani);
    formData.ProteinOrani = formData.ProteinOrani === '' ? null : parseFloat(formData.ProteinOrani);
    if (!formData.Tarih || !formData.Saat) {
        window.toast.warning(t('date_time_required_error'));
        return;
    }

    try {
        if (editingMilkingRecordId) {
            formData.Id = parseInt(editingMilkingRecordId);
            await window.api.invoke('sagimverileri:update', formData);
            window.toast.success(t('toast_success_update'));
        } else {
            delete formData.Id;
            await window.api.invoke('sagimverileri:add', formData);
            window.toast.success(t('toast_success_save'));
        }
        milkingModal.classList.add('hidden');
        editingMilkingRecordId = null;
        allMilkingDataForLactation = await window.api.invoke('sagimverileri:list', selectedLactationIdMilkYield);
        applyFiltersAndSortMilkingData();
        // Refresh recent activities
        if (milkRecentActivities) {
            milkRecentActivities.refresh();
        }
    } catch (error) {
        console.error("Error saving milking data:", error);
        window.toast.error(t('error_saving_milking_data') + ': ' + (error.message || ''));
    }
}


// --- COMMON SORTING & EVENT LISTENER SETUP ---
function sortMilkYieldList(list, column, direction, type) { // type: 'lactation' or 'milking'
    return [...list].sort((a, b) => {
        let valA = a[column];
        let valB = b[column];
        const dateColumns = ['BaslangicTarihi', 'BitisTarihi', 'Tarih'];
        const numericColumns = ['Miktar', 'YagOrani', 'ProteinOrani'];

        if (dateColumns.includes(column)) {
            valA = valA ? new Date(valA) : null;
            valB = valB ? new Date(valB) : null;
            if (valA === null && valB === null) return 0;
            if (valA === null) return 1 * direction; // Nulls last
            if (valB === null) return -1 * direction;
             return (valA - valB) * direction;
        } else if (numericColumns.includes(column)) {
            valA = valA !== null ? parseFloat(valA) : -Infinity; // Treat null as very small for sorting
            valB = valB !== null ? parseFloat(valB) : -Infinity;
             return (valA - valB) * direction;
        } else {
            valA = (valA || '').toString().toLowerCase();
            valB = (valB || '').toString().toLowerCase();
            return valA.localeCompare(valB) * direction;
        }
    });
}

function setupTableSortingMilkYield(tableSelector, columnKeys, type, applyFunc) {
    const tableContainer = type === 'lactation' ? lactationsContainerEl : milkingContainerEl;
    const ths = tableContainer.querySelectorAll(`${tableSelector} thead th[data-column-key]`);

    ths.forEach(th => {
        const columnKey = th.dataset.columnKey;
        if (!columnKey) return;
        th.classList.add('sortable');

        // Add current sort indicator
        let currentSortCol = type === 'lactation' ? sortColumnLactations : sortColumnMilking;
        let currentSortDir = type === 'lactation' ? sortDirectionLactations : sortDirectionMilking;

        // Set initial sorting state
        if (columnKey === currentSortCol) {
            th.classList.add(currentSortDir === 1 ? 'sorted-asc' : 'sorted-desc');
        }

        th.onclick = () => {
            // Clear all sorting classes first
            ths.forEach(headerTh => {
                headerTh.classList.remove('sorted-asc', 'sorted-desc');
                headerTh.querySelector('.sort-arrow')?.remove();
            });

            if (type === 'lactation') {
                if (sortColumnLactations === columnKey) sortDirectionLactations *= -1;
                else { sortColumnLactations = columnKey; sortDirectionLactations = 1; }
                th.classList.add(sortDirectionLactations === 1 ? 'sorted-asc' : 'sorted-desc');
            } else { // milking
                if (sortColumnMilking === columnKey) sortDirectionMilking *= -1;
                else { sortColumnMilking = columnKey; sortDirectionMilking = 1; }
                th.classList.add(sortDirectionMilking === 1 ? 'sorted-asc' : 'sorted-desc');
            }
            applyFunc(); // This will re-render the table, which will re-run this setup
        };
    });
}

function setupFilterListenersMilkYield(type, applyFunc) {
    const container = type === 'lactation' ? lactationsContainerEl : milkingContainerEl;
    let filterSelectors = [];
    if (type === 'lactation') {
        filterSelectors = ['#filter-lactation-start-date', '#filter-lactation-end-date'];
    } else { // milking
        filterSelectors = ['#filter-milking-tarih', '#filter-milking-saat', '#filter-milking-miktar-min', '#filter-milking-miktar-max'];
    }
    filterSelectors.forEach(selector => {
        const inputElement = container.querySelector(selector);
        if (inputElement) {
            // Remove any existing listeners first
            inputElement.removeEventListener('input', applyFunc);
            inputElement.removeEventListener('change', applyFunc);

            // Add new listeners using addEventListener
            inputElement.addEventListener('input', applyFunc);
            if (inputElement.type === 'date') {
                inputElement.addEventListener('change', applyFunc);
            }
        }
    });
}

function setupPaginationListenersMilkYield(type, renderFunc, updateControlsFunc, getCurrentPageFunc, setCurrentPageFunc, itemsPerPage, getCurrentDataArrayFunc) {
    const container = type === 'lactation' ? lactationsContainerEl : milkingContainerEl;
    const prevBtn = container.querySelector(`#prev-page-${type}s`); // e.g. prev-page-lactations
    const nextBtn = container.querySelector(`#next-page-${type}s`);

    if (prevBtn) prevBtn.onclick = () => {
        if (getCurrentPageFunc() > 1) {
            setCurrentPageFunc(getCurrentPageFunc() - 1);
            renderFunc(); // This will re-render table and call updateControlsFunc via its own flow
        }
    };
    if (nextBtn) nextBtn.onclick = () => {
        const totalPages = Math.ceil(getCurrentDataArrayFunc().length / itemsPerPage);
        if (getCurrentPageFunc() < totalPages) {
            setCurrentPageFunc(getCurrentPageFunc() + 1);
            renderFunc();
        }
    };
}

// --- BULK MILKING MODAL FUNCTIONS ---
let bulkMilkingModal, bulkMilkingForm, bulkMilkingModalTitle;

function initializeBulkMilkingModal() {
    bulkMilkingModal = document.getElementById('bulk-milking-modal');
    bulkMilkingForm = document.getElementById('bulk-milking-form');
    bulkMilkingModalTitle = document.getElementById('bulk-milking-modal-title');

    if (bulkMilkingModal) {
        bulkMilkingModal.querySelector('.close-btn').onclick = () => bulkMilkingModal.classList.add('hidden');
        bulkMilkingModal.onclick = (e) => { if (e.target === bulkMilkingModal) bulkMilkingModal.classList.add('hidden'); };
        bulkMilkingForm.onsubmit = submitBulkMilkingForm;
    }
}

async function openBulkMilkingModal() {
    const t = passedI18nMilkYield.t;

    try {
        // Get animals with active lactation
        const lactatingAnimals = await window.api.invoke('hayvanlar:getActiveLactating');

        if (lactatingAnimals.length === 0) {
            window.toast.warning(t('no_lactating_animals') || 'Aktif laktasyonu olan hayvan bulunamadı.');
            return;
        }

        bulkMilkingModal.classList.remove('hidden');
        bulkMilkingForm.reset();

        // Set default date and time
        const now = new Date();
        const dateInput = document.getElementById('bulk-milking-date');
        const timeInput = document.getElementById('bulk-milking-time');

        dateInput.valueAsDate = now;
        timeInput.value = now.toTimeString().slice(0, 5);

        // Populate animals list
        populateBulkMilkingAnimals(lactatingAnimals);

    } catch (error) {
        console.error('Error opening bulk milking modal:', error);
        window.toast.error(t('error_loading_animals') || 'Hayvanlar yüklenirken hata oluştu.');
    }
}

function populateBulkMilkingAnimals(animals) {
    const animalsList = document.getElementById('bulk-milking-animals-list');
    animalsList.innerHTML = '';

    animals.forEach(animal => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="animal-info">
                <strong>${animal.KupeNo}</strong>
                <input type="hidden" class="animal-id" value="${animal.HayvanId}">
                <input type="hidden" class="lactation-id" value="${animal.LaktasyonId}">
            </td>
            <td class="animal-name">${animal.Isim || '-'}</td>
            <td>
                <input type="number" class="milking-amount" step="0.1" min="0" placeholder="0.0">
            </td>
            <td>
                <input type="number" class="fat-percentage" step="0.1" min="0" max="100" placeholder="0.0">
            </td>
            <td>
                <input type="number" class="protein-percentage" step="0.1" min="0" max="100" placeholder="0.0">
            </td>
        `;
        animalsList.appendChild(row);
    });
}

async function submitBulkMilkingForm(e) {
    e.preventDefault();
    const t = passedI18nMilkYield.t;

    try {
        const dateInput = document.getElementById('bulk-milking-date');
        const timeInput = document.getElementById('bulk-milking-time');

        if (!dateInput.value || !timeInput.value) {
            window.toast.warning(t('please_fill_date_time') || 'Lütfen tarih ve saat alanlarını doldurun.');
            return;
        }

        const milkingData = [];
        const rows = document.querySelectorAll('#bulk-milking-animals-list tr');

        rows.forEach(row => {
            const amount = row.querySelector('.milking-amount').value;

            // Only include animals with milking amount
            if (amount && parseFloat(amount) > 0) {
                const data = {
                    LaktasyonId: parseInt(row.querySelector('.lactation-id').value),
                    Miktar: parseFloat(amount),
                    Tarih: dateInput.value,
                    Saat: timeInput.value,
                    YagOrani: row.querySelector('.fat-percentage').value ? parseFloat(row.querySelector('.fat-percentage').value) : null,
                    ProteinOrani: row.querySelector('.protein-percentage').value ? parseFloat(row.querySelector('.protein-percentage').value) : null
                };
                milkingData.push(data);
            }
        });

        if (milkingData.length === 0) {
            window.toast.warning(t('no_milking_data_entered') || 'Hiçbir hayvan için sağım verisi girilmedi.');
            return;
        }

        // Save bulk milking data
        const result = await window.api.invoke('sagimverileri:bulkAdd', milkingData);

        if (result.success) {
            window.toast.success(t('bulk_milking_saved_success') || `${result.insertedCount} hayvan için sağım verisi başarıyla kaydedildi.`);
            bulkMilkingModal.classList.add('hidden');

            // Refresh recent activities if visible
            if (milkRecentActivities) {
                milkRecentActivities.refresh();
            }
        } else {
            window.toast.error(t('error_saving_bulk_milking') || 'Toplu sağım verisi kaydedilirken hata oluştu.');
        }

    } catch (error) {
        console.error('Error saving bulk milking data:', error);
        window.toast.error(t('error_saving_bulk_milking') || 'Toplu sağım verisi kaydedilirken hata oluştu.');
    }
}

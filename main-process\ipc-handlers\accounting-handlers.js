const { ipcMain } = require('electron');
const accountingService = require('../services/accounting-service');

function setupAccountingHandlers() {
  // --- <PERSON>r<PERSON>nler IPC Handlers ---
  ipcMain.handle('products:list', async () => {
    try {
      return await accountingService.getAllProducts();
    } catch (error) {
      console.error('IPC Error - products:list:', error);
      throw error; // Renderer tarafında hatayı yakalamak için yeniden fırlat
    }
  });

  ipcMain.handle('products:get', async (event, id) => {
    try {
      return await accountingService.getProductById(id);
    } catch (error) {
      console.error(`IPC Error - products:get (id: ${id}):`, error);
      throw error;
    }
  });

  ipcMain.handle('products:add', async (event, product) => {
    try {
      return await accountingService.addProduct(product);
    } catch (error) {
      console.error('IPC Error - products:add:', error, product);
      throw error;
    }
  });

  ipcMain.handle('products:update', async (event, args) => {
    try {
      const { id, data: product } = args;
      if (!product) {
        console.error('IPC Error - products:update - product (data) is undefined in args:', args);
      }
      return await accountingService.updateProduct(id, product);
    } catch (error) {
      console.error(`IPC Error - products:update (args: ${args ? JSON.stringify(args) : 'undefined'}):`, error, args?.data);
      throw error;
    }
  });

  ipcMain.handle('products:delete', async (event, id) => {
    try {
      return await accountingService.deleteProduct(id);
    } catch (error) {
      console.error(`IPC Error - products:delete (id: ${id}):`, error);
      throw error;
    }
  });

  // --- Accounts IPC Handlers ---
  ipcMain.handle('accounts:list', async (event, { filters, sortBy } = {}) => {
    try {
      return await accountingService.getAllAccounts({ filters, sortBy });
    } catch (error) {
      console.error('IPC Error - accounts:list:', error);
      throw error;
    }
  });

  ipcMain.handle('accounts:get', async (event, id) => {
    try {
      return await accountingService.getAccountById(id);
    } catch (error) {
      console.error(`IPC Error - accounts:get (id: ${id}):`, error);
      throw error;
    }
  });

  ipcMain.handle('accounts:get-details', async (event, accountId) => {
    try {
      return await accountingService.getAccountDetails(accountId);
    } catch (error) {
      console.error(`IPC Error - accounts:get-details (accountId: ${accountId}):`, error);
      throw error;
    }
  });

  ipcMain.handle('accounts:add', async (event, account) => {
    try {
      return await accountingService.addAccount(account);
    } catch (error) {
      console.error('IPC Error - accounts:add:', error, account);
      throw error;
    }
  });

  ipcMain.handle('accounts:update', async (event, args) => {
    try {
      const { id, data: account } = args;
      if (!account) {
        console.error('IPC Error - accounts:update - account (data) is undefined in args:', args);
      }
      return await accountingService.updateAccount(id, account);
    } catch (error) {
      console.error(`IPC Error - accounts:update (args: ${args ? JSON.stringify(args) : 'undefined'}):`, error, args?.data);
      throw error;
    }
  });

  ipcMain.handle('accounts:delete', async (event, id) => {
    try {
      return await accountingService.deleteAccount(id);
    } catch (error) {
      console.error(`IPC Error - accounts:delete (id: ${id}):`, error);
      throw error;
    }
  });

  // --- Purchases IPC Handlers ---
  ipcMain.handle('purchases:list', async (event, { startDate, endDate } = {}) => {
    try {
      return await accountingService.getAllPurchases({ startDate, endDate });
    } catch (error) {
      console.error('IPC Error - purchases:list:', error);
      throw error;
    }
  });

  ipcMain.handle('purchases:add', async (event, purchase) => {
    try {
      return await accountingService.addPurchase(purchase);
    } catch (error) {
      console.error('IPC Error - purchases:add:', error, purchase);
      throw error;
    }
  });

  ipcMain.handle('purchases:update', async (event, args) => {
    try {
      const { id, data: purchase } = args;
      if (!purchase) {
        console.error('IPC Error - purchases:update - purchase (data) is undefined in args:', args);
      }
      return await accountingService.updatePurchase(id, purchase);
    } catch (error) {
      console.error(`IPC Error - purchases:update (args: ${args ? JSON.stringify(args) : 'undefined'}):`, error, args?.data);
      throw error;
    }
  });

  ipcMain.handle('purchases:delete', async (event, id) => {
    try {
      return await accountingService.deletePurchase(id);
    } catch (error) {
      console.error(`IPC Error - purchases:delete (id: ${id}):`, error);
      throw error;
    }
  });

  // --- Sales IPC Handlers ---
  ipcMain.handle('sales:list', async (event, { startDate, endDate } = {}) => {
    try {
      return await accountingService.getAllSales({ startDate, endDate });
    } catch (error) {
      console.error('IPC Error - sales:list:', error);
      throw error;
    }
  });

  ipcMain.handle('sales:add', async (event, sale) => {
    try {
      return await accountingService.addSale(sale);
    } catch (error) {
      console.error('IPC Error - sales:add:', error, sale);
      throw error;
    }
  });

  ipcMain.handle('sales:update', async (event, args) => {
    try {
      const { id, data: sale } = args;
      if (!sale) {
        console.error('IPC Error - sales:update - sale (data) is undefined in args:', args);
      }
      return await accountingService.updateSale(id, sale);
    } catch (error) {
      console.error(`IPC Error - sales:update (args: ${args ? JSON.stringify(args) : 'undefined'}):`, error, args?.data);
      throw error;
    }
  });

  ipcMain.handle('sales:delete', async (event, id) => {
    try {
      return await accountingService.deleteSale(id);
    } catch (error) {
      console.error(`IPC Error - sales:delete (id: ${id}):`, error);
      throw error;
    }
  });

  // --- Payments IPC Handlers ---
  ipcMain.handle('payments:list', async (event, { startDate, endDate } = {}) => {
    try {
      return await accountingService.getAllPayments({ startDate, endDate });
    } catch (error) {
      console.error('IPC Error - payments:list:', error);
      throw error;
    }
  });

  ipcMain.handle('payments:add', async (event, payment) => {
    try {
      return await accountingService.addPayment(payment);
    } catch (error) {
      console.error('IPC Error - payments:add:', error, payment);
      throw error;
    }
  });

  ipcMain.handle('payments:update', async (event, args) => {
    try {
      const { id, data: payment } = args;
      if (!payment) {
        console.error('IPC Error - payments:update - payment (data) is undefined in args:', args);
      }
      return await accountingService.updatePayment(id, payment);
    } catch (error) {
      console.error(`IPC Error - payments:update (args: ${args ? JSON.stringify(args) : 'undefined'}):`, error, args?.data);
      throw error;
    }
  });

  ipcMain.handle('payments:delete', async (event, id) => {
    try {
      return await accountingService.deletePayment(id);
    } catch (error) {
      console.error(`IPC Error - payments:delete (id: ${id}):`, error);
      throw error;
    }
  });

  // --- Reports IPC Handlers ---
  ipcMain.handle('accounting:debtor-accounts-report', async (event, { startDate, endDate }) => {
    return await accountingService.getDebtorAccounts(startDate, endDate);
  });
  ipcMain.handle('accounting:creditor-accounts-report', async (event, { startDate, endDate }) => {
    return await accountingService.getCreditorAccounts(startDate, endDate);
  });
  ipcMain.handle('accounting:income-expense-report', async (event, { startDate, endDate }) => {
    return await accountingService.getIncomeExpenseReport(startDate, endDate);
  });
  ipcMain.handle('accounting:accounts-net-balance-report', async (event, { startDate, endDate }) => {
    return await accountingService.getAccountsNetBalanceReport(startDate, endDate);
  });
  ipcMain.handle('accounting:account-statement', async (event, { accountId, startDate, endDate }) => {
    return await accountingService.getAccountStatement(accountId, startDate, endDate);
  });
  ipcMain.handle('accounting:monthly-net-profit-loss-trend', async (event, { monthCount }) => {
    return await accountingService.getMonthlyNetProfitLossTrend(monthCount);
  });
  ipcMain.handle('accounting:accounts-net-balance-dashboard', async () => {
    return await accountingService.getAccountsNetBalanceDashboard();
  });
  ipcMain.handle('accounting:detailed-financial-report', async (event, { startDate, endDate }) => {
    return await accountingService.getDetailedFinancialReport(startDate, endDate);
  });

  console.log('Accounting IPC handlers setup complete.');
}

module.exports = { setupAccountingHandlers };

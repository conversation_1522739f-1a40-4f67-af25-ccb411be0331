const { ipcMain } = require('electron');
const { getDb } = require('../services/database');

// Yem İşlemleri
ipcMain.handle('get-feeds', async (event, profilId) => {
  return new Promise((resolve, reject) => {
    try {
      console.log('Fetching feeds for profile ID:', profilId);

      const sql = `
        SELECT y.*, c<PERSON> as <PERSON><PERSON>kciAdi
        FROM Yemler y
        LEFT JOIN Cariler c ON y.TedarikciId = c.Id
        WHERE y.AktifMi = 1 AND (y.ProfilId = ? OR ? IS NULL)
        ORDER BY y.YemAdi
      `;

      const db = getDb();
      db.all(sql, [profilId, profilId], (err, rows) => {
        if (err) {
          console.error('Error fetching feeds:', err);
          reject(err);
        } else {
          console.log('Fetched feeds count:', rows ? rows.length : 0);
          resolve(rows || []);
        }
      });
    } catch (error) {
      console.error('Error in get-feeds handler:', error);
      reject(error);
    }
  });
});

console.log('Registering add-feed handler...');

ipcMain.handle('add-feed', async (event, feedData) => {
  console.log('=== ADD-FEED HANDLER CALLED ===');
  return new Promise((resolve, reject) => {
    try {
      console.log('Adding feed with data:', feedData);

      const sql = `
        INSERT INTO Yemler (
          ProfilId, YemAdi, YemTipi, Aciklama,
          KuruMadde, HamProtein, RDP, RUP, MetabolikEnerji, NEL, HamYag,
          Nisasta, Seker, Kul,
          NDF, ADF, Hemiseluloz, Seluloz, Lignin,
          Kalsiyum, Fosfor, Magnezyum, Potasyum, Sodyum, Klorur, sulfur,
          Demir, Cinko, Bakir, Manganez, Selenyum, Iyot, Kobalt, Molibden,
          VitaminA, VitaminD, VitaminE, VitaminK, Thiamin, Riboflavin, Niacin, Biotin, Folat, Cobalamin,
          Tanen, Fitat, Saponin, Oksalat,
          BirimFiyat, TedarikciId
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        feedData.profilId || null,
        feedData.yemAdi || '',
        feedData.yemTipi || null,
        feedData.aciklama || null,
        // Temel besin değerleri (restored RDP, RUP, Nisasta, Seker, Kul)
        feedData.kuruMadde || null,
        feedData.hamProtein || null,
        feedData.rdp || null,
        feedData.rup || null,
        feedData.metabolikEnerji || null,
        feedData.nel || null,
        feedData.hamYag || null,
        // Karbohidrat fraksiyonları
        feedData.nisasta || null,
        feedData.seker || null,
        feedData.kul || null,
        // Fiber fraksiyonları
        feedData.ndf || null,
        feedData.adf || null,
        feedData.hemiseluloz || null,
        feedData.seluloz || null,
        feedData.lignin || null,
        // Makro mineraller
        feedData.kalsiyum || null,
        feedData.fosfor || null,
        feedData.magnezyum || null,
        feedData.potasyum || null,
        feedData.sodyum || null,
        feedData.klorur || null,
        feedData.sulfur || null,
        // Mikro mineraller
        feedData.demir || null,
        feedData.cinko || null,
        feedData.bakir || null,
        feedData.manganez || null,
        feedData.selenyum || null,
        feedData.iyot || null,
        feedData.kobalt || null,
        feedData.molibden || null,
        // Vitaminler
        feedData.vitaminA || null,
        feedData.vitaminD || null,
        feedData.vitaminE || null,
        feedData.vitaminK || null,
        feedData.thiamin || null,
        feedData.riboflavin || null,
        feedData.niacin || null,
        feedData.biotin || null,
        feedData.folat || null,
        feedData.cobalamin || null,
        // Anti-besin faktörleri
        feedData.tanen || null,
        feedData.fitat || null,
        feedData.saponin || null,
        feedData.oksalat || null,
        // Ekonomik bilgiler
        feedData.birimFiyat || null,
        feedData.tedarikciId || null
      ];

      console.log('SQL placeholders count:', (sql.match(/\?/g) || []).length);
      console.log('Values array length:', values.length);

      const db = getDb();
      db.run(sql, values, function(err) {
        if (err) {
          console.error('Error adding feed:', err);
          reject(err);
        } else {
          console.log('Feed added successfully with ID:', this.lastID);
          resolve({ id: this.lastID, success: true });
        }
      });
    } catch (error) {
      console.error('Error in add-feed handler:', error);
      reject(error);
    }
  });
});

console.log('Registering update-feed handler...');

ipcMain.handle('update-feed', async (event, updateData) => {
  return new Promise((resolve, reject) => {
    try {
      console.log('Update-feed called with updateData:', updateData);
      console.log('updateData type:', typeof updateData);
      console.log('updateData keys:', updateData ? Object.keys(updateData) : 'N/A');

      if (!updateData || typeof updateData !== 'object') {
        reject(new Error(`Invalid updateData: ${updateData}`));
        return;
      }

      const feedId = updateData.feedId;
      const feedData = { ...updateData };
      delete feedData.feedId; // Remove feedId from feedData object

      console.log('Extracted feedId:', feedId);
      console.log('Extracted feedData.yemAdi:', feedData.yemAdi);

      const sql = `
        UPDATE Yemler SET
          YemAdi = ?, YemTipi = ?, Aciklama = ?,
          KuruMadde = ?, HamProtein = ?, RDP = ?, RUP = ?, MetabolikEnerji = ?, NEL = ?, HamYag = ?,
          Nisasta = ?, Seker = ?, Kul = ?,
          NDF = ?, ADF = ?, Hemiseluloz = ?, Seluloz = ?, Lignin = ?,
          Kalsiyum = ?, Fosfor = ?, Magnezyum = ?, Potasyum = ?, Sodyum = ?, Klorur = ?, sulfur = ?,
          Demir = ?, Cinko = ?, Bakir = ?, Manganez = ?, Selenyum = ?, Iyot = ?, Kobalt = ?, Molibden = ?,
          VitaminA = ?, VitaminD = ?, VitaminE = ?, VitaminK = ?, Thiamin = ?, Riboflavin = ?, Niacin = ?, Biotin = ?, Folat = ?, Cobalamin = ?,
          Tanen = ?, Fitat = ?, Saponin = ?, Oksalat = ?,
          BirimFiyat = ?, TedarikciId = ?,
          GuncellemeTarihi = CURRENT_TIMESTAMP
        WHERE Id = ? AND AktifMi = 1
      `;

      const values = [
        feedData.yemAdi || '',
        feedData.yemTipi || null,
        feedData.aciklama || null,
        // Temel besin değerleri (restored RDP, RUP, Nisasta, Seker, Kul)
        feedData.kuruMadde || null,
        feedData.hamProtein || null,
        feedData.rdp || null,
        feedData.rup || null,
        feedData.metabolikEnerji || null,
        feedData.nel || null,
        feedData.hamYag || null,
        // Karbohidrat fraksiyonları
        feedData.nisasta || null,
        feedData.seker || null,
        feedData.kul || null,
        // Fiber fraksiyonları
        feedData.ndf || null,
        feedData.adf || null,
        feedData.hemiseluloz || null,
        feedData.seluloz || null,
        feedData.lignin || null,
        // Makro mineraller
        feedData.kalsiyum || null,
        feedData.fosfor || null,
        feedData.magnezyum || null,
        feedData.potasyum || null,
        feedData.sodyum || null,
        feedData.klorur || null,
        feedData.sulfur || null,
        // Mikro mineraller
        feedData.demir || null,
        feedData.cinko || null,
        feedData.bakir || null,
        feedData.manganez || null,
        feedData.selenyum || null,
        feedData.iyot || null,
        feedData.kobalt || null,
        feedData.molibden || null,
        // Vitaminler
        feedData.vitaminA || null,
        feedData.vitaminD || null,
        feedData.vitaminE || null,
        feedData.vitaminK || null,
        feedData.thiamin || null,
        feedData.riboflavin || null,
        feedData.niacin || null,
        feedData.biotin || null,
        feedData.folat || null,
        feedData.cobalamin || null,
        // Anti-besin faktörleri
        feedData.tanen || null,
        feedData.fitat || null,
        feedData.saponin || null,
        feedData.oksalat || null,
        // Ekonomik bilgiler
        feedData.birimFiyat || null,
        feedData.tedarikciId || null,
        // WHERE clause parameter
        feedId
      ];

      console.log('SQL placeholders count:', (sql.match(/\?/g) || []).length);
      console.log('Values array length:', values.length);

      const db = getDb();
      db.run(sql, values, function(err) {
        if (err) {
          console.error('Error updating feed:', err);
          reject(err);
        } else {
          console.log('Feed updated successfully, changes:', this.changes);
          resolve({ success: true, changes: this.changes });
        }
      });
    } catch (error) {
      console.error('Error in update-feed handler:', error);
      reject(error);
    }
  });
});

ipcMain.handle('delete-feed', async (event, feedId) => {
  return new Promise((resolve, reject) => {
    try {
      console.log('Deleting feed ID:', feedId);

      const sql = `UPDATE Yemler SET AktifMi = 0, GuncellemeTarihi = CURRENT_TIMESTAMP WHERE Id = ? AND AktifMi = 1`;

      const db = getDb();
      db.run(sql, [feedId], function(err) {
        if (err) {
          console.error('Error deleting feed:', err);
          reject(err);
        } else {
          console.log('Feed deleted successfully, changes:', this.changes);
          if (this.changes === 0) {
            reject(new Error('Feed not found or already deleted'));
          } else {
            resolve({ success: true, changes: this.changes });
          }
        }
      });
    } catch (error) {
      console.error('Error in delete-feed handler:', error);
      reject(error);
    }
  });
});

// Rasyon İşlemleri
ipcMain.handle('get-rations', async (event, profilId) => {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT r.*, 
        COUNT(ry.Id) as YemSayisi,
        SUM(ry.Miktar) as ToplamMiktar
      FROM Rasyonlar r 
      LEFT JOIN RasyonYemleri ry ON r.Id = ry.RasyonId 
      WHERE r.ProfilId = ? AND r.AktifMi = 1 
      GROUP BY r.Id
      ORDER BY r.RasyonAdi
    `;

    const db = getDb();
    db.all(sql, [profilId], (err, rows) => {
      if (err) {
        console.error('Error fetching rations:', err);
        reject(err);
      } else {
        resolve(rows || []);
      }
    });
  });
});

ipcMain.handle('get-ration-details', async (event, rationId) => {
  return new Promise((resolve, reject) => {
    const rationSql = `SELECT * FROM Rasyonlar WHERE Id = ?`;
    const feedsSql = `
      SELECT ry.*, y.YemAdi, y.YemTipi, y.BirimFiyat
      FROM RasyonYemleri ry
      JOIN Yemler y ON ry.YemId = y.Id
      WHERE ry.RasyonId = ?
      ORDER BY ry.Sira
    `;
    const analysisSql = `SELECT * FROM RasyonAnalizleri WHERE RasyonId = ? ORDER BY HesaplamaTarihi DESC LIMIT 1`;

    const db = getDb();
    db.get(rationSql, [rationId], (err, ration) => {
      if (err) {
        reject(err);
        return;
      }
      
      db.all(feedsSql, [rationId], (err, feeds) => {
        if (err) {
          reject(err);
          return;
        }
        
        db.get(analysisSql, [rationId], (err, analysis) => {
          if (err) {
            reject(err);
            return;
          }
          
          resolve({
            ration: ration,
            feeds: feeds || [],
            analysis: analysis
          });
        });
      });
    });
  });
});

ipcMain.handle('save-ration', async (event, rationData) => {
  return new Promise((resolve, reject) => {
    const db = getDb();
    
    console.log('Saving ration with data:', rationData);
    
    db.serialize(() => {
      db.run('BEGIN TRANSACTION', (err) => {
        if (err) {
          console.error('Error starting transaction:', err);
          reject(err);
          return;
        }
        
        // Rasyon kaydet
        const rationSql = `
          INSERT INTO Rasyonlar (
            ProfilId, RasyonAdi, HayvanKategorisi, HedefCanlıAgirlik, HedefSutVerimi, 
            HedefSutYagOrani, GebelikDurumu, AktiviteSeviyesi, HedefKuruMadde, 
            HedefHamProtein, HedefMetabolikEnerji, HedefNEL, HedefKalsiyum, HedefFosfor, Notlar
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const rationValues = [
          rationData.profilId, rationData.rasyonAdi, rationData.hayvanKategorisi,
          rationData.hedefCanlıAgirlik, rationData.hedefSutVerimi, rationData.hedefSutYagOrani,
          rationData.gebelikDurumu, rationData.aktiviteSeviyesi, rationData.hedefKuruMadde,
          rationData.hedefHamProtein, rationData.hedefMetabolikEnerji, rationData.hedefNEL,
          rationData.hedefKalsiyum, rationData.hedefFosfor, rationData.notlar
        ];
        
        console.log('Inserting ration with values:', rationValues);
        
        db.run(rationSql, rationValues, function(err) {
          if (err) {
            console.error('Error inserting ration:', err);
            db.run('ROLLBACK');
            reject(err);
            return;
          }
          
          const rationId = this.lastID;
          console.log('Ration inserted with ID:', rationId);
          
          // Yemler kaydet
          if (rationData.yemler && rationData.yemler.length > 0) {
            const feedSql = `INSERT INTO RasyonYemleri (RasyonId, YemId, Miktar, Yuzde, Sira) VALUES (?, ?, ?, ?, ?)`;
            let completedFeeds = 0;
            const totalFeeds = rationData.yemler.length;
            
            console.log('Inserting', totalFeeds, 'feeds for ration');
            
            rationData.yemler.forEach((yem, index) => {
              const feedValues = [rationId, yem.yemId, yem.miktar, yem.yuzde, index + 1];
              console.log('Inserting feed:', feedValues);
              
              db.run(feedSql, feedValues, function(err) {
                if (err) {
                  console.error('Error inserting feed:', err);
                  db.run('ROLLBACK');
                  reject(err);
                  return;
                }
                
                completedFeeds++;
                console.log('Feed inserted, completed:', completedFeeds, 'of', totalFeeds);
                
                // Tüm yemler kaydedildiyse commit yap
                if (completedFeeds === totalFeeds) {
                  db.run('COMMIT', (err) => {
                    if (err) {
                      console.error('Error committing transaction:', err);
                      reject(err);
                    } else {
                      console.log('Ration saved successfully with ID:', rationId);
                      resolve({ id: rationId, success: true });
                    }
                  });
                }
              });
            });
          } else {
            // Yem yoksa direkt commit yap
            db.run('COMMIT', (err) => {
              if (err) {
                console.error('Error committing transaction:', err);
                reject(err);
              } else {
                console.log('Ration saved successfully with ID:', rationId);
                resolve({ id: rationId, success: true });
              }
            });
          }
        });
      });
    });
  });
});

ipcMain.handle('delete-ration', async (event, rationId) => {
  return new Promise((resolve, reject) => {
    const db = getDb();
    
    console.log('Deleting ration with ID:', rationId);
    
    db.serialize(() => {
      db.run('BEGIN TRANSACTION', (err) => {
        if (err) {
          console.error('Error starting transaction:', err);
          reject(err);
          return;
        }
        
        // Önce rasyon yemlerini sil
        const deleteFeedsSql = `DELETE FROM RasyonYemleri WHERE RasyonId = ?`;
        db.run(deleteFeedsSql, [rationId], function(err) {
          if (err) {
            console.error('Error deleting ration feeds:', err);
            db.run('ROLLBACK');
            reject(err);
            return;
          }
          
          console.log('Deleted', this.changes, 'ration feeds');
          
          // Rasyon analizlerini sil
          const deleteAnalysisSql = `DELETE FROM RasyonAnalizleri WHERE RasyonId = ?`;
          db.run(deleteAnalysisSql, [rationId], function(err) {
            if (err) {
              console.error('Error deleting ration analysis:', err);
              db.run('ROLLBACK');
              reject(err);
              return;
            }
            
            console.log('Deleted', this.changes, 'ration analysis records');
            
            // Son olarak rasyonu sil
            const deleteRationSql = `DELETE FROM Rasyonlar WHERE Id = ?`;
            db.run(deleteRationSql, [rationId], function(err) {
              if (err) {
                console.error('Error deleting ration:', err);
                db.run('ROLLBACK');
                reject(err);
                return;
              }
              
              console.log('Deleted ration, changes:', this.changes);
              
              if (this.changes === 0) {
                db.run('ROLLBACK');
                reject(new Error('Ration not found'));
                return;
              }
              
              db.run('COMMIT', (err) => {
                if (err) {
                  console.error('Error committing transaction:', err);
                  reject(err);
                } else {
                  console.log('Ration deleted successfully');
                  resolve({ success: true, changes: this.changes });
                }
              });
            });
          });
        });
      });
    });
  });
});

console.log('Feed Ration IPC handlers registered.');

module.exports = {};

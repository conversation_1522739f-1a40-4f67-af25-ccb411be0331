/* =================================
   Reproduction Page Styles
   ================================= */

/* =================================
   Reproduction Header Section
   ================================= */
.reproduction-header-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.reproduction-header-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.reproduction-page-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.reproduction-page-title svg {
  width: 28px;
  height: 28px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

/* =================================
   Reproduction Animal Selection
   ================================= */
.reproduction-animal-selection {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.reproduction-animal-selection:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.animal-selection-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
}

.animal-selection-header svg {
  width: 20px;
  height: 20px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

.animal-selection-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.animal-selection-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.animal-selection-content select {
  width: 100%;
  max-width: 400px;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.animal-selection-content select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.animal-selection-content select:hover {
  border-color: var(--border-secondary);
}

/* =================================
   Reproduction Navigation Section
   ================================= */
.reproduction-navigation-section {
  margin-bottom: var(--space-6);
}

/* =================================
   Animal Selection Bar
   ================================= */
.reproduction-navigation-section .animal-selection-bar {
  background: var(--primary-50);
  border: 1px solid var(--border-primary);
  border-top: 1px solid var(--primary-200);
  border-radius: var(--radius-lg);
  padding: var(--space-4) var(--space-6);
  margin-top: var(--space-3);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.reproduction-navigation-section .animal-selection-bar-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  max-width: 600px;
}

.reproduction-navigation-section .animal-selection-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--primary-700);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.reproduction-navigation-section .animal-selection-info svg {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.reproduction-navigation-section .animal-selection-bar select {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--primary-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--bg-elevated);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  min-width: 200px;
}

.reproduction-navigation-section .animal-selection-bar select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.reproduction-navigation-section .animal-selection-bar select:hover {
  border-color: var(--primary-400);
}

/* Reproduction Page Container */
.reproduction-page-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* Reproduction Navigation Tabs */
.reproduction-nav-tabs,
#repro-tabs.modern-tabs {
  display: flex;
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-1);
  margin-bottom: var(--space-6);
  overflow-x: auto;
  box-shadow: var(--shadow-sm);
  gap: var(--space-1);
}

.reproduction-tab,
.repro-tab {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: var(--radius-lg);
  white-space: nowrap;
  font-size: var(--text-sm);
  min-width: 140px;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  justify-content: center;
}

.reproduction-tab:hover,
.repro-tab:hover {
  color: var(--text-primary);
  background: var(--interactive-secondary);
  transform: translateY(-1px);
}

.reproduction-tab.active,
.repro-tab.active {
  color: var(--text-inverse);
  background: var(--interactive-primary);
  box-shadow: var(--shadow-sm);
}

.reproduction-tab svg,
.repro-tab svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.reproduction-tab span,
.repro-tab span {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

/* Reproduction Content Area */
.reproduction-content {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  min-height: 400px;
}

/* Section Headers */
.reproduction-content .section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
}

.reproduction-content .section-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

/* Animal Selection */
.animal-selection-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.animal-selection-section h3 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.animal-selection-section select {
  width: 100%;
  max-width: 300px;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
}

.animal-selection-section select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Empty States */
.reproduction-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12) var(--space-6);
  text-align: center;
  color: var(--text-secondary);
}

.reproduction-empty-state svg {
  width: 48px;
  height: 48px;
  margin-bottom: var(--space-4);
  opacity: 0.6;
}

.reproduction-empty-state p {
  margin: 0;
  font-size: var(--text-sm);
}

/* Form Sections */
.reproduction-form-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
}

.reproduction-form-section h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-primary);
  padding-bottom: var(--space-3);
}

/* Form Grid */
.reproduction-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

.reproduction-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.reproduction-form-group label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.reproduction-form-group input,
.reproduction-form-group select,
.reproduction-form-group textarea {
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.reproduction-form-group input:focus,
.reproduction-form-group select:focus,
.reproduction-form-group textarea:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.reproduction-form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Form Actions */
.reproduction-form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
  margin-top: var(--space-6);
}

/* Statistics Cards */
.reproduction-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.reproduction-stat-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.reproduction-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.reproduction-stat-card .stat-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.reproduction-stat-card .stat-icon svg {
  width: 20px;
  height: 20px;
}

.reproduction-stat-card .stat-content {
  flex: 1;
}

.reproduction-stat-card .stat-value {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  line-height: var(--leading-tight);
}

.reproduction-stat-card .stat-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Stat Card Variants */
.reproduction-stat-card.inseminations .stat-icon {
  background: var(--info-50);
  color: var(--info-600);
}

.reproduction-stat-card.pregnancies .stat-icon {
  background: var(--success-50);
  color: var(--success-600);
}

.reproduction-stat-card.births .stat-icon {
  background: var(--warning-50);
  color: var(--warning-600);
}

.reproduction-stat-card.success-rate .stat-icon {
  background: var(--purple-50);
  color: var(--purple-600);
}

/* Calendar Integration */
.reproduction-calendar-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
}

.reproduction-calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.reproduction-calendar-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.reproduction-calendar-controls {
  display: flex;
  gap: var(--space-2);
}

.reproduction-calendar-controls .btn {
  font-size: var(--text-sm);
  padding: var(--space-2) var(--space-3);
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  .reproduction-header-section {
    padding: var(--space-4);
  }

  .reproduction-page-title {
    font-size: var(--text-xl);
  }

  .reproduction-page-title svg {
    width: 24px;
    height: 24px;
  }

  .reproduction-navigation-section .animal-selection-bar {
    padding: var(--space-3) var(--space-4);
  }

  .reproduction-navigation-section .animal-selection-bar-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
    max-width: none;
  }

  .reproduction-navigation-section .animal-selection-bar select {
    width: 100%;
    min-width: auto;
  }

  .reproduction-nav-tabs {
    flex-direction: column;
    gap: var(--space-2);
  }

  .reproduction-tab,
  .repro-tab {
    min-width: auto;
    justify-content: flex-start;
    padding: var(--space-3);
  }

  .reproduction-tab span,
  .repro-tab span {
    font-size: var(--text-xs);
  }
}

@media (max-width: 480px) {
  .reproduction-header-section {
    padding: var(--space-3);
  }

  .reproduction-page-title {
    font-size: var(--text-lg);
  }

  .reproduction-navigation-section .animal-selection-bar {
    padding: var(--space-2) var(--space-3);
  }

  .reproduction-navigation-section .animal-selection-info {
    font-size: var(--text-xs);
  }

  .reproduction-navigation-section .animal-selection-info svg {
    width: 16px;
    height: 16px;
  }

  .reproduction-nav-tabs {
    padding: var(--space-2);
  }

  .reproduction-tab,
  .repro-tab {
    padding: var(--space-2);
    font-size: var(--text-xs);
  }

  .reproduction-tab svg,
  .repro-tab svg {
    width: 14px;
    height: 14px;
  }
}

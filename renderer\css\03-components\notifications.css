/* =================================
   Modern Toast Notification System
   ================================= */

/* =================================
   Toast Container
   ================================= */
.toast-container {
  position: fixed;
  top: var(--space-6);
  right: var(--space-6);
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  pointer-events: none;
  max-width: 400px;
  width: auto;
}

/* =================================
   Toast Base
   ================================= */
.toast {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all var(--transition-base);
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
  min-width: 300px;
  max-width: 400px;
}

.toast.show,
.toast.toast-show {
  transform: translateX(0);
  opacity: 1;
}

.toast.hide,
.toast.toast-hide {
  transform: translateX(100%);
  opacity: 0;
}

/* =================================
   Toast Icon
   ================================= */
.toast-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 14px;
}

.toast-icon svg {
  width: 16px;
  height: 16px;
}

.toast-icon i {
  font-size: 16px;
}

/* =================================
   Toast Content
   ================================= */
.toast-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.toast-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--leading-tight);
}

.toast-message {
  font-size: var(--text-sm);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--leading-normal);
  word-wrap: break-word;
}

/* =================================
   Toast Close Button
   ================================= */
.toast-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-close:hover {
  background: var(--interactive-secondary);
  color: var(--text-primary);
}

.toast-close svg {
  width: 14px;
  height: 14px;
}

/* =================================
   Toast Progress Bar
   ================================= */
.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: currentColor;
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  transition: width linear;
  opacity: 0.3;
}

/* =================================
   Toast Variants
   ================================= */

/* Success Toast */
.toast.success,
.toast.toast-success {
  border-left: 4px solid var(--status-success);
}

.toast.success .toast-icon,
.toast.toast-success .toast-icon {
  background: var(--status-success-bg);
  color: var(--status-success);
}

.toast.success .toast-progress,
.toast.toast-success .toast-progress {
  color: var(--status-success);
}

/* Error Toast */
.toast.error,
.toast.toast-error {
  border-left: 4px solid var(--status-error);
}

.toast.error .toast-icon,
.toast.toast-error .toast-icon {
  background: var(--status-error-bg);
  color: var(--status-error);
}

.toast.error .toast-progress,
.toast.toast-error .toast-progress {
  color: var(--status-error);
}

/* Warning Toast */
.toast.warning,
.toast.toast-warning {
  border-left: 4px solid var(--status-warning);
}

.toast.warning .toast-icon,
.toast.toast-warning .toast-icon {
  background: var(--status-warning-bg);
  color: var(--status-warning);
}

.toast.warning .toast-progress,
.toast.toast-warning .toast-progress {
  color: var(--status-warning);
}

/* Info Toast */
.toast.info,
.toast.toast-info {
  border-left: 4px solid var(--status-info);
}

.toast.info .toast-icon,
.toast.toast-info .toast-icon {
  background: var(--status-info-bg);
  color: var(--status-info);
}

.toast.info .toast-progress,
.toast.toast-info .toast-progress {
  color: var(--status-info);
}

/* =================================
   Toast Confirmation (Center Positioned)
   ================================= */
.toast-confirmation {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  right: auto !important;
  transform: translate(-50%, -50%) !important;
  z-index: var(--z-modal) !important;
  max-width: min(420px, 90vw) !important;
  min-width: min(300px, 80vw) !important;
  width: auto !important;
  background: rgba(255, 255, 255, 1) !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 12px !important;
  padding: 24px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 16px !important;
  backdrop-filter: none !important;
  filter: none !important;
}

.toast-confirmation .toast-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  gap: 16px !important;
  padding: 8px 0 !important;
  backdrop-filter: none !important;
  filter: none !important;
}

.toast-confirmation .toast-icon {
  width: 56px !important;
  height: 56px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  font-size: 24px !important;
  background: rgba(254, 243, 199, 1) !important;
  color: rgba(245, 158, 11, 1) !important;
  backdrop-filter: none !important;
  filter: none !important;
}

.toast-confirmation .toast-message {
  font-size: 16px !important;
  font-weight: 500 !important;
  color: rgba(17, 24, 39, 1) !important;
  margin: 0 !important;
  line-height: 1.6 !important;
  max-width: 100% !important;
  word-wrap: break-word !important;
  backdrop-filter: none !important;
  filter: none !important;
}

.toast-confirmation .toast-close {
  position: absolute !important;
  top: 12px !important;
  right: 12px !important;
  background: none !important;
  border: none !important;
  color: rgba(107, 114, 128, 1) !important;
  cursor: pointer !important;
  padding: 4px !important;
  border-radius: 6px !important;
  transition: all 150ms ease !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  backdrop-filter: none !important;
  filter: none !important;
}

.toast-confirmation .toast-close:hover {
  background: rgba(243, 244, 246, 1) !important;
  color: rgba(17, 24, 39, 1) !important;
}

.toast-confirmation .toast-actions {
  display: flex !important;
  gap: 12px !important;
  justify-content: center !important;
  margin-top: 12px !important;
  width: 100% !important;
  backdrop-filter: none !important;
  filter: none !important;
}

.toast-confirmation .toast-btn {
  padding: 12px 20px !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 150ms ease !important;
  min-width: 100px !important;
  border: 1px solid transparent !important;
  flex: 1 !important;
  max-width: 140px !important;
  backdrop-filter: none !important;
  filter: none !important;
}

.toast-confirmation .toast-btn-confirm {
  background: rgba(239, 68, 68, 1) !important;
  color: white !important;
  border-color: rgba(239, 68, 68, 1) !important;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2) !important;
  backdrop-filter: none !important;
  filter: none !important;
}

.toast-confirmation .toast-btn-confirm:hover {
  background: rgba(220, 38, 38, 1) !important;
  opacity: 1 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3) !important;
}

.toast-confirmation .toast-btn-cancel {
  background: rgba(255, 255, 255, 1) !important;
  color: rgba(17, 24, 39, 1) !important;
  border-color: rgba(209, 213, 219, 1) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: none !important;
  filter: none !important;
}

.toast-confirmation .toast-btn-cancel:hover {
  background: rgba(243, 244, 246, 1) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.toast-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  transition: opacity var(--transition-base);
}

.toast-backdrop.show {
  opacity: 1;
}

/* Dark theme specific styles for confirmation */
[data-theme="dark"] .toast-confirmation {
  background: rgba(30, 41, 59, 1) !important;
  border-color: rgba(71, 85, 105, 1) !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: none !important;
  filter: none !important;
}

[data-theme="dark"] .toast-confirmation .toast-message {
  color: rgba(241, 245, 249, 1) !important;
}

[data-theme="dark"] .toast-confirmation .toast-close {
  color: rgba(148, 163, 184, 1) !important;
}

[data-theme="dark"] .toast-confirmation .toast-close:hover {
  background: rgba(51, 65, 85, 1) !important;
  color: rgba(241, 245, 249, 1) !important;
}

[data-theme="dark"] .toast-confirmation .toast-btn-cancel {
  background: rgba(30, 41, 59, 1) !important;
  color: rgba(241, 245, 249, 1) !important;
  border-color: rgba(71, 85, 105, 1) !important;
}

[data-theme="dark"] .toast-confirmation .toast-btn-cancel:hover {
  background: rgba(51, 65, 85, 1) !important;
}

/* Light theme specific styles for confirmation */
[data-theme="light"] .toast-confirmation,
:root:not([data-theme="dark"]) .toast-confirmation {
  background: rgba(255, 255, 255, 1) !important;
  border-color: #e5e7eb !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* =================================
   Confirmation Dialog
   ================================= */
.confirmation-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--modal-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-2xl);
  z-index: var(--z-modal);
  max-width: 400px;
  width: 90vw;
  opacity: 0;
  scale: 0.95;
  transition: all var(--transition-base);
}

.confirmation-dialog.show {
  opacity: 1;
  scale: 1;
}

.confirmation-dialog-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  transition: opacity var(--transition-base);
}

.confirmation-dialog-backdrop.show {
  opacity: 1;
}

.confirmation-dialog-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-4);
  background: var(--warning-50);
  color: var(--status-warning);
}

.confirmation-dialog-icon svg {
  width: 24px;
  height: 24px;
}

.confirmation-dialog-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-align: center;
  margin: 0 0 var(--space-3) 0;
}

.confirmation-dialog-message {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-align: center;
  margin: 0 0 var(--space-6) 0;
  line-height: var(--leading-relaxed);
}

.confirmation-dialog-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: center;
}

.confirmation-dialog-actions .btn {
  min-width: 100px;
}

/* =================================
   Alert Banners
   ================================= */
.alert-banner {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  position: relative;
}

.alert-banner-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-banner-content {
  flex: 1;
  min-width: 0;
}

.alert-banner-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
}

.alert-banner-message {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--leading-normal);
}

.alert-banner-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.alert-banner-close:hover {
  background: var(--interactive-secondary);
  color: var(--text-primary);
}

/* Alert Banner Variants */
.alert-banner.success {
  border-left: 4px solid var(--status-success);
  background: var(--status-success-bg);
}

.alert-banner.success .alert-banner-icon {
  color: var(--status-success);
}

.alert-banner.error {
  border-left: 4px solid var(--status-error);
  background: var(--status-error-bg);
}

.alert-banner.error .alert-banner-icon {
  color: var(--status-error);
}

.alert-banner.warning {
  border-left: 4px solid var(--status-warning);
  background: var(--status-warning-bg);
}

.alert-banner.warning .alert-banner-icon {
  color: var(--status-warning);
}

.alert-banner.info {
  border-left: 4px solid var(--status-info);
  background: var(--status-info-bg);
}

.alert-banner.info .alert-banner-icon {
  color: var(--status-info);
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  .toast-container {
    top: var(--space-4);
    right: var(--space-4);
    left: var(--space-4);
    max-width: none;
  }

  .toast {
    padding: var(--space-3);
    gap: var(--space-2);
  }

  .toast-icon {
    width: 20px;
    height: 20px;
  }

  .toast-icon svg {
    width: 14px;
    height: 14px;
  }

  .toast-title,
  .toast-message {
    font-size: var(--text-xs);
  }

  .confirmation-dialog {
    padding: var(--space-4);
    max-width: 350px;
  }

  .confirmation-dialog-icon {
    width: 40px;
    height: 40px;
    margin-bottom: var(--space-3);
  }

  .confirmation-dialog-icon svg {
    width: 20px;
    height: 20px;
  }

  .confirmation-dialog-title {
    font-size: var(--text-base);
  }

  .confirmation-dialog-message {
    font-size: var(--text-xs);
    margin-bottom: var(--space-4);
  }

  .confirmation-dialog-actions {
    flex-direction: column;
    gap: var(--space-2);
  }

  .confirmation-dialog-actions .btn {
    min-width: auto;
  }

  .alert-banner {
    padding: var(--space-3);
    gap: var(--space-2);
  }

  .alert-banner-title,
  .alert-banner-message {
    font-size: var(--text-xs);
  }
}

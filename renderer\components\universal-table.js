/**
 * Universal Table Component System
 * Provides standardized table functionality across the livestock management application
 * Features: sorting, filtering, pagination, responsive design, consistent styling
 */

import { EnhancedSorting } from '../utils/enhanced-sorting.js';
import { EnhancedFiltering } from '../utils/enhanced-filtering.js';
import { EnhancedPagination } from '../utils/enhanced-pagination.js';

export class UniversalTable {
  constructor(config) {
    this.config = {
      containerId: config.containerId,
      tableId: config.tableId || `${config.containerId}-table`,
      columns: config.columns || [],
      data: config.data || [],
      itemsPerPage: config.itemsPerPage || 10,
      sortable: config.sortable !== false,
      filterable: config.filterable !== false,
      paginated: config.paginated !== false,
      responsive: config.responsive !== false,
      emptyStateMessage: config.emptyStateMessage || 'No records found',
      emptyStateIcon: config.emptyStateIcon || null,
      onRowClick: config.onRowClick || null,
      onRowAction: config.onRowAction || null,
      className: config.className || 'modern-table',
      ...config
    };

    // State management
    this.state = {
      currentData: [...this.config.data],
      filteredData: [...this.config.data],
      currentPage: 1,
      sortColumn: null,
      sortDirection: 1, // 1 for asc, -1 for desc
      filters: {},
      totalPages: 1,
      isLoading: false
    };

    // DOM references
    this.container = null;
    this.table = null;
    this.tbody = null;
    this.thead = null;
    this.paginationContainer = null;
    this.filterContainer = null;

    this.init();
  }

  init() {
    this.container = document.getElementById(this.config.containerId);
    if (!this.container) {
      throw new Error(`Container with ID '${this.config.containerId}' not found`);
    }

    this.render();
    this.attachEventListeners();
    this.updateDisplay();
  }

  render() {
    const html = this.generateHTML();
    this.container.innerHTML = html;
    
    // Cache DOM references
    this.table = this.container.querySelector(`#${this.config.tableId}`);
    this.tbody = this.table.querySelector('tbody');
    this.thead = this.table.querySelector('thead');
    this.paginationContainer = this.container.querySelector('.pagination-container');
    this.filterContainer = this.container.querySelector('.filter-container');
  }

  generateHTML() {
    return `
      <div class="universal-table-wrapper">
        ${this.config.filterable ? this.generateFilterHTML() : ''}
        <div class="table-container">
          <table id="${this.config.tableId}" class="${this.config.className}">
            ${this.generateHeaderHTML()}
            <tbody></tbody>
          </table>
        </div>
        ${this.config.paginated ? this.generatePaginationHTML() : ''}
      </div>
    `;
  }

  generateHeaderHTML() {
    const headerRows = [];
    
    // Main header row
    const mainHeaderCells = this.config.columns.map(column => {
      const sortableClass = this.config.sortable && column.sortable !== false ? 'sortable' : '';
      const dataAttributes = column.key ? `data-column-key="${column.key}"` : '';
      return `<th class="${sortableClass}" ${dataAttributes}>${column.title}</th>`;
    }).join('');
    
    headerRows.push(`<tr>${mainHeaderCells}</tr>`);

    // Filter row if enabled
    if (this.config.filterable) {
      const filterCells = this.config.columns.map(column => {
        if (column.filterable === false) {
          return '<th></th>';
        }
        return `<th>${this.generateFilterInput(column)}</th>`;
      }).join('');
      
      headerRows.push(`<tr class="filter-row">${filterCells}</tr>`);
    }

    return `<thead>${headerRows.join('')}</thead>`;
  }

  generateFilterInput(column) {
    const baseAttributes = `data-column="${column.key}" class="filter-input"`;
    
    switch (column.filterType) {
      case 'select':
        const options = column.filterOptions || [];
        const optionsHTML = options.map(opt => 
          `<option value="${opt.value}">${opt.label}</option>`
        ).join('');
        return `<select ${baseAttributes}><option value="">All</option>${optionsHTML}</select>`;
      
      case 'date':
        return `<input type="date" ${baseAttributes} />`;
      
      case 'number':
        const min = column.filterMin !== undefined ? `min="${column.filterMin}"` : '';
        const max = column.filterMax !== undefined ? `max="${column.filterMax}"` : '';
        const step = column.filterStep !== undefined ? `step="${column.filterStep}"` : '';
        return `<input type="number" ${baseAttributes} ${min} ${max} ${step} placeholder="${column.filterPlaceholder || ''}" />`;
      
      case 'text':
      default:
        return `<input type="text" ${baseAttributes} placeholder="${column.filterPlaceholder || column.title}" />`;
    }
  }

  generateFilterHTML() {
    return `
      <div class="filter-container">
        <div class="filter-actions">
          <button class="btn btn-secondary clear-filters-btn">Clear Filters</button>
        </div>
      </div>
    `;
  }

  generatePaginationHTML() {
    // Initial placeholder - will be updated with actual pagination info
    return `<div class="pagination-container"></div>`;
  }

  attachEventListeners() {
    // Sorting listeners
    if (this.config.sortable) {
      this.thead.addEventListener('click', (e) => {
        const th = e.target.closest('th.sortable');
        if (th) {
          const columnKey = th.dataset.columnKey;
          this.handleSort(columnKey);
        }
      });
    }

    // Filter listeners
    if (this.config.filterable) {
      this.thead.addEventListener('input', (e) => {
        if (e.target.classList.contains('filter-input')) {
          this.handleFilter(e.target.dataset.column, e.target.value);
        }
      });

      this.thead.addEventListener('change', (e) => {
        if (e.target.classList.contains('filter-input')) {
          this.handleFilter(e.target.dataset.column, e.target.value);
        }
      });

      // Clear filters button
      const clearBtn = this.container.querySelector('.clear-filters-btn');
      if (clearBtn) {
        clearBtn.addEventListener('click', () => this.clearFilters());
      }
    }

    // Pagination listeners
    if (this.config.paginated) {
      EnhancedPagination.attachPaginationListeners(
        this.container,
        (page) => this.goToPage(page),
        (itemsPerPage) => this.setItemsPerPage(itemsPerPage)
      );
    }

    // Row click listeners
    if (this.config.onRowClick) {
      this.tbody.addEventListener('click', (e) => {
        const row = e.target.closest('tr');
        if (row) {
          const rowIndex = Array.from(this.tbody.children).indexOf(row);
          const pageStartIndex = (this.state.currentPage - 1) * this.config.itemsPerPage;
          const dataIndex = pageStartIndex + rowIndex;
          const rowData = this.state.filteredData[dataIndex];
          if (rowData) {
            this.config.onRowClick(rowData, row, e);
          }
        }
      });
    }
  }

  handleSort(columnKey) {
    const sortState = EnhancedSorting.toggleSortDirection(
      columnKey,
      this.state.sortColumn,
      this.state.sortDirection
    );

    this.state.sortColumn = sortState.column;
    this.state.sortDirection = sortState.direction;

    this.applySorting();
    this.updateSortIndicators();
    this.updateDisplay();
  }

  handleFilter(columnKey, value) {
    if (value === '') {
      delete this.state.filters[columnKey];
    } else {
      this.state.filters[columnKey] = value;
    }

    this.applyFilters();
    this.state.currentPage = 1; // Reset to first page
    this.updateDisplay();
  }

  applySorting() {
    if (!this.state.sortColumn) return;

    const column = this.config.columns.find(col => col.key === this.state.sortColumn);
    if (!column) return;

    // Use enhanced sorting system
    this.state.filteredData = EnhancedSorting.sortTableData(
      this.state.filteredData,
      this.state.sortColumn,
      this.state.sortDirection,
      column
    );
  }

  applyFilters() {
    this.state.filteredData = EnhancedFiltering.applyFilters(
      this.state.currentData,
      this.state.filters,
      this.config.columns
    );
  }

  getNestedValue(obj, path) {
    return EnhancedSorting.getNestedValue(obj, path);
  }

  updateSortIndicators() {
    // Clear all sort indicators
    this.thead.querySelectorAll('th').forEach(th => {
      th.classList.remove('sorted-asc', 'sorted-desc');
    });

    // Add indicator to current sort column using enhanced sorting helper
    if (this.state.sortColumn) {
      const th = this.thead.querySelector(`th[data-column-key="${this.state.sortColumn}"]`);
      if (th) {
        const indicatorClass = EnhancedSorting.getSortIndicatorClass(
          this.state.sortColumn,
          this.state.sortColumn,
          this.state.sortDirection
        );
        if (indicatorClass) {
          th.classList.add(indicatorClass);
        }
      }
    }
  }

  updateDisplay() {
    this.calculatePagination();
    this.renderTableBody();
    this.updatePaginationControls();
  }

  calculatePagination() {
    const paginationInfo = EnhancedPagination.calculatePagination(
      this.state.filteredData,
      this.state.currentPage,
      this.config.itemsPerPage
    );

    this.state.currentPage = paginationInfo.currentPage;
    this.state.totalPages = paginationInfo.totalPages;
    this.paginationInfo = paginationInfo;
  }

  renderTableBody() {
    this.tbody.innerHTML = '';

    if (this.state.filteredData.length === 0) {
      this.renderEmptyState();
      return;
    }

    // Use pagination info to get page items
    const pageItems = this.paginationInfo ? this.paginationInfo.pageItems : this.state.filteredData;

    pageItems.forEach(item => {
      const row = this.createTableRow(item);
      this.tbody.appendChild(row);
    });
  }

  createTableRow(item) {
    const row = document.createElement('tr');
    
    this.config.columns.forEach(column => {
      const cell = document.createElement('td');
      const value = this.getNestedValue(item, column.key);
      
      if (column.render && typeof column.render === 'function') {
        cell.innerHTML = column.render(value, item);
      } else {
        cell.textContent = this.formatCellValue(value, column);
      }

      if (column.className) {
        cell.className = column.className;
      }

      row.appendChild(cell);
    });

    if (this.config.onRowClick) {
      row.classList.add('clickable-row');
    }

    return row;
  }

  formatCellValue(value, column) {
    if (value === null || value === undefined) return '';

    switch (column.dataType) {
      case 'date':
        return new Date(value).toLocaleDateString();
      case 'number':
        return Number(value).toFixed(column.decimals || 0);
      case 'currency':
        return Number(value).toFixed(2);
      default:
        return value.toString();
    }
  }

  renderEmptyState() {
    const colspan = this.config.columns.length;
    const iconHTML = this.config.emptyStateIcon ? 
      `<div class="empty-state-icon">${this.config.emptyStateIcon}</div>` : '';
    
    this.tbody.innerHTML = `
      <tr>
        <td colspan="${colspan}">
          <div class="empty-state-container">
            ${iconHTML}
            <p>${this.config.emptyStateMessage}</p>
          </div>
        </td>
      </tr>
    `;
  }

  updatePaginationControls() {
    if (!this.config.paginated || !this.paginationInfo) return;

    const paginationConfig = EnhancedPagination.createConfig({
      itemsPerPage: this.config.itemsPerPage,
      showInfo: true,
      showPageNumbers: true,
      showItemsPerPage: true,
      showFirstLast: true
    });

    EnhancedPagination.updatePaginationControls(
      this.container,
      this.paginationInfo,
      paginationConfig
    );

    // Re-attach listeners after updating controls
    EnhancedPagination.attachPaginationListeners(
      this.container,
      (page) => this.goToPage(page),
      (itemsPerPage) => this.setItemsPerPage(itemsPerPage)
    );
  }

  // Public API methods
  setData(data) {
    this.state.currentData = [...data];
    this.state.filteredData = [...data];
    this.state.currentPage = 1;
    this.applyFilters();
    this.applySorting();
    this.updateDisplay();
  }

  addRow(item) {
    this.state.currentData.push(item);
    this.setData(this.state.currentData);
  }

  removeRow(predicate) {
    this.state.currentData = this.state.currentData.filter(item => !predicate(item));
    this.setData(this.state.currentData);
  }

  updateRow(predicate, updates) {
    const index = this.state.currentData.findIndex(predicate);
    if (index !== -1) {
      this.state.currentData[index] = { ...this.state.currentData[index], ...updates };
      this.setData(this.state.currentData);
    }
  }

  clearFilters() {
    this.state.filters = {};
    EnhancedFiltering.clearAllFilters(this.container);
    this.applyFilters();
    this.state.currentPage = 1;
    this.updateDisplay();
  }

  goToPage(page) {
    this.state.currentPage = page;
    this.updateDisplay();
  }

  setItemsPerPage(itemsPerPage) {
    this.config.itemsPerPage = itemsPerPage;
    this.state.currentPage = 1;
    this.updateDisplay();
  }

  previousPage() {
    this.goToPage(this.state.currentPage - 1);
  }

  nextPage() {
    this.goToPage(this.state.currentPage + 1);
  }

  // Loading state management
  setLoading(isLoading) {
    this.state.isLoading = isLoading;

    if (isLoading) {
      this.container.classList.add('table-loading');
      this.renderSkeletonRows();
    } else {
      this.container.classList.remove('table-loading');
      this.updateDisplay();
    }
  }

  renderSkeletonRows() {
    if (!this.tbody) return;

    this.tbody.innerHTML = '';
    const skeletonRowCount = Math.min(this.config.itemsPerPage, 5);

    for (let i = 0; i < skeletonRowCount; i++) {
      const row = document.createElement('tr');
      row.className = 'skeleton-row';

      this.config.columns.forEach(() => {
        const cell = document.createElement('td');
        cell.innerHTML = '<div class="skeleton-cell"></div>';
        row.appendChild(cell);
      });

      this.tbody.appendChild(row);
    }
  }

  // Enhanced data loading with loading states
  async loadData(dataPromise) {
    this.setLoading(true);

    try {
      const data = await dataPromise;
      this.setData(data);
    } catch (error) {
      console.error('Error loading table data:', error);
      this.renderErrorState(error.message);
    } finally {
      this.setLoading(false);
    }
  }

  renderErrorState(message) {
    const colspan = this.config.columns.length;
    this.tbody.innerHTML = `
      <tr>
        <td colspan="${colspan}">
          <div class="empty-state-container error-state">
            <div class="error-icon">⚠️</div>
            <p>Error loading data: ${message}</p>
            <button class="btn btn-secondary retry-btn">Retry</button>
          </div>
        </td>
      </tr>
    `;

    // Add retry functionality
    const retryBtn = this.tbody.querySelector('.retry-btn');
    if (retryBtn) {
      retryBtn.addEventListener('click', () => {
        if (this.config.onRetry && typeof this.config.onRetry === 'function') {
          this.config.onRetry();
        }
      });
    }
  }

  refresh() {
    this.updateDisplay();
  }

  destroy() {
    if (this.container) {
      this.container.innerHTML = '';
    }
  }
}

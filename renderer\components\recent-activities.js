// Recent Activities Component
// Reusable component for displaying recent activities across different pages

export class RecentActivities {
  constructor(config) {
    this.config = {
      containerId: config.containerId,
      title: config.title || window.i18n.t('recent_activities'),
      maxItems: config.maxItems || 5,
      onViewAll: config.onViewAll || (() => {}),
      formatRecord: config.formatRecord,
      getRecords: config.getRecords,
      emptyMessage: config.emptyMessage || window.i18n.t('no_recent_activities'),
      ...config
    };
    this.container = null;
  }

  /**
   * Initialize the recent activities component
   */
  init() {
    this.container = document.getElementById(this.config.containerId);
    if (!this.container) {
      console.warn(`Recent Activities: Container with ID '${this.config.containerId}' not found`);
      return;
    }
    this.render();
  }

  /**
   * Render the recent activities section
   */
  render() {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="recent-activities-section">
        <div class="recent-activities-header">
          <h3 class="recent-activities-title">
            <svg class="recent-activities-icon" viewBox="0 0 20 20" fill="none">
              <path d="M10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2Z" stroke="currentColor" stroke-width="1.5"/>
              <path d="M10 6V10L13 13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            ${this.config.title}
          </h3>
          <button class="recent-activities-view-all" type="button">
            <span>${window.i18n.t('view_all')}</span>
            <svg viewBox="0 0 20 20" fill="none">
              <path d="M7 14L12 9L7 4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
        <div class="recent-activities-content">
          <div class="recent-activities-loading">
            <div class="loading-spinner"></div>
            <span>${window.i18n.t('loading_recent_activities')}</span>
          </div>
        </div>
      </div>
    `;

    // Add event listener for "View All" button
    const viewAllBtn = this.container.querySelector('.recent-activities-view-all');
    if (viewAllBtn) {
      viewAllBtn.addEventListener('click', () => {
        this.config.onViewAll();
      });
    }

    // Load and display activities
    this.loadActivities();
  }

  /**
   * Load and display recent activities
   */
  async loadActivities() {
    if (!this.container) return;

    const contentEl = this.container.querySelector('.recent-activities-content');
    if (!contentEl) return;



    try {
      // Show loading state
      contentEl.innerHTML = `
        <div class="recent-activities-loading">
          <div class="loading-spinner"></div>
          <span>Loading recent activities...</span>
        </div>
      `;

      // Get records using the provided function
      const records = await this.config.getRecords();
      const recentRecords = records.slice(0, this.config.maxItems);

      if (recentRecords.length === 0) {
        const emptyMessage = typeof this.config.emptyMessage === 'function'
          ? this.config.emptyMessage()
          : this.config.emptyMessage;

        contentEl.innerHTML = `
          <div class="recent-activities-empty">
            <svg class="empty-icon" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5"/>
              <path d="M8 12h8M12 8v8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
            <p>${emptyMessage}</p>
          </div>
        `;
        return;
      }

      // Render activities
      const activitiesHTML = recentRecords.map(record => {
        const formattedRecord = this.config.formatRecord(record);
        return `
          <div class="recent-activity-card" data-id="${record.Id || ''}">
            <div class="activity-main">
              <div class="activity-info">
                <div class="activity-title">${formattedRecord.title}</div>
                <div class="activity-subtitle">${formattedRecord.subtitle}</div>
              </div>
              <div class="activity-meta">
                <div class="activity-date">${formattedRecord.date}</div>
                ${formattedRecord.amount ? `<div class="activity-amount">${formattedRecord.amount}</div>` : ''}
              </div>
            </div>
            ${formattedRecord.badge ? `<div class="activity-badge">${formattedRecord.badge}</div>` : ''}
          </div>
        `;
      }).join('');

      contentEl.innerHTML = `
        <div class="recent-activities-list">
          ${activitiesHTML}
        </div>
      `;

    } catch (error) {
      console.error('Error loading recent activities:', error);
      contentEl.innerHTML = `
        <div class="recent-activities-error">
          <svg class="error-icon" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5"/>
            <path d="M12 8v4M12 16h.01" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
          <p>Failed to load recent activities</p>
        </div>
      `;
    }
  }

  /**
   * Refresh the activities (call this when new records are added)
   */
  refresh() {
    this.loadActivities();
  }

  /**
   * Destroy the component
   */
  destroy() {
    if (this.container) {
      this.container.innerHTML = '';
    }
  }
}

/**
 * Utility function to format date for activities
 */
export function formatActivityDate(dateString) {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) {
    return window.i18n.t('today');
  } else if (diffDays === 2) {
    return window.i18n.t('yesterday');
  } else if (diffDays <= 7) {
    return window.i18n.t('days_ago', { days: diffDays - 1 });
  } else {
    return date.toLocaleDateString();
  }
}

/**
 * Utility function to truncate text
 */
export function truncateText(text, maxLength = 30) {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

const { ipcMain } = require('electron');
const { getDb } = require('../services/database.js');

/**
 * Business Rule Validation Handlers
 * 
 * İş Kuralları:
 * 1. Erkek hayvanlara gebelik, to<PERSON>lama, laktasyon ve sağım verisi girilemez
 * 2. Aynı hayvan için belirlenmiş 2 laktasyon döneminin tarihleri birbirlerini içeremez (çakışamaz)
 * 3. Aynı hayvan devam eden bir gebelik sırasında tekrar gebe kalamaz
 */

// Hayvanın cinsiyetini kontrol et
ipcMain.handle('validation:checkAnimalGender', async (event, animalId) => {
  return new Promise((resolve, reject) => {
    const db = getDb();
    db.get('SELECT Cinsiyet FROM Hayvanlar WHERE Id = ?', [animalId], (err, row) => {
      if (err) reject(err);
      else resolve(row ? row.Cinsiyet : null);
    });
  });
});

// Erkek hayvan için üreme verisi kontrolü
ipcMain.handle('validation:validateMaleReproductionData', async (event, animalId) => {
  return new Promise((resolve, reject) => {
    const db = getDb();
    db.get('SELECT Cinsiyet FROM Hayvanlar WHERE Id = ?', [animalId], (err, row) => {
      if (err) reject(err);
      else {
        const isMale = row && row.Cinsiyet && row.Cinsiyet.toLowerCase() === 'erkek';
        resolve({
          isValid: !isMale,
          isMale: isMale,
          message: isMale ? 'validation_male_animal_reproduction' : null
        });
      }
    });
  });
});

// Çakışan laktasyon dönemleri kontrolü
ipcMain.handle('validation:checkLactationOverlap', async (event, animalId, startDate, endDate, excludeId = null) => {
  return new Promise((resolve, reject) => {
    const db = getDb();
    
    // Eğer bitiş tarihi yoksa, şu anki tarihi kullan
    const effectiveEndDate = endDate || new Date().toISOString().split('T')[0];
    
    let query = `
      SELECT Id, BaslangicTarihi, BitisTarihi 
      FROM Laktasyonlar 
      WHERE HayvanId = ? 
      AND (
        (BaslangicTarihi <= ? AND (BitisTarihi IS NULL OR BitisTarihi >= ?))
        OR (BaslangicTarihi <= ? AND (BitisTarihi IS NULL OR BitisTarihi >= ?))
        OR (BaslangicTarihi >= ? AND BaslangicTarihi <= ?)
      )
    `;
    
    const params = [animalId, startDate, startDate, effectiveEndDate, effectiveEndDate, startDate, effectiveEndDate];
    
    if (excludeId) {
      query += ' AND Id != ?';
      params.push(excludeId);
    }
    
    db.all(query, params, (err, rows) => {
      if (err) reject(err);
      else {
        const hasOverlap = rows.length > 0;
        resolve({
          isValid: !hasOverlap,
          hasOverlap: hasOverlap,
          overlappingPeriods: rows,
          message: hasOverlap ? 'validation_lactation_overlap' : null
        });
      }
    });
  });
});

// Devam eden gebelik kontrolü
ipcMain.handle('validation:checkActivePregnancy', async (event, animalId, excludeId = null) => {
  return new Promise((resolve, reject) => {
    const db = getDb();
    
    let query = `
      SELECT Id, BaslangicTarihi, BeklenenDogumTarihi, GebelikSonucu 
      FROM Gebelikler 
      WHERE HayvanId = ? 
      AND (GebelikSonucu IS NULL OR GebelikSonucu = '' OR GebelikSonucu = 'Devam Ediyor')
    `;
    
    const params = [animalId];
    
    if (excludeId) {
      query += ' AND Id != ?';
      params.push(excludeId);
    }
    
    db.all(query, params, (err, rows) => {
      if (err) reject(err);
      else {
        const hasActivePregnancy = rows.length > 0;
        resolve({
          isValid: !hasActivePregnancy,
          hasActivePregnancy: hasActivePregnancy,
          activePregnancies: rows,
          message: hasActivePregnancy ? 'validation_active_pregnancy' : null
        });
      }
    });
  });
});

// Kombine validation - tüm iş kurallarını kontrol et
ipcMain.handle('validation:validateBusinessRules', async (event, data) => {
  const { animalId, operation, startDate, endDate, excludeId } = data;

  try {
    const results = {
      isValid: true,
      errors: [],
      warnings: []
    };

    const db = getDb();

    // Erkek hayvan kontrolü (gebelik, tohumlama, laktasyon, sağım için)
    if (['pregnancy', 'insemination', 'lactation', 'milking'].includes(operation)) {
      const animalGender = await new Promise((resolve, reject) => {
        db.get('SELECT Cinsiyet FROM Hayvanlar WHERE Id = ?', [animalId], (err, row) => {
          if (err) reject(err);
          else resolve(row ? row.Cinsiyet : null);
        });
      });

      if (animalGender && animalGender.toLowerCase() === 'erkek') {
        results.isValid = false;
        results.errors.push('validation_male_animal_reproduction');
      }
    }

    // Laktasyon çakışma kontrolü
    if (operation === 'lactation' && startDate && results.isValid) {
      const effectiveEndDate = endDate || new Date().toISOString().split('T')[0];

      const overlapCheck = await new Promise((resolve, reject) => {
        let query = `
          SELECT Id, BaslangicTarihi, BitisTarihi
          FROM Laktasyonlar
          WHERE HayvanId = ?
          AND (
            (BaslangicTarihi <= ? AND (BitisTarihi IS NULL OR BitisTarihi >= ?))
            OR (BaslangicTarihi <= ? AND (BitisTarihi IS NULL OR BitisTarihi >= ?))
            OR (BaslangicTarihi >= ? AND BaslangicTarihi <= ?)
          )
        `;

        const params = [animalId, startDate, startDate, effectiveEndDate, effectiveEndDate, startDate, effectiveEndDate];

        if (excludeId) {
          query += ' AND Id != ?';
          params.push(excludeId);
        }

        db.all(query, params, (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      if (overlapCheck.length > 0) {
        results.isValid = false;
        results.errors.push('validation_lactation_overlap');
      }
    }

    // Aktif gebelik kontrolü
    if (operation === 'pregnancy' && results.isValid) {
      const pregnancyCheck = await new Promise((resolve, reject) => {
        let query = `
          SELECT Id, BaslangicTarihi, BeklenenDogumTarihi, GebelikSonucu
          FROM Gebelikler
          WHERE HayvanId = ?
          AND (GebelikSonucu IS NULL OR GebelikSonucu = '' OR GebelikSonucu = 'Devam Ediyor')
        `;

        const params = [animalId];

        if (excludeId) {
          query += ' AND Id != ?';
          params.push(excludeId);
        }

        db.all(query, params, (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      if (pregnancyCheck.length > 0) {
        results.isValid = false;
        results.errors.push('validation_active_pregnancy');
      }
    }

    return results;

  } catch (error) {
    return {
      isValid: false,
      errors: ['validation_error_occurred: ' + error.message],
      warnings: []
    };
  }
});

console.log('Business rule validation IPC handlers registered.');

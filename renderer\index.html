<!DOCTYPE html>
<html lang="tr">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Can<PERSON>ı Hayvan Yönetimi</title>
  <!-- FontAwesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Choices.js CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css"/>
  <!-- Modern CSS Architecture -->
  <!-- Foundation Layer -->
  <link rel="stylesheet" href="css/01-foundation/reset.css">
  <link rel="stylesheet" href="css/01-foundation/tokens.css">
  <link rel="stylesheet" href="css/01-foundation/themes.css">

  <!-- Layout Layer -->
  <link rel="stylesheet" href="css/02-layout/grid.css">
  <link rel="stylesheet" href="css/02-layout/sidebar.css">

  <!-- Component Layer -->
  <link rel="stylesheet" href="css/03-components/buttons.css">
  <link rel="stylesheet" href="css/03-components/cards.css">
  <link rel="stylesheet" href="css/03-components/badges.css">
  <link rel="stylesheet" href="css/03-components/tables.css">
  <link rel="stylesheet" href="css/03-components/modals.css">
  <link rel="stylesheet" href="css/03-components/forms.css">
  <link rel="stylesheet" href="css/03-components/notifications.css">
  <!-- Progress modal CSS removed -->

  <!-- Page Layer -->
  <link rel="stylesheet" href="css/04-pages/dashboard.css">
  <link rel="stylesheet" href="css/04-pages/animals.css">
  <link rel="stylesheet" href="css/04-pages/health.css">
  <link rel="stylesheet" href="css/04-pages/reproduction.css">
  <link rel="stylesheet" href="css/04-pages/milk-yield.css">
  <link rel="stylesheet" href="css/04-pages/reports.css">
  <link rel="stylesheet" href="css/04-pages/accounting.css">
  <link rel="stylesheet" href="css/04-pages/feed-ration.css">
  <link rel="stylesheet" href="css/04-pages/settings.css">

  <!-- Utilities Layer (always last) -->
  <link rel="stylesheet" href="css/05-utilities/utilities.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body style="font-family: var(--font-sans); background: var(--bg-primary); color: var(--text-primary);" data-theme="light">
  <div class="sidebar">
    <div class="logo">
      <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M16 8.36364C16 7.26089 15.5469 6.20343 14.7464 5.40294C13.9459 4.60246 12.8885 4.14929 11.7857 4.14929C10.6829 4.14929 9.62548 4.60246 8.82499 5.40294C8.0245 6.20343 7.57143 7.26089 7.57143 8.36364V9.27273H16V8.36364Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.7857 1C10.0479 1 8.38136 1.70585 7.16961 2.9176C5.95786 4.12935 5.25201 5.79589 5.25201 7.5337V9.27277H3.03571C2.48346 9.27277 2 9.75623 2 10.3085V14.8546C2 15.4068 2.48346 15.8903 3.03571 15.8903H4.14286" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M19.8571 15.8903H20.9643C21.5165 15.8903 22 15.4068 22 14.8546V10.3085C22 9.75623 21.5165 9.27277 20.9643 9.27277H18.748" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M7.57141 12.5454H16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M7.57141 15.8182H16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M10.6071 19.0909H13.3928" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M4.14285 15.8903C4.14285 18.2137 6.14285 20.1273 8.71428 20.1273H15.2857C17.8571 20.1273 19.8571 18.2137 19.8571 15.8903" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span>SürüYönet</span>
    </div>
    <ul class="menu">
      <li class="menu-item" data-page="dashboard">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg>
        <span data-i18n="dashboard">Anasayfa</span>
      </li>
      <li class="menu-item accordion-toggle">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 8.36364C16 7.26089 15.5469 6.20343 14.7464 5.40294C13.9459 4.60246 12.8885 4.14929 11.7857 4.14929C10.6829 4.14929 9.62548 4.60246 8.82499 5.40294C8.0245 6.20343 7.57143 7.26089 7.57143 8.36364V9.27273H16V8.36364Z"></path><path d="M11.7857 1C10.0479 1 8.38136 1.70585 7.16961 2.9176C5.95786 4.12935 5.25201 5.79589 5.25201 7.5337V9.27277H3.03571C2.48346 9.27277 2 9.75623 2 10.3085V14.8546C2 15.4068 2.48346 15.8903 3.03571 15.8903H4.14286"></path><path d="M19.8571 15.8903H20.9643C21.5165 15.8903 22 15.4068 22 14.8546V10.3085C22 9.75623 21.5165 9.27277 20.9643 9.27277H18.748"></path><path d="M7.57141 12.5454H16"></path><path d="M7.57141 15.8182H16"></path><path d="M10.6071 19.0909H13.3928"></path><path d="M4.14285 15.8903C4.14285 18.2137 6.14285 20.1273 8.71428 20.1273H15.2857C17.8571 20.1273 19.8571 18.2137 19.8571 15.8903"></path></svg>
        <span data-i18n="herd_management">Sürü Yönetimi</span>
        <svg class="arrow" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>
      </li>
      <ul class="submenu">
        <li class="menu-item" data-page="animals">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><path d="M16 8.36364C16 7.26089 15.5469 6.20343 14.7464 5.40294C13.9459 4.60246 12.8885 4.14929 11.7857 4.14929C10.6829 4.14929 9.62548 4.60246 8.82499 5.40294C8.0245 6.20343 7.57143 7.26089 7.57143 8.36364V9.27273H16V8.36364Z"></path><path d="M11.7857 1C10.0479 1 8.38136 1.70585 7.16961 2.9176C5.95786 4.12935 5.25201 5.79589 5.25201 7.5337V9.27277H3.03571C2.48346 9.27277 2 9.75623 2 10.3085V14.8546C2 15.4068 2.48346 15.8903 3.03571 15.8903H4.14286"></path><path d="M19.8571 15.8903H20.9643C21.5165 15.8903 22 15.4068 22 14.8546V10.3085C22 9.75623 21.5165 9.27277 20.9643 9.27277H18.748"></path><path d="M7.57141 12.5454H16"></path><path d="M7.57141 15.8182H16"></path><path d="M10.6071 19.0909H13.3928"></path><path d="M4.14285 15.8903C4.14285 18.2137 6.14285 20.1273 8.71428 20.1273H15.2857C17.8571 20.1273 19.8571 18.2137 19.8571 15.8903"></path></svg>
          <span data-i18n="animals">Hayvanlar</span>
        </li>
        <li class="menu-item" data-page="health">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>
          <span data-i18n="health">Sağlık</span>
        </li>
        <li class="menu-item" data-page="reproduction">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><path d="M12 2a10 10 0 1 0 10 10"></path><path d="M12 18a6 6 0 1 0 0-12v12z"></path></svg>
          <span data-i18n="reproduction">Üreme</span>
        </li>
        <li class="menu-item" data-page="milk">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><path d="M12 22a7 7 0 0 0 7-7c0-2-1-3.9-3-5.5s-3.5-4-3.5-4-1.5 2.5-3.5 4-3 3.5-3 5.5a7 7 0 0 0 7 7z"></path></svg>
          <span data-i18n="milk">Süt Verimi</span>
        </li>
         <li class="menu-item" data-page="reports">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><path d="M10 3H6a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h4v0M10 3l4 4h6a2 2 0 0 1 2 2v4M10 3v4H6m14 5l-3-3-3 3m1-10v4h4V3M14 17v4h4v-4M14 17h4"/></svg>
          <span data-i18n="reports">Raporlamalar</span>
        </li>
      </ul>
      <li class="menu-item accordion-toggle">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path><path d="M22 12A10 10 0 0 0 12 2v10z"></path><line x1="12" y1="7.5" x2="12" y2="12.5"></line><line x1="12" y1="17.5" x2="12" y2="22.5"></line><line x1="17.5" y1="12" x2="22.5" y2="12"></line><line x1="7.5" y1="12" x2="2.5" y2="12"></line></svg>
        <span data-i18n="accounting">Muhasebe</span>
        <svg class="arrow" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>
      </li>
      <ul class="submenu">
        <li class="menu-item" data-page="accounting-products">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path></svg>
          <span data-i18n="products">Ürünler</span>
        </li>
        <li class="menu-item" data-page="accounting-accounts">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
          <span data-i18n="current_accounts">Cariler</span>
        </li>
        <li class="menu-item" data-page="accounting-purchases">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><circle cx="10" cy="20.5" r="1"/><circle cx="18" cy="20.5" r="1"/><path d="M2.5 2.5h3l2.7 12.4a2 2 0 0 0 2 1.6h7.7a2 2 0 0 0 2-1.6l1.6-8.4H7.1"/></svg>
          <span data-i18n="purchases">Alışlar</span>
        </li>
        <li class="menu-item" data-page="accounting-sales">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><path d="M12.5 22H4.8C3.5 22 3 21.1 3 20V6c0-1.1.5-2 1.8-2h14.4c1.3 0 1.8.9 1.8 2v4.5"/><path d="M3 10h18"/><path d="M16 18h6"/><path d="M19 15v6"/></svg>
          <span data-i18n="sales">Satışlar</span>
        </li>
        <li class="menu-item" data-page="accounting-payments">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><rect x="3" y="3" width="18" height="18" rx="2"/><line x1="8" y1="12" x2="16" y2="12"/><line x1="12" y1="8" x2="12" y2="16"/></svg>
          <span data-i18n="payments">Ödemeler</span>
        </li>
        <li class="menu-item" data-page="accounting-reports">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sub-icon"><rect x="3" y="3" width="18" height="18" rx="2"/><line x1="8" y1="12" x2="16" y2="12"/><line x1="12" y1="8" x2="12" y2="16"/></svg>
          <span data-i18n="accounting_reports">Raporlamalar</span>
        </li>
      </ul>
      <li class="menu-item" data-page="feed_ration">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5z"></path><path d="M2 17l10 5 10-5"></path><path d="M2 12l10 5 10-5"></path></svg>
        <span data-i18n="feed_ration">Rasyon Yönetimi</span>
      </li>
      <li class="menu-item" data-page="settings">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
        <span data-i18n="settings">Ayarlar</span>
      </li>
      <!-- Raporlamalar Sürü Yönetimi altına taşındı -->
      </li>
    </ul>
  </div>
  <div class="main-content">
    <nav class="breadcrumb-nav" id="breadcrumb-nav">
      <div class="breadcrumb-container">
        <span class="breadcrumb-item home">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
            <polyline points="9,22 9,12 15,12 15,22"/>
          </svg>
          <span data-i18n="home">Ana Sayfa</span>
        </span>
        <span class="breadcrumb-separator">
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"/>
          </svg>
        </span>
        <span class="breadcrumb-item current" id="current-page-breadcrumb" data-i18n="dashboard">Anasayfa</span>
      </div>
    </nav>
    <section id="content-area">
      </section>
    <div id="animal-modal" class="modal hidden">
      <div class="modal-content large">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="modal-title" data-i18n="animal_modal_title_add">Hayvan Ekle</h2>
        </div>
        <form id="animal-form">
          <div class="modal-body">
          <!-- Basic Information Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_basic_info">Temel Bilgiler</h3>
            <div class="form-grid">
              <div class="form-row">
                <label data-i18n="label_kupe_no">Küpe No</label>
                <input type="text" name="KupeNo" required />
              </div>
              <div class="form-row">
                <label data-i18n="label_isletme_no">İşletme No</label>
                <input type="text" name="IsletmeNo" />
              </div>
              <div class="form-row">
                <label data-i18n="label_isim">İsim</label>
                <input type="text" name="Isim" />
              </div>
              <div class="form-row">
                <label data-i18n="label_cinsiyet">Cinsiyet</label>
                <select name="Cinsiyet" required>
                  <option value="">Seçiniz</option>
                  <option value="Erkek" data-i18n="option_erkek">Erkek</option>
                  <option value="Dişi" data-i18n="option_dişi">Dişi</option>
                </select>
              </div>
              <div class="form-row">
                <label data-i18n="label_aktif_mi">Aktif mi?</label>
                <select name="AktifMi">
                  <option value="1" data-i18n="option_yes">Evet</option>
                  <option value="0" data-i18n="option_no">Hayır</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Physical Characteristics Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_physical_characteristics">Fiziksel Özellikler</h3>
            <div class="form-grid">
              <div class="form-row">
                <label data-i18n="label_tur">Tür</label>
                <select name="Tur" id="tur-select">
                  <option value="">Seçiniz</option>
                  <option value="Sığır" data-i18n="species_cattle">Sığır</option>
                  <option value="Koyun" data-i18n="species_sheep">Koyun</option>
                  <option value="Keçi" data-i18n="species_goat">Keçi</option>
                  <option value="Manda" data-i18n="species_buffalo">Manda</option>
                  <option value="Diğer" data-i18n="species_other">Diğer</option>
                </select>
                <input type="text" name="TurCustom" id="tur-custom-input" placeholder="Tür adını giriniz" style="display: none; margin-top: 8px;" />
              </div>
              <div class="form-row">
                <label data-i18n="label_irk">Irk</label>
                <select name="Irk" id="irk-select">
                  <option value="">Seçiniz</option>
                  <option value="Holstein" data-i18n="breed_holstein">Holstein</option>
                  <option value="Simmental" data-i18n="breed_simmental">Simmental</option>
                  <option value="Jersey" data-i18n="breed_jersey">Jersey</option>
                  <option value="Angus" data-i18n="breed_angus">Angus</option>
                  <option value="Yerli Kara" data-i18n="breed_native_black">Yerli Kara</option>
                  <option value="Diğer" data-i18n="breed_other">Diğer</option>
                </select>
                <input type="text" name="IrkCustom" id="irk-custom-input" placeholder="Irk adını giriniz" style="display: none; margin-top: 8px;" />
              </div>
              <div class="form-row">
                <label data-i18n="label_boynuz_durumu">Boynuz Durumu</label>
                <div class="radio-group">
                  <label class="radio-option">
                    <input type="radio" name="BoynuzDurumu" value="Boynuzlu" />
                    <span data-i18n="horn_status_horned">Boynuzlu</span>
                  </label>
                  <label class="radio-option">
                    <input type="radio" name="BoynuzDurumu" value="Boynuzsuz" />
                    <span data-i18n="horn_status_polled">Boynuzsuz</span>
                  </label>
                  <label class="radio-option">
                    <input type="radio" name="BoynuzDurumu" value="Kesilmiş" />
                    <span data-i18n="horn_status_dehorned">Kesilmiş</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Birth Information Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_birth_info">Doğum Bilgileri</h3>
            <div class="form-grid">
              <div class="form-row">
                <label data-i18n="label_dogum_tarihi">Doğum Tarihi</label>
                <input type="date" name="DogumTarihi" />
              </div>
              <div class="form-row">
                <label data-i18n="label_dogum_agirligi">Doğum Ağırlığı (kg)</label>
                <input type="number" step="0.01" name="DogumAgirligi" />
              </div>
              <div class="form-row">
                <label data-i18n="label_dogum_tipi">Doğum Tipi</label>
                <div class="radio-group">
                  <label class="radio-option">
                    <input type="radio" name="DogumTipi" value="Normal" />
                    <span data-i18n="birth_type_normal">Normal</span>
                  </label>
                  <label class="radio-option">
                    <input type="radio" name="DogumTipi" value="Sezaryen" />
                    <span data-i18n="birth_type_cesarean">Sezaryen</span>
                  </label>
                  <label class="radio-option">
                    <input type="radio" name="DogumTipi" value="Yardımlı" />
                    <span data-i18n="birth_type_assisted">Yardımlı</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Parentage Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_parentage">Ebeveyn Bilgileri</h3>
            <div class="form-grid">
              <div class="form-row">
                <label data-i18n="label_anne_kupe">Anne Küpe</label>
                <select name="AnneKupe"></select>
              </div>
              <div class="form-row">
                <label data-i18n="label_baba_kupe">Baba Küpe</label>
                <select name="BabaKupe"></select>
              </div>
            </div>
          </div>

          <!-- Management Information Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_management">Yönetim Bilgileri</h3>
            <div class="form-grid">
              <div class="form-row">
                <label data-i18n="label_isletmeye_giris">İşletmeye Giriş Tarihi</label>
                <input type="date" name="IsletmeyeGirisTarihi" />
              </div>
              <div class="form-row">
                <label data-i18n="label_isletmeden_cikis">İşletmeden Çıkış Tarihi</label>
                <input type="date" name="IsletmedenCikisTarihi" />
              </div>
              <div class="form-row">
                <label data-i18n="label_cikis_sebebi">Çıkış Sebebi</label>
                <select name="CikisSebebi">
                  <option value="">Seçiniz</option>
                  <option value="Satış" data-i18n="exit_reason_sale">Satış</option>
                  <option value="Ölüm" data-i18n="exit_reason_death">Ölüm</option>
                  <option value="Transfer" data-i18n="exit_reason_transfer">Transfer</option>
                  <option value="Kesim" data-i18n="exit_reason_slaughter">Kesim</option>
                  <option value="Diğer" data-i18n="exit_reason_other">Diğer</option>
                </select>
              </div>
              <div class="form-row">
                <label data-i18n="label_fotograf_url">Fotoğraf URL</label>
                <input type="text" name="FotografUrl" />
              </div>
            </div>
          </div>

          <!-- Additional Notes Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_notes">Ek Notlar</h3>
            <div class="form-row full">
              <label data-i18n="label_notlar">Notlar</label>
              <textarea name="Notlar" rows="3"></textarea>
            </div>
          </div>

          <input type="hidden" name="Id" />
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('animal-modal').classList.add('hidden')" data-i18n="btn_cancel">İptal</button>
              <button type="submit" class="btn btn-primary" data-i18n="btn_save">Kaydet</button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div id="vaccine-modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="vaccine-modal-title">Aşı Kaydı</h2>
        </div>
        <form id="vaccine-form">
          <div class="modal-body">
          <!-- Vaccine Information Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_vaccine_info">Aşı Bilgileri</h3>
            <div class="form-grid">
              <div class="form-row">
                <label>Aşı Adı</label>
                <select name="AsiAdi" id="vaccine-name-select" required>
                  <option value="">Seçiniz</option>
                  <option value="Şap Aşısı">Şap Aşısı</option>
                  <option value="Şarbon Aşısı">Şarbon Aşısı</option>
                  <option value="Enterotoksemi Aşısı">Enterotoksemi Aşısı</option>
                  <option value="Brucella Aşısı">Brucella Aşısı</option>
                  <option value="IBR/IPV Aşısı">IBR/IPV Aşısı</option>
                  <option value="BVD Aşısı">BVD Aşısı</option>
                  <option value="Pastörella Aşısı">Pastörella Aşısı</option>
                  <option value="Diğer">Diğer</option>
                </select>
                <input type="text" name="AsiAdiDiger" id="vaccine-name-other" placeholder="Diğer aşı adını giriniz" style="display: none; margin-top: 8px;" />
              </div>
              <div class="form-row">
                <label>Tarih</label>
                <input type="date" name="AsilamaTarihi" required />
              </div>
              <div class="form-row">
                <label>Doz</label>
                <input type="text" name="Doz" placeholder="Örn: 2 ml, 1 doz" />
              </div>
              <div class="form-row">
                <label>Veteriner</label>
                <input type="text" name="UygulayanVeteriner" placeholder="Veteriner adı" />
              </div>
            </div>
          </div>

          <!-- Additional Notes Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_notes">Ek Notlar</h3>
            <div class="form-row full">
              <label>Notlar</label>
              <textarea name="Notlar" rows="3"></textarea>
            </div>
          </div>

          <input type="hidden" name="Id" />
          <input type="hidden" name="HayvanId" />
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('vaccine-modal').classList.add('hidden')" data-i18n="btn_cancel">İptal</button>
              <button type="submit" class="btn btn-primary">Kaydet</button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div id="treatment-modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="treatment-modal-title" data-i18n="treatment_modal_title_add">Tedavi Kaydı</h2>
        </div>
        <form id="treatment-form">
          <div class="modal-body">
          <!-- Diagnosis Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_diagnosis">Teşhis Bilgileri</h3>
            <div class="form-grid">
              <div class="form-row">
                <label data-i18n="label_teshis">Teşhis</label>
                <select name="Teshis" id="diagnosis-select" required>
                  <option value="">Seçiniz</option>
                  <option value="Mastitis">Mastitis</option>
                  <option value="Laminitis">Laminitis</option>
                  <option value="Pneumonia">Pneumonia</option>
                  <option value="Diarrhea">Diarrhea</option>
                  <option value="Ketosis">Ketosis</option>
                  <option value="Milk Fever">Milk Fever</option>
                  <option value="Retained Placenta">Retained Placenta</option>
                  <option value="Metritis">Metritis</option>
                  <option value="Bloat">Bloat</option>
                  <option value="Diğer">Diğer</option>
                </select>
                <input type="text" name="TeshisDiger" id="diagnosis-other" placeholder="Diğer teşhisi giriniz" style="display: none; margin-top: 8px;" />
              </div>
              <div class="form-row">
                <label data-i18n="label_tedavi_baslangic">Başlangıç Tarihi</label>
                <input type="date" name="TedaviBaslangicTarihi" required />
              </div>
              <div class="form-row">
                <label data-i18n="label_tedavi_bitis">Bitiş Tarihi</label>
                <input type="date" name="TedaviBitisTarihi" />
              </div>
              <div class="form-row">
                <label data-i18n="label_kullanilan_ilaclar">Kullanılan İlaçlar</label>
                <textarea name="KullanilanIlaclar" rows="2" placeholder="Kullanılan ilaçları giriniz"></textarea>
              </div>
              <div class="form-row">
                <label data-i18n="label_karantina_durumu">Karantina Durumu</label>
                <select name="KarantinaDurumu">
                  <option value="">Seçiniz</option>
                  <option value="Yok">Yok</option>
                  <option value="Var">Var</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Additional Notes Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_notes">Ek Notlar</h3>
            <div class="form-row full">
              <label data-i18n="label_notlar">Notlar</label>
              <textarea name="Notlar" rows="3"></textarea>
            </div>
          </div>

          <input type="hidden" name="Id" />
          <input type="hidden" name="HayvanId" />
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('treatment-modal').classList.add('hidden')" data-i18n="btn_cancel">İptal</button>
              <button type="submit" class="btn btn-primary" data-i18n="btn_save">Kaydet</button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div id="insemination-modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="insemination-modal-title" data-i18n="tohumlama_modal_title_add">Tohumlama Kaydı</h2>
        </div>
        <form id="insemination-form">
          <div class="modal-body">
          <!-- Insemination Details Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_insemination_details">Tohumlama Detayları</h3>
            <div class="form-grid">
              <div class="form-row">
                <label data-i18n="label_tohumlama_tarihi">Tohumlama Tarihi</label>
                <input type="date" name="TohumlamaTarihi" required />
              </div>
              <div class="form-row">
                <label data-i18n="label_tohumlama_tipi">Tohumlama Tipi</label>
                <select name="TohumlamaTipi" required>
                  <option value="">Seçiniz</option>
                  <option value="Suni Tohumlama">Suni Tohumlama</option>
                  <option value="Doğal Çiftleşme">Doğal Çiftleşme</option>
                </select>
              </div>
              <div class="form-row">
                <label data-i18n="label_sperma_kodu">Sperma Kodu</label>
                <select name="SpermaKodu" id="sperma-kodu-select">
                  <option value="">Seçiniz</option>
                  <option value="HOL001">HOL001 - Holstein Bull A</option>
                  <option value="HOL002">HOL002 - Holstein Bull B</option>
                  <option value="SIM001">SIM001 - Simmental Bull A</option>
                  <option value="ANG001">ANG001 - Angus Bull A</option>
                  <option value="Diğer">Diğer</option>
                </select>
                <input type="text" name="SpermaKoduDiger" id="sperma-kodu-other" placeholder="Diğer sperma kodunu giriniz" style="display: none; margin-top: 8px;" />
              </div>
            </div>
          </div>

          <!-- Additional Notes Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_notes">Ek Notlar</h3>
            <div class="form-row full">
              <label data-i18n="label_notlar">Notlar</label>
              <textarea name="Notlar" rows="3"></textarea>
            </div>
          </div>

          <input type="hidden" name="Id" />
          <input type="hidden" name="HayvanId" />
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('insemination-modal').classList.add('hidden')" data-i18n="btn_cancel">İptal</button>
              <button type="submit" class="btn btn-primary" data-i18n="btn_save">Kaydet</button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div id="pregnancy-modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="pregnancy-modal-title" data-i18n="pregnancy_modal_title_add">Gebelik Kaydı</h2>
        </div>
        <form id="pregnancy-form">
          <div class="modal-body">
          <!-- Pregnancy Timeline Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_pregnancy_timeline">Gebelik Bilgileri</h3>
            <div class="form-grid">
              <div class="form-row">
                <label data-i18n="label_gebelik_baslangic_tarihi">Başlangıç Tarihi</label>
                <input type="date" name="BaslangicTarihi" required />
              </div>
              <div class="form-row">
                <label data-i18n="label_beklenen_dogum_tarihi">Beklenen Doğum Tarihi</label>
                <input type="date" name="BeklenenDogumTarihi" />
              </div>
              <div class="form-row">
                <label data-i18n="label_gebelik_sonucu">Gebelik Sonucu</label>
                <select name="GebelikSonucu">
                  <option value="Devam Ediyor">Devam Ediyor</option>
                  <option value="Başarılı">Başarılı</option>
                  <option value="Başarısız">Başarısız</option>
                </select>
              </div>
              <div class="form-row">
                <label data-i18n="label_actual_birth_date">Doğum Tarihi</label>
                <input type="date" name="DogumTarihi" />
              </div>
            </div>
          </div>

          <!-- Additional Notes Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_notes">Ek Notlar</h3>
            <div class="form-row full">
              <label data-i18n="label_notlar">Notlar</label>
              <textarea name="Notlar" rows="3"></textarea>
            </div>
          </div>

          <input type="hidden" name="Id" />
          <input type="hidden" name="HayvanId" />
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('pregnancy-modal').classList.add('hidden')" data-i18n="btn_cancel">İptal</button>
              <button type="submit" class="btn btn-primary" data-i18n="btn_save">Kaydet</button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div id="lactation-modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="lactation-modal-title" data-i18n="lactation_modal_title_add">Laktasyon Kaydı</h2>
        </div>
        <form id="lactation-form">
          <div class="modal-body">
          <!-- Lactation Period Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_lactation_period">Laktasyon Bilgileri</h3>
            <div class="form-grid">
              <div class="form-row">
                <label data-i18n="label_laktasyon_baslangic">Başlangıç Tarihi</label>
                <input type="date" name="BaslangicTarihi" required />
              </div>
              <div class="form-row">
                <label data-i18n="label_laktasyon_bitis">Bitiş Tarihi</label>
                <input type="date" name="BitisTarihi" />
              </div>
            </div>
          </div>

          <input type="hidden" name="Id">
          <input type="hidden" name="HayvanId">
          <input type="hidden" name="GebelikId">
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('lactation-modal').classList.add('hidden')" data-i18n="btn_cancel">İptal</button>
              <button type="submit" class="btn btn-primary" data-i18n="btn_save">Kaydet</button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div id="milking-modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="milking-modal-title" data-i18n="milking_modal_title_add">Sağım Verisi Kaydı</h2>
        </div>
        <form id="milking-form">
          <div class="modal-body">
          <!-- Milking Session Section -->
          <div class="form-section">
            <h3 class="section-title" data-i18n="section_milking_session">Sağım Bilgileri</h3>
            <div class="form-grid">
              <div class="form-row">
                <label data-i18n="label_sagim_tarih">Tarih</label>
                <input type="date" name="Tarih" required />
              </div>
              <div class="form-row">
                <label data-i18n="label_sagim_saat">Saat</label>
                <input type="time" name="Saat" />
              </div>
              <div class="form-row">
                <label data-i18n="label_sagim_miktar">Miktar (L)</label>
                <input type="number" step="0.1" name="Miktar" required />
              </div>
              <div class="form-row">
                <label data-i18n="label_yag_orani">Yağ (%)</label>
                <input type="number" step="0.01" name="YagOrani" min="0" max="10" />
              </div>
              <div class="form-row">
                <label data-i18n="label_protein_orani">Protein (%)</label>
                <input type="number" step="0.01" name="ProteinOrani" min="0" max="6" />
              </div>
            </div>
          </div>

          <input type="hidden" name="Id">
          <input type="hidden" name="LaktasyonId">
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('milking-modal').classList.add('hidden')" data-i18n="btn_cancel">İptal</button>
              <button type="submit" class="btn btn-primary" data-i18n="btn_save">Kaydet</button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div id="bulk-milking-modal" class="modal hidden">
      <div class="modal-content large">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="bulk-milking-modal-title" data-i18n="bulk_milking_modal_title">Toplu Sağım Verisi Girdisi</h2>
        </div>
        <form id="bulk-milking-form">
          <div class="modal-body">
            <!-- Common Milking Information Section -->
            <div class="form-section">
              <h3 class="section-title" data-i18n="section_common_milking_info">Ortak Sağım Bilgileri</h3>
              <div class="form-grid">
                <div class="form-row">
                  <label data-i18n="label_sagim_tarih">Sağım Tarihi</label>
                  <input type="date" id="bulk-milking-date" required />
                </div>
                <div class="form-row">
                  <label data-i18n="label_sagim_saat">Sağım Saati</label>
                  <input type="time" id="bulk-milking-time" required />
                </div>
              </div>
            </div>

            <!-- Animals List Section -->
            <div class="form-section">
              <h3 class="section-title" data-i18n="section_animals_milking_data">Hayvan Sağım Verileri</h3>
              <div id="bulk-milking-animals-container">
                <div class="bulk-milking-table-container">
                  <table class="bulk-milking-table">
                    <thead>
                      <tr>
                        <th data-i18n="label_kupe_no">Küpe No</th>
                        <th data-i18n="label_isim">İsim</th>
                        <th data-i18n="label_sagim_miktar">Miktar (L)</th>
                        <th data-i18n="label_yag_orani">Yağ Oranı (%)</th>
                        <th data-i18n="label_protein_orani">Protein Oranı (%)</th>
                      </tr>
                    </thead>
                    <tbody id="bulk-milking-animals-list">
                      <!-- Animals will be populated here -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('bulk-milking-modal').classList.add('hidden')" data-i18n="btn_cancel">İptal</button>
              <button type="submit" class="btn btn-primary" data-i18n="btn_save_all">Tümünü Kaydet</button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div id="animal-detail-modal" class="modal hidden">
      <div class="modal-content detail-modal-content">
        <div class="detail-modal-header">
          <button class="detail-close-btn">&times;</button>
          <div class="detail-header-info">
            <div class="detail-avatar">
              <img id="animal-detail-avatar" src="" alt="Animal Avatar" onerror="this.style.display='none'; document.getElementById('animal-detail-avatar-placeholder').style.display='flex';">
              <div id="animal-detail-avatar-placeholder" class="detail-avatar-placeholder" style="display:none;">
                <i class="fas fa-paw"></i>
              </div>
            </div>
            <div class="detail-header-content">
              <h2 id="animal-detail-name" class="detail-title">Animal Name</h2>
              <div class="detail-tags">
                <span id="animal-detail-kupe-tag" class="detail-tag"></span>
                <span id="animal-detail-gender-tag" class="detail-tag"></span>
                <span id="animal-detail-age" class="detail-tag"></span>
              </div>
              <div class="detail-badges">
                <span id="animal-detail-pregnancy" class="badge"></span>
                <span id="animal-detail-status" class="badge"></span>
              </div>
            </div>
          </div>
        </div>
        <div class="detail-modal-tabs">
          <button class="detail-tab-link active" data-tab="animal-info-tab">
            <i class="fas fa-info-circle"></i>
            <span data-i18n="tab_general_info">General Info</span>
          </button>
          <button class="detail-tab-link" data-tab="animal-health-tab">
            <i class="fas fa-heartbeat"></i>
            <span data-i18n="tab_health_repro">Health & Reproduction</span>
          </button>
          <button class="detail-tab-link" data-tab="animal-notes-tab">
            <i class="fas fa-sticky-note"></i>
            <span data-i18n="tab_notes">Notes</span>
          </button>
        </div>
        <div class="detail-modal-body">
          <div id="animal-info-tab" class="detail-tab-content active">
            <div class="detail-info-sections">
              <!-- Basic Information Section -->
              <div class="detail-info-section">
                <div class="detail-section-header">
                  <i class="fas fa-id-card"></i>
                  <h3 data-i18n="section_basic_info">Basic Information</h3>
                </div>
                <div class="detail-info-cards">
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-tag"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_tur">Species</label>
                      <span id="animal-detail-tur" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-dna"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_irk">Breed</label>
                      <span id="animal-detail-irk" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-birthday-cake"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_dogum_tarihi">Birth Date</label>
                      <span id="animal-detail-dogum" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-weight"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_dogum_agirligi">Birth Weight</label>
                      <span id="animal-detail-dogum-agirligi" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-baby"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_dogum_tipi">Birth Type</label>
                      <span id="animal-detail-dogum-tipi" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-venus"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_anne_kupe">Mother Tag</label>
                      <span id="animal-detail-anne-kupe" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-mars"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_baba_kupe">Father Tag</label>
                      <span id="animal-detail-baba-kupe" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-crown"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_boynuz_durumu">Horn Status</label>
                      <span id="animal-detail-boynuz" class="detail-info-value">-</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Farm Management Section -->
              <div class="detail-info-section">
                <div class="detail-section-header">
                  <i class="fas fa-farm"></i>
                  <h3 data-i18n="section_farm_management">Farm Management</h3>
                </div>
                <div class="detail-info-cards">
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-sign-in-alt"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_isletmeye_giris">Farm Entry</label>
                      <span id="animal-detail-giris" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_isletmeden_cikis">Farm Exit</label>
                      <span id="animal-detail-cikis" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card full-width">
                    <div class="detail-info-icon">
                      <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_cikis_sebebi">Exit Reason</label>
                      <span id="animal-detail-cikis-sebebi" class="detail-info-value">-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="animal-health-tab" class="detail-tab-content">
            <div class="detail-info-sections">
              <div class="detail-info-section">
                <div class="detail-section-header">
                  <i class="fas fa-heartbeat"></i>
                  <h3 data-i18n="section_health_records">Health Records</h3>
                </div>
                <div class="detail-info-cards">
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-syringe"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_last_vaccine">Last Vaccine</label>
                      <span id="animal-detail-last-vaccine" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-pills"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_last_treatment">Last Treatment</label>
                      <span id="animal-detail-last-treatment" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-seedling"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_last_insemination">Last Insemination</label>
                      <span id="animal-detail-last-insemination" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-heart"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_last_pregnancy">Last Pregnancy</label>
                      <span id="animal-detail-last-pregnancy" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-tint"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_daily_milk_avg">Daily Avg. Milk</label>
                      <span id="animal-detail-daily-milk" class="detail-info-value">-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="animal-notes-tab" class="detail-tab-content">
            <div class="detail-info-sections">
              <div class="detail-info-section">
                <div class="detail-section-header">
                  <i class="fas fa-sticky-note"></i>
                  <h3 data-i18n="section_notes">Notes</h3>
                </div>
                <div style="padding: 16px; background: var(--content-bg); border-radius: 8px; border: 1px solid var(--border-color); min-height: 100px;">
                  <p id="animal-detail-notlar" style="margin: 0; color: var(--text-color); line-height: 1.6;">No notes available.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="cari-detail-modal" class="modal hidden">
      <div class="modal-content detail-modal-content">
        <div class="detail-modal-header">
          <button class="detail-close-btn">&times;</button>
          <div class="detail-header-info">
            <div class="detail-avatar">
              <div class="detail-avatar-placeholder">
                <i class="fas fa-user-tie"></i>
              </div>
            </div>
            <div class="detail-header-content">
              <h2 id="cari-detail-name" class="detail-title">Customer Name</h2>
              <div class="detail-tags">
                <span id="cari-detail-type-tag" class="detail-tag"></span>
                <span id="cari-detail-balance-tag" class="detail-tag"></span>
              </div>
            </div>
          </div>
        </div>
        <div class="detail-modal-tabs">
          <button class="detail-tab-link active" data-tab="cari-info-tab">
            <i class="fas fa-info-circle"></i>
            <span data-i18n="tab_general_info">General Info</span>
          </button>
          <button class="detail-tab-link" data-tab="cari-balance-tab">
            <i class="fas fa-chart-line"></i>
            <span data-i18n="tab_balance_info">Balance Info</span>
          </button>
          <button class="detail-tab-link" data-tab="cari-transactions-tab">
            <i class="fas fa-history"></i>
            <span data-i18n="tab_transactions">Transactions</span>
          </button>
        </div>
        <div class="detail-modal-body">
          <div id="cari-info-tab" class="detail-tab-content active">
            <div class="detail-info-sections">
              <!-- Contact Information Section -->
              <div class="detail-info-section">
                <div class="detail-section-header">
                  <i class="fas fa-address-card"></i>
                  <h3 data-i18n="section_contact_info">Contact Information</h3>
                </div>
                <div class="detail-info-cards">
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-id-badge"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_cari_kodu">Customer Code</label>
                      <span id="cari-detail-code" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-phone"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_telefon">Phone</label>
                      <span id="cari-detail-phone" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-envelope"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_email">Email</label>
                      <span id="cari-detail-email" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card full-width">
                    <div class="detail-info-icon">
                      <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_adres">Address</label>
                      <span id="cari-detail-address" class="detail-info-value">-</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Tax Information Section -->
              <div class="detail-info-section">
                <div class="detail-section-header">
                  <i class="fas fa-receipt"></i>
                  <h3 data-i18n="section_tax_info">Tax Information</h3>
                </div>
                <div class="detail-info-cards">
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-hashtag"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_vergi_no">Tax Number</label>
                      <span id="cari-detail-taxno" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-university"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_vergi_dairesi">Tax Office</label>
                      <span id="cari-detail-taxoffice" class="detail-info-value">-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="cari-balance-tab" class="detail-tab-content">
            <div class="detail-info-sections">
              <div class="detail-info-section">
                <div class="detail-section-header">
                  <i class="fas fa-chart-line"></i>
                  <h3 data-i18n="section_balance_summary">Balance Summary</h3>
                </div>
                <div class="detail-info-cards">
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_total_purchases">Total Purchases</label>
                      <span id="cari-detail-total-purchases" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-cash-register"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_total_sales">Total Sales</label>
                      <span id="cari-detail-total-sales" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_total_payments_made">Payments Made</label>
                      <span id="cari-detail-total-payments-made" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card">
                    <div class="detail-info-icon">
                      <i class="fas fa-hand-holding-dollar"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_total_payments_received">Payments Received</label>
                      <span id="cari-detail-total-payments-received" class="detail-info-value">-</span>
                    </div>
                  </div>
                  <div class="detail-info-card full-width">
                    <div class="detail-info-icon">
                      <i class="fas fa-balance-scale"></i>
                    </div>
                    <div class="detail-info-content">
                      <label data-i18n="label_current_balance">Current Balance</label>
                      <span id="cari-detail-current-balance" class="detail-info-value">-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="cari-transactions-tab" class="detail-tab-content">
            <div class="detail-info-sections">
              <div class="detail-info-section">
                <div class="detail-section-header">
                  <i class="fas fa-history"></i>
                  <h3 data-i18n="section_recent_transactions">Recent Transactions</h3>
                  <button id="account-statement-btn" class="btn btn-primary" style="margin-left: auto;">
                    <i class="fas fa-file-invoice"></i>
                    <span data-i18n="btn_account_statement">Hesap Ekstresi</span>
                  </button>
                </div>
                <div style="background: var(--content-bg); border-radius: 8px; border: 1px solid var(--border-color); overflow: hidden;">
                  <table id="cari-transactions-table" class="modern-table" style="margin: 0;">
                    <thead>
                      <tr>
                        <th data-i18n="label_date">Date</th>
                        <th data-i18n="label_type">Type</th>
                        <th data-i18n="label_description">Description</th>
                        <th data-i18n="label_amount">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- Transaction rows will be added here -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



  <div id="account-statement-modal" class="statement-modal-overlay hidden">
    <div class="modal-content statement-modal">
      <!-- Modal Header -->
      <div class="statement-modal-header">
        <div class="statement-title">
          <i class="fas fa-file-invoice-dollar"></i>
          <span id="statement-account-title" data-i18n="account_statement_title">Hesap Ekstresi</span>
        </div>
        <button class="statement-close-btn" type="button">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Date Range Controls -->
      <div class="statement-controls">
        <div class="date-inputs">
          <div class="date-group">
            <label for="statement-start-date" data-i18n="label_start_date">Başlangıç Tarihi</label>
            <input type="date" id="statement-start-date" class="date-input">
          </div>
          <div class="date-group">
            <label for="statement-end-date" data-i18n="label_end_date">Bitiş Tarihi</label>
            <input type="date" id="statement-end-date" class="date-input">
          </div>
          <button id="generate-statement-btn" class="generate-btn">
            <i class="fas fa-search"></i>
            <span data-i18n="btn_generate">Oluştur</span>
          </button>
        </div>
      </div>

      <!-- Statement Content Area -->
      <div class="statement-main-content">
        <!-- Loading State -->
        <div id="statement-loading" class="statement-empty-state">
          <div class="empty-icon">
            <i class="fas fa-file-invoice"></i>
          </div>
          <p data-i18n="select_date_range_message">Hesap ekstresi oluşturmak için tarih aralığı seçin ve "Oluştur" butonuna tıklayın.</p>
        </div>

        <!-- Statement Results -->
        <div id="statement-results" class="statement-results" style="display: none;">
          <!-- Transactions Table -->
          <div class="statement-table-container">
            <table id="statement-table" class="statement-table">
              <thead>
                <tr>
                  <th data-i18n="label_date">Tarih</th>
                  <th data-i18n="label_type">İşlem Tipi</th>
                  <th data-i18n="label_invoice_no">Fatura No</th>
                  <th data-i18n="label_description">Açıklama</th>
                  <th data-i18n="label_debit">Borç</th>
                  <th data-i18n="label_credit">Alacak</th>
                  <th data-i18n="label_balance">Bakiye</th>
                </tr>
              </thead>
              <tbody>
                <!-- Statement rows will be added here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Export Buttons -->
      <div class="statement-footer">
        <div id="statement-export-buttons" class="export-buttons" style="display: none;">
          <button id="export-statement-pdf" class="export-btn pdf-btn">
            <i class="fas fa-file-pdf"></i>
            <span data-i18n="btn_export_pdf">PDF İndir</span>
          </button>
          <button id="export-statement-excel" class="export-btn excel-btn">
            <i class="fas fa-file-excel"></i>
            <span data-i18n="btn_export_excel">Excel İndir</span>
          </button>
          <button id="print-statement" class="export-btn print-btn">
            <i class="fas fa-print"></i>
            <span data-i18n="btn_print">Yazdır</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Choices.js JS -->
  <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
  <!-- pdfmake CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
  <!-- xlsx (SheetJS) CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
  <script src="i18n.js"></script>
  <script src="modal-utils.js"></script>
  <script type="module" src="./app.js"></script>
</body>
</html>
// Expects contentArea, i18n, global applyLanguage function, and global applyTheme function

export async function renderSettingsPage(contentArea, i18n, applyLanguageCallback, applyThemeCallback) {
  const t = i18n.t;
  const appVersion = await window.api.invoke('settings:get-app-version');

  contentArea.innerHTML = `
    <div id="settings-page" class="settings-page-container">
      <div class="settings-section modern-settings-section">
        <h2 class="settings-section-title">${t('label_appearance')}</h2>
        <div class="setting-item modern-setting-item">
          <label for="theme-select" class="setting-label">${t('label_theme')}</label>
          <select id="theme-select" class="setting-select">
            <option value="system">${t('option_system')}</option>
            <option value="light">${t('option_light')}</option>
            <option value="dark">${t('option_dark')}</option>
          </select>
        </div>
        <div class="setting-item modern-setting-item">
          <label for="language-select" class="setting-label">${t('label_language')}</label>
          <select id="language-select" class="setting-select">
            <option value="tr">${t('language_tr')}</option>
            <option value="en">${t('language_en')}</option>
          </select>
        </div>
      </div>

      <div class="settings-section modern-settings-section">
        <h2 class="settings-section-title">Kullanıcı Hesabı</h2>
        <div class="setting-item modern-setting-item">
          <label class="setting-label">Oturum Bilgileri</label>
          <div class="user-session-clickable modern-clickable-item" id="logout-btn">
            <div class="session-details">
              <span id="current-user-name">Yükleniyor...</span>
              <small id="last-login-time">Son giriş: Yükleniyor...</small>
            </div>
            <div class="logout-indicator">
              <span class="logout-icon">🚪</span>
            </div>
          </div>
        </div>
      </div>

      <div class="settings-section modern-settings-section">
        <h2 class="settings-section-title">Profil Yönetimi</h2>
        <div class="setting-item modern-setting-item">
          <label class="setting-label">Aktif Profil</label>
          <div class="profile-current-clickable modern-clickable-item" id="switch-profile-btn">
            <span id="current-profile-name">Yükleniyor...</span>
            <span class="profile-switch-icon">🔄</span>
          </div>
        </div>
        <div class="setting-item modern-setting-item">
          <label class="setting-label">Profil İşlemleri</label>
          <div class="profile-actions modern-profile-actions">
            <button id="create-profile-btn" class="btn btn-primary">✨ Yeni Profil Oluştur</button>
            <button id="manage-profiles-btn" class="btn btn-secondary">⚙️ Profilleri Yönet</button>
          </div>
        </div>
      </div>

      <div class="settings-section modern-settings-section">
        <h2 class="settings-section-title">${t('label_database')}</h2>
        <div class="setting-item modern-setting-item">
          <p class="setting-description">${t('db_backup_recommendation')}</p>
          <div class="db-buttons modern-db-buttons">
            <button id="backup-db-btn" class="btn btn-primary">${t('btn_backup_db')}</button>
            <button id="restore-db-btn" class="btn btn-danger">${t('btn_restore_db')}</button>
          </div>
        </div>
      </div>

      <div class="settings-section modern-settings-section">
        <h2 class="settings-section-title">${t('label_about')}</h2>
        <div class="setting-item modern-setting-item">
          <p class="setting-description">${t('label_app_version')}: ${appVersion}</p>
        </div>
      </div>
    </div>
  `;

  // Theme Changer
  const themeSelect = contentArea.querySelector('#theme-select');
  const savedTheme = localStorage.getItem('theme') || 'system';
  themeSelect.value = savedTheme;

  themeSelect.addEventListener('change', (e) => {
    const selectedTheme = e.target.value;
    localStorage.setItem('theme', selectedTheme);

    // Use global applyTheme function or callback
    if (typeof window.applyTheme === 'function') {
      window.applyTheme(selectedTheme);
    } else if (typeof applyThemeCallback === 'function') {
      applyThemeCallback(selectedTheme);
    }
  });

  // Language Changer
  const langSelect = contentArea.querySelector('#language-select');
  langSelect.value = i18n.getLanguage();
  langSelect.addEventListener('change', (e) => {
    applyLanguageCallback(e.target.value); // Call the passed applyLanguage function
  });

  // Database Buttons
  contentArea.querySelector('#backup-db-btn').addEventListener('click', async () => {
    const result = await window.api.invoke('settings:backup-db');
    if (result.success) {
      window.toast.success(t('alert_backup_success') + result.path);
    } else if (result.error !== 'İşlem iptal edildi.') {
      window.toast.error(t('alert_error') + result.error);
    }
  });

  contentArea.querySelector('#restore-db-btn').addEventListener('click', async () => {
    const confirmed = await window.toast.confirm(t('confirm_restore_db'), {
      confirmText: t('toast_confirm'),
      cancelText: t('toast_cancel')
    });

    if (confirmed) {
      const result = await window.api.invoke('settings:restore-db');
      if (result.error && result.error !== 'İşlem iptal edildi.') {
        window.toast.error(t('alert_error') + result.error);
      }
      // App will restart on successful restore from main.js
    }
  });

  // User Session and Profile Management
  await loadCurrentSession();

  // User Session Event Handlers
  contentArea.querySelector('#logout-btn').addEventListener('click', async () => {
    const confirmed = await window.toast.confirm('Çıkış yapmak istediğinizden emin misiniz?', {
      confirmText: 'Çıkış Yap',
      cancelText: 'İptal'
    });

    if (confirmed) {
      try {
        // Ask if user wants to clear "Remember Me" as well
        const clearRemembered = await window.toast.confirm('Hatırlanan giriş bilgilerini de temizlemek istiyor musunuz?', {
          confirmText: 'Evet, Temizle',
          cancelText: 'Hayır, Kalsın'
        });

        await window.api.invoke('auth:logout', clearRemembered);

        // Clear remembered credentials if requested
        if (clearRemembered) {
          localStorage.removeItem('livestock_remembered_user');
        }

        window.toast.success('Başarıyla çıkış yapıldı');

        // Redirect to login page after short delay
        setTimeout(() => {
          window.location.href = 'login.html';
        }, 1000);
      } catch (error) {
        console.error('Logout error:', error);
        window.toast.error('Çıkış yapılırken hata oluştu');
        // Force redirect even if logout fails
        setTimeout(() => {
          window.location.href = 'login.html';
        }, 2000);
      }
    }
  });

  // Profile Management Event Handlers
  contentArea.querySelector('#create-profile-btn').addEventListener('click', () => {
    showCreateProfileModal();
  });

  contentArea.querySelector('#switch-profile-btn').addEventListener('click', () => {
    showProfileSwitchModal();
  });

  contentArea.querySelector('#manage-profiles-btn').addEventListener('click', () => {
    showManageProfilesModal();
  });

  // Load current session and profile information
  async function loadCurrentSession() {
    try {
      const session = await window.api.invoke('auth:getCurrentSession');

      // Update user information
      if (session.user) {
        contentArea.querySelector('#current-user-name').textContent = session.user.adSoyad;

        // Format last login time
        if (session.user.sonGirisTarihi) {
          const lastLogin = new Date(session.user.sonGirisTarihi);
          const formattedTime = lastLogin.toLocaleDateString('tr-TR') + ' ' +
                               lastLogin.toLocaleTimeString('tr-TR', {
                                 hour: '2-digit',
                                 minute: '2-digit'
                               });
          contentArea.querySelector('#last-login-time').textContent = `Son giriş: ${formattedTime}`;
        } else {
          contentArea.querySelector('#last-login-time').textContent = 'Son giriş: İlk giriş';
        }
      } else {
        contentArea.querySelector('#current-user-name').textContent = 'Kullanıcı bulunamadı';
        contentArea.querySelector('#last-login-time').textContent = 'Son giriş: Bilinmiyor';
      }

      // Update profile information
      if (session.profile) {
        contentArea.querySelector('#current-profile-name').textContent = session.profile.profilAdi;
      } else {
        contentArea.querySelector('#current-profile-name').textContent = 'Profil bulunamadı';
      }
    } catch (error) {
      console.error('Error loading current session:', error);
      contentArea.querySelector('#current-user-name').textContent = 'Hata';
      contentArea.querySelector('#last-login-time').textContent = 'Son giriş: Hata';
      contentArea.querySelector('#current-profile-name').textContent = 'Hata';
    }
  }

  // Show create profile modal
  function showCreateProfileModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
      <div class="modal-content modal-compact">
        <div class="modal-header">
          <h2>Yeni Profil Oluştur</h2>
          <span class="close-btn">&times;</span>
        </div>
        <form id="create-profile-form">
          <div class="modal-body">
            <div class="form-row">
              <label for="profile-name">Profil Adı</label>
              <input type="text" id="profile-name" name="profilAdi" required placeholder="Örn: Ana Çiftlik, Test Profili">
            </div>
            <div class="form-row">
              <label for="profile-description">Açıklama (İsteğe bağlı)</label>
              <textarea id="profile-description" name="aciklama" rows="3" placeholder="Bu profil hakkında kısa bir açıklama yazın..."></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">İptal</button>
              <button type="submit" class="btn btn-primary">Oluştur</button>
            </div>
          </div>
        </form>
      </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';

    // Handle form submission
    modal.querySelector('#create-profile-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);
      const profileData = Object.fromEntries(formData);

      try {
        const result = await window.api.invoke('profiles:create', profileData);
        window.toast.success(result.message);
        modal.remove();
        await loadCurrentSession();
      } catch (error) {
        console.error('Error creating profile:', error);
        window.toast.error(error.message || 'Profil oluşturulurken hata oluştu');
      }
    });

    // Handle close button
    modal.querySelector('.close-btn').addEventListener('click', () => {
      modal.remove();
    });
  }

  // Show profile switch modal
  async function showProfileSwitchModal() {
    try {
      const profiles = await window.api.invoke('profiles:list');

      const modal = document.createElement('div');
      modal.className = 'modal profile-switch-modal';
      modal.innerHTML = `
        <div class="profile-switch-content">
          <div class="profile-switch-header">
            <div class="profile-switch-header-content">
              <h2 class="profile-switch-title">🔄 Profil Değiştir</h2>
              <p class="profile-switch-description">
                Çalışmak istediğiniz profili seçin. Verileriniz seçilen profile göre filtrelenecektir.
              </p>
            </div>
            <span class="profile-switch-close">&times;</span>
          </div>
          <div class="profile-switch-body">
            <div class="profile-switch-list">
              ${profiles.map(profile => `
                <div class="profile-switch-item" data-profile-id="${profile.Id}">
                  <div class="profile-switch-info">
                    <h4 class="profile-switch-name">📁 ${profile.ProfilAdi}</h4>
                    <p class="profile-switch-desc">${profile.Aciklama || 'Bu profil için açıklama eklenmemiş'}</p>
                    ${profile.VarsayilanMi ? '<span class="profile-switch-badge">⭐ VARSAYILAN</span>' : ''}
                  </div>
                  <div class="profile-switch-indicator">
                    <span class="profile-switch-icon">👆</span>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          <div class="profile-switch-footer">
            <div class="profile-switch-actions">
              <button type="button" class="profile-switch-cancel" onclick="this.closest('.modal').remove()">İptal</button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      modal.style.display = 'flex';

      // Handle profile selection - click on entire profile item
      modal.querySelectorAll('.profile-switch-item').forEach(item => {
        item.addEventListener('click', async () => {
          const profileId = item.dataset.profileId;
          try {
            const result = await window.api.invoke('profiles:switch', parseInt(profileId));
            window.toast.success(result.message);
            modal.remove();
            await loadCurrentSession();
            // Optionally reload the page to reflect profile change
            window.location.reload();
          } catch (error) {
            console.error('Error switching profile:', error);
            window.toast.error(error.message || 'Profil değiştirilirken hata oluştu');
          }
        });
      });

      // Handle close button
      modal.querySelector('.profile-switch-close').addEventListener('click', () => {
        modal.remove();
      });

    } catch (error) {
      console.error('Error loading profiles:', error);
      window.toast.error('Profiller yüklenirken hata oluştu');
    }
  }

  // Show manage profiles modal
  async function showManageProfilesModal() {
    try {
      const profiles = await window.api.invoke('profiles:list');

      const modal = document.createElement('div');
      modal.className = 'modal profile-manage-modal';
      modal.innerHTML = `
        <div class="profile-manage-content">
          <div class="profile-manage-header">
            <div class="profile-manage-header-content">
              <h2 class="profile-manage-title">⚙️ Profilleri Yönet</h2>
              <p class="profile-manage-description">
                Profillerinizi yönetin. Varsayılan profil silinemez ancak diğer profilleri silebilirsiniz.
              </p>
            </div>
            <span class="profile-manage-close">&times;</span>
          </div>
          <div class="profile-manage-body">
            <div class="profile-manage-list">
              ${profiles.map(profile => `
                <div class="profile-manage-item" data-profile-id="${profile.Id}">
                  <div class="profile-manage-info">
                    <h4 class="profile-manage-name">📂 ${profile.ProfilAdi}</h4>
                    <p class="profile-manage-desc">${profile.Aciklama || 'Bu profil için açıklama eklenmemiş'}</p>
                    <small class="profile-manage-date">📅 Oluşturulma: ${new Date(profile.OlusturmaTarihi).toLocaleDateString('tr-TR')}</small>
                    ${profile.VarsayilanMi ? '<span class="profile-manage-badge">⭐ VARSAYILAN</span>' : ''}
                  </div>
                  <div class="profile-manage-actions">
                    ${!profile.VarsayilanMi ? `
                      <button class="profile-manage-delete" data-profile-id="${profile.Id}">
                        🗑️ Sil
                      </button>
                    ` : '<span class="profile-manage-protected">🔒 Varsayılan profil korunuyor</span>'}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          <div class="profile-manage-footer">
            <div class="profile-manage-form-actions">
              <button type="button" class="profile-manage-cancel" onclick="this.closest('.modal').remove()">Kapat</button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      modal.style.display = 'flex';

      // Handle profile deletion
      modal.querySelectorAll('.profile-manage-delete').forEach(btn => {
        btn.addEventListener('click', async (e) => {
          const profileId = e.target.dataset.profileId;
          const confirmed = await window.toast.confirm('Bu profili silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.', {
            confirmText: 'Sil',
            cancelText: 'İptal'
          });

          if (confirmed) {
            try {
              const result = await window.api.invoke('profiles:delete', parseInt(profileId));
              window.toast.success(result.message);
              modal.remove();
              await loadCurrentSession();
              showManageProfilesModal(); // Refresh the modal
            } catch (error) {
              console.error('Error deleting profile:', error);
              window.toast.error(error.message || 'Profil silinirken hata oluştu');
            }
          }
        });
      });

      // Handle close button
      modal.querySelector('.profile-manage-close').addEventListener('click', () => {
        modal.remove();
      });

    } catch (error) {
      console.error('Error loading profiles for management:', error);
      window.toast.error('Profiller yüklenirken hata oluştu');
    }
  }
}

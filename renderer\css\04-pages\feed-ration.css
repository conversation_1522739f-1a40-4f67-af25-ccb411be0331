/* =================================
   Feed Ration Page Styles
   ================================= */

/* =================================
   Feed Ration Header Section
   ================================= */
.feed-ration-header-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.feed-ration-header-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.feed-ration-page-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.feed-ration-page-title svg {
  width: 24px;
  height: 24px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

/* =================================
   Feed Ration Navigation Section
   ================================= */
.feed-ration-navigation-section {
  margin-bottom: var(--space-6);
}

.feed-ration-nav-tabs {
  display: flex;
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-1);
  margin-bottom: var(--space-6);
  overflow-x: auto;
  box-shadow: var(--shadow-sm);
  gap: var(--space-1);
}

.feed-ration-tab {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: var(--radius-lg);
  white-space: nowrap;
  font-size: var(--text-sm);
  min-width: 140px;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  justify-content: center;
}

.feed-ration-tab:hover {
  color: var(--text-primary);
  background: var(--interactive-secondary);
  transform: translateY(-1px);
}

.feed-ration-tab.active {
  color: var(--text-inverse);
  background: var(--interactive-primary);
  box-shadow: var(--shadow-sm);
}

.feed-ration-tab svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.feed-ration-tab span {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

/* =================================
   Feed Ration Content Area
   ================================= */
.feed-ration-tab-container {
  margin-bottom: var(--space-6);
}

.feed-ration-content {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  min-height: 500px;
  transition: all var(--transition-fast);
  overflow: visible;
}

.feed-ration-content:hover {
  box-shadow: var(--shadow-md);
}

.feed-ration-content-section {
  display: none;
}

.feed-ration-content-section.active {
  display: block;
}

/* Section Headers */
.feed-ration-content .section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
}

.feed-ration-content .section-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.feed-ration-content .section-header h3 svg {
  width: 18px;
  height: 18px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

.feed-ration-actions {
  display: flex;
  gap: var(--space-3);
}

/* =================================
   Feed Ration Manager Cards
   ================================= */
/* =================================
   Feed Ration Managers Layout
   ================================= */
.feed-ration-managers {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  width: 100%;
  overflow: visible;
}

/* Top Row: 2 cards side by side (50% each) */
.feed-ration-top-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
  width: 100%;
}

/* Bottom Rows: Full width cards (100% each) */
.feed-ration-bottom-row {
  display: flex;
  width: 100%;
}

.feed-ration-bottom-row .feed-ration-manager {
  width: 100%;
  flex: 1;
}

.feed-ration-manager {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: visible;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  width: 100%;
  min-height: 300px;
}

/* Specific styling for nutritional needs table container */
.feed-ration-manager:nth-child(2) .table-container {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: visible;
}

/* Fix tooltip clipping in scrollable container */
.feed-ration-manager:nth-child(2) .table-container {
  position: relative;
  overflow-x: visible; /* Allow tooltips to extend beyond container */
}

/* Tooltips in scrollable nutritional needs table - override default behavior */
.feed-ration-manager:nth-child(2) .tooltip {
  position: fixed !important;
  z-index: 10000 !important;
  pointer-events: none !important;
  bottom: auto !important;
  left: auto !important;
  right: auto !important;
  top: auto !important;
  transform: none !important;
  transition: opacity 0.2s ease, visibility 0.2s ease !important;
}

/* Disable ALL default hover behavior for scrollable tooltips */
.feed-ration-manager:nth-child(2) .tooltip-container:hover .tooltip {
  opacity: 0 !important;
  visibility: hidden !important;
  position: fixed !important;
  bottom: auto !important;
  left: auto !important;
  right: auto !important;
  top: auto !important;
  transform: none !important;
}

/* Hide arrow for fixed positioned tooltips in scrollable area */
.feed-ration-manager:nth-child(2) .tooltip::after {
  display: none !important;
}

/* Feed Form Styling */
.feed-form {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: var(--space-2);
}

.form-section {
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-light);
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-2);
  border-bottom: 2px solid var(--primary-color);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.form-section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: var(--primary-color);
  border-radius: 2px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  align-items: start;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.feed-form .form-group label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
  display: block;
}

.feed-form .form-group input,
.feed-form .form-group select,
.feed-form .form-group textarea {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
  background: var(--bg-elevated);
}

.feed-form .form-group input:focus,
.feed-form .form-group select:focus,
.feed-form .form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-color-alpha);
}

.feed-form .form-group input[type="number"] {
  text-align: right;
}

.feed-form .form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Modal adjustments for larger form */
.modal-container {
  max-width: 900px;
  max-height: 90vh;
  width: 90vw;
}

.modal-body {
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* Feed table styling */
.feed-name {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.feed-type-badge {
  font-size: var(--text-xs);
  padding: 2px 6px;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  width: fit-content;
}

.actions {
  display: flex;
  gap: var(--space-2);
  justify-content: center;
}

.text-center {
  text-align: center;
}

.text-muted {
  color: var(--text-muted);
  font-style: italic;
}

/* Custom scrollbar for nutritional needs table */
.feed-ration-manager:nth-child(2) .table-container::-webkit-scrollbar {
  width: 8px;
}

.feed-ration-manager:nth-child(2) .table-container::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  margin: var(--space-2) 0;
}

.feed-ration-manager:nth-child(2) .table-container::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: var(--radius-md);
  border: 2px solid var(--bg-elevated);
}

.feed-ration-manager:nth-child(2) .table-container::-webkit-scrollbar-thumb:hover {
  background: var(--interactive-primary);
}

/* =================================
   Ration Builder Table Container
   ================================= */
/* Ration builder table container - prevent overflow from card */
.ration-builder-container .table-container {
  max-height: 400px;
  overflow-x: auto;
  overflow-y: auto;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-elevated);
  box-shadow: var(--shadow-sm);
}

/* Custom scrollbar for ration builder table */
.ration-builder-container .table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ration-builder-container .table-container::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  margin: var(--space-1);
}

.ration-builder-container .table-container::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: var(--radius-md);
  border: 2px solid var(--bg-elevated);
}

.ration-builder-container .table-container::-webkit-scrollbar-thumb:hover {
  background: var(--interactive-primary);
}

.ration-builder-container .table-container::-webkit-scrollbar-corner {
  background: var(--bg-tertiary);
}

/* Ensure table fits within container */
.ration-builder-container .table-container table {
  margin: 0;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

/* Fix table header stickiness in scrollable container */
.ration-builder-container .table-container thead th {
  position: sticky;
  top: 0;
  z-index: 10;
  background: var(--bg-secondary);
  border-bottom: 2px solid var(--border-primary);
}

/* Keep header visible */
.feed-ration-manager:nth-child(2) .feed-ration-manager-header {
  position: sticky;
  top: 0;
  background: var(--bg-elevated);
  z-index: 10;
  border-bottom: 1px solid var(--border-primary);
  margin-bottom: var(--space-4);
}

.feed-ration-manager:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.feed-ration-manager-header {
  padding: var(--space-6);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feed-ration-manager-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.feed-ration-manager-actions {
  display: flex;
  gap: var(--space-2);
}

.feed-ration-manager-body {
  padding: var(--space-6);
}

/* =================================
   Feed Ration Tabs
   ================================= */
.feed-ration-tabs {
  display: flex;
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-1);
  margin-bottom: var(--space-6);
  overflow-x: auto;
  box-shadow: var(--shadow-sm);
  gap: var(--space-1);
}

.feed-ration-tab {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: var(--radius-lg);
  white-space: nowrap;
  font-size: var(--text-sm);
  min-width: 140px;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  justify-content: center;
}

.feed-ration-tab:hover {
  color: var(--text-primary);
  background: var(--interactive-secondary);
  transform: translateY(-1px);
}

.feed-ration-tab.active {
  color: var(--text-inverse);
  background: var(--interactive-primary);
  box-shadow: var(--shadow-sm);
}

/* =================================
   Feed Ration Content
   ================================= */
.feed-ration-content {
  min-height: 300px;
}

.feed-ration-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.feed-ration-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.feed-ration-form-group label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.feed-ration-form-group input,
.feed-ration-form-group select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.feed-ration-form-group input:focus,
.feed-ration-form-group select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

/* =================================
   Feed Ration Results
   ================================= */
.feed-ration-results {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-top: var(--space-4);
}

.feed-ration-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.feed-ration-results-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.feed-ration-results-actions {
  display: flex;
  gap: var(--space-2);
}

/* =================================
   Feed Ration Table
   ================================= */
.feed-ration-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
  background: var(--bg-elevated);
}

.feed-ration-table th,
.feed-ration-table td {
  padding: var(--space-2) var(--space-3);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.feed-ration-table th {
  background: var(--bg-secondary);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-size: var(--text-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.feed-ration-table td {
  color: var(--text-secondary);
}

.feed-ration-table tbody tr:hover {
  background: var(--bg-secondary);
}

/* =================================
   Ration Builder Styles
   ================================= */
.ration-builder-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.ration-builder-footer {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-top: var(--space-4);
}

.add-feed-form {
  display: grid;
  grid-template-columns: 2fr 1fr auto;
  gap: var(--space-4);
  align-items: end;
}

.add-feed-form .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.add-feed-form label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.add-feed-form .form-control {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.add-feed-form .form-control:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.totals-row {
  background: var(--bg-secondary) !important;
  font-weight: var(--font-semibold);
  border-top: 2px solid var(--border-primary);
}

.totals-row td {
  padding: var(--space-3) var(--space-4) !important;
  color: var(--text-primary);
}

#ration-builder-table .remove-feed-btn {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  min-width: auto;
}

/* =================================
   Feed Management Styles
   ================================= */
.card-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.card-title svg {
  width: 18px;
  height: 18px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

/* =================================
   Modal Styles
   ================================= */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-container {
  background: var(--bg-elevated);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.modal-title svg {
  width: 20px;
  height: 20px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-6);
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

/* =================================
   Form Styles
   ================================= */
.feed-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* =================================
   Nutritional Calculation Styles
   ================================= */
#nutritional-needs-tbody {
  transition: background-color var(--transition-fast);
}

#nutritional-needs-tbody td {
  transition: all var(--transition-fast);
}

.feed-ration-form input:invalid,
.feed-ration-form select:invalid {
  border-color: var(--danger-500);
}

.feed-ration-form input:valid,
.feed-ration-form select:valid {
  border-color: var(--success-500);
}

.calculation-highlight {
  background-color: var(--bg-success-subtle) !important;
  animation: highlightFade 2s ease-out;
}

@keyframes highlightFade {
  0% {
    background-color: var(--success-200);
  }
  100% {
    background-color: var(--bg-success-subtle);
  }
}

/* =================================
   Tooltip Styles
   ================================= */
.tooltip-container {
  position: relative;
  display: inline-block;
  cursor: help;
}

.tooltip-container:hover {
  color: var(--interactive-primary);
}

.tooltip {
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-elevated);
  color: var(--text-primary);
  padding: var(--space-4) var(--space-5);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-primary);
  font-size: var(--text-sm);
  line-height: 1.5;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  min-width: 400px;
  max-width: 500px;
  width: max-content;
  white-space: normal;
  text-align: left;
  pointer-events: none;
  word-wrap: break-word;
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: var(--bg-elevated);
}

.tooltip-container:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

.tooltip-title {
  font-weight: var(--font-semibold);
  color: var(--interactive-primary);
  margin-bottom: var(--space-2);
  display: block;
  font-size: var(--text-base);
  border-bottom: 1px solid var(--border-primary);
  padding-bottom: var(--space-1);
}

.tooltip-formula {
  font-family: 'Courier New', monospace;
  background: var(--bg-secondary);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  margin: var(--space-2) 0;
  font-size: var(--text-sm);
  color: var(--text-primary);
  border-left: 3px solid var(--interactive-primary);
  font-weight: var(--font-medium);
}

.tooltip-source {
  font-style: italic;
  color: var(--text-tertiary);
  font-size: var(--text-xs);
  margin-top: var(--space-3);
  padding-top: var(--space-2);
  border-top: 1px solid var(--border-secondary);
}

.tooltip p {
  margin: var(--space-2) 0;
  line-height: 1.6;
}

.tooltip strong {
  color: var(--text-primary);
  font-weight: var(--font-semibold);
}

/* Smart tooltip positioning - disabled for scrollable areas */
.tooltip-container:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* Completely disable default tooltip behavior in scrollable nutritional table */
.feed-ration-manager:nth-child(2) .tooltip-container:hover .tooltip {
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Left-aligned tooltips for right side elements */
.tooltip-container:nth-child(n+3) .tooltip {
  left: auto;
  right: 0;
  transform: translateX(0);
}

.tooltip-container:nth-child(n+3) .tooltip::after {
  left: auto;
  right: 20px;
  transform: translateX(0);
}

/* Responsive tooltip positioning */
@media (max-width: 1200px) {
  .tooltip {
    min-width: 350px;
    max-width: 450px;
  }
}

@media (max-width: 768px) {
  .tooltip {
    position: fixed;
    bottom: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    min-width: auto;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    padding: var(--space-4);
  }

  .tooltip::after {
    display: none;
  }
}

/* =================================
   Responsive Design
   ================================= */
/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  /* Stack all cards vertically on mobile */
  .feed-ration-top-row {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .feed-ration-managers {
    gap: var(--space-4);
  }

  .add-feed-form {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .add-feed-form .form-group:last-child {
    justify-self: stretch;
  }

  .add-feed-form .btn {
    width: 100%;
  }

  .feed-ration-manager-header {
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }

  .feed-ration-manager-actions {
    justify-content: center;
  }

  /* Ration builder table responsive adjustments */
  .ration-builder-container .table-container {
    max-height: 300px;
    font-size: var(--text-xs);
  }

  .ration-builder-container .table-container th,
  .ration-builder-container .table-container td {
    padding: var(--space-2) var(--space-1);
    font-size: var(--text-xs);
  }

  /* Hide less important columns on mobile */
  .ration-builder-container .table-container th:nth-child(3),
  .ration-builder-container .table-container td:nth-child(3),
  .ration-builder-container .table-container th:nth-child(4),
  .ration-builder-container .table-container td:nth-child(4),
  .ration-builder-container .table-container th:nth-child(5),
  .ration-builder-container .table-container td:nth-child(5) {
    display: none;
  }
}

  .feed-ration-manager {
    min-width: auto;
  }

  .feed-ration-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
  }

  .feed-ration-actions {
    justify-content: center;
  }

  .feed-ration-form {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .feed-ration-tabs {
    flex-direction: column;
    gap: var(--space-1);
  }

  .feed-ration-tab {
    text-align: center;
  }

  .feed-ration-manager-header {
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }

  .feed-ration-manager-actions {
    justify-content: center;
  }

@media (max-width: 480px) {
  .feed-ration-manager-body {
    padding: var(--space-4);
  }

  .feed-ration-manager-header {
    padding: var(--space-4);
  }

  .feed-ration-results {
    padding: var(--space-3);
  }

  .feed-ration-table th,
  .feed-ration-table td {
    padding: var(--space-1) var(--space-2);
    font-size: var(--text-xs);
  }

  .modal-container {
    width: 95%;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--space-4);
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .modal-footer {
    flex-direction: column-reverse;
  }

  .modal-footer .btn {
    width: 100%;
  }

  .card-header-content {
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }

  .card-header-content .btn {
    width: 100%;
    justify-content: center;
  }
}

/* =================================
   Ration Validation Styles
   ================================= */
.ration-validation-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

#validation-results-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

#validation-results-table th {
  background: var(--bg-secondary);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-size: var(--text-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: var(--space-3) var(--space-4);
  text-align: left;
  border-bottom: 2px solid var(--border-primary);
}

#validation-results-table td {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
}

.validation-row {
  transition: all var(--transition-fast);
}

.validation-row:hover {
  background: var(--bg-secondary);
}

.validation-row.adequate {
  background: var(--bg-success-subtle);
}

.validation-row.deficient {
  background: var(--bg-danger-subtle);
}

.validation-row.excess {
  background: var(--bg-warning-subtle);
}

.difference-value {
  font-weight: var(--font-semibold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
}

.difference-value.adequate {
  color: var(--success-700);
  background: var(--success-100);
}

.difference-value.deficient {
  color: var(--danger-700);
  background: var(--danger-100);
}

.difference-value.excess {
  color: var(--warning-700);
  background: var(--warning-100);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.adequate {
  color: var(--success-700);
  background: var(--success-100);
  border: 1px solid var(--success-200);
}

.status-badge.deficient {
  color: var(--danger-700);
  background: var(--danger-100);
  border: 1px solid var(--danger-200);
}

.status-badge.excess {
  color: var(--warning-700);
  background: var(--warning-100);
  border: 1px solid var(--warning-200);
}

.recommendation-cell {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-style: italic;
}

/* Validation Summary Styles */
.validation-summary {
  margin-top: var(--space-4);
}

.summary-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.summary-card h4 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.overall-status {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
}

.overall-status.excellent {
  background: var(--success-50);
  border: 1px solid var(--success-200);
}

.overall-status.good {
  background: var(--success-50);
  border: 1px solid var(--success-200);
}

.overall-status.acceptable {
  background: var(--warning-50);
  border: 1px solid var(--warning-200);
}

.overall-status.needs-improvement {
  background: var(--danger-50);
  border: 1px solid var(--danger-200);
}

.status-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.status-icon {
  font-size: var(--text-2xl);
}

.status-header h5 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.status-breakdown {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  min-width: 80px;
}

.status-item.adequate {
  background: var(--success-100);
  color: var(--success-700);
}

.status-item.deficient {
  background: var(--danger-100);
  color: var(--danger-700);
}

.status-item.excess {
  background: var(--warning-100);
  color: var(--warning-700);
}

.status-item .count {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
}

.status-item .label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.critical-issues {
  background: var(--danger-50);
  border: 1px solid var(--danger-200);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.critical-issues h6 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--danger-700);
}

.critical-issues ul {
  margin: 0;
  padding-left: var(--space-4);
  color: var(--danger-600);
}

.critical-issues li {
  margin-bottom: var(--space-1);
}

.recommendations {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
}

.recommendations h6 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.recommendations ul {
  margin: 0;
  padding-left: var(--space-4);
  color: var(--text-secondary);
}

.recommendations li {
  margin-bottom: var(--space-1);
  line-height: 1.5;
}

/* Responsive validation styles */
@media (max-width: 768px) {
  .status-breakdown {
    flex-direction: column;
    gap: var(--space-2);
  }

  .status-item {
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
    width: 100%;
  }

  #validation-results-table {
    font-size: var(--text-xs);
  }

  #validation-results-table th,
  #validation-results-table td {
    padding: var(--space-2) var(--space-3);
  }

  .summary-card {
    padding: var(--space-4);
  }
}

/* =================================
   Detailed Analysis Modal Styles
   ================================= */
.detail-modal-content {
  background: var(--modal-bg);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 1200px;
  width: 95vw;
  max-height: calc(100vh - 2 * var(--space-4));
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.95);
  transition: transform var(--transition-base);
}

.modal:not(.hidden) .detail-modal-content {
  transform: scale(1);
}

.detail-modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-elevated);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-shrink: 0;
}

.detail-title-section {
  flex: 1;
}

.detail-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
  display: flex;
  align-items: center;
}

.detail-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
}

.detail-modal-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: var(--modal-bg);
}

.detail-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-elevated);
  overflow-x: auto;
  flex-shrink: 0;
}

.detail-tab-link {
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-bottom: 2px solid transparent;
  white-space: nowrap;
  font-size: var(--text-sm);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.detail-tab-link:hover {
  color: var(--text-primary);
  background: var(--interactive-secondary);
}

.detail-tab-link.active {
  color: var(--primary-600);
  border-bottom-color: var(--primary-600);
  background: var(--primary-50);
}

.tab-icon {
  font-size: var(--text-base);
}

.detail-tab-content {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
  display: none;
  min-height: 0;
}

.detail-tab-content.active {
  display: block;
}

.detail-modal-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-elevated);
  flex-shrink: 0;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
}

/* Responsive detailed analysis modal */
@media (max-width: 768px) {
  .detail-modal-content {
    width: 98vw;
    max-height: calc(100vh - var(--space-4));
    border-radius: var(--radius-xl);
  }

  .detail-modal-header,
  .detail-modal-footer {
    padding: var(--space-4);
  }

  .detail-tab-content {
    padding: var(--space-4);
  }

  .detail-tabs {
    overflow-x: auto;
    scrollbar-width: thin;
  }

  .detail-tab-link {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }

  .tab-icon {
    font-size: var(--text-sm);
  }

  .footer-actions {
    flex-direction: column;
    gap: var(--space-2);
  }

  .footer-actions .btn {
    width: 100%;
    justify-content: center;
  }
}

/* =================================
   AI Settings Modal Styles
   ================================= */

.ai-settings-modal {
  max-width: 600px;
  width: 90vw;
}

.ai-settings-intro {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  border-left: 4px solid var(--primary-500);
}

.ai-settings-intro p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.ai-settings-sliders {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.setting-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.setting-icon {
  font-size: 1.2rem;
  margin-right: var(--space-2);
}

.setting-name {
  flex: 1;
  font-size: var(--text-base);
}

.setting-value {
  font-weight: var(--font-bold);
  color: var(--primary-600);
  min-width: 50px;
  text-align: right;
}

.setting-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: var(--bg-tertiary);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  transition: all var(--transition-base);
}

.setting-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-500);
  cursor: pointer;
  border: 2px solid var(--bg-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-base);
}

.setting-slider::-webkit-slider-thumb:hover {
  background: var(--primary-600);
  transform: scale(1.1);
}

.setting-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-500);
  cursor: pointer;
  border: 2px solid var(--bg-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-base);
}

.setting-description {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-top: var(--space-1);
  line-height: var(--leading-relaxed);
}

.ai-settings-total {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  text-align: center;
}

.total-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.total-label {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.total-value {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--success-600);
  transition: color var(--transition-base);
}

.ai-settings-presets h4 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.preset-btn {
  flex: 1;
  min-width: 120px;
  font-size: var(--text-xs);
  padding: var(--space-2) var(--space-3);
  white-space: nowrap;
}

/* Responsive AI Settings Modal */
@media (max-width: 768px) {
  .ai-settings-modal {
    width: 95vw;
    max-width: none;
  }

  .preset-buttons {
    flex-direction: column;
  }

  .preset-btn {
    min-width: auto;
    width: 100%;
  }

  .setting-label {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }

  .setting-value {
    align-self: flex-end;
  }
}

/* =================================
   Ration Evaluation Progress Bar
   ================================= */

.ration-evaluation-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-2);
}

.circular-progress-evaluation {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
}

/* SVG Progress Ring */
.progress-ring-evaluation {
  width: 80px;
  height: 80px;
  position: absolute;
  top: 0;
  left: 0;
}

.progress-ring-background-evaluation {
  stroke: #e6e6e6;
  stroke-width: 6;
  fill: transparent;
  opacity: 0.3;
}

.progress-ring-progress-evaluation {
  stroke: #4CAF50;
  stroke-width: 6;
  fill: transparent;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.8s cubic-bezier(0.4, 0, 0.2, 1),
              stroke 0.3s ease;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
}

/* Progress Text Overlay */
.progress-text-evaluation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.score-value-evaluation {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1;
}

.score-unit-evaluation {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-left: 1px;
}

.score-label-evaluation {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  margin-top: 2px;
  line-height: 1;
}

/* Progress Color States */
.progress-ring-progress-evaluation.low-score {
  stroke: #f44336 !important; /* Kırmızı */
}

.progress-ring-progress-evaluation.medium-score {
  stroke: #ff9800 !important; /* Turuncu */
}

.progress-ring-progress-evaluation.high-score {
  stroke: #4caf50 !important; /* Yeşil */
}

.progress-ring-progress-evaluation.excellent-score {
  stroke: #2196f3 !important; /* Mavi */
}

/* Animation for progress changes */
@keyframes progressPulseEvaluation {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.circular-progress-evaluation.updating {
  animation: progressPulseEvaluation 0.6s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .circular-progress-evaluation {
    width: 70px;
    height: 70px;
  }

  .progress-ring-evaluation {
    width: 70px;
    height: 70px;
  }

  .score-value-evaluation {
    font-size: var(--text-base);
  }
}



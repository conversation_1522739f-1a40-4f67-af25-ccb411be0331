/**
 * Toast Notification System for Livestock Management Application
 * Replaces all alert(), confirm(), and prompt() dialogs with modern toast notifications
 */

class ToastSystem {
  constructor() {
    this.toasts = [];
    this.container = null;
    this.maxToasts = 5;
    this.defaultDuration = {
      success: 4000,
      error: 6000,
      warning: 5000,
      info: 4000
    };
    this.init();
  }

  init() {
    // Create toast container if it doesn't exist
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'toast-container';
      this.container.className = 'toast-container';
      this.container.setAttribute('aria-live', 'polite');
      this.container.setAttribute('aria-label', 'Notifications');
      this.container.setAttribute('role', 'region');
      document.body.appendChild(this.container);
    }
  }

  /**
   * Show a toast notification
   * @param {string} message - The message to display
   * @param {string} type - Toast type: 'success', 'error', 'warning', 'info'
   * @param {Object} options - Additional options
   */
  show(message, type = 'info', options = {}) {
    const toast = this.createToast(message, type, options);
    this.addToast(toast);
    return toast;
  }

  /**
   * Show success toast
   */
  success(message, options = {}) {
    return this.show(message, 'success', options);
  }

  /**
   * Show error toast
   */
  error(message, options = {}) {
    return this.show(message, 'error', options);
  }

  /**
   * Show warning toast
   */
  warning(message, options = {}) {
    return this.show(message, 'warning', options);
  }

  /**
   * Show info toast
   */
  info(message, options = {}) {
    return this.show(message, 'info', options);
  }

  /**
   * Show confirmation dialog as toast with action buttons
   */
  confirm(message, options = {}) {
    return new Promise((resolve) => {
      const confirmOptions = {
        ...options,
        persistent: true,
        showActions: true,
        isConfirmation: true,
        onConfirm: () => resolve(true),
        onCancel: () => resolve(false)
      };
      this.show(message, 'warning', confirmOptions);
    });
  }

  /**
   * Create toast element
   */
  createToast(message, type, options) {
    const toast = document.createElement('div');
    const toastId = 'toast-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);

    toast.id = toastId;
    toast.className = `toast toast-${type}`;

    // Add confirmation class for center positioning
    if (options.isConfirmation) {
      toast.classList.add('toast-confirmation');
    }

    toast.setAttribute('role', options.isConfirmation ? 'dialog' : 'alert');
    toast.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');
    toast.setAttribute('aria-atomic', 'true');

    const icon = this.getIcon(type);
    const duration = options.duration || this.defaultDuration[type];
    const showActions = options.showActions || false;

    toast.innerHTML = `
      <div class="toast-icon">${icon}</div>
      <div class="toast-content">
        <div class="toast-message">${message}</div>
      </div>
      <button class="toast-close" aria-label="Close notification" tabindex="0">
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
          <path d="M1 1L13 13M1 13L13 1" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </button>
      ${showActions ? `
        <div class="toast-actions">
          <button class="toast-btn toast-btn-confirm" tabindex="0">
            ${options.confirmText || 'Confirm'}
          </button>
          <button class="toast-btn toast-btn-cancel" tabindex="0">
            ${options.cancelText || 'Cancel'}
          </button>
        </div>
      ` : ''}
    `;

    // Add event listeners
    this.setupToastEvents(toast, options, duration);

    return toast;
  }

  /**
   * Setup event listeners for toast
   */
  setupToastEvents(toast, options, duration) {
    const closeBtn = toast.querySelector('.toast-close');
    const confirmBtn = toast.querySelector('.toast-btn-confirm');
    const cancelBtn = toast.querySelector('.toast-btn-cancel');

    // Close button
    closeBtn.addEventListener('click', () => {
      if (options.onCancel) options.onCancel();
      this.removeToast(toast);
    });

    // Confirm button
    if (confirmBtn) {
      confirmBtn.addEventListener('click', () => {
        if (options.onConfirm) options.onConfirm();
        this.removeToast(toast);
      });
    }

    // Cancel button
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => {
        if (options.onCancel) options.onCancel();
        this.removeToast(toast);
      });
    }

    // Keyboard navigation
    toast.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        if (options.onCancel) options.onCancel();
        this.removeToast(toast);
      }

      // Tab navigation within toast
      if (e.key === 'Tab') {
        const focusableElements = toast.querySelectorAll('button, [tabindex="0"]');
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    });

    // Auto-dismiss (unless persistent)
    if (!options.persistent && duration > 0) {
      setTimeout(() => {
        this.removeToast(toast);
      }, duration);
    }

    // Pause auto-dismiss on hover
    if (!options.persistent) {
      let timeoutId;
      toast.addEventListener('mouseenter', () => {
        clearTimeout(timeoutId);
      });
      
      toast.addEventListener('mouseleave', () => {
        timeoutId = setTimeout(() => {
          this.removeToast(toast);
        }, 2000); // Shorter timeout after hover
      });
    }
  }

  /**
   * Add toast to container
   */
  addToast(toast) {
    const isConfirmation = toast.classList.contains('toast-confirmation');

    if (isConfirmation) {
      // Create backdrop for confirmation dialogs
      const backdrop = document.createElement('div');
      backdrop.className = 'toast-backdrop';
      backdrop.setAttribute('data-toast-id', toast.id);
      document.body.appendChild(backdrop);

      // Add toast directly to body for center positioning
      document.body.appendChild(toast);

      // Show backdrop and toast
      requestAnimationFrame(() => {
        backdrop.classList.add('show');
        toast.classList.add('toast-show');
      });

      // Close on backdrop click
      backdrop.addEventListener('click', () => {
        this.removeToast(toast);
      });
    } else {
      // Regular toast behavior
      // Remove oldest toast if we exceed max limit
      if (this.toasts.length >= this.maxToasts) {
        this.removeToast(this.toasts[0]);
      }

      this.container.appendChild(toast);

      // Trigger animation
      requestAnimationFrame(() => {
        toast.classList.add('toast-show');
      });
    }

    this.toasts.push(toast);

    // Focus management for accessibility (disabled for cleaner UI)
    // if (toast.querySelector('.toast-actions')) {
    //   const firstButton = toast.querySelector('.toast-btn');
    //   if (firstButton) {
    //     firstButton.focus();
    //   }
    // }
  }

  /**
   * Remove toast from container
   */
  removeToast(toast) {
    if (!toast || !toast.parentNode) return;

    const isConfirmation = toast.classList.contains('toast-confirmation');

    toast.classList.add('toast-hide');

    // Remove backdrop if it's a confirmation dialog
    if (isConfirmation) {
      const backdrop = document.querySelector(`[data-toast-id="${toast.id}"]`);
      if (backdrop) {
        backdrop.classList.remove('show');
        setTimeout(() => {
          if (backdrop.parentNode) {
            backdrop.parentNode.removeChild(backdrop);
          }
        }, 300);
      }
    }

    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
      this.toasts = this.toasts.filter(t => t !== toast);
    }, 300); // Match CSS animation duration
  }

  /**
   * Get icon for toast type
   */
  getIcon(type) {
    const icons = {
      success: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none">
        <circle cx="10" cy="10" r="9" stroke="currentColor" stroke-width="2"/>
        <path d="M6 10L8.5 12.5L14 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>`,
      error: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none">
        <circle cx="10" cy="10" r="9" stroke="currentColor" stroke-width="2"/>
        <path d="M8 8L12 12M8 12L12 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>`,
      warning: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none">
        <path d="M10 2L18 16H2L10 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M10 8V12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        <circle cx="10" cy="15" r="1" fill="currentColor"/>
      </svg>`,
      info: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none">
        <circle cx="10" cy="10" r="9" stroke="currentColor" stroke-width="2"/>
        <path d="M10 14V10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        <circle cx="10" cy="7" r="1" fill="currentColor"/>
      </svg>`
    };
    return icons[type] || icons.info;
  }

  /**
   * Clear all toasts
   */
  clearAll() {
    // Remove all backdrops first
    document.querySelectorAll('.toast-backdrop').forEach(backdrop => {
      if (backdrop.parentNode) {
        backdrop.parentNode.removeChild(backdrop);
      }
    });

    this.toasts.forEach(toast => this.removeToast(toast));
  }
}

// Create global instance
const toastSystem = new ToastSystem();

// Export for use in modules
export { toastSystem };

// Also make it globally available
window.toast = toastSystem;

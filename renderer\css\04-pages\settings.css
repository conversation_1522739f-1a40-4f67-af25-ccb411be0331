/* =================================
   Settings Page Styles
   ================================= */

/* Settings Page Container */
.settings-page-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-6);
}

/* Modern Settings Section */
.modern-settings-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.settings-section-title {
  margin: 0 0 var(--space-6) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-primary);
  padding-bottom: var(--space-3);
}

/* Modern Setting Item */
.modern-setting-item {
  margin-bottom: var(--space-6);
}

.modern-setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.setting-select {
  width: 100%;
  max-width: 300px;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.setting-select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.setting-description {
  margin: 0 0 var(--space-4) 0;
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* Modern Clickable Items */
.modern-clickable-item {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modern-clickable-item:hover {
  background: var(--interactive-secondary);
  border-color: var(--border-focus);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.session-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.session-details span {
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.session-details small {
  color: var(--text-secondary);
  font-size: var(--text-xs);
}

.logout-indicator,
.profile-switch-icon {
  font-size: var(--text-lg);
  opacity: 0.7;
  transition: opacity var(--transition-fast);
}

.modern-clickable-item:hover .logout-indicator,
.modern-clickable-item:hover .profile-switch-icon {
  opacity: 1;
}

/* Modern Profile Actions */
.modern-profile-actions {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

/* Modern Database Buttons */
.modern-db-buttons {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

/* Profile Switch Modal */
.profile-switch-modal .modal-content {
  max-width: 600px;
  width: 90vw;
}

.profile-switch-content {
  background: var(--bg-elevated);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.profile-switch-header {
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-6);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.profile-switch-title {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.profile-switch-description {
  margin: 0;
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.profile-switch-close {
  font-size: var(--text-2xl);
  cursor: pointer;
  color: var(--text-secondary);
  transition: color var(--transition-fast);
  line-height: 1;
}

.profile-switch-close:hover {
  color: var(--text-primary);
}

.profile-switch-body {
  padding: var(--space-6);
  max-height: 400px;
  overflow-y: auto;
}

.profile-switch-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.profile-switch-item {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.profile-switch-item:hover {
  background: var(--interactive-secondary);
  border-color: var(--border-focus);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.profile-switch-info {
  flex: 1;
}

.profile-switch-name {
  margin: 0 0 var(--space-1) 0;
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.profile-switch-desc {
  margin: 0 0 var(--space-2) 0;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
}

.profile-switch-badge {
  display: inline-block;
  padding: var(--space-1) var(--space-2);
  background: var(--warning-50);
  color: var(--warning-700);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.profile-switch-footer {
  background: var(--bg-elevated);
  border-top: 1px solid var(--border-primary);
  padding: var(--space-4) var(--space-6);
}

.profile-switch-actions {
  display: flex;
  justify-content: flex-end;
}

.profile-switch-cancel {
  padding: var(--space-2) var(--space-4);
  background: none;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.profile-switch-cancel:hover {
  background: var(--interactive-secondary);
  color: var(--text-primary);
}

/* Profile Manage Modal */
.profile-manage-modal .modal-content {
  max-width: 700px;
  width: 90vw;
}

.profile-manage-content {
  background: var(--bg-elevated);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.profile-manage-header {
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-6);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.profile-manage-title {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.profile-manage-description {
  margin: 0;
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.profile-manage-close {
  font-size: var(--text-2xl);
  cursor: pointer;
  color: var(--text-secondary);
  transition: color var(--transition-fast);
  line-height: 1;
}

.profile-manage-close:hover {
  color: var(--text-primary);
}

.profile-manage-body {
  padding: var(--space-6);
  max-height: 400px;
  overflow-y: auto;
}

.profile-manage-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.profile-manage-item {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.profile-manage-info {
  flex: 1;
}

.profile-manage-name {
  margin: 0 0 var(--space-1) 0;
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.profile-manage-desc {
  margin: 0 0 var(--space-1) 0;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
}

.profile-manage-date {
  color: var(--text-tertiary);
  font-size: var(--text-xs);
}

.profile-manage-badge {
  display: inline-block;
  padding: var(--space-1) var(--space-2);
  background: var(--warning-50);
  color: var(--warning-700);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  margin-top: var(--space-2);
}

.profile-manage-actions {
  display: flex;
  gap: var(--space-2);
}

.profile-manage-delete {
  padding: var(--space-2) var(--space-3);
  background: var(--error-50);
  color: var(--error-700);
  border: 1px solid var(--error-200);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--text-sm);
}

.profile-manage-delete:hover {
  background: var(--error-100);
  border-color: var(--error-300);
}

.profile-manage-protected {
  color: var(--text-tertiary);
  font-size: var(--text-sm);
  font-style: italic;
}

.profile-manage-footer {
  background: var(--bg-elevated);
  border-top: 1px solid var(--border-primary);
  padding: var(--space-4) var(--space-6);
}

.profile-manage-form-actions {
  display: flex;
  justify-content: flex-end;
}

.profile-manage-cancel {
  padding: var(--space-2) var(--space-4);
  background: none;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.profile-manage-cancel:hover {
  background: var(--interactive-secondary);
  color: var(--text-primary);
}

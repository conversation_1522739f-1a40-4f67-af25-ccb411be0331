import { renderAnimalsPage as renderAnimalsPageModule } from './page-modules/animals-page.js';
// Removed duplicate import of renderAnimalsPageModule
import { renderDashboardPage as renderDashboardPageModule } from './page-modules/dashboard-page.js';
import { renderHealthPage as renderHealthPageModule } from './page-modules/health-page.js';
import { renderReproductionPage as renderReproductionPageModule } from './page-modules/reproduction-page.js';
import { renderMilkYieldPage as renderMilkYieldPageModule } from './page-modules/milk-yield-page.js';

import { renderSettingsPage as renderSettingsPageModule } from './page-modules/settings-page.js';
import { renderReportsPage as renderReportsPageModule } from './page-modules/reports-page.js'; // Yeni Raporlar sayfası
import { renderAccountingProductsPage as renderAccountingProductsPageModule } from './page-modules/accounting-products-page.js';
import { renderAccountingAccountsPage as renderAccountingAccountsPageModule } from './page-modules/accounting-accounts-page.js';
import { renderAccountingPurchasesPage as renderAccountingPurchasesPageModule } from './page-modules/accounting-purchases-page.js';
import { renderAccountingSalesPage as renderAccountingSalesPageModule } from './page-modules/accounting-sales-page.js';
import { renderAccountingPaymentsPage as renderAccountingPaymentsPageModule } from './page-modules/accounting-payments-page.js';
import { renderFeedRationPage as renderFeedRationPageModule } from './page-modules/feed-ration-page.js';

import { calculateAge, defaultAvatarSVG } from './utils/ui-helpers.js';
import { renderAccountingReportsPage } from './page-modules/accounting-reports-page.js';
import { toastSystem } from './utils/toast-system.js';
import SessionManager from './utils/session-manager.js';

document.addEventListener('DOMContentLoaded', async () => {
  // Check authentication status before initializing the app
  const isAuthenticated = await window.sessionManager.checkAuthenticationStatus();

  if (!isAuthenticated) {
    // Redirect to login if not authenticated
    window.location.href = 'login.html';
    return;
  }

  // Initialize toast system
  window.toast = toastSystem;

  const menuItems = document.querySelectorAll('.sidebar .menu > li.menu-item:not(.accordion-toggle), .sidebar .menu .submenu li.menu-item');
  const accordionToggles = document.querySelectorAll('.sidebar .menu li.accordion-toggle');
  const currentPageBreadcrumb = document.getElementById('current-page-breadcrumb');
  const contentArea = document.getElementById('content-area'); // Keep contentArea accessible

  // Breadcrumb home navigation
  const homeBreadcrumb = document.querySelector('.breadcrumb-item.home');
  if (homeBreadcrumb) {
    homeBreadcrumb.addEventListener('click', () => {
      // Navigate to dashboard
      const dashboardItem = document.querySelector('.menu-item[data-page="dashboard"]');
      if (dashboardItem) {
        // Remove active from all items
        document.querySelectorAll('.menu-item').forEach(item => item.classList.remove('active'));
        // Add active to dashboard
        dashboardItem.classList.add('active');
        // Update breadcrumb and load page
        currentPageBreadcrumb.textContent = i18n.t('dashboard');
        currentPageBreadcrumb.setAttribute('data-i18n', 'dashboard');
        loadPage('dashboard');
      }
    });
  }

  // Akordiyon menü işlevselliği
  accordionToggles.forEach(toggle => {
    toggle.addEventListener('click', () => {
      const submenu = toggle.nextElementSibling;
      const arrow = toggle.querySelector('.arrow');

      // Close all other open submenus
      accordionToggles.forEach(otherToggle => {
        if (otherToggle !== toggle) {
          const otherSubmenu = otherToggle.nextElementSibling;
          const otherArrow = otherToggle.querySelector('.arrow');
          if (otherSubmenu && otherSubmenu.classList.contains('submenu') && otherSubmenu.classList.contains('open')) {
            otherSubmenu.classList.remove('open');
            otherToggle.classList.remove('open');
            if (otherArrow) otherArrow.classList.remove('open');
          }
        }
      });

      // Toggle the clicked submenu
      if (submenu && submenu.classList.contains('submenu')) {
        submenu.classList.toggle('open');
        toggle.classList.toggle('open');
        if (arrow) arrow.classList.toggle('open');
      }
    });
  });

  menuItems.forEach(item => {
    // Akordiyon toggle'ları zaten yukarıda handle edildi, burada sayfa yükleme ile ilgili olmayan
    // accordion-toggle class'ına sahip öğeler için click event'i eklememek gerekir.
    // Ancak, menuItems seçicisi zaten accordion-toggle olmayanları ve submenu içindekileri alıyor.
    // Bu yüzden if (item.classList.contains('accordion-toggle')) kontrolü burada gereksiz olabilir
    // ya da menuItems seçicisi `.sidebar .menu li:not(.accordion-toggle)` şeklinde olmalıydı.
    // Mevcut menuItems seçicisi:
    // document.querySelectorAll('.sidebar .menu > li.menu-item:not(.accordion-toggle), .sidebar .menu .submenu li.menu-item');
    // Bu seçici zaten accordion-toggle'ları dışlıyor (ana seviyede).
    // Bu nedenle aşağıdaki if kontrolü aslında çalışmayacak, bu iyi.

    item.addEventListener('click', () => {
      document.querySelectorAll('.sidebar .menu li.active').forEach(activeItem => {
        activeItem.classList.remove('active');
      });
      item.classList.add('active');

      const parentAccordionToggle = item.closest('ul.submenu')?.previousElementSibling;
      if (parentAccordionToggle && parentAccordionToggle.classList.contains('accordion-toggle')) {
        // İsteğe bağlı: Aktif alt menü öğesinin üst akordiyonunu da 'active' yapabiliriz,
        // ama bu genellikle sadece alt menü öğesinin aktif olmasıyla belirtilir.
        // Şimdilik yoruma alalım, eğer istenirse eklenebilir.
        // parentAccordionToggle.classList.add('active');
      }

      const page = item.getAttribute('data-page');
      const pageTitleText = item.querySelector('span[data-i18n]')?.getAttribute('data-i18n') || page;
      currentPageBreadcrumb.textContent = i18n.t(pageTitleText); // Update breadcrumb instead of page title
      currentPageBreadcrumb.setAttribute('data-i18n', pageTitleText);
      loadPage(page);
    });
  });

  function loadPage(page) {
    // Aktif sayfa içeriğini temizle
    contentArea.innerHTML = '';

    if (page === 'animals') {
      renderAnimalsPageModule(contentArea, i18n, (dateStr) => calculateAge(dateStr, i18n.t), defaultAvatarSVG, openAnimalModal);
    } else if (page === 'health') {
      renderHealthPageModule(contentArea, i18n, defaultAvatarSVG);
    } else if (page === 'reproduction') {
      renderReproductionPageModule(contentArea, i18n, defaultAvatarSVG);
    } else if (page === 'milk') {
      renderMilkYieldPageModule(contentArea, i18n, defaultAvatarSVG);
    } else if (page === 'dashboard') {
      renderDashboardPageModule(contentArea, i18n, { openAnimalModal });

    } else if (page === 'feed_ration') {
      renderFeedRationPageModule(contentArea, i18n);
    } else if (page === 'settings') {
      renderSettingsPageModule(contentArea, i18n, applyLanguage, applyTheme);
    } else if (page === 'reports') {
      renderReportsPageModule(contentArea, i18n);
    } else if (page.startsWith('accounting-')) {
      // Standardized accounting pages using pure JavaScript modules
      if (page === 'accounting-products') {
        renderAccountingProductsPageModule(contentArea, i18n, defaultAvatarSVG);
      } else if (page === 'accounting-accounts') {
        renderAccountingAccountsPageModule(contentArea, i18n, defaultAvatarSVG);
      } else if (page === 'accounting-purchases') {
        renderAccountingPurchasesPageModule(contentArea, i18n, defaultAvatarSVG);
      } else if (page === 'accounting-sales') {
        renderAccountingSalesPageModule(contentArea, i18n, defaultAvatarSVG);
      } else if (page === 'accounting-payments') {
        renderAccountingPaymentsPageModule(contentArea, i18n, defaultAvatarSVG);
      } else if (page === 'accounting-reports') {
        renderAccountingReportsPage(contentArea, i18n);
      } else {
        console.error(`Unknown accounting page: ${page}`);
        contentArea.innerHTML = `<p>Unknown page: ${page}</p>`;
      }
    } else {
      contentArea.innerHTML = `<div style='font-size:1.2rem;'>${i18n.t('page_soon', { pageName: i18n.t(page) })}</div>`;
    }

    // Trigger modal structure conversion for dynamically loaded content
    setTimeout(() => {
      if (window.reinitializeModals) {
        window.reinitializeModals();
      }
    }, 100);
  }

  // `renderDashboardPage` has been moved to page-modules/dashboard-page.js

  // Modal yönetimi
  const animalModal = document.getElementById('animal-modal');
  const animalForm = document.getElementById('animal-form');
  const closeBtn = animalModal.querySelector('.close-btn');
  const modalTitle = document.getElementById('modal-title');

  async function openAnimalModal(animal = null) {
    animalModal.classList.remove('hidden');
    animalForm.reset();

    // Reset custom inputs
    const turCustomInput = document.getElementById('tur-custom-input');
    const irkCustomInput = document.getElementById('irk-custom-input');
    if (turCustomInput) {
      turCustomInput.style.display = 'none';
      turCustomInput.required = false;
      turCustomInput.value = '';
    }
    if (irkCustomInput) {
      irkCustomInput.style.display = 'none';
      irkCustomInput.required = false;
      irkCustomInput.value = '';
    }

    const allAnimals = await window.api.invoke('hayvanlar:list');
    const anneKupeSelect = animalForm.elements['AnneKupe'];
    const babaKupeSelect = animalForm.elements['BabaKupe'];

    // Clear previous options
    anneKupeSelect.innerHTML = '';
    babaKupeSelect.innerHTML = '';

    // Add placeholder/default option
    const placeholderOptionEl = document.createElement('option');
    placeholderOptionEl.value = '';
    placeholderOptionEl.textContent = i18n.t('option_select'); // 'Seçiniz'
    anneKupeSelect.appendChild(placeholderOptionEl.cloneNode(true));
    babaKupeSelect.appendChild(placeholderOptionEl.cloneNode(true));

    // Populate with animals
    allAnimals.forEach(a => {
      // Don't add the animal being edited to its own parent list
      if (animal && a.Id === animal.Id) {
        return;
      }
      const option = document.createElement('option');
      option.value = a.KupeNo;
      option.textContent = `${a.KupeNo} (${a.Isim || i18n.t('unknown_animal_name')})`;
      anneKupeSelect.appendChild(option.cloneNode(true));
      babaKupeSelect.appendChild(option.cloneNode(true));
    });

    if (animal) {
      modalTitle.textContent = i18n.t('animal_modal_title_edit');
      Object.keys(animal).forEach(key => {
        if (animalForm.elements[key]) {
          animalForm.elements[key].value = animal[key] ?? '';
        }
      });

      // Check if custom inputs should be shown for editing
      const turSelect = document.getElementById('tur-select');
      const turCustomInput = document.getElementById('tur-custom-input');
      const irkSelect = document.getElementById('irk-select');
      const irkCustomInput = document.getElementById('irk-custom-input');

      // Check if Tür is a custom value (not in predefined options)
      if (animal.Tur && turSelect && turCustomInput) {
        const turOptions = Array.from(turSelect.options).map(opt => opt.value);
        if (!turOptions.includes(animal.Tur)) {
          turSelect.value = 'Diğer';
          turCustomInput.value = animal.Tur;
          turCustomInput.style.display = 'block';
          turCustomInput.required = true;
        }
      }

      // Check if Irk is a custom value (not in predefined options)
      if (animal.Irk && irkSelect && irkCustomInput) {
        const irkOptions = Array.from(irkSelect.options).map(opt => opt.value);
        if (!irkOptions.includes(animal.Irk)) {
          irkSelect.value = 'Diğer';
          irkCustomInput.value = animal.Irk;
          irkCustomInput.style.display = 'block';
          irkCustomInput.required = true;
        }
      }
    } else {
      modalTitle.textContent = i18n.t('animal_modal_title_add');
      animalForm.elements['Id'].value = '';

      // İşletmeye giriş tarihini bugünün tarihiyle otomatik doldur
      const today = new Date().toISOString().split('T')[0];
      if (animalForm.elements['IsletmeyeGirisTarihi']) {
        animalForm.elements['IsletmeyeGirisTarihi'].value = today;
      }
    }
  }

  closeBtn.addEventListener('click', () => {
    animalModal.classList.add('hidden');
  });

  animalModal.addEventListener('click', e => {
    if (e.target === animalModal) {
      animalModal.classList.add('hidden');
    }
  });

  // Setup custom input handlers for Tür and Irk
  function setupCustomInputHandlers() {
    const turSelect = document.getElementById('tur-select');
    const turCustomInput = document.getElementById('tur-custom-input');
    const irkSelect = document.getElementById('irk-select');
    const irkCustomInput = document.getElementById('irk-custom-input');

    if (turSelect && turCustomInput) {
      turSelect.addEventListener('change', function() {
        if (this.value === 'Diğer') {
          turCustomInput.style.display = 'block';
          turCustomInput.required = true;
          turCustomInput.focus();
        } else {
          turCustomInput.style.display = 'none';
          turCustomInput.required = false;
          turCustomInput.value = '';
        }
      });
    }

    if (irkSelect && irkCustomInput) {
      irkSelect.addEventListener('change', function() {
        if (this.value === 'Diğer') {
          irkCustomInput.style.display = 'block';
          irkCustomInput.required = true;
          irkCustomInput.focus();
        } else {
          irkCustomInput.style.display = 'none';
          irkCustomInput.required = false;
          irkCustomInput.value = '';
        }
      });
    }
  }

  // Call setup function
  setupCustomInputHandlers();

  animalForm.onsubmit = async function(e) {
    e.preventDefault();
    const formData = Object.fromEntries(new FormData(animalForm).entries());
    formData.AktifMi = formData.AktifMi === '1' ? 1 : 0;

    // Handle custom inputs for Tür and Irk
    if (formData.Tur === 'Diğer' && formData.TurCustom) {
      formData.Tur = formData.TurCustom;
    }
    if (formData.Irk === 'Diğer' && formData.IrkCustom) {
      formData.Irk = formData.IrkCustom;
    }

    // Remove custom input fields from formData
    delete formData.TurCustom;
    delete formData.IrkCustom;

    if (formData.Id) {
      formData.Id = parseInt(formData.Id);
      await window.api.invoke('hayvanlar:update', formData);
      window.toast.success(i18n.t('toast_success_update'));
    } else {
      await window.api.invoke('hayvanlar:add', formData);
      window.toast.success(i18n.t('toast_success_save'));
    }
    animalModal.classList.add('hidden');
    const activePageItem = document.querySelector('.sidebar .menu li.active');
    if (activePageItem && activePageItem.getAttribute('data-page') === 'animals') {
      loadPage('animals');
    }
  };

  // openAnimalDetailModal has been moved to animals-page.js

  // Dil değiştirme ve uygulama
  function applyLanguage(lang) {
    i18n.setLanguage(lang);
    const t = i18n.t;

    // 1. data-i18n attribute'u olan tüm elementleri güncelle
    document.querySelectorAll('[data-i18n]').forEach(el => {
      const key = el.getAttribute('data-i18n');
      const translation = t(key);
      if (translation !== key) {
        el.textContent = translation;
      }
    });

    // 2. Açık modallardaki çevirileri de güncelle
    document.querySelectorAll('.modal:not(.hidden) [data-i18n]').forEach(el => {
      const key = el.getAttribute('data-i18n');
      const translation = t(key);
      if (translation !== key) {
        el.textContent = translation;
      }
    });

    // 2. Aktif sayfa başlığını güncelle (breadcrumb)
    const activePageItem = document.querySelector('.sidebar .menu li.active');
    if (activePageItem) {
      const pageKey = activePageItem.getAttribute('data-page');
      const breadcrumbElement = document.getElementById('current-page-breadcrumb');
      if (breadcrumbElement) {
        breadcrumbElement.textContent = t(pageKey);
        breadcrumbElement.setAttribute('data-i18n', pageKey);
      }
    }

    // 3. Mevcut sayfayı yeniden render et (içerikdeki çeviriler için)
    if (activePageItem) {
      const pageKey = activePageItem.getAttribute('data-page');
      loadPage(pageKey);
    }
  }

  // Varsayılan olarak dashboard'u yükle
  menuItems[0].classList.add('active');
  applyLanguage(i18n.getLanguage()); // i18n is global

  // defaultAvatarSVG is now imported from ui-helpers.js

  // `renderHealthPage` has been moved to page-modules/health-page.js

  // `renderReproductionPage` has been moved to page-modules/reproduction-page.js

  // `renderMilkYieldPage` has been moved to page-modules/milk-yield-page.js

  // `renderSettingsPage` has been moved to page-modules/settings-page.js

  // Tema uygulama fonksiyonu (global scope in app.js)
  window.applyTheme = function(theme) {
    const html = document.documentElement;
    const body = document.body;

    // Remove existing theme attributes from both html and body
    html.removeAttribute('data-theme');
    body.removeAttribute('data-theme');
    html.classList.remove('light-theme', 'dark-theme');
    body.classList.remove('light-theme', 'dark-theme');

    let finalTheme = 'light'; // default

    if (theme === 'light') {
      finalTheme = 'light';
      html.setAttribute('data-theme', 'light');
      body.setAttribute('data-theme', 'light');
    } else if (theme === 'dark') {
      finalTheme = 'dark';
      html.setAttribute('data-theme', 'dark');
      body.setAttribute('data-theme', 'dark');
    } else if (theme === 'system') {
      // System theme - detect user preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      finalTheme = prefersDark ? 'dark' : 'light';
      html.setAttribute('data-theme', finalTheme);
      body.setAttribute('data-theme', finalTheme);
    }
  };

  // System theme değişikliklerini dinle
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  mediaQuery.addEventListener('change', () => {
    const currentTheme = localStorage.getItem('theme') || 'system';
    if (currentTheme === 'system') {
      applyTheme('system');
    }
  });

  // Başlangıçta temayı uygula
  applyTheme(localStorage.getItem('theme') || 'system');

  // Hayvan detay modalı her zaman kapalı başlasın
  const animalDetailModal = document.getElementById('animal-detail-modal');
  if (animalDetailModal) animalDetailModal.classList.add('hidden');

  // Session management event listeners
  document.addEventListener('profileChanged', (event) => {
    // Reload current page when profile changes
    const currentPage = document.querySelector('.menu-item.active')?.dataset.page || 'dashboard';
    renderPage(currentPage);

    // Show profile change notification
    if (window.toast) {
      window.toast.success(`Profil değiştirildi: ${event.detail.profile.profilAdi}`);
    }
  });

  // Add user info display to header (optional)
  displayUserInfo();

  async function displayUserInfo() {
    try {
      const sessionInfo = window.sessionManager.getSessionInfo();
      if (sessionInfo.user && sessionInfo.profile) {
        // You can add user info display to the header here if needed
      }
    } catch (error) {
      console.error('Error displaying user info:', error);
    }
  }

  // Add logout functionality (you can add a logout button to the UI)
  window.logout = async () => {
    try {
      await window.sessionManager.logout();
    } catch (error) {
      console.error('Error during logout:', error);
      // Force redirect to login even if logout fails
      window.location.href = 'login.html';
    }
  };
});
/* =================================
   Modern Table System v2.0
   Minimal, Clean, User-Friendly Design
   ================================= */

/* =================================
   Table Container
   ================================= */
.table-container {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: visible;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all var(--transition-fast);
}

.table-container:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.table-wrapper {
  overflow-x: auto;
  overflow-y: visible;
  scrollbar-width: thin;
  scrollbar-color: var(--border-secondary) transparent;
}

.table-wrapper::-webkit-scrollbar {
  height: 6px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: var(--radius-full);
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--border-primary);
}

/* =================================
   Modern Table
   ================================= */
.modern-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
  background: var(--bg-elevated);
  table-layout: auto;
}

/* =================================
   Table Header - Clean & Minimal
   ================================= */
.modern-table thead th {
  background: linear-gradient(to bottom, var(--bg-elevated), var(--bg-secondary));
  padding: var(--space-4) var(--space-4);
  text-align: left;
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-primary);
  white-space: nowrap;
  user-select: none;
  position: relative;
  transition: all var(--transition-fast);
}

.modern-table thead th:first-child {
  padding-left: var(--space-6);
  border-top-left-radius: var(--radius-lg);
}

.modern-table thead th:last-child {
  padding-right: var(--space-6);
  border-top-right-radius: var(--radius-lg);
}

/* Sortable Headers */
.modern-table thead th.sortable {
  cursor: pointer;
  position: relative;
  padding-right: var(--space-8);
  transition: all var(--transition-fast);
}

.modern-table thead th.sortable:hover {
  background: var(--interactive-secondary);
  color: var(--interactive-primary);
  transform: translateY(-1px);
}

.modern-table thead th.sortable::after {
  content: '';
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 9l4-4 4 4'/%3E%3Cpath d='M16 15l-4 4-4-4'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: all var(--transition-fast);
}

.modern-table thead th.sortable:hover::after {
  opacity: 1;
}

.modern-table thead th.sorted-asc::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232563eb' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 9l4-4 4 4'/%3E%3C/svg%3E");
  opacity: 1;
}

.modern-table thead th.sorted-desc::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232563eb' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M16 15l-4 4-4-4'/%3E%3C/svg%3E");
  opacity: 1;
}

/* =================================
   Table Body - Enhanced UX
   ================================= */
.modern-table tbody tr {
  border-bottom: 1px solid var(--border-primary);
  transition: all var(--transition-fast);
  position: relative;
}

.modern-table tbody tr:hover {
  background: var(--interactive-secondary);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.modern-table tbody tr:last-child {
  border-bottom: none;
}

.modern-table tbody tr.selected {
  background: var(--primary-50);
  border-left: 3px solid var(--interactive-primary);
}

.modern-table tbody tr.clickable-row {
  cursor: pointer;
}

.modern-table tbody tr.clickable-row:hover {
  background: var(--interactive-secondary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modern-table tbody tr.clickable-row:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* =================================
   Table Cells - Clean & Readable
   ================================= */
.modern-table td {
  padding: var(--space-4) var(--space-4);
  vertical-align: middle;
  font-size: var(--text-sm);
  color: var(--text-primary);
  line-height: 1.5;
  transition: all var(--transition-fast);
}

.modern-table td:first-child {
  padding-left: var(--space-6);
  font-weight: var(--font-medium);
}

.modern-table td:last-child {
  padding-right: var(--space-6);
}

/* Cell Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* Legacy column classes */
.col-avatar {
  width: 60px;
  text-align: center;
}

.col-gender,
.col-age,
.col-check {
  width: 80px;
  text-align: center;
}

.col-actions {
  width: 120px;
  text-align: center;
}

.col-date {
  width: 100px;
  text-align: center;
}

/* Legacy cell classes */
.cell-products-id {
  width: 60px;
  text-align: center;
}

.cell-products-name {
  text-align: left;
  font-weight: var(--font-medium);
}

.cell-products-category,
.cell-products-unit {
  text-align: center;
}

/* =================================
   Avatar System
   ================================= */
.animal-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--border-primary);
  background: var(--bg-elevated);
}

.animal-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-50);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--border-primary);
}

.animal-avatar-placeholder svg {
  width: 20px;
  height: 20px;
}

/* =================================
   Empty State
   ================================= */
.empty-state-container {
  text-align: center;
  padding: var(--space-12) var(--space-6);
  color: var(--text-tertiary);
}

.empty-state-container .empty-state-icon,
.empty-state-container svg {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-4);
  opacity: 0.5;
  color: var(--text-tertiary);
}

.empty-state-container p {
  font-size: var(--text-sm);
  margin: 0;
  color: var(--text-secondary);
}

/* =================================
   Table Actions
   ================================= */
.table-actions {
  display: flex;
  gap: var(--space-2);
  align-items: center;
  justify-content: center;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  border-radius: var(--radius-full);
  background: transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-btn:hover {
  background: var(--bg-secondary);
  transform: translateY(-1px);
}

.action-btn:active {
  transform: translateY(0);
}

.edit-btn:hover {
  background: var(--primary-50);
}

.delete-btn:hover {
  background: var(--error-50);
}

.view-btn:hover {
  background: var(--secondary-50);
}

.complete-btn:hover {
  background: var(--success-50);
}

.action-btn svg {
  width: 18px;
  height: 18px;
  transition: transform var(--transition-fast);
}

.action-btn:hover svg {
  transform: scale(1.1);
}

.modern-table tbody tr:hover .table-actions .action-btn {
  opacity: 1;
}

/* =================================
   Filter Row - Modern Input Design
   ================================= */
.modern-table thead tr.filter-row th {
  padding: var(--space-3) var(--space-4);
  background: var(--bg-secondary);
  border-bottom: 2px solid var(--border-primary);
}

.modern-table thead tr.filter-row th input,
.modern-table thead tr.filter-row th select {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background: var(--bg-elevated);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.modern-table thead tr.filter-row th input:focus,
.modern-table thead tr.filter-row th select:focus {
  outline: none;
  border-color: var(--interactive-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}

.modern-table thead tr.filter-row th input::placeholder {
  color: var(--text-tertiary);
  font-style: italic;
}

/* Date inputs in filter rows should be narrower */
.modern-table thead tr.filter-row th input[type="date"] {
  width: 48%;
  margin-right: 2%;
}

.modern-table thead tr.filter-row th input[type="date"]:last-child {
  margin-right: 0;
}
/* =================================
   Empty State
   ================================= */
.table-empty-state {
  text-align: center;
  padding: var(--space-12) var(--space-6);
  color: var(--text-tertiary);
}

.table-empty-state svg {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-4);
  opacity: 0.5;
}

.table-empty-state h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.table-empty-state p {
  font-size: var(--text-sm);
  margin: 0;
}

/* =================================
   Pagination - Clean & Modern
   ================================= */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) var(--space-6);
  background: linear-gradient(to top, var(--bg-elevated), var(--bg-secondary));
  border-top: 1px solid var(--border-primary);
  border-bottom-left-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

.pagination-info {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.pagination-controls {
  display: flex;
  gap: var(--space-1);
  align-items: center;
}

.pagination-controls button {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-secondary);
  background: var(--bg-elevated);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-controls button:hover:not(:disabled) {
  background: var(--interactive-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--bg-secondary);
}

.page-info {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: var(--font-medium);
  padding: 0 var(--space-4);
  background: var(--primary-50);
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--primary-200);
}

/* =================================
   Table States - Empty & Loading
   ================================= */
.table-empty-state {
  text-align: center;
  padding: var(--space-12) var(--space-6);
  color: var(--text-secondary);
}

.table-empty-state svg {
  width: 48px;
  height: 48px;
  margin-bottom: var(--space-4);
  opacity: 0.6;
  color: var(--text-tertiary);
}

.table-empty-state h3 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.table-empty-state p {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* Loading skeleton */
.skeleton-row {
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-cell {
  height: 16px;
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--border-primary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

/* =================================
   Standardized Table Animations
   ================================= */

/* Table initial load animation */
.modern-table {
  opacity: 0;
  transform: translateY(20px);
  animation: tableSlideIn 0.6s ease-out forwards;
}

/* Row animations for dynamic content */
.modern-table tbody tr {
  opacity: 0;
  transform: translateY(10px);
  animation: rowFadeIn 0.4s ease-out forwards;
}

/* Staggered animation for multiple rows */
.modern-table tbody tr:nth-child(1) { animation-delay: 0.1s; }
.modern-table tbody tr:nth-child(2) { animation-delay: 0.15s; }
.modern-table tbody tr:nth-child(3) { animation-delay: 0.2s; }
.modern-table tbody tr:nth-child(4) { animation-delay: 0.25s; }
.modern-table tbody tr:nth-child(5) { animation-delay: 0.3s; }
.modern-table tbody tr:nth-child(6) { animation-delay: 0.35s; }
.modern-table tbody tr:nth-child(7) { animation-delay: 0.4s; }
.modern-table tbody tr:nth-child(8) { animation-delay: 0.45s; }
.modern-table tbody tr:nth-child(9) { animation-delay: 0.5s; }
.modern-table tbody tr:nth-child(10) { animation-delay: 0.55s; }
.modern-table tbody tr:nth-child(n+11) { animation-delay: 0.6s; }

/* Animation keyframes */
@keyframes tableSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rowFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Disable animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  .modern-table,
  .modern-table tbody tr {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* Animation classes for dynamic updates */
.table-updating .modern-table tbody tr {
  animation: none;
  opacity: 1;
  transform: none;
}

.table-refresh .modern-table tbody tr {
  animation: rowFadeIn 0.3s ease-out forwards;
}

/* Existing keyframes */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  .modern-table {
    font-size: var(--text-xs);
  }

  .modern-table thead th,
  .modern-table td {
    padding: var(--space-2) var(--space-3);
  }

  .modern-table thead th:first-child,
  .modern-table td:first-child {
    padding-left: var(--space-4);
  }

  .modern-table thead th:last-child,
  .modern-table td:last-child {
    padding-right: var(--space-4);
  }

  .pagination-container {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }

  /* Stack table on very small screens */
  .modern-table.stack-mobile thead {
    display: none;
  }

  .modern-table.stack-mobile tbody tr {
    display: block;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-3);
    padding: var(--space-4);
    background: var(--bg-elevated);
  }

  .modern-table.stack-mobile tbody td {
    display: block;
    text-align: left;
    padding: var(--space-1) 0;
    border: none;
  }

  .modern-table.stack-mobile tbody td::before {
    content: attr(data-label) ": ";
    font-weight: var(--font-semibold);
    color: var(--text-secondary);
  }
}

.dry-matter-cell, .dry-matter-percent-cell {
  text-align: center;
  font-weight: var(--font-medium);
  color: var(--primary-700, #2563eb);
}
@media (max-width: 768px) {
  .dry-matter-cell, .dry-matter-percent-cell {
    font-size: var(--text-xs);
    padding: var(--space-1) 0;
  }
}


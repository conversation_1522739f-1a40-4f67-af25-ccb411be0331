// Muhasebe Raporlamalar Sayfası
// Expects contentArea, i18n to be passed
import { AccountingHeader } from '../components/accounting-header.js';

export async function renderAccountingReportsPage(contentArea, i18n) {
  const t = i18n.t;

  // Initialize accounting header immediately to prevent jumping
  const accountingHeader = new AccountingHeader({
    containerId: 'accounting-header',
    pageTitle: t('accounting_reports_page_title') || 'Muhasebe Raporları',
    pageIcon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
      <polyline points="14,2 14,8 20,8"/>
      <line x1="16" y1="13" x2="8" y2="13"/>
      <line x1="16" y1="17" x2="8" y2="17"/>
      <polyline points="10,9 9,9 8,9"/>
    </svg>`
  });

  contentArea.innerHTML = `
    <div id="accounting-header"></div>

    <!-- Report Criteria Section -->
    <div class="reports-criteria-section">
      <div class="reports-criteria-header">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"/>
          <path d="M21 21l-4.35-4.35"/>
        </svg>
        <h3>${t('report_criteria') || 'Rapor Kriterleri'}</h3>
      </div>
      <div class="reports-criteria-content">
        <div class="reports-form-grid">
          <div class="form-group">
            <label for="muhasebe-report-type">${t('label_report_type') || 'Rapor Türü'}</label>
            <select id="muhasebe-report-type" name="muhasebe-report-type">
              <option value="gelir-gider">${t('gelir_gider_tablosu') || 'Dönemlik Gelir-Gider Tablosu'}</option>
              <option value="net-bakiye">${t('net_bakiye_raporu') || 'Net Bakiye Raporu'}</option>
            </select>
          </div>
          <div class="form-group">
            <label for="muhasebe-report-start-date">${t('label_start_date') || 'Başlangıç Tarihi'}</label>
            <input type="date" id="muhasebe-report-start-date" name="muhasebe-report-start-date">
          </div>
          <div class="form-group">
            <label for="muhasebe-report-end-date">${t('label_end_date') || 'Bitiş Tarihi'}</label>
            <input type="date" id="muhasebe-report-end-date" name="muhasebe-report-end-date">
          </div>
          <div class="form-group">
            <label>
              <input type="checkbox" id="muhasebe-report-all-dates" name="muhasebe-report-all-dates">
              ${t('label_all_dates') || 'Tümü'}
            </label>
          </div>
          <div class="form-group">
            <button id="generate-muhasebe-report-btn" class="btn btn-primary">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              ${t('btn_generate_report') || 'Rapor Oluştur'}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Report Output Section -->
    <div class="reports-output-section">
      <div class="reports-output-header">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 11H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2h-4"/>
          <polyline points="9,11 12,14 15,11"/>
          <line x1="12" y1="2" x2="12" y2="14"/>
        </svg>
        <h3>${t('report_output') || 'Rapor Çıktısı'}</h3>
        <div id="muhasebe-report-actions" class="report-export-actions" style="display: none;">
          <button id="export-muhasebe-pdf-btn" class="btn btn-secondary">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
            ${t('btn_export_pdf') || 'PDF Olarak Aktar'}
          </button>
          <button id="export-muhasebe-excel-btn" class="btn btn-secondary">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
            ${t('btn_export_excel') || 'Excel Olarak Aktar'}
          </button>
        </div>
      </div>
      <div class="reports-output-content">
        <div id="muhasebe-report-output-area" class="empty-message">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
            <polyline points="14,2 14,8 20,8"/>
            <line x1="16" y1="13" x2="8" y2="13"/>
            <line x1="16" y1="17" x2="8" y2="17"/>
            <polyline points="10,9 9,9 8,9"/>
          </svg>
          <p>${t('report_select_criteria_generate') || 'Lütfen rapor kriterlerini seçip "Rapor Oluştur" butonuna tıklayın.'}</p>
        </div>
      </div>
    </div>
  `;

  // Initialize accounting header immediately after HTML is created
  accountingHeader.init();

  const generateBtn = contentArea.querySelector('#generate-muhasebe-report-btn');
  const outputArea = contentArea.querySelector('#muhasebe-report-output-area');
  const actionsArea = contentArea.querySelector('#muhasebe-report-actions');
  const exportPdfBtn = contentArea.querySelector('#export-muhasebe-pdf-btn');
  const exportExcelBtn = contentArea.querySelector('#export-muhasebe-excel-btn');
  const allDatesCheckbox = contentArea.querySelector('#muhasebe-report-all-dates');
  const startDateInput = contentArea.querySelector('#muhasebe-report-start-date');
  const endDateInput = contentArea.querySelector('#muhasebe-report-end-date');

  allDatesCheckbox.addEventListener('change', () => {
    startDateInput.disabled = allDatesCheckbox.checked;
    endDateInput.disabled = allDatesCheckbox.checked;
    if (allDatesCheckbox.checked) {
      startDateInput.value = '';
      endDateInput.value = '';
    }
  });

  generateBtn.addEventListener('click', async () => {
    const type = contentArea.querySelector('#muhasebe-report-type').value;
    let startDate = startDateInput.value;
    let endDate = endDateInput.value;

    if (allDatesCheckbox.checked) {
      startDate = null; // Or undefined, or an empty string, depending on backend expectation
      endDate = null;   // Or undefined, or an empty string
    } else if (!startDate || !endDate) {
      outputArea.innerHTML = `
        <div class="empty-message">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          <p style="color: var(--status-error);">${t('error_date_range_required') || 'Tarih aralığı zorunludur.'}</p>
        </div>
      `;
      actionsArea.style.display = 'none';
      return;
    }
    outputArea.innerHTML = `
      <div class="empty-message">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <path d="M12 6v6l4 2"/>
        </svg>
        <p>${t('report_generating') || 'Rapor oluşturuluyor...'}</p>
      </div>
    `;
    actionsArea.style.display = 'none';
    try {
      if (type === 'gelir-gider') {
        const data = await window.api.invoke('accounting:income-expense-report', { startDate, endDate });

        // Header kartlarındaki verilerle uyumlu detaylı rapor
        const detailedData = await window.api.invoke('accounting:detailed-financial-report', { startDate, endDate });

        outputArea.innerHTML = `
          <h3>${t('gelir_gider_tablosu')} - Detaylı Finansal Rapor</h3>
          <table class="report-table" id="detailed-financial-table">
            <thead>
              <tr>
                <th>Ödenmemiş Borç</th>
                <th>Tahsil Edilmemiş Alacak</th>
                <th>Ödenmiş Para</th>
                <th>Tahsil Edilmiş Para</th>
                <th>Kasa Net Bakiye</th>
                <th>Net Finansal Durum</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="cell-text-right">₺${detailedData.unpaidDebt.toFixed(2)}</td>
                <td class="cell-text-right">₺${detailedData.uncollectedReceivable.toFixed(2)}</td>
                <td class="cell-text-right">₺${detailedData.paidMoney.toFixed(2)}</td>
                <td class="cell-text-right">₺${detailedData.collectedMoney.toFixed(2)}</td>
                <td class="cell-text-right ${detailedData.cashNetBalance >= 0 ? 'positive' : 'negative'}">₺${detailedData.cashNetBalance.toFixed(2)}</td>
                <td class="cell-text-right ${detailedData.netFinancialPosition >= 0 ? 'positive' : 'negative'}">₺${detailedData.netFinancialPosition.toFixed(2)}</td>
              </tr>
            </tbody>
          </table>

          <h4 style="margin-top: 30px;">Geleneksel Gelir-Gider Özeti</h4>
          <table class="report-table" id="traditional-income-expense-table">
            <thead>
              <tr>
                <th>${t('label_total_income') || 'Toplam Gelir'}</th>
                <th>${t('label_total_expense') || 'Toplam Gider'}</th>
                <th>${t('label_net_profit') || 'Net Kar/Zarar'}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="cell-text-right">₺${data.ToplamGelir.toFixed(2)}</td>
                <td class="cell-text-right">₺${data.ToplamGider.toFixed(2)}</td>
                <td class="cell-text-right ${data.NetKarZarar >= 0 ? 'positive' : 'negative'}">₺${data.NetKarZarar.toFixed(2)}</td>
              </tr>
            </tbody>
          </table>
        `;

        // Store data for export
        window.currentFinancialReportData = {
          detailed: detailedData,
          traditional: data,
          startDate,
          endDate
        };

        actionsArea.style.display = 'block';
      } else if (type === 'net-bakiye') {
        const data = await window.api.invoke('accounting:accounts-net-balance-report', { startDate, endDate });
        // NetBakiye > 0 ise biz CARİDEN ALACAKLIYIZ.
        // NetBakiye < 0 ise biz CARİYE BORÇLUYUZ.

        // Bizim Alacaklı Olduğumuz Cariler (Carilerin Bize Borcu Var)
        const bizimAlacakliOlduklarimiz = data.filter(row => row.NetBakiye > 0.005);
        // Bizim Borçlu Olduğumuz Cariler (Carilerin Bizden Alacağı Var)
        const bizimBorcluOlduklarimiz = data.filter(row => row.NetBakiye < -0.005);

        outputArea.innerHTML = `
          <h3>${t('report_title_receivables_from_cariler')}</h3>
          <p>${t('report_subtitle_cariler_owe_us', { startDate: new Date(startDate).toLocaleDateString(), endDate: new Date(endDate).toLocaleDateString() })}</p>
          <table class="report-table" id="alacakli-cariler-table">
            <thead>
              <tr>
                <th>${t('label_cari_unvan')}</th>
                <th>${t('label_total_sales_period') || 'Dönem Toplam Satış'}</th>
                <th>${t('label_total_collections_period') || 'Dönem Tahsilat'}</th>
                <th>${t('label_net_receivable_overall') || 'Net Alacak (Genel)'}</th>
              </tr>
            </thead>
            <tbody>
              ${bizimAlacakliOlduklarimiz.length === 0 ? `<tr><td colspan="4">${t('no_data_for_report')}</td></tr>` : bizimAlacakliOlduklarimiz.map(row => `
                <tr>
                  <td>${row.Unvan}</td>
                  <td class="cell-text-right">${row.ToplamSatis.toFixed(2)}</td>
                  <td class="cell-text-right">${row.Alinan.toFixed(2)}</td>
                  <td class="cell-text-right">${row.NetBakiye.toFixed(2)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <h3 style="margin-top:32px;">${t('report_title_payables_to_cariler')}</h3>
          <p>${t('report_subtitle_we_owe_cariler', { startDate: new Date(startDate).toLocaleDateString(), endDate: new Date(endDate).toLocaleDateString() })}</p>
          <table class="report-table" id="borclu-cariler-table">
            <thead>
              <tr>
                <th>${t('label_cari_unvan')}</th>
                <th>${t('label_total_purchases_period') || 'Dönem Toplam Alış'}</th>
                <th>${t('label_total_payments_period') || 'Dönem Ödeme'}</th>
                <th>${t('label_net_payable_overall') || 'Net Borç (Genel)'}</th>
              </tr>
            </thead>
            <tbody>
              ${bizimBorcluOlduklarimiz.length === 0 ? `<tr><td colspan="4">${t('no_data_for_report')}</td></tr>` : bizimBorcluOlduklarimiz.map(row => `
                <tr>
                  <td>${row.Unvan}</td>
                  <td class="cell-text-right">${row.ToplamAlis.toFixed(2)}</td>
                  <td class="cell-text-right">${row.Odenen.toFixed(2)}</td>
                  <td class="cell-text-right">${Math.abs(row.NetBakiye).toFixed(2)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        `;
      }
      if (outputArea.querySelector('table')) {
        actionsArea.style.display = 'flex';
      }
    } catch (error) {
      console.error('Rapor oluşturma hatası:', error);
      outputArea.innerHTML = `
        <div class="empty-message">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          <p style="color: var(--status-error);">${t('error_generating_report', {error: error.message}) || 'Rapor oluşturulurken hata oluştu: ' + error.message}</p>
        </div>
      `;
      actionsArea.style.display = 'none';
    }
  });

  // Export helpers
  function getReportDataFromTable(tableElement) {
    if (!tableElement) return { headers: [], rows: [] };
    const headers = Array.from(tableElement.querySelectorAll('thead th')).map(th => th.textContent);
    const rows = Array.from(tableElement.querySelectorAll('tbody tr')).map(tr =>
      Array.from(tr.querySelectorAll('td')).map(td => td.textContent)
    );
    return { headers, rows };
  }

  exportPdfBtn.addEventListener('click', () => {
    const reportType = contentArea.querySelector('#muhasebe-report-type').value;
    const reportTitleText = outputArea.querySelector('h3')?.textContent || t('muhasebe_reports');
    const tablesToExport = [];

    if (reportType === 'gelir-gider') {
        const detailedTable = outputArea.querySelector('#detailed-financial-table');
        const traditionalTable = outputArea.querySelector('#traditional-income-expense-table');
        if (detailedTable) tablesToExport.push({title: 'Detaylı Finansal Rapor', table: detailedTable});
        if (traditionalTable) tablesToExport.push({title: 'Geleneksel Gelir-Gider Özeti', table: traditionalTable});
    } else if (reportType === 'net-bakiye') {
        const alacakliTable = outputArea.querySelector('#alacakli-cariler-table');
        const borcluTable = outputArea.querySelector('#borclu-cariler-table');
        const alacakliTitle = outputArea.querySelector('h3:first-of-type')?.textContent;
        const borcluTitle = outputArea.querySelector('#borclu-cariler-table')?.previousElementSibling?.previousElementSibling?.textContent;


        if (alacakliTable) tablesToExport.push({title: alacakliTitle || t('report_title_receivables_from_cariler'), table: alacakliTable});
        if (borcluTable) tablesToExport.push({title: borcluTitle || t('report_title_payables_to_cariler'), table: borcluTable});
    }

    if (tablesToExport.length === 0) {
      return;
    }
    if (typeof pdfMake === 'undefined') {
      window.toast?.error(t('pdf_library_not_loaded') || 'PDF kütüphanesi yüklenemedi.');
      return;
    }

    const docContent = [];
    let combinedReportTitle = reportType === 'gelir-gider' ? t('gelir_gider_tablosu') : t('net_bakiye_raporu');

    tablesToExport.forEach(item => {
        const { headers, rows } = getReportDataFromTable(item.table);
        if (rows.length > 0 && !(rows.length === 1 && rows[0].length === 1 && rows[0][0] === t('no_data_for_report'))) { // Check if it's not just the "no data" message
            docContent.push({ text: item.title, style: 'subheader', margin: [0, 15, 0, 5] });
            docContent.push({
                table: {
                    headerRows: 1,
                    widths: headers.map(() => '*'),
                    body: [headers, ...rows]
                },
                layout: 'lightHorizontalLines'
            });
        }
    });

    if (docContent.length === 0) {
        window.toast?.warning(t('no_data_to_export') || 'Dışa aktarılacak veri bulunamadı.');
        return;
    }

    const documentDefinition = {
      content: [
          { text: combinedReportTitle, style: 'header' },
          ...docContent
      ],
      styles: {
        header: { fontSize: 18, bold: true, margin: [0, 0, 0, 10] },
        subheader: { fontSize: 15, bold: true, margin: [0, 10, 0, 5] }
      }
    };
    pdfMake.createPdf(documentDefinition).download(`${combinedReportTitle.replace(/\s+/g, '_').toLowerCase()}_${new Date().toISOString().slice(0,10)}.pdf`);
  });

  exportExcelBtn.addEventListener('click', () => {
    const reportType = contentArea.querySelector('#muhasebe-report-type').value;
    let csvContent = "";
    let fileName = (reportType === 'gelir-gider' ? t('gelir_gider_tablosu') : t('net_bakiye_raporu')).replace(/\s+/g, '_').toLowerCase();

    const tablesToExport = [];
     if (reportType === 'gelir-gider') {
        const detailedTable = outputArea.querySelector('#detailed-financial-table');
        const traditionalTable = outputArea.querySelector('#traditional-income-expense-table');
        if (detailedTable) tablesToExport.push({title: 'Detaylı Finansal Rapor', table: detailedTable});
        if (traditionalTable) tablesToExport.push({title: 'Geleneksel Gelir-Gider Özeti', table: traditionalTable});
    } else if (reportType === 'net-bakiye') {
        const alacakliTable = outputArea.querySelector('#alacakli-cariler-table');
        const borcluTable = outputArea.querySelector('#borclu-cariler-table');
        if (alacakliTable) tablesToExport.push({title: t('report_title_receivables_from_cariler'), table: alacakliTable});
        if (borcluTable) tablesToExport.push({title: t('report_title_payables_to_cariler'), table: borcluTable});
    }

    if (tablesToExport.length === 0) {
      window.toast?.warning(t('no_report_to_export') || 'Dışa aktarılacak rapor bulunamadı.');
      return;
    }

    tablesToExport.forEach((item, index) => {
        const { headers, rows } = getReportDataFromTable(item.table);
        if (rows.length > 0 && !(rows.length === 1 && rows[0].length === 1 && rows[0][0] === t('no_data_for_report'))) {
            if (index > 0) csvContent += "\n\n"; // Add space between tables
            csvContent += item.title + '\n';
            csvContent += headers.join(';') + '\n';
            rows.forEach(row => {
                csvContent += row.join(';') + '\n';
            });
        }
    });

    if (csvContent.trim() === "") {
        window.toast?.warning(t('no_data_to_export') || 'Dışa aktarılacak veri bulunamadı.');
        return;
    }

    const blob = new Blob(["\uFEFF" + csvContent], { type: 'text/csv;charset=utf-8;' }); // BOM for Excel UTF-8
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${fileName}_${new Date().toISOString().slice(0,10)}.csv`;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 100);
  });


}
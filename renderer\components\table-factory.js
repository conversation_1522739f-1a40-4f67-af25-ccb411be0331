/**
 * Table Factory - Simplified table creation with common configurations
 * Provides pre-configured table setups for common use cases in the livestock management application
 */

import { UniversalTable } from './universal-table.js';
import { IconSystem } from '../utils/icon-system.js';
import { BadgeSystem } from '../utils/badge-system.js';

export class TableFactory {
  /**
   * Create a standard data table with common configurations
   */
  static createDataTable(config) {
    const defaultConfig = {
      sortable: true,
      filterable: true,
      paginated: true,
      responsive: true,
      itemsPerPage: 10,
      className: 'modern-table'
    };

    return new UniversalTable({ ...defaultConfig, ...config });
  }

  /**
   * Create an animals table with livestock-specific configurations
   */
  static createAnimalsTable(config) {
    const animalsConfig = {
      columns: [
        {
          key: 'avatar',
          title: '',
          sortable: false,
          filterable: false,
          className: 'col-avatar',
          render: (value, item) => {
            if (item.FotografUrl) {
              return `<img class="table-avatar" src="${item.FotografUrl}" alt="" onerror="this.style.display='none'">`;
            }
            return `<div class="avatar-placeholder">${(item.KupeNo || '?').charAt(0)}</div>`;
          }
        },
        {
          key: 'KupeNo',
          title: 'Ear Tag',
          dataType: 'text',
          filterType: 'text',
          filterPlaceholder: 'Search ear tag...'
        },
        {
          key: 'Isim',
          title: 'Name',
          dataType: 'text',
          filterType: 'text',
          filterPlaceholder: 'Search name...'
        },
        {
          key: 'Irk',
          title: 'Breed',
          dataType: 'text',
          filterType: 'text',
          filterPlaceholder: 'Search breed...'
        },
        {
          key: 'Cinsiyet',
          title: 'Gender',
          dataType: 'text',
          filterType: 'select',
          filterOptions: [
            { value: 'Disi', label: 'Female' },
            { value: 'Erkek', label: 'Male' }
          ],
          render: (value, item) => {
            const genderMap = { 'Disi': 'Female', 'Erkek': 'Male' };
            return genderMap[value] || value;
          }
        },
        {
          key: 'DogumTarihi',
          title: 'Birth Date',
          dataType: 'date',
          filterType: 'date',
          className: 'cell-date'
        },
        {
          key: 'GebelikDurumu',
          title: 'Pregnancy Status',
          dataType: 'text',
          filterType: 'select',
          filterOptions: [
            { value: 'Gebe', label: 'Pregnant' },
            { value: 'Bos', label: 'Empty' }
          ],
          render: (value, item) => BadgeSystem.createAnimalStatusBadge(value, { pregnant: 'Pregnant', empty: 'Empty' })
        },
        {
          key: 'Durum',
          title: 'Status',
          dataType: 'text',
          filterType: 'select',
          filterOptions: [
            { value: 'Sagimda', label: 'Milking' },
            { value: 'Kuru', label: 'Dry' }
          ],
          render: (value, item) => BadgeSystem.createAnimalStatusBadge(value, { milking: 'Milking', dry: 'Dry' })
        },
        {
          key: 'GunlukOrtalamaSut',
          title: 'Daily Milk Avg',
          dataType: 'number',
          filterType: 'number',
          className: 'cell-number',
          decimals: 1,
          render: (value, item) => value ? `${Number(value).toFixed(1)} L` : '-'
        },
        {
          key: 'AktifMi',
          title: 'Active',
          dataType: 'boolean',
          filterType: 'select',
          filterOptions: [
            { value: '1', label: 'Active' },
            { value: '0', label: 'Inactive' }
          ],
          render: (value, item) => BadgeSystem.createActiveStatusBadge(value == 1, { active: 'Active', inactive: 'Inactive' })
        },
        {
          key: 'actions',
          title: 'Actions',
          sortable: false,
          filterable: false,
          className: 'col-actions',
          render: (value, item) => IconSystem.createActionsWrapper(item.Id, { edit: true, delete: true }, { edit: 'Edit', delete: 'Delete' })
        }
      ],
      emptyStateMessage: 'No animals found',
      ...config
    };

    return TableFactory.createDataTable(animalsConfig);
  }

  /**
   * Create an accounting table with financial data configurations
   */
  static createAccountingTable(config) {
    const accountingConfig = {
      columns: [
        {
          key: 'Id',
          title: 'ID',
          dataType: 'number',
          filterType: 'number',
          className: 'cell-number'
        },
        {
          key: 'Unvan',
          title: 'Company Name',
          dataType: 'text',
          filterType: 'text',
          filterPlaceholder: 'Search company...'
        },
        {
          key: 'Tip',
          title: 'Type',
          dataType: 'text',
          filterType: 'select',
          filterOptions: [
            { value: 'Musteri', label: 'Customer' },
            { value: 'Tedarikci', label: 'Supplier' }
          ],
          render: (value, item) => BadgeSystem.createBusinessTypeBadge(value, { customer: 'Customer', supplier: 'Supplier' })
        },
        {
          key: 'Telefon',
          title: 'Phone',
          dataType: 'text',
          filterType: 'text',
          filterable: false
        },
        {
          key: 'Email',
          title: 'Email',
          dataType: 'text',
          filterType: 'text',
          filterable: false
        },
        {
          key: 'VergiNo',
          title: 'Tax Number',
          dataType: 'text',
          filterType: 'text',
          filterable: false
        },
        {
          key: 'actions',
          title: 'Actions',
          sortable: false,
          filterable: false,
          className: 'col-actions',
          render: (value, item) => IconSystem.createActionsWrapper(item.Id, { edit: true, delete: true }, { edit: 'Edit', delete: 'Delete' })
        }
      ],
      emptyStateMessage: 'No accounts found',
      ...config
    };

    return TableFactory.createDataTable(accountingConfig);
  }

  /**
   * Create a health records table
   */
  static createHealthTable(config) {
    const healthConfig = {
      columns: [
        {
          key: 'AsiAdi',
          title: 'Vaccine Name',
          dataType: 'text',
          filterType: 'text',
          filterPlaceholder: 'Search vaccine...'
        },
        {
          key: 'AsilamaTarihi',
          title: 'Vaccination Date',
          dataType: 'date',
          filterType: 'date',
          className: 'cell-date'
        },
        {
          key: 'Doz',
          title: 'Dose',
          dataType: 'text',
          filterType: 'text'
        },
        {
          key: 'UygulayanVeteriner',
          title: 'Veterinarian',
          dataType: 'text',
          filterType: 'text',
          filterPlaceholder: 'Search vet...'
        },
        {
          key: 'Notlar',
          title: 'Notes',
          dataType: 'text',
          filterType: 'text',
          filterable: false
        },
        {
          key: 'actions',
          title: 'Actions',
          sortable: false,
          filterable: false,
          className: 'col-actions',
          render: (value, item) => IconSystem.createActionsWrapper(item.Id, { edit: true, delete: true }, { edit: 'Edit', delete: 'Delete' })
        }
      ],
      emptyStateMessage: 'No health records found',
      ...config
    };

    return TableFactory.createDataTable(healthConfig);
  }

  /**
   * Create a milk yield table
   */
  static createMilkYieldTable(config) {
    const milkConfig = {
      columns: [
        {
          key: 'Tarih',
          title: 'Date',
          dataType: 'date',
          filterType: 'date',
          className: 'cell-date'
        },
        {
          key: 'Saat',
          title: 'Time',
          dataType: 'text',
          filterType: 'text',
          className: 'cell-center'
        },
        {
          key: 'Miktar',
          title: 'Amount (L)',
          dataType: 'number',
          filterType: 'number',
          className: 'cell-number',
          decimals: 1,
          render: (value, item) => value ? Number(value).toFixed(1) : '-'
        },
        {
          key: 'YagOrani',
          title: 'Fat %',
          dataType: 'number',
          filterType: 'number',
          className: 'cell-number',
          decimals: 1,
          render: (value, item) => value ? Number(value).toFixed(1) : '-'
        },
        {
          key: 'ProteinOrani',
          title: 'Protein %',
          dataType: 'number',
          filterType: 'number',
          className: 'cell-number',
          decimals: 1,
          render: (value, item) => value ? Number(value).toFixed(1) : '-'
        },
        {
          key: 'actions',
          title: 'Actions',
          sortable: false,
          filterable: false,
          className: 'col-actions',
          render: (value, item) => IconSystem.createActionsWrapper(item.Id, { edit: true, delete: true }, { edit: 'Edit', delete: 'Delete' })
        }
      ],
      emptyStateMessage: 'No milk yield records found',
      ...config
    };

    return TableFactory.createDataTable(milkConfig);
  }

  /**
   * Create a simple report table (no interactions)
   */
  static createReportTable(config) {
    const reportConfig = {
      sortable: false,
      filterable: false,
      paginated: false,
      responsive: true,
      className: 'modern-table',
      emptyStateMessage: 'No data available for this report',
      ...config
    };

    return new UniversalTable(reportConfig);
  }

  /**
   * Helper method to add common action handlers to a table
   */
  static addActionHandlers(table, handlers = {}) {
    if (table.config.onRowAction) return; // Already has action handler

    table.tbody.addEventListener('click', (e) => {
      const actionBtn = e.target.closest('.action-btn');
      if (!actionBtn) return;

      const action = actionBtn.dataset.action;
      const id = actionBtn.dataset.id;
      const row = actionBtn.closest('tr');
      const rowIndex = Array.from(table.tbody.children).indexOf(row);
      const pageStartIndex = (table.state.currentPage - 1) * table.config.itemsPerPage;
      const dataIndex = pageStartIndex + rowIndex;
      const rowData = table.state.filteredData[dataIndex];

      if (handlers[action] && typeof handlers[action] === 'function') {
        handlers[action](rowData, id, row, e);
      }
    });
  }

  /**
   * Helper method to create a table with standard CRUD operations
   */
  static createCrudTable(config, crudHandlers = {}) {
    const table = TableFactory.createDataTable(config);
    
    TableFactory.addActionHandlers(table, {
      edit: crudHandlers.onEdit || (() => console.log('Edit handler not implemented')),
      delete: crudHandlers.onDelete || (() => console.log('Delete handler not implemented')),
      view: crudHandlers.onView || (() => console.log('View handler not implemented')),
      ...crudHandlers
    });

    return table;
  }
}

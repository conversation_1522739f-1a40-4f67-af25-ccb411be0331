window.i18n = (function() {
  const translations = {
    tr: {
      dashboard: 'Anasay<PERSON>',
      total_animals: 'Toplam Aktif Hayvan Sayısı',
      total_animals_desc: 'Sistemdeki toplam aktif hayvan sayısı.',
      male_animals: '<PERSON><PERSON><PERSON> Hay<PERSON>',
      male_animals_desc: 'Sistemdeki toplam erkek hayvan sayısı.',
      female_animals: 'Dişi Hayvan',
      female_animals_desc: 'Sistemdeki toplam dişi hayvan sayısı.',
      upcoming_births_30_days: 'Yaklaş<PERSON> (30 gün)',
      upcoming_births_30_days_desc: 'Önümüzdeki 30 gün içinde beklenen doğumlar.',
      daily_milk_yield: 'Bugünkü Süt Verimi (L)',
      monthly_milk_yield: '<PERSON>u Aylık Süt Verimi (L)',
      milking_animals: 'Sağ<PERSON>',
      milking_animals_desc: 'Sağmal durumda olan dişi hayvanların oranı.',
      ongoing_pregnancies: '<PERSON><PERSON> Gebelik',
      ongoing_pregnancies_desc: '<PERSON><PERSON> anda gebe olan hayvan sayısı.',
      health_alerts_active: 'Akt<PERSON> Uyarıları',
      health_alerts_active_desc: 'Karantinada veya tedavisi devam eden hayvan sayısı.',
      daily_milk_yield_avg: 'Günlük Ortalama Süt',
      daily_milk_yield_avg_desc: 'Tüm sağmal hayvanların günlük süt ortalaması.',
      welcome_message: 'Hoş Geldiniz!',
      dashboard_subtitle: 'Sürünüzün genel durumu aşağıdadır.',
      animal_distribution_chart_title: 'Hayvan Dağılımı',
      milk_production_chart_title: 'Aylık Süt Üretimi (Son 6 Ay)',
      quick_actions: 'Hızlı İşlemler',
      animals: 'Hayvanlar',
      health: 'Sağlık',
      reproduction: 'Üreme',
      milk: 'Süt Verimi',
      reports: 'Raporlamalar',
      feed_ration: 'Rasyon Yönetimi',
      settings: 'Ayarlar',
      // Form ve tablo
      label_kupe_no: 'Küpe No',
      label_isletme_no: 'İşletme No',
      label_isim: 'İsim',
      label_tur: 'Tür',
      label_irk: 'Irk',
      label_cinsiyet: 'Cinsiyet',
      label_dogum_tarihi: 'Doğum Tarihi',
      label_dogum_agirligi: 'Doğum Ağırlığı (kg)',
      label_dogum_tipi: 'Doğum Tipi',
      label_anne_kupe: 'Anne Küpe',
      label_baba_kupe: 'Baba Küpe',
      label_boynuz_durumu: 'Boynuz Durumu',
      label_isletmeye_giris: 'İşletmeye Giriş Tarihi',
      label_isletmeden_cikis: 'İşletmeden Çıkış Tarihi',
      label_cikis_sebebi: 'Çıkış Sebebi',
      label_fotograf_url: 'Fotoğraf URL',
      label_notlar: 'Notlar',
      label_aktif_mi: 'Aktiflik',
      label_actions: 'İşlemler',
      label_age: 'Yaş',
      // Tedavi modalı ve tablo
      label_teshis: 'Teşhis',
      label_tedavi_baslangic: 'Başlangıç Tarihi',
      label_tedavi_bitis: 'Bitiş Tarihi',
      label_kullanilan_ilaclar: 'Kullanılan İlaçlar',
      label_karantina_durumu: 'Karantina Durumu',
      label_karantina_baslangic: 'Karantina Başlangıç',
      label_karantina_bitis: 'Karantina Bitiş',
      treatment_modal_title_add: 'Tedavi Kaydı',
      treatment_modal_title_edit: 'Tedaviyi Düzenle',
      option_none: 'Yok',
      // Uyarılar
      confirm_delete_treatment: 'Bu tedavi kaydını silmek istediğinize emin misiniz?',
      confirm_delete_animal: 'Bu hayvan kaydını ve ilişkili tüm verileri (aşı, tedavi vb.) silmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
      // Modal başlıkları
      animal_modal_title_add: 'Hayvan Ekle',
      animal_modal_title_edit: 'Hayvanı Düzenle',
      // Butonlar
      btn_save: 'Kaydet',
      btn_add_animal: '+ Hayvan Ekle',
      btn_edit: 'Düzenle',
      btn_delete: 'Sil',
      btn_add_vaccine: 'Aşı Ekle',
      vaccine_modal_title_add: 'Aşı Kaydı Ekle',
      vaccine_modal_title_edit: 'Aşı Kaydını Düzenle',
      btn_add_milking_record: 'Sağım Kaydı Ekle',
      btn_view_alerts: 'Uyarıları Görüntüle',
      confirm_delete_vaccine: 'Bu aşı kaydını silmek istediğinize emin misiniz?',
      btn_add_treatment: 'Tedavi Ekle',
      // Seçenekler
      option_yes: 'Evet',
      option_no: 'Hayır',
      option_erkek: 'Erkek',
      option_dişi: 'Dişi',
      option_all: 'Tümü',
      option_active: 'Aktif',
      option_inactive: 'Pasif',
      // Tohumlamalar
      health_vaccinations: 'Aşılamalar',
      health_treatments: 'Tedaviler',
      label_tohumlamalar: 'Tohumlamalar',
      label_tohumlama_tarihi: 'Tohumlama Tarihi',
      label_tohumlama_tipi: 'Tohumlama Tipi',
      label_kullanilan_tohum: 'Kullanılan Tohum',
      tohumlama_modal_title_add: 'Tohumlama Kaydı',
      tohumlama_modal_title_edit: 'Tohumlamayı Düzenle',
      btn_add_insemination: 'Tohumlama Ekle',
      confirm_delete_insemination: 'Bu tohumlama kaydını silmek istediğinize emin misiniz?',
      // Gebelikler
      label_gebelikler: 'Gebelikler',
      btn_add_pregnancy: 'Gebelik Ekle',
      label_gebelik_baslangic: 'Başlangıç',
      label_gebelik_bitis: 'Bitiş',
      label_gebelik_sonuc: 'Sonuç',
      pregnancy_modal_title_add: 'Gebelik Kaydı',
      pregnancy_modal_title_edit: 'Gebeliği Düzenle',
      confirm_delete_pregnancy: 'Bu gebelik kaydını silmek istediğinize emin misiniz?',
      label_sperma_kodu: 'Sperma Kodu',
      label_gebelik_baslangic_tarihi: 'Başlangıç Tarihi',
      label_beklenen_dogum_tarihi: 'Beklenen Doğum Tarihi',
      label_gebelik_sonucu: 'Gebelik Sonucu',
      option_select: 'Seçiniz',
      pregnancy_result_ongoing: 'Devam Ediyor',
      pregnancy_result_devam_ediyor: 'Devam Ediyor',
      pregnancy_result_successful: 'Başarılı',
      pregnancy_result_failed: 'Başarısız',
      pregnancy_status_pregnant: 'Gebe',
      pregnancy_status_empty: 'Boş',
      lactation_status_milking: 'Sağmal',
      lactation_status_dry: 'Kuruda',
      // Süt Verimi
      label_milk_yield: 'Süt Verimi',
      label_laktasyonlar: 'Laktasyonlar',
      label_sagim_verileri: 'Sağım Verileri',
      btn_add_lactation: 'Laktasyon Ekle',
      btn_add_milking: 'Sağım Verisi Ekle',
      btn_bulk_milking: 'Toplu Sağım Girdisi',
      bulk_milking_modal_title: 'Toplu Sağım Verisi Girdisi',
      section_common_milking_info: 'Ortak Sağım Bilgileri',
      section_animals_milking_data: 'Hayvan Sağım Verileri',
      btn_save_all: 'Tümünü Kaydet',
      no_lactating_animals: 'Aktif laktasyonu olan hayvan bulunamadı.',
      error_loading_animals: 'Hayvanlar yüklenirken hata oluştu.',
      please_fill_date_time: 'Lütfen tarih ve saat alanlarını doldurun.',
      no_milking_data_entered: 'Hiçbir hayvan için sağım verisi girilmedi.',
      bulk_milking_saved_success: 'Toplu sağım verisi başarıyla kaydedildi.',
      error_saving_bulk_milking: 'Toplu sağım verisi kaydedilirken hata oluştu.',
      placeholder_notes: 'Notlar...',

      // Business Rule Validation Messages
      validation_male_animal_reproduction: 'Erkek hayvanlara gebelik, tohumlama, laktasyon ve sağım verisi girilemez!',
      validation_lactation_overlap: 'Aynı hayvan için belirlenmiş laktasyon dönemlerinin tarihleri çakışamaz!',
      validation_active_pregnancy: 'Aynı hayvan devam eden bir gebelik sırasında tekrar gebe kalamaz!',
      validation_error_occurred: 'Validation kontrolü sırasında hata oluştu',
      validation_check_failed: 'Validation kontrolü yapılamadı',
      validation_due_date_after_start: 'Beklenen doğum tarihi başlangıç tarihinden sonra olmalıdır',
      validation_milk_yield_too_high: 'Günlük süt verimi çok yüksek görünüyor',
      validation_field_required: '{{field}} gereklidir',
      validation_invalid_email: 'Geçerli bir e-posta adresi giriniz',
      validation_invalid_number: 'Geçerli bir sayı giriniz',
      validation_min_value: 'Değer en az {{min}} olmalıdır',
      validation_max_value: 'Değer en fazla {{max}} olmalıdır',
      validation_future_date_not_allowed: '{{field}} gelecek tarih olamaz (sadece geçmiş olaylar için)',
      validation_date_too_old: '{{field}} çok eski görünüyor',
      lactation_modal_title_add: 'Laktasyon Kaydı',
      milking_modal_title_add: 'Sağım Verisi Kaydı',
      milking_modal_title_edit: 'Sağım Verisini Düzenle',
      confirm_delete_milking: 'Bu sağım verisini silmek istediğinize emin misiniz?',
      label_laktasyon_baslangic: 'Başlangıç Tarihi',
      label_laktasyon_bitis: 'Bitiş Tarihi',
      label_sagim_tarih: 'Tarih',
      label_sagim_saat: 'Saat',
      label_sagim_miktar: 'Miktar (L)',
      label_yag_orani: 'Yağ (%)',
      label_protein_orani: 'Protein (%)',
      label_laktasyon_secin: 'Lütfen bir laktasyon dönemi seçin.',
      please_select_animal_for_lactation: 'Laktasyon eklemek için lütfen bir hayvan seçin.',
      please_select_lactation_for_milking: 'Sağım verisi eklemek için lütfen bir laktasyon seçin.',
      confirm_delete_lactation: 'Bu laktasyon kaydını silmek istediğinize emin misiniz?',
      // Ayarlar
      label_settings: 'Ayarlar',
      label_appearance: 'Görünüm',
      label_theme: 'Tema',
      label_language: 'Dil',
      option_light: 'Açık',
      option_dark: 'Karanlık',
      option_system: 'Sistem',
      language_tr: 'Türkçe',
      language_en: 'English',
      label_all_dates: 'Tümü',
      label_total_expense: 'Toplam Gider',
      label_net_profit: 'Net Kar/Zarar',
      debtor_current_accounts_us: 'Alacaklı Cariler',
      creditor_current_accounts_them: 'Borçlu Cariler',
      no_creditor_accounts_found: 'Alacaklı cari bulunamadı.',
      no_debtor_accounts_found: 'Borçlu cari bulunamadı.',
      amount_try: 'Tutar (TL)',
      label_report_type: 'Rapor Türü',
      gelir_gider_tablosu: 'Dönemlik Gelir-Gider Tablosu',
      net_bakiye_raporu: 'Net Bakiye Raporu',
      label_start_date: 'Başlangıç Tarihi',
      label_end_date: 'Bitiş Tarihi',
      btn_generate_report: 'Rapor Oluştur',
      report_select_criteria_generate: 'Lütfen rapor kriterlerini seçip "Rapor Oluştur" butonuna tıklayın.',
      btn_export_pdf: 'PDF Olarak Aktar',
      btn_export_excel: 'Excel Olarak Aktar',
      error_date_range_required: 'Tarih aralığı zorunludur.',
      report_generating: 'Rapor oluşturuluyor...', 
      label_total_income: 'Toplam Gelir',
      report_title_receivables_from_cariler: 'Cari Hesaplardan Alacaklar',
      report_subtitle_cariler_owe_us: 'Cari Hesapların Bize Borcu Var (Dönem: {{startDate}} - {{endDate}})',
      label_cari_unvan: 'Cari Ünvan',
      label_total_sales_period: 'Dönem Toplam Satış',
      label_total_collections_period: 'Dönem Tahsilat',
      label_net_receivable_overall: 'Net Alacak (Genel)',
      no_data_for_report: 'Rapor için veri bulunamadı.',
      report_title_payables_to_cariler: 'Cari Hesaplara Borçlar',
      report_subtitle_we_owe_cariler: 'Bizim Cari Hesaplara Borcumuz Var (Dönem: {{startDate}} - {{endDate}})',
      label_total_purchases_period: 'Dönem Toplam Alış',
      label_total_payments_period: 'Dönem Ödeme',
      label_net_payable_overall: 'Net Borç (Genel)',
      error_generating_report: 'Rapor oluşturulurken bir hata oluştu: {{error}}',
      muhasebe_reports: 'Muhasebe Raporları',
      pdf_library_not_loaded: 'PDF kütüphanesi yüklenemedi.',
      label_database: 'Veritabanı',
      db_backup_recommendation: 'Veri kaybını önlemek için veritabanınızı düzenli olarak yedeklemeniz önerilir.',
      btn_backup_db: 'Veritabanını Yedekle',
      btn_restore_db: 'Yedekten Geri Yükle',
      alert_backup_success: 'Veritabanı başarıyla yedeklendi: ',
      alert_error: 'Hata: ',
      confirm_restore_db: 'Bu işlem mevcut veritabanınızı seçeceğiniz yedek ile değiştirecektir. Uygulama yeniden başlatılacak. Devam etmek istiyor musunuz?',
      label_about: 'Hakkında',
      label_app_version: 'Uygulama Versiyonu',
      // Genel
      search_placeholder_animals: 'Ara: Küpe No, İsim...',
      no_records_found: 'Kayıt bulunamadı.',
      please_select_animal: 'Lütfen bir hayvan seçiniz.',
      label_vaccine_name: 'Aşı Adı',
      label_date: 'Tarih',
      label_dose: 'Doz',
      label_vet: 'Veteriner',
      year_unit: 'yıl',
      month_unit: 'ay',
      day_unit: 'gün',
      label_gebelik_durumu: 'Gebelik',
      label_durum: 'Durum',
      label_daily_milk_avg: 'Günlük Ort. Süt',
      feature_not_implemented_yet: 'Bu özellik henüz tamamlanmadı.',
      error_loading_details: 'Detaylar yüklenirken hata oluştu.',
      no_notes_available: 'Not bulunmuyor.',
      error_modal_function_not_available: 'İlgili fonksiyon mevcut olmadığından modal açılamıyor.',

      // Vaccination completion
      complete_vaccination: 'Aşıyı Tamamla',
      vaccination_completed_successfully: 'Aşı başarıyla tamamlandı',
      schedule_not_found: 'Takvim kaydı bulunamadı',
      animal_not_found: 'Hayvan bulunamadı',
      planned_date: 'Planlanan Tarih',
      animal: 'Hayvan',

      // Vaccination Management
      vaccination_management: 'Aşı Yönetimi',
      vaccination_templates: 'Aşı Şablonları',
      btn_add_template: 'Şablon Ekle',
      template_name: 'Şablon Adı',
      animal_type: 'Hayvan Türü',
      interval: 'Periyot',
      option_active: 'Aktif',
      option_inactive: 'Pasif',
      quick_actions: 'Hızlı İşlemler',
      bulk_vaccination: 'Toplu Aşılama',
      bulk_vaccination_desc: 'Birden fazla hayvana aynı anda aşı uygula',
      generate_schedule: 'Takvim Oluştur',
      generate_schedule_desc: 'Hayvanlar için aşı takvimi oluştur',
      view_reminders: 'Hatırlatıcıları Gör',
      view_reminders_desc: 'Yaklaşan ve geciken aşıları görüntüle',
      confirm_delete_template: 'Bu aşı şablonunu silmek istediğinize emin misiniz?',
      page: 'Sayfa',
      records: 'kayıt',

      // Vaccination Reminders
      vaccination_reminders: 'Aşı Hatırlatıcıları',
      overdue_vaccinations: 'Geciken Aşılar',
      upcoming_vaccinations: 'Yaklaşan Aşılar',
      no_overdue_vaccinations: 'Geciken aşı bulunmuyor',
      no_upcoming_vaccinations: 'Yaklaşan aşı bulunmuyor',
      planned_date: 'Planlanan Tarih',
      days_overdue: 'gün gecikti',
      mark_completed: 'Tamamlandı İşaretle',
      due_today: 'Bugün yapılmalı',
      due_soon: 'Yakında yapılmalı',
      btn_close: 'Kapat',
      btn_refresh: 'Yenile',

      // Bulk Vaccination
      vaccination_information: 'Aşı Bilgileri',
      option_other: 'Diğer',
      enter_vaccine_name: 'Aşı adını giriniz',
      label_vaccination_date: 'Aşılama Tarihi',
      enter_dose: 'Doz giriniz',
      enter_veterinarian: 'Veteriner adını giriniz',
      enter_notes: 'Notlar giriniz',
      select_animals: 'Hayvan Seçimi',
      select_all: 'Tümünü Seç',
      deselect_all: 'Tümünü Kaldır',
      selected: 'Seçilen',
      no_active_animals_found: 'Aktif hayvan bulunamadı',
      please_select_animals: 'Lütfen en az bir hayvan seçiniz',
      btn_save_vaccinations: 'Aşılamaları Kaydet',
      bulk_vaccination_success: '{count} hayvana aşı kaydı başarıyla eklendi',

      // Vaccination Schedule
      vaccination_schedule: 'Aşı Takvimi',
      upcoming_30_days: 'Yaklaşan (30 gün)',
      completed_this_month: 'Bu Ay Tamamlanan',
      animal: 'Hayvan',
      completion_date: 'Tamamlanma Tarihi',
      status_pending: 'Bekliyor',
      status_completed: 'Tamamlandı',
      status_overdue: 'Gecikti',
      status_cancelled: 'İptal',
      confirm_delete_schedule: 'Bu aşı takvim kaydını silmek istediğinize emin misiniz?',

      // Template Modal
      edit_template: 'Şablon Düzenle',
      add_template: 'Şablon Ekle',
      template_information: 'Şablon Bilgileri',
      enter_template_name: 'Şablon adını giriniz',
      interval_value: 'Periyot Değeri',
      interval_type: 'Periyot Tipi',
      enter_interval: 'Periyot değerini giriniz',
      days: 'Gün',
      months: 'Ay',
      years: 'Yıl',
      first_vaccination_age: 'İlk Aşı Yaşı',
      days_after_birth: 'Doğumdan sonra gün',
      repeat_count: 'Tekrar Sayısı',
      unlimited_if_negative: 'Sınırsız için -1',
      description: 'Açıklama',
      enter_description: 'Açıklama giriniz',

      // Generate Schedule Modal
      select_animal: 'Hayvan Seçimi',
      select_templates: 'Şablon Seçimi',
      schedule_settings: 'Takvim Ayarları',
      start_date: 'Başlangıç Tarihi',
      btn_generate: 'Oluştur',
      no_active_templates_found: 'Aktif şablon bulunamadı',
      please_select_templates: 'Lütfen en az bir şablon seçiniz',
      schedule_generated_success: '{count} adet takvim kaydı başarıyla oluşturuldu',

      // Quick Filter
      filter_by_animal: 'Hayvana Göre Filtrele',
      all_animals: 'Tüm Hayvanlar',
      single_animal: 'Tek Hayvan',
      select_animals: 'Hayvan Seçimi',
      manage_templates: 'Şablonları Yönet',
      templates: 'Şablonlar',
      error_loading_stats: 'İstatistikler yüklenirken hata oluştu.',
      chart_library_not_loaded: 'Grafik kütüphanesi yüklenemedi. Grafikler gösterilemiyor.',
      error_loading_dashboard_data: 'Anasayfa verileri yüklenirken hata oluştu.',
      other_genders: 'Diğer',
      error_loading_accounting_stats: 'Muhasebe istatistikleri yüklenirken hata oluştu',
      see_all: 'Tümünü Gör',
      navigation_function_missing_error: 'Navigasyon fonksiyonu bulunamadı',
      net_profit_loss: 'Net Kar/Zarar',
      milk_production_liters: 'Süt Üretimi (L)',
      no_data: 'Veri Yok',
      liters: 'Litre',
      months: 'Aylar',
      // Pagination
      btn_previous: 'Önceki',
      btn_next: 'Sonraki',
      page_info_text: 'Sayfa {{currentPage}} / {{totalPages}} ({{totalItems}} kayıt)',
      // Milk Yield Page Alerts
      lactation_modal_title_edit: 'Laktasyon Düzenle',
      start_date_required_for_lactation: 'Laktasyon için başlangıç tarihi zorunludur.',
      animal_id_missing_for_lactation: 'Laktasyon için Hayvan ID eksik.',
      error_saving_lactation_data: 'Laktasyon verisi kaydedilirken hata oluştu: ',
      lactation_id_missing_error: 'Laktasyon ID eksik. Lütfen bir laktasyon dönemi seçin.',
      miktar_required_error: 'Miktar gereklidir ve sayı olmalıdır.',
      miktar_positive_error: 'Miktar pozitif bir sayı olmalıdır.',
      yag_orani_invalid_error: 'Yağ Oranı (sağlandıysa) geçerli bir sayı olmalıdır.',
      protein_orani_invalid_error: 'Protein Oranı (sağlandıysa) geçerli bir sayı olmalıdır.',
      date_required_error: 'Tarih gereklidir.',
      time_required_error: 'Saat gereklidir.',
      error_saving_milking_data: 'Sağım verisi kaydedilirken hata oluştu: ',
      unknown_animal_name: "Bilinmeyen",
      unknown_ear_tag: "Bilinmeyen Küpe No",
      daily_milk: "Günlük Süt",
      recent_health_activities: "Son Sağlık Aktiviteleri",
      vaccination_record: "Aşı Kaydı",
      treatment_record: "Tedavi Kaydı",
      no_recent_health_activities: "Son sağlık aktivitesi bulunamadı",
      unknown_animal: "Bilinmeyen Hayvan",
      vaccination: "Aşılama",
      treatment: "Tedavi",
      recent_reproduction_activities: "Son Üreme Aktiviteleri",
      insemination_record: "Tohumlama Kaydı",
      pregnancy_record: "Gebelik Kaydı",
      no_recent_reproduction_activities: "Son üreme aktivitesi bulunamadı",
      insemination: "Tohumlama",
      pregnancy: "Gebelik",
      recent_milk_activities: "Son Süt Aktiviteleri",
      lactation_period: "Laktasyon Dönemi",
      completed_lactation: "Tamamlanmış Laktasyon",
      ongoing_lactation: "Devam Eden Laktasyon",
      milking_record: "Sağım Kaydı",
      no_recent_milk_activities: "Son süt aktivitesi bulunamadı",
      label_min: "Min",
      label_max: "Maks",
      date_time_required_error: "Tarih ve saat gereklidir",
      // Choices.js için
      choices_item_select_text: 'Seçmek için Enter\'a basın',
      choices_no_results: 'Sonuç bulunamadı',
      choices_no_choices: 'Seçenek yok',
      select_parent_placeholder: "Ebeveyn seçin veya küpe no yazın...",
      choices_no_choices_can_add: "Seçenek yok, yeni bir tane ekleyebilirsiniz",
      choices_add_item_text: '"<strong>{{value}}</strong>" öğesini eklemek için Enter\'a basın',

      // Hayvan Türleri
      species_cattle: 'Sığır',
      species_sheep: 'Koyun',
      species_goat: 'Keçi',
      species_buffalo: 'Manda',
      species_other: 'Diğer',

      // Hayvan Irkları
      breed_holstein: 'Holstein',
      breed_simmental: 'Simmental',
      breed_jersey: 'Jersey',
      breed_angus: 'Angus',
      breed_native_black: 'Yerli Kara',
      breed_other: 'Diğer',

      // Boynuz Durumu
      horn_status_horned: 'Boynuzlu',
      horn_status_polled: 'Boynuzsuz',
      horn_status_dehorned: 'Kesilmiş',

      // Doğum Tipi
      birth_type_normal: 'Normal',
      birth_type_cesarean: 'Sezaryen',
      birth_type_assisted: 'Yardımlı',

      // Çıkış Sebepleri
      exit_reason_sale: 'Satış',
      exit_reason_death: 'Ölüm',
      exit_reason_transfer: 'Transfer',
      exit_reason_slaughter: 'Kesim',
      exit_reason_other: 'Diğer',
      // Raporlama Sayfası
      label_report_type: 'Rapor Türü',
      report_milk_yield_summary: 'Süt Verimi Özeti',
      report_animal_list: 'Hayvan Listesi Raporu',
      label_start_date: 'Başlangıç Tarihi',
      label_end_date: 'Bitiş Tarihi',
      btn_generate_report: 'Rapor Oluştur',
      report_select_criteria_generate: 'Lütfen rapor kriterlerini seçip "Rapor Oluştur" butonuna tıklayın.',
      btn_export_pdf: 'PDF Olarak Aktar',
      btn_export_excel: 'Excel Olarak Aktar',
      error_date_range_required: 'Süt verimi özeti için tarih aralığı zorunludur.',
      report_generating: 'Rapor oluşturuluyor...',
      label_animal_tag: 'Hayvan Küpe No',
      label_total_milk: 'Toplam Süt (L)',
      label_average_daily_milk: 'Ort. Günlük Süt (L)',
      label_milking_days: 'Sağım Yapılan Gün',
      no_data_for_report: 'Rapor için veri bulunamadı.',
      error_generating_report: 'Rapor oluşturulurken bir hata oluştu:',
      // Muhasebe Raporları için eklenenler
      muhasebe_reports: 'Muhasebe Raporları',
      gelir_gider_tablosu: 'Gelir-Gider Tablosu',
      net_bakiye_raporu: 'Cari Hesap Ekstresi',
      borclu_cariler: 'Borçlu Olduğumuz Cariler (Alacaklılar)', // Bizim borcumuz var
      alacakli_cariler: 'Alacaklı Olduğumuz Cariler (Borçlular)', // Bize borçları var

      // Hesap Ekstresi
      btn_account_statement: 'Hesap Ekstresi',
      account_statement_title: 'Hesap Ekstresi',
      account_statement: 'Hesap Ekstresi',
      label_start_date: 'Başlangıç Tarihi',
      label_end_date: 'Bitiş Tarihi',
      btn_generate: 'Oluştur',
      label_total_debit: 'Toplam Borç',
      label_total_credit: 'Toplam Alacak',
      label_final_balance: 'Son Bakiye',
      label_debit: 'Borç',
      label_credit: 'Alacak',
      label_balance: 'Bakiye',
      label_invoice_no: 'Fatura No',
      label_description: 'Açıklama',
      btn_export_pdf: 'PDF İndir',
      btn_export_excel: 'Excel İndir',
      btn_print: 'Yazdır',
      select_date_range_message: 'Hesap ekstresi oluşturmak için tarih aralığı seçin ve "Oluştur" butonuna tıklayın.',
      please_select_date_range: 'Lütfen tarih aralığı seçin.',
      error_generating_statement: 'Hesap ekstresi oluşturulurken hata oluştu.',
      no_data_to_export: 'Dışa aktarılacak veri bulunamadı.',
      pdf_library_not_loaded: 'PDF kütüphanesi yüklenemedi.',
      excel_library_not_loaded: 'Excel kütüphanesi yüklenemedi.',
      unknown_account: 'Bilinmeyen Cari',
      unknown_type: 'Bilinmeyen Tip',
      date_range: 'Tarih Aralığı',
      label_cari_unvan: 'Cari Ünvan',
      label_total_purchase: 'Toplam Alış (Dönem)',
      label_paid: 'Ödenen (Dönem)', // Alışlara yapılan ödeme
      label_total_sale: 'Toplam Satış (Dönem)',
      label_collected: 'Tahsil Edilen (Dönem)', // Satışlardan alınan
      label_balance: 'Net Bakiye (Genel)',
      // Alışlar & Satışlar Sayfaları için
      table_header_paid_amount: 'Ödenen Tutar', // Alışlar için
      table_header_received_amount: 'Alınan Tutar', // Satışlar için (i18n'de farklı anahtar daha iyi)
      table_header_remaining_amount: 'Kalan Tutar',
      table_header_payment_type: 'Ödeme Şekli', // Eskiden Ödeme Durumu idi
      table_header_transaction_status: 'İşlem Durumu',
      transaction_status_paid: 'Ödendi',
      transaction_status_partially_paid: 'Kısmi Ödendi',
      transaction_status_unpaid: 'Ödenmedi',
      payment_type_pesin: 'Peşin',
      payment_type_vadeli: 'Vadeli',
      label_payment_type: 'Ödeme Şekli', // Modal içinde
      // Ödemeler sayfası için
      option_no_payable_receivable_found: 'Ödenecek/Alınacak Vadesi Gelmiş Fatura Bulunamadı',
      remaining_amount: 'Kalan',
      // Raporlar sayfası (Net Bakiye) için yeni başlıklar
      report_title_receivables_from_cariler: 'Alacaklı Olduğumuz Cariler (Bize Borcu Olanlar)',
      report_subtitle_cariler_owe_us: '{{startDate}} - {{endDate}} arası hareketler ve genel bakiye durumları.',
      label_total_sales_period: 'Dönem Toplam Satış',
      label_total_collections_period: 'Dönem Tahsilat',
      label_net_receivable_overall: 'Net Alacak (Genel)',
      report_title_payables_to_cariler: 'Borçlu Olduğumuz Cariler (Bizden Alacaklı Olanlar)',
      report_subtitle_we_owe_cariler: '{{startDate}} - {{endDate}} arası hareketler ve genel bakiye durumları.',
      label_total_purchases_period: 'Dönem Toplam Alış',
      label_total_payments_period: 'Dönem Ödeme',
      label_net_payable_overall: 'Net Borç (Genel)',
      // Muhasebe genel
      products: "Ürünler",
      current_accounts: "Cari Hesaplar",
      purchases: "Alışlar",
      sales: "Satışlar",
      payments: "Ödemeler",
      accounting_reports: "Raporlar",
      cari_type_tedarikci: 'Tedarikçi',
      cari_type_musteri: 'Müşteri',
      cari_type_diger: 'Diğer',
      transaction_type_satis: 'Satış',
      transaction_type_alis: 'Alış',
      muhasebe: 'Muhasebe',
      muhasebe_urunler: 'Ürünler',
      muhasebe_cariler: 'Cariler',
      muhasebe_alislar: 'Alışlar',
      muhasebe_satislar: 'Satışlar',
      muhasebe_odemeler: 'Ödemeler',
      muhasebe_raporlamalar: 'Raporlamalar',

      // Muhasebe Ürünler Sayfası
      muhasebe_urunler_page_title_add_button: 'Yeni Ürün Ekle',
      urunler_col_id: 'ID',
      urunler_col_ad: 'Ürün Adı',
      urunler_col_kategori: 'Kategori',
      urunler_col_varsayilan_birim: 'Varsayılan Birim',
      urunler_col_notlar: 'Notlar',
      urun_modal_title_add: 'Ürün Ekle',
      urun_modal_title_edit: 'Ürün Düzenle',

      // Muhasebe Cariler Sayfası
      muhasebe_cariler_page_title_add_button: 'Yeni Cari Ekle',
      cariler_col_id: 'ID',
      cariler_col_unvan: 'Unvan',
      cariler_col_tip: 'Tip',
      cariler_col_telefon: 'Telefon',
      cariler_col_email: 'Email',
      cariler_col_adres: 'Adres',
      cariler_col_vergi_no: 'Vergi No',
      cariler_col_vergi_dairesi: 'Vergi Dairesi',
      cariler_col_notlar: 'Notlar',
      cari_modal_title_add: 'Cari Ekle',
      cari_modal_title_edit: 'Cari Düzenle',
      confirm_delete_cari: '{{cariName}} adlı cariyi silmek istediğinizden emin misiniz?',
      unknown_cari_name: 'Bilinmeyen Cari',
      loading_transactions: 'İşlemler yükleniyor...',
      label_total_purchases_long: 'Toplam Alış Tutarı',
      label_total_sales_long: 'Toplam Satış Tutarı',
      label_current_balance: 'Güncel Bakiye',
      label_balance: 'Bakiye',
      label_alacak: 'Alacak',
      label_borc: 'Borç',

      // Muhasebe Alışlar Sayfası
      muhasebe_alislar_page_title_add_button: 'Yeni Alış Ekle',
      alislar_col_id: 'ID',
      alislar_col_cari: 'Cari',
      alislar_col_urun: 'Ürün',
      alislar_col_tarih: 'Tarih',
      alislar_col_fatura_no: 'Fatura No',
      alislar_col_miktar: 'Miktar',
      alislar_col_birim_fiyat: 'Birim Fiyat',
      alislar_col_toplam_tutar: 'Toplam Tutar',
      alislar_col_odenen_tutar: 'Ödenen Tutar',
      alislar_col_kalan_tutar: 'Kalan Tutar',
      alislar_col_odeme_durumu: 'Ödeme Durumu',
      alislar_col_islem_durumu: 'İşlem Durumu',
      alis_modal_title_add: 'Alış Ekle',
      alis_modal_title_edit: 'Alış Düzenle',

      // Muhasebe Satışlar Sayfası
      muhasebe_satislar_page_title_add_button: 'Yeni Satış Ekle',
      satislar_col_id: 'ID',
      satislar_col_cari: 'Cari',
      satislar_col_urun: 'Ürün',
      satislar_col_tarih: 'Tarih',
      satislar_col_fatura_no: 'Fatura No',
      satislar_col_miktar: 'Miktar',
      satislar_col_birim_fiyat: 'Birim Fiyat',
      satislar_col_toplam_tutar: 'Toplam Tutar',
      satislar_col_odenen_tutar: 'Tahsil Edilen',
      satislar_col_kalan_tutar: 'Kalan Alacak',
      satislar_col_odeme_durumu: 'Ödeme Durumu',
      satislar_col_islem_durumu: 'İşlem Durumu',
      satislar_col_vade_tarihi: 'Vade Tarihi',
      satis_modal_title_add: 'Satış Ekle',
      satis_modal_title_edit: 'Satış Düzenle',

      // Muhasebe Ödemeler Sayfası
      muhasebe_odemeler_page_title_add_button: 'Yeni Ödeme Ekle',
      odemeler_col_id: 'ID',
      odemeler_col_islem_tipi: 'İşlem Tipi',
      odemeler_col_fatura_no: 'Fatura No',
      odemeler_col_cari_unvan: 'Cari Unvan',
      odemeler_col_odeme_tarihi: 'Ödeme Tarihi',
      odemeler_col_tutar: 'Tutar',
      odemeler_col_yontem: 'Yöntem',
      odemeler_col_notlar: 'Notlar',
      odeme_modal_title_add: 'Ödeme Ekle',
      odeme_modal_title_edit: 'Ödeme Düzenle',

      // Ödeme ve İşlem Durumları
      payment_type_pesin: 'Peşin',
      payment_type_vadeli: 'Vadeli',
      payment_type_cash: 'Peşin',
      payment_type_credit: 'Vadeli',
      transaction_status_paid: 'Ödendi',
      transaction_status_partially_paid: 'Kısmi Ödendi',
      transaction_status_unpaid: 'Ödenmedi',
      transaction_type_alis: 'Alış Ödemesi',
      transaction_type_satis: 'Satış Tahsilatı',

      // Ödeme Yöntemleri
      payment_method_nakit: 'Nakit',
      payment_method_banka: 'Banka Havalesi',
      payment_method_kart: 'Kredi Kartı',
      payment_method_cek: 'Çek',
      payment_method_senet: 'Senet',
      payment_method_diger: 'Diğer',

      // Cari Detay Modal Tabları
      tab_general_info: 'Genel Bilgiler',
      tab_balance_info: 'Bakiye Bilgileri',
      tab_recent_transactions: 'Son İşlemler',
      tab_health_repro: 'Sağlık & Üreme',
      tab_notes: 'Notlar',
      tab_transactions: 'İşlemler',

      // Modal Bölümleri
      section_contact_info: 'İletişim Bilgileri',
      section_tax_info: 'Vergi Bilgileri',
      section_balance_summary: 'Bakiye Özeti',
      section_recent_transactions: 'Son İşlemler',
      section_basic_info: 'Temel Bilgiler',
      section_farm_management: 'Çiftlik Yönetimi',
      section_health_records: 'Sağlık Kayıtları',
      section_notes: 'Notlar',
      section_physical_characteristics: 'Fiziksel Özellikler',
      section_birth_info: 'Doğum Bilgileri',
      section_parentage: 'Ebeveyn Bilgileri',
      section_management: 'Yönetim Bilgileri',
      section_vaccine_info: 'Aşı Bilgileri',
      section_diagnosis: 'Teşhis Bilgileri',
      section_insemination_details: 'Tohumlama Detayları',
      section_pregnancy_timeline: 'Gebelik Bilgileri',
      section_lactation_period: 'Laktasyon Bilgileri',
      section_milking_session: 'Sağım Bilgileri',
      section_product_info: 'Ürün Bilgileri',
      section_account_info: 'Cari Bilgileri',
      section_transaction_info: 'İşlem Bilgileri',
      btn_cancel: 'İptal',
      home: 'Ana Sayfa',
      label_actual_birth_date: 'Doğum Tarihi',

      // Genel
      option_all: 'Tümü',
      option_select_type: 'Tip Seçiniz...',
      option_select_cari: 'Cari Seçiniz...',
      option_select_product: 'Ürün Seçiniz...',
      unknown: 'Bilinmiyor',
      no_records_found: 'Kayıt bulunamadı',
      loading: 'Yükleniyor...',
      no_recent_transactions: 'Son işlem bulunamadı',

      // Ürün Kategorileri
      product_category_yem: 'Yem',
      product_category_ilac: 'İlaç',
      product_category_ekipman: 'Ekipman',
      product_category_diger: 'Diğer',

      // Ürün Birimleri
      product_unit_kg: 'Kilogram',
      product_unit_lt: 'Litre',
      product_unit_adet: 'Adet',
      product_unit_paket: 'Paket',
      product_unit_doz: 'Doz',
      recent_purchase_activities: 'Son Alış İşlemleri',
      recent_sales_activities: 'Son Satış İşlemleri',
      recent_payment_activities: 'Son Ödeme İşlemleri',
      no_recent_purchase_activities: 'Son alış işlemi bulunamadı',
      no_recent_sales_activities: 'Son satış işlemi bulunamadı',
      no_recent_payment_activities: 'Son ödeme işlemi bulunamadı',
      error_loading_cari_details: 'Cari detayları yüklenirken hata oluştu',
      error_loading_transactions: 'İşlemler yüklenirken hata oluştu', // zaten var: muhasebe_reports
      purchases_page_title: 'Alış İşlemleri',
      add_new_purchase: 'Yeni Alış Ekle',
      table_header_product_name: 'Ürün Adı',
      alis_modal_title_add: 'Yeni Alış İşlemi',
      alis_modal_title_edit: 'Alış İşlemini Düzenle',
      confirm_delete_purchase: '{{purchaseId}} ID\'li alış işlemini silmek istediğinize emin misiniz?',
      sales_page_title: 'Satış İşlemleri',
      add_new_sale: 'Yeni Satış Ekle',
      option_select_customer: 'Müşteri Seçiniz...',
      satis_modal_title_add: 'Yeni Satış İşlemi',
      satis_modal_title_edit: 'Satış İşlemini Düzenle',
      confirm_delete_sale: '{{saleId}} ID\'li satış işlemini silmek istediğinize emin misiniz?',
      payments_page_title: 'Ödeme İşlemleri',
      add_new_payment: 'Yeni Ödeme Ekle',
      table_header_transaction_type: 'İşlem Tipi',
      table_header_related_invoice: 'İlişkili Fatura/İşlem',
      table_header_payment_date: 'Ödeme Tarihi',
      table_header_amount: 'Tutar',
      table_header_method: 'Yöntem',
      odeme_modal_title_add: 'Yeni Ödeme Kaydı',
      odeme_modal_title_edit: 'Ödeme Kaydını Düzenle',
      label_transaction_type: 'İşlem Tipi',
      option_purchase: 'Alış',
      option_sale: 'Satış',
      label_related_transaction: 'İlişkili Alış/Satış',
      option_select_invoice_after_type: 'Önce İşlem Tipi Seçin...',
      option_select_invoice: 'Fatura/İşlem Seçiniz...',
      label_payment_date: 'Ödeme Tarihi',
      label_amount: 'Tutar',
      label_payment_method: 'Ödeme Yöntemi',
      confirm_delete_payment: '{{paymentId}} ID\'li ödeme kaydını silmek istediğinize emin misiniz?',
      invoice_id_placeholder: 'ID: {{id}}',
      error_generic_api: 'API Hatası: {{error}}',
      error_loading_data: 'Veriler yüklenirken hata oluştu.',
      option_paid_in_cash: 'Peşin',
      option_installments: 'Vadeli',
      label_due_date: 'Vade Tarihi',
      no_report_to_export: 'Dışa aktarılacak rapor bulunamadı.',
      pdf_library_not_loaded: 'PDF kütüphanesi yüklenemedi.',
      no_data_to_export: 'Dışa aktarılacak veri bulunamadı.',



      // Toast Notification System
      toast_confirm: 'Onayla',
      toast_cancel: 'İptal',
      toast_close: 'Kapat',
      toast_success_save: 'Kayıt başarıyla tamamlandı',
      toast_success_update: 'Güncelleme başarıyla tamamlandı',
      toast_success_delete: 'Silme işlemi başarıyla tamamlandı',
      toast_success_backup: 'Yedekleme başarıyla tamamlandı',
      toast_success_restore: 'Geri yükleme başarıyla tamamlandı',
      toast_error_save: 'Kayıt sırasında hata oluştu',
      toast_error_update: 'Güncelleme sırasında hata oluştu',
      toast_error_delete: 'Silme sırasında hata oluştu',
      toast_error_load: 'Veriler yüklenirken hata oluştu',
      toast_error_validation: 'Form doğrulama hatası',
      toast_error_network: 'Ağ bağlantısı hatası',
      toast_warning_unsaved: 'Kaydedilmemiş değişiklikler var',
      toast_warning_validation: 'Lütfen gerekli alanları doldurun',
      toast_info_loading: 'Yükleniyor...',
      toast_info_processing: 'İşleniyor...',
      toast_confirm_delete: 'Bu kaydı silmek istediğinizden emin misiniz?',
      toast_confirm_update: 'Bu kaydı güncellemek istediğinizden emin misiniz?',
      toast_confirm_restore: 'Veritabanını geri yüklemek istediğinizden emin misiniz?',

      // Missing translations identified during audit
      table_header_id: 'ID',
      table_header_name: 'Ad',
      table_header_category: 'Kategori',
      table_header_default_unit: 'Varsayılan Birim',
      table_header_actions: 'İşlemler',
      animals_management: 'Hayvan Yönetimi',
      status_active: 'Aktif',
      status_inactive: 'Pasif',
      health_management: 'Sağlık Yönetimi',
      reproduction_management: 'Üreme Yönetimi',
      milk_yield_management: 'Süt Verimi Yönetimi',
      reports_management: 'Raporlama Yönetimi',
      // Recent Activities translations
      recent_activities: 'Son Aktiviteler',
      recent_purchase_activities: 'Son Alış İşlemleri',
      recent_sales_activities: 'Son Satış İşlemleri',
      recent_payment_activities: 'Son Ödeme İşlemleri',
      no_recent_activities: 'Son aktivite bulunmuyor',
      no_recent_purchase_activities: 'Son alış işlemi bulunmuyor',
      no_recent_sales_activities: 'Son satış işlemi bulunmuyor',
      no_recent_payment_activities: 'Son ödeme işlemi bulunmuyor',
      loading_recent_activities: 'Son aktiviteler yükleniyor...',
      days_ago: '{{days}} gün önce',
      view_all: 'Tümünü Gör',
      today: 'Bugün',
      yesterday: 'Dün',
      this_week: 'Bu Hafta',
      last_week: 'Geçen Hafta',
      this_month: 'Bu Ay',
      last_month: 'Geçen Ay',
      add_new_product: 'Yeni Ürün Ekle',
      product_modal_title_add: 'Ürün Ekle',
      product_modal_title_edit: 'Ürün Düzenle',
      label_name: 'Ad',
      label_category: 'Kategori',
      label_default_unit: 'Varsayılan Birim',
      label_notes: 'Notlar',
      confirm_delete_product: '{{productName}} ürününü silmek istediğinizden emin misiniz?',

      // Table Factory translations
      search_ear_tag: 'Küpe no ara...',
      search_name: 'İsim ara...',
      search_breed: 'Irk ara...',
      female: 'Dişi',
      male: 'Erkek',
      birth_date: 'Doğum Tarihi',
      pregnancy_status: 'Gebelik Durumu',
      pregnant: 'Gebe',
      empty: 'Boş',
      status: 'Durum',
      milking: 'Sağmal',
      dry: 'Kuru',
      active: 'Aktif',
      inactive: 'Pasif',
      actions: 'İşlemler',
      edit: 'Düzenle',
      delete: 'Sil',
      no_animals_found: 'Hayvan bulunamadı',

      // Account related
      company_name: 'Şirket Adı',
      type: 'Tip',
      customer: 'Müşteri',
      supplier: 'Tedarikçi',
      phone: 'Telefon',
      email: 'Email',
      address: 'Adres',
      tax_number: 'Vergi No',
      tax_office: 'Vergi Dairesi',
      no_accounts_found: 'Cari bulunamadı',

      // Health related
      vaccine_name: 'Aşı Adı',
      vaccination_date: 'Aşılama Tarihi',
      dose: 'Doz',
      veterinarian: 'Veteriner',
      no_health_records_found: 'Sağlık kaydı bulunamadı',

      // Milk yield related
      date: 'Tarih',
      time: 'Saat',
      amount: 'Miktar',
      fat_percentage: 'Yağ %',
      protein_percentage: 'Protein %',
      no_milk_yield_records_found: 'Süt verimi kaydı bulunamadı',

      // Report related
      no_data_available_for_report: 'Rapor için veri bulunamadı',

      // Dashboard related
      accounting_summary: 'Muhasebe Özeti',
      monthly_profit_loss_chart_title: 'Aylık Net Kar/Zarar (Son 12 Ay)',

      // Health page specific
      label_start_date_short: 'Başlangıç',
      label_end_date_short: 'Bitiş',

      // Reproduction page specific
      pregnancy_result_canli_dogum: 'Canlı Doğum',
      pregnancy_result_olu_dogum: 'Ölü Doğum',
      pregnancy_result_abort: 'Düşük',
      pregnancy_result_miscarriage: 'Düşük',

      // General UI
      page_soon: '{{pageName}} sayfası yakında gelecek',
      label_last_vaccine: 'Son Aşı',
      label_last_treatment: 'Son Tedavi',
      label_last_insemination: 'Son Tohumlama',
      label_last_pregnancy: 'Son Gebelik',
      label_type: 'Tip',
      label_description: 'Açıklama',

      // HTML file missing translations
      app_title: 'Canlı Hayvan Yönetimi',
      app_name: 'SürüYönet',
      herd_management: 'Sürü Yönetimi',
      accounting: 'Muhasebe',
      current_accounts: 'Cariler',
      close: 'Kapat',
      select_option: 'Seçiniz',
      animal_avatar: 'Hayvan Avatarı',
      animal_name: 'Hayvan Adı',
      tab_general_info: 'Genel Bilgiler',
      tab_health_repro: 'Sağlık & Üreme',
      tab_notes: 'Notlar',
      tab_transactions: 'İşlemler',
      account_name: 'Cari Adı',
      label_cari_kodu: 'Cari Kodu',
      label_telefon: 'Telefon',
      label_email: 'E-posta',
      label_adres: 'Adres',
      label_vergi_no: 'Vergi No',
      label_vergi_dairesi: 'Vergi Dairesi',
      balance_status: 'Bakiye Durumu',
      label_total_purchases: 'Toplam Alışlar',
      label_total_sales: 'Toplam Satışlar',
      label_total_payments_made: 'Yapılan Ödemeler',
      label_total_payments_received: 'Alınan Ödemeler',
      recent_transactions: 'Son İşlemler',

      // Vaccine modal missing translations
      vaccine_record: 'Aşı Kaydı',
      vaccine_name_label: 'Aşı Adı',
      date_label: 'Tarih',
      dose_label: 'Doz',
      veterinarian_label: 'Veteriner',
      notes_label: 'Notlar',
      save_button: 'Kaydet',

      // Missing account-related translations
      add_new_account: 'Yeni Cari Ekle',
      account_type_tedarikci: 'Tedarikçi',
      account_type_musteri: 'Müşteri',
      table_header_unvan: 'Ünvan',
      table_header_type: 'Tip',
      table_header_phone: 'Telefon',
      table_header_email: 'Email',
      table_header_tax_no: 'Vergi No',
      table_header_tax_office: 'Vergi Dairesi',
      table_header_address: 'Adres',
      table_header_notes: 'Notlar',
      table_header_balance: 'Bakiye',
      table_header_total_purchases: 'Toplam Alışlar',
      table_header_total_sales: 'Toplam Satışlar',
      table_header_date: 'Tarih',
      table_header_time: 'Saat',
      table_header_quantity: 'Miktar',
      table_header_unit_price: 'Birim Fiyat',
      table_header_total_price: 'Toplam Fiyat',
      table_header_invoice_no: 'Fatura No',
      table_header_description: 'Açıklama',
      account_modal_title_add: 'Yeni Cari Ekle',
      account_modal_title_edit: 'Cari Düzenle',

      // Additional table headers for accounting modules
      table_header_account: 'Cari',
      table_header_product: 'Ürün',
      table_header_invoice_number: 'Fatura No',
      table_header_unit: 'Birim',
      table_header_paid_amount: 'Ödenen Tutar',
      table_header_remaining_amount: 'Kalan Tutar',
      table_header_payment_status: 'Ödeme Durumu',
      table_header_payment_type: 'Ödeme Tipi',
      table_header_transaction_status: 'İşlem Durumu',
      table_header_total_amount: 'Toplam Tutar',
      table_header_collected_amount: 'Tahsil Edilen',
      table_header_remaining_receivable: 'Kalan Alacak',
      table_header_payment_method: 'Ödeme Yöntemi',
      table_header_payment_date: 'Ödeme Tarihi',
      table_header_transaction_type: 'İşlem Tipi',
      table_header_related_invoice: 'İlişkili Fatura',
      table_header_method: 'Yöntem',

      // Accounting page titles
      accounting_accounts_page_title: 'Cari Hesaplar',
      accounting_products_page_title: 'Ürünler',
      accounting_sales_page_title: 'Satışlar',
      accounting_purchases_page_title: 'Alımlar',
      accounting_payments_page_title: 'Ödemeler/Tahsilatlar',
      accounting_reports_page_title: 'Muhasebe Raporları',

      // Reports common
      report_criteria: 'Rapor Kriterleri',
      report_output: 'Rapor Çıktısı',

      // Short table headers for narrow tables
      table_header_id_short: 'ID',
      table_header_account_short: 'Cari',
      table_header_product_short: 'Ürün',
      table_header_date_short: 'Tarih',
      table_header_invoice_no_short: 'Fatura',
      table_header_quantity_short: 'Adet',
      table_header_unit_price_short: 'B.Fiyat',
      table_header_total_amount_short: 'Toplam',
      table_header_paid_amount_short: 'Ödenen',
      table_header_collected_amount_short: 'Tahsil',
      table_header_remaining_amount_short: 'Kalan',
      table_header_remaining_receivable_short: 'K.Alacak',
      table_header_payment_type_short: 'Ö.Tipi',
      table_header_transaction_status_short: 'Durum',
      table_header_actions_short: 'İşlem',
    },
    en: {
      dashboard: 'Dashboard',
      label_all_dates: 'All Dates',
      label_total_expense: 'Total Expense',
      label_net_profit: 'Net Profit',
      debtor_current_accounts_us: 'Debtor Current Accounts (Us)',
      creditor_current_accounts_them: 'Creditor Current Accounts (Them)',
      no_creditor_accounts_found: 'No creditor accounts found.',
      no_debtor_accounts_found: 'No debtor accounts found.',
      amount_try: 'Amount (TRY)',
      total_animals: 'Total Active Animals',
      total_animals_desc: 'Total number of active animals in the system.',
      male_animals: 'Male Animals',
      male_animals_desc: 'Total number of male animals in the system.',
      female_animals: 'Female Animals',
      female_animals_desc: 'Total number of female animals in the system.',
      upcoming_births_30_days: 'Upcoming Births (30 days)',
      upcoming_births_30_days_desc: 'Expected births within the next 30 days.',
      daily_milk_yield: 'Today\'s Milk Yield (L)',
      monthly_milk_yield: 'This Month\'s Milk Yield (L)',
      milking_animals: 'Milking Animals',
      milking_animals_desc: 'Ratio of female animals that are currently milking.',
      ongoing_pregnancies: 'Ongoing Pregnancies',
      ongoing_pregnancies_desc: 'Number of currently pregnant animals.',
      health_alerts_active: 'Active Health Alerts',
      health_alerts_active_desc: 'Number of animals in quarantine or under treatment.',
      daily_milk_yield_avg: 'Daily Avg. Milk Yield',
      daily_milk_yield_avg_desc: 'Daily milk average for all milking animals.',
      welcome_message: 'Welcome!',
      dashboard_subtitle: 'Below is the general status of your herd.',
      animal_distribution_chart_title: 'Animal Distribution',
      milk_production_chart_title: 'Monthly Milk Production (Last 6 Months)',
      quick_actions: 'Quick Actions',
      animals: 'Animals',
      health: 'Health',
      reproduction: 'Reproduction',
      milk: 'Milk Yield',
      reports: 'Reports',
      feed_ration: 'Feed Ration Management',
      settings: 'Settings',
      // Form ve tablo
      label_kupe_no: 'Tag No',
      label_isletme_no: 'Farm No',
      label_isim: 'Name',
      label_tur: 'Species',
      label_irk: 'Breed',
      label_cinsiyet: 'Gender',
      label_dogum_tarihi: 'Birth Date',
      label_dogum_agirligi: 'Birth Weight (kg)',
      label_dogum_tipi: 'Birth Type',
      label_anne_kupe: 'Mother Tag',
      label_baba_kupe: 'Father Tag',
      label_boynuz_durumu: 'Horn Status',
      label_isletmeye_giris: 'Farm Entry Date',
      label_isletmeden_cikis: 'Exit Date',
      label_cikis_sebebi: 'Exit Reason',
      label_fotograf_url: 'Photo URL',
      label_notlar: 'Notes',
      label_aktif_mi: 'Active?',
      label_actions: 'Actions',
      label_age: 'Age',
      // Tedavi modalı ve tablo
      label_teshis: 'Diagnosis',
      label_tedavi_baslangic: 'Start Date',
      label_tedavi_bitis: 'End Date',
      label_kullanilan_ilaclar: 'Medications',
      label_karantina_durumu: 'Quarantine Status',
      label_karantina_baslangic: 'Quarantine Start',
      label_karantina_bitis: 'Quarantine End',
      treatment_modal_title_add: 'Add Treatment',
      treatment_modal_title_edit: 'Edit Treatment',
      option_none: 'None',
      // Uyarılar
      confirm_delete_treatment: 'Are you sure you want to delete this treatment?',
      confirm_delete_animal: 'Are you sure you want to delete this animal and all its related data (vaccinations, treatments, etc.)? This action cannot be undone.',
      // Modal başlıkları
      animal_modal_title_add: 'Add Animal',
      animal_modal_title_edit: 'Edit Animal',
      // Butonlar
      btn_save: 'Save',
      btn_add_animal: '+ Add Animal',
      btn_edit: 'Edit',
      btn_delete: 'Delete',
      btn_add_vaccine: 'Add Vaccine',
      vaccine_modal_title_add: 'Add Vaccine Record',
      vaccine_modal_title_edit: 'Edit Vaccine Record',
      btn_add_milking_record: 'Add Milking Record',
      btn_view_alerts: 'View Alerts',
      confirm_delete_vaccine: 'Are you sure you want to delete this vaccine record?',
      btn_add_treatment: 'Add Treatment',
      // Seçenekler
      option_yes: 'Yes',
      option_no: 'No',
      option_erkek: 'Male',
      option_dişi: 'Female',
      option_all: 'All',
      option_active: 'Active',
      option_inactive: 'Inactive',
      // Tohumlamalar
      health_vaccinations: 'Vaccinations',
      health_treatments: 'Treatments',
      label_tohumlamalar: 'Inseminations',
      label_tohumlama_tarihi: 'Insemination Date',
      label_tohumlama_tipi: 'Insemination Type',
      label_kullanilan_tohum: 'Used Semen',
      tohumlama_modal_title_add: 'Add Insemination',
      tohumlama_modal_title_edit: 'Edit Insemination',
      btn_add_insemination: 'Add Insemination',
      confirm_delete_insemination: 'Are you sure you want to delete this insemination?',
      // Gebelikler
      label_gebelikler: 'Pregnancies',
      btn_add_pregnancy: 'Add Pregnancy',
      label_gebelik_baslangic: 'Start',
      label_gebelik_bitis: 'End',
      label_gebelik_sonuc: 'Result',
      pregnancy_modal_title_add: 'Add Pregnancy',
      pregnancy_modal_title_edit: 'Edit Pregnancy',
      confirm_delete_pregnancy: 'Are you sure you want to delete this pregnancy?',
      label_sperma_kodu: 'Semen Code',
      label_gebelik_baslangic_tarihi: 'Start Date',
      label_beklenen_dogum_tarihi: 'Expected Due Date',
      label_gebelik_sonucu: 'Pregnancy Result',
      option_select: 'Select',
      pregnancy_result_ongoing: 'Ongoing',
      pregnancy_result_devam_ediyor: 'Ongoing',
      pregnancy_result_successful: 'Successful',
      pregnancy_result_failed: 'Failed',
      pregnancy_status_pregnant: 'Pregnant',
      pregnancy_status_empty: 'Empty',
      lactation_status_milking: 'Milking',
      lactation_status_dry: 'Dry',
      // Milk Yield
      label_milk_yield: 'Milk Yield',
      label_laktasyonlar: 'Lactations',
      label_sagim_verileri: 'Milking Data',
      btn_add_lactation: 'Add Lactation',
      btn_add_milking: 'Add Milking Data',
      btn_bulk_milking: 'Bulk Milking Entry',
      bulk_milking_modal_title: 'Bulk Milking Data Entry',
      section_common_milking_info: 'Common Milking Information',
      section_animals_milking_data: 'Animal Milking Data',
      btn_save_all: 'Save All',
      no_lactating_animals: 'No animals with active lactation found.',
      error_loading_animals: 'Error loading animals.',
      please_fill_date_time: 'Please fill in the date and time fields.',
      no_milking_data_entered: 'No milking data entered for any animal.',
      bulk_milking_saved_success: 'Bulk milking data saved successfully.',
      error_saving_bulk_milking: 'Error saving bulk milking data.',
      placeholder_notes: 'Notes...',

      // Business Rule Validation Messages
      validation_male_animal_reproduction: 'Male animals cannot have pregnancy, insemination, lactation, or milking data!',
      validation_lactation_overlap: 'Lactation periods for the same animal cannot overlap!',
      validation_active_pregnancy: 'An animal cannot become pregnant during an existing pregnancy!',
      validation_error_occurred: 'Error occurred during validation check',
      validation_check_failed: 'Validation check could not be performed',
      validation_due_date_after_start: 'Due date must be after start date',
      validation_milk_yield_too_high: 'Daily milk yield seems unusually high',
      validation_field_required: '{{field}} is required',
      validation_invalid_email: 'Please enter a valid email address',
      validation_invalid_number: 'Please enter a valid number',
      validation_min_value: 'Value must be at least {{min}}',
      validation_max_value: 'Value must be at most {{max}}',
      validation_future_date_not_allowed: '{{field}} cannot be in the future (only for past events)',
      validation_date_too_old: '{{field}} seems too old',
      lactation_modal_title_add: 'Add Lactation',
      milking_modal_title_add: 'Add Milking Record',
      milking_modal_title_edit: 'Edit Milking Record',
      confirm_delete_milking: 'Are you sure you want to delete this milking record?',
      label_laktasyon_baslangic: 'Start Date',
      label_laktasyon_bitis: 'End Date',
      label_sagim_tarih: 'Date',
      label_sagim_saat: 'Time',
      label_sagim_miktar: 'Amount (L)',
      label_yag_orani: 'Fat (%)',
      label_protein_orani: 'Protein (%)',
      label_laktasyon_secin: 'Please select a lactation period.',
      please_select_animal_for_lactation: 'Please select an animal to add a lactation.',
      please_select_lactation_for_milking: 'Please select a lactation to add milking data.',
      confirm_delete_lactation: 'Are you sure you want to delete this lactation record?',
      // Settings
      label_settings: 'Settings',
      label_appearance: 'Appearance',
      label_theme: 'Theme',
      label_language: 'Language',
      option_light: 'Light',
      option_dark: 'Dark',
      option_system: 'System',
      language_tr: 'Türkçe',
      language_en: 'English',
      label_database: 'Database',
      db_backup_recommendation: 'It is recommended to back up your database regularly to prevent data loss.',
      btn_backup_db: 'Backup Database',
      btn_restore_db: 'Restore from Backup',
      alert_backup_success: 'Database backed up successfully to: ',
      alert_error: 'Error: ',
      confirm_restore_db: 'This action will replace your current database with the selected backup. The application will restart. Do you want to continue?',
      label_about: 'About',
      label_app_version: 'Application Version',
      // Genel
      search_placeholder_animals: 'Search: Tag No, Name...',
      no_records_found: 'No records found.',
      please_select_animal: 'Please select an animal.',
      label_vaccine_name: 'Vaccine Name',
      label_date: 'Date',
      label_dose: 'Dose',
      label_vet: 'Veterinarian',
      year_unit: 'year',
      month_unit: 'month',
      day_unit: 'day',
      label_gebelik_durumu: 'Pregnancy Status',
      label_durum: 'Status',
      feature_not_implemented_yet: 'This feature is not implemented yet.',
      error_loading_details: 'Error loading details.',
      no_notes_available: 'No notes available.',
      error_modal_function_not_available: 'Cannot open modal because the related function is not available.',

      // Vaccination completion
      complete_vaccination: 'Complete Vaccination',
      vaccination_completed_successfully: 'Vaccination completed successfully',
      schedule_not_found: 'Schedule record not found',
      animal_not_found: 'Animal not found',
      planned_date: 'Planned Date',
      animal: 'Animal',

      // Vaccination Management
      vaccination_management: 'Vaccination Management',
      vaccination_templates: 'Vaccination Templates',
      btn_add_template: 'Add Template',
      template_name: 'Template Name',
      animal_type: 'Animal Type',
      interval: 'Interval',
      option_active: 'Active',
      option_inactive: 'Inactive',
      quick_actions: 'Quick Actions',
      bulk_vaccination: 'Bulk Vaccination',
      bulk_vaccination_desc: 'Vaccinate multiple animals at once',
      generate_schedule: 'Generate Schedule',
      generate_schedule_desc: 'Create vaccination schedule for animals',
      view_reminders: 'View Reminders',
      view_reminders_desc: 'View upcoming and overdue vaccinations',
      confirm_delete_template: 'Are you sure you want to delete this vaccination template?',
      page: 'Page',
      records: 'records',

      // Vaccination Reminders
      vaccination_reminders: 'Vaccination Reminders',
      overdue_vaccinations: 'Overdue Vaccinations',
      upcoming_vaccinations: 'Upcoming Vaccinations',
      no_overdue_vaccinations: 'No overdue vaccinations found',
      no_upcoming_vaccinations: 'No upcoming vaccinations found',
      planned_date: 'Planned Date',
      days_overdue: 'days overdue',
      mark_completed: 'Mark Completed',
      due_today: 'Due today',
      due_soon: 'Due soon',
      btn_close: 'Close',
      btn_refresh: 'Refresh',

      // Bulk Vaccination
      vaccination_information: 'Vaccination Information',
      option_other: 'Other',
      enter_vaccine_name: 'Enter vaccine name',
      label_vaccination_date: 'Vaccination Date',
      enter_dose: 'Enter dose',
      enter_veterinarian: 'Enter veterinarian name',
      enter_notes: 'Enter notes',
      select_animals: 'Select Animals',
      select_all: 'Select All',
      deselect_all: 'Deselect All',
      selected: 'Selected',
      no_active_animals_found: 'No active animals found',
      please_select_animals: 'Please select at least one animal',
      btn_save_vaccinations: 'Save Vaccinations',
      bulk_vaccination_success: 'Vaccination records successfully added for {count} animals',

      // Vaccination Schedule
      vaccination_schedule: 'Vaccination Schedule',
      upcoming_30_days: 'Upcoming (30 days)',
      completed_this_month: 'Completed This Month',
      animal: 'Animal',
      completion_date: 'Completion Date',
      status_pending: 'Pending',
      status_completed: 'Completed',
      status_overdue: 'Overdue',
      status_cancelled: 'Cancelled',
      confirm_delete_schedule: 'Are you sure you want to delete this vaccination schedule record?',

      // Template Modal
      edit_template: 'Edit Template',
      add_template: 'Add Template',
      template_information: 'Template Information',
      enter_template_name: 'Enter template name',
      interval_value: 'Interval Value',
      interval_type: 'Interval Type',
      enter_interval: 'Enter interval value',
      days: 'Days',
      months: 'Months',
      years: 'Years',
      first_vaccination_age: 'First Vaccination Age',
      days_after_birth: 'Days after birth',
      repeat_count: 'Repeat Count',
      unlimited_if_negative: '-1 for unlimited',
      description: 'Description',
      enter_description: 'Enter description',

      // Generate Schedule Modal
      select_animal: 'Select Animal',
      select_templates: 'Select Templates',
      schedule_settings: 'Schedule Settings',
      start_date: 'Start Date',
      btn_generate: 'Generate',
      no_active_templates_found: 'No active templates found',
      please_select_templates: 'Please select at least one template',
      schedule_generated_success: '{count} schedule records successfully generated',

      // Quick Filter
      filter_by_animal: 'Filter by Animal',
      all_animals: 'All Animals',
      single_animal: 'Single Animal',
      select_animals: 'Select Animals',
      manage_templates: 'Manage Templates',
      templates: 'Templates',
      error_loading_stats: 'Error loading statistics.',
      chart_library_not_loaded: 'Chart library failed to load. Charts cannot be displayed.',
      error_loading_dashboard_data: 'Error loading dashboard data.',
      other_genders: 'Other',
      error_loading_accounting_stats: 'Error loading accounting statistics',
      see_all: 'See All',
      navigation_function_missing_error: 'Navigation function not found',
      net_profit_loss: 'Net Profit/Loss',
      milk_production_liters: 'Milk Production (L)',
      no_data: 'No Data',
      liters: 'Liters',
      months: 'Months',
      // Pagination
      btn_previous: 'Previous',
      label_report_type: 'Report Type',
      gelir_gider_tablosu: 'Income-Expense Statement',
      net_bakiye_raporu: 'Net Balance Report',
      label_start_date: 'Start Date',
      label_end_date: 'End Date',
      btn_generate_report: 'Generate Report',
      report_select_criteria_generate: 'Please select report criteria and click "Generate Report" button.',
      btn_export_pdf: 'Export as PDF',
      btn_export_excel: 'Export as Excel',
      error_date_range_required: 'Date range is required.',
      report_generating: 'Generating report...', 
      label_total_income: 'Total Income',
      report_title_receivables_from_cariler: 'Receivables from Current Accounts',
      report_subtitle_cariler_owe_us: 'Current Accounts Owe Us (Period: {{startDate}} - {{endDate}})',
      label_cari_unvan: 'Account Title',
      label_total_sales_period: 'Period Total Sales',
      label_total_collections_period: 'Period Collections',
      label_net_receivable_overall: 'Net Receivable (Overall)',
      no_data_for_report: 'No data found for the report.',
      report_title_payables_to_cariler: 'Payables to Current Accounts',
      report_subtitle_we_owe_cariler: 'We Owe Current Accounts (Period: {{startDate}} - {{endDate}})',
      label_total_purchases_period: 'Period Total Purchases',
      label_total_payments_period: 'Period Payments',
      label_net_payable_overall: 'Net Payable (Overall)',
      error_generating_report: 'An error occurred while generating the report: {{error}}',
      muhasebe_reports: 'Accounting Reports',
      pdf_library_not_loaded: 'PDF library could not be loaded.',
      btn_next: 'Next',
      page_info_text: 'Page {{currentPage}} of {{totalPages}} ({{totalItems}} items)',
      // Milk Yield Page Alerts
      lactation_modal_title_edit: 'Edit Lactation',
      start_date_required_for_lactation: 'Start date is required for lactation.',
      animal_id_missing_for_lactation: 'Animal ID is missing for lactation.',
      error_saving_lactation_data: 'Error saving lactation data: ',
      lactation_id_missing_error: 'Lactation ID is missing. Please select a lactation period.',
      miktar_required_error: 'Amount is required and must be a number.',
      miktar_positive_error: 'Amount must be a positive number.',
      yag_orani_invalid_error: 'Fat Content must be a valid number if provided.',
      protein_orani_invalid_error: 'Protein Content must be a valid number if provided.',
      date_required_error: 'Date is required.',
      time_required_error: 'Time is required.',
      error_saving_milking_data: 'Error saving milking data: ',
      unknown_animal_name: "Unknown",
      unknown_ear_tag: "Unknown Tag No",
      daily_milk: "Daily Milk",
      recent_health_activities: "Recent Health Activities",
      vaccination_record: "Vaccination Record",
      treatment_record: "Treatment Record",
      no_recent_health_activities: "No recent health activities found",
      unknown_animal: "Unknown Animal",
      vaccination: "Vaccination",
      treatment: "Treatment",
      recent_reproduction_activities: "Recent Reproduction Activities",
      insemination_record: "Insemination Record",
      pregnancy_record: "Pregnancy Record",
      no_recent_reproduction_activities: "No recent reproduction activities found",
      insemination: "Insemination",
      pregnancy: "Pregnancy",
      recent_milk_activities: "Recent Milk Activities",
      lactation_period: "Lactation Period",
      completed_lactation: "Completed Lactation",
      ongoing_lactation: "Ongoing Lactation",
      milking_record: "Milking Record",
      no_recent_milk_activities: "No recent milk activities found",
      label_min: "Min",
      label_max: "Max",
      date_time_required_error: "Date and time are required",
      // Choices.js için
      choices_item_select_text: 'Press Enter to select',
      choices_no_results: 'No results found',
      choices_no_choices: 'No choices',
      select_parent_placeholder: "Select parent or type tag number...",
      choices_no_choices_can_add: "No choices, you can add a new one",
      choices_add_item_text: 'Press Enter to add "<strong>{{value}}</strong>"',

      // Animal Species
      species_cattle: 'Cattle',
      species_sheep: 'Sheep',
      species_goat: 'Goat',
      species_buffalo: 'Buffalo',
      species_other: 'Other',

      // Animal Breeds
      breed_holstein: 'Holstein',
      breed_simmental: 'Simmental',
      breed_jersey: 'Jersey',
      breed_angus: 'Angus',
      breed_native_black: 'Native Black',
      breed_other: 'Other',

      // Horn Status
      horn_status_horned: 'Horned',
      horn_status_polled: 'Polled',
      horn_status_dehorned: 'Dehorned',

      // Birth Type
      birth_type_normal: 'Normal',
      birth_type_cesarean: 'Cesarean',
      birth_type_assisted: 'Assisted',

      // Exit Reasons
      exit_reason_sale: 'Sale',
      exit_reason_death: 'Death',
      exit_reason_transfer: 'Transfer',
      exit_reason_slaughter: 'Slaughter',
      exit_reason_other: 'Other',
      // Reports Page
      label_report_type: 'Report Type',
      report_milk_yield_summary: 'Milk Yield Summary',
      report_animal_list: 'Animal List Report',
      label_start_date: 'Start Date',
      label_end_date: 'End Date',
      btn_generate_report: 'Generate Report',
      report_select_criteria_generate: 'Please select report criteria and click "Generate Report".',
      btn_export_pdf: 'Export as PDF',
      btn_export_excel: 'Export as Excel',
      error_date_range_required: 'Date range is required for milk yield summary.',
      report_generating: 'Generating report...',
      label_animal_tag: 'Animal Tag No',
      label_total_milk: 'Total Milk (L)',
      label_average_daily_milk: 'Avg. Daily Milk (L)',
      label_milking_days: 'Milking Days',
      no_data_for_report: 'No data found for the report.',
      error_generating_report: 'An error occurred while generating the report:',
      // Accounting Reports additions
      muhasebe_reports: 'Accounting Reports',
      gelir_gider_tablosu: 'Income-Expense Statement',
      net_bakiye_raporu: 'Account Statement',
      borclu_cariler: 'Payables to Accounts (Creditors)', // We owe them
      alacakli_cariler: 'Receivables from Accounts (Debtors)', // They owe us

      // Account Statement
      btn_account_statement: 'Account Statement',
      account_statement_title: 'Account Statement',
      account_statement: 'Account Statement',
      label_start_date: 'Start Date',
      label_end_date: 'End Date',
      btn_generate: 'Generate',
      label_total_debit: 'Total Debit',
      label_total_credit: 'Total Credit',
      label_final_balance: 'Final Balance',
      label_debit: 'Debit',
      label_credit: 'Credit',
      label_balance: 'Balance',
      label_invoice_no: 'Invoice No',
      label_description: 'Description',
      btn_export_pdf: 'Export PDF',
      btn_export_excel: 'Export Excel',
      btn_print: 'Print',
      select_date_range_message: 'Select a date range and click "Generate" to create the account statement.',
      please_select_date_range: 'Please select a date range.',
      error_generating_statement: 'Error occurred while generating account statement.',
      no_data_to_export: 'No data found to export.',
      pdf_library_not_loaded: 'PDF library could not be loaded.',
      excel_library_not_loaded: 'Excel library could not be loaded.',
      unknown_account: 'Unknown Account',
      unknown_type: 'Unknown Type',
      date_range: 'Date Range',

      // Muhasebe Ürünler Sayfası
      muhasebe_urunler_page_title_add_button: 'Add New Product',
      urunler_col_id: 'ID',
      urunler_col_ad: 'Product Name',
      urunler_col_kategori: 'Category',
      urunler_col_varsayilan_birim: 'Default Unit',
      urunler_col_notlar: 'Notes',
      urun_modal_title_add: 'Add Product',
      urun_modal_title_edit: 'Edit Product',

      // Muhasebe Cariler Sayfası
      muhasebe_cariler_page_title_add_button: 'Add New Account',
      cariler_col_id: 'ID',
      cariler_col_unvan: 'Title',
      cariler_col_tip: 'Type',
      cariler_col_telefon: 'Phone',
      cariler_col_email: 'Email',
      cariler_col_adres: 'Address',
      cariler_col_vergi_no: 'Tax No',
      cariler_col_vergi_dairesi: 'Tax Office',
      cariler_col_notlar: 'Notes',
      cari_modal_title_add: 'Add Account',
      cari_modal_title_edit: 'Edit Account',
      confirm_delete_cari: 'Are you sure you want to delete the account {{cariName}}?',
      unknown_cari_name: 'Unknown Account',
      loading_transactions: 'Loading transactions...',
      label_total_purchases_long: 'Total Purchase Amount',
      label_total_sales_long: 'Total Sales Amount',
      label_current_balance: 'Current Balance',
      label_balance: 'Balance',
      label_alacak: 'Receivable',
      label_borc: 'Payable',

      // Muhasebe Alışlar Sayfası
      muhasebe_alislar_page_title_add_button: 'Add New Purchase',
      alislar_col_id: 'ID',
      alislar_col_cari: 'Account',
      alislar_col_urun: 'Product',
      alislar_col_tarih: 'Date',
      alislar_col_fatura_no: 'Invoice No',
      alislar_col_miktar: 'Quantity',
      alislar_col_birim_fiyat: 'Unit Price',
      alislar_col_toplam_tutar: 'Total Amount',
      alislar_col_odenen_tutar: 'Paid Amount',
      alislar_col_kalan_tutar: 'Remaining Amount',
      alislar_col_odeme_durumu: 'Payment Status',
      alislar_col_islem_durumu: 'Transaction Status',
      alis_modal_title_add: 'Add Purchase',
      alis_modal_title_edit: 'Edit Purchase',

      // Muhasebe Satışlar Sayfası
      muhasebe_satislar_page_title_add_button: 'Add New Sale',
      satislar_col_id: 'ID',
      satislar_col_cari: 'Account',
      satislar_col_urun: 'Product',
      satislar_col_tarih: 'Date',
      satislar_col_fatura_no: 'Invoice No',
      satislar_col_miktar: 'Quantity',
      satislar_col_birim_fiyat: 'Unit Price',
      satislar_col_toplam_tutar: 'Total Amount',
      satislar_col_odenen_tutar: 'Collected Amount',
      satislar_col_kalan_tutar: 'Remaining Receivable',
      satislar_col_odeme_durumu: 'Payment Status',
      satislar_col_islem_durumu: 'Transaction Status',
      satislar_col_vade_tarihi: 'Due Date',
      satis_modal_title_add: 'Add Sale',
      satis_modal_title_edit: 'Edit Sale',

      // Muhasebe Ödemeler Sayfası
      muhasebe_odemeler_page_title_add_button: 'Add New Payment',
      odemeler_col_id: 'ID',
      odemeler_col_islem_tipi: 'Transaction Type',
      odemeler_col_fatura_no: 'Invoice No',
      odemeler_col_cari_unvan: 'Account Title',
      odemeler_col_odeme_tarihi: 'Payment Date',
      odemeler_col_tutar: 'Amount',
      odemeler_col_yontem: 'Method',
      odemeler_col_notlar: 'Notes',
      odeme_modal_title_add: 'Add Payment',
      odeme_modal_title_edit: 'Edit Payment',

      // Ödeme ve İşlem Durumları
      payment_type_pesin: 'Cash',
      payment_type_vadeli: 'Credit',
      payment_type_cash: 'Cash',
      payment_type_credit: 'Credit',
      transaction_status_paid: 'Paid',
      transaction_status_partially_paid: 'Partially Paid',
      transaction_status_unpaid: 'Unpaid',
      transaction_type_alis: 'Purchase Payment',
      transaction_type_satis: 'Sales Collection',

      // Ödeme Yöntemleri
      payment_method_nakit: 'Cash',
      payment_method_banka: 'Bank Transfer',
      payment_method_kart: 'Credit Card',
      payment_method_cek: 'Check',
      payment_method_senet: 'Promissory Note',
      payment_method_diger: 'Other',

      // Cari Detay Modal Tabları
      tab_general_info: 'General Information',
      tab_balance_info: 'Balance Information',
      tab_recent_transactions: 'Recent Transactions',
      tab_health_repro: 'Health & Reproduction',
      tab_notes: 'Notes',
      tab_transactions: 'Transactions',

      // Modal Sections
      section_contact_info: 'Contact Information',
      section_tax_info: 'Tax Information',
      section_balance_summary: 'Balance Summary',
      section_recent_transactions: 'Recent Transactions',
      section_basic_info: 'Basic Information',
      section_farm_management: 'Farm Management',
      section_health_records: 'Health Records',
      section_notes: 'Notes',
      section_physical_characteristics: 'Physical Characteristics',
      section_birth_info: 'Birth Information',
      section_parentage: 'Parentage Information',
      section_management: 'Management Information',
      section_vaccine_info: 'Vaccine Information',
      section_diagnosis: 'Diagnosis Information',
      section_purchase_info: 'Purchase Information',
      section_sale_info: 'Sale Information',
      section_payment_info: 'Payment Information',
      section_insemination_details: 'Insemination Details',
      section_pregnancy_timeline: 'Pregnancy Information',
      section_lactation_period: 'Lactation Information',
      section_milking_session: 'Milking Information',
      section_product_info: 'Product Information',
      section_account_info: 'Account Information',
      section_transaction_info: 'Transaction Information',
      btn_cancel: 'Cancel',
      home: 'Home',
      label_actual_birth_date: 'Birth Date',

      // Genel
      option_all: 'All',
      option_select_type: 'Select Type...',
      option_select_cari: 'Select Account...',
      option_select_product: 'Select Product...',
      unknown: 'Unknown',
      no_records_found: 'No records found',
      loading: 'Loading...',
      no_recent_transactions: 'No recent transactions found',

      // Product Categories
      product_category_yem: 'Feed',
      product_category_ilac: 'Medicine',
      product_category_ekipman: 'Equipment',
      product_category_diger: 'Other',

      // Product Units
      product_unit_kg: 'Kilogram',
      product_unit_lt: 'Liter',
      product_unit_adet: 'Piece',
      product_unit_paket: 'Package',
      product_unit_doz: 'Dose',
      recent_purchase_activities: 'Recent Purchase Activities',
      recent_sales_activities: 'Recent Sales Activities',
      recent_payment_activities: 'Recent Payment Activities',
      no_recent_purchase_activities: 'No recent purchase activities found',
      no_recent_sales_activities: 'No recent sales activities found',
      no_recent_payment_activities: 'No recent payment activities found',
      error_loading_cari_details: 'Error loading account details',
      error_loading_transactions: 'Error loading transactions',
      label_cari_unvan: 'Account Title',
      label_total_purchase: 'Total Purchase (Period)',
      label_paid: 'Paid (Period)', // Payments for purchases
      label_total_sale: 'Total Sale (Period)',
      label_collected: 'Collected (Period)', // Collections from sales
      label_balance: 'Net Balance (Overall)',
      // Purchases & Sales Pages
      table_header_paid_amount: 'Amount Paid', // For Purchases
      table_header_received_amount: 'Amount Received', // For Sales
      table_header_remaining_amount: 'Remaining Amount',
      table_header_payment_type: 'Payment Type', // Formerly Payment Status
      table_header_transaction_status: 'Transaction Status',
      transaction_status_paid: 'Paid',
      transaction_status_partially_paid: 'Partially Paid',
      transaction_status_unpaid: 'Unpaid',
      payment_type_pesin: 'Cash',
      payment_type_vadeli: 'Installment',
      label_payment_type: 'Payment Type', // In Modal
      // Payments page
      option_no_payable_receivable_found: 'No Due Invoices Found for Payment/Collection',
      remaining_amount: 'Remaining',
      // Reports page (Net Balance) new titles
      report_title_receivables_from_cariler: 'Receivables from Accounts (Debtors to Us)',
      report_subtitle_cariler_owe_us: 'Transactions between {{startDate}} - {{endDate}} and overall balance.',
      label_total_sales_period: 'Total Sales (Period)',
      label_total_collections_period: 'Total Collections (Period)',
      label_net_receivable_overall: 'Net Receivable (Overall)',
      report_title_payables_to_cariler: 'Payables to Accounts (Creditors to Us)',
      report_subtitle_we_owe_cariler: 'Transactions between {{startDate}} - {{endDate}} and overall balance.',
      label_total_purchases_period: 'Total Purchases (Period)',
      label_total_payments_period: 'Total Payments (Period)',
      label_net_payable_overall: 'Net Payable (Overall)',
      // Accounting general
      current_accounts: "Current Accounts",
      purchases: "Purchases",
      sales: "Sales",
      payments: "Payments",
      accounting_reports: "Reports",
      cari_type_tedarikci: 'Supplier',
      cari_type_musteri: 'Customer',
      cari_type_diger: 'Other',
      products: "Products",
      transaction_type_satis: 'Sale',
      transaction_type_alis: 'Purchase',
      muhasebe: 'Accounting',
      muhasebe_urunler: 'Products',
      muhasebe_cariler: 'Accounts',
      muhasebe_alislar: 'Purchases',
      muhasebe_satislar: 'Sales',
      muhasebe_odemeler: 'Payments',
      muhasebe_raporlamalar: 'Reports',
      purchases_page_title: 'Purchase Transactions',
      add_new_purchase: 'Add New Purchase',
      table_header_product_name: 'Product Name',
      alis_modal_title_add: 'New Purchase Transaction',
      alis_modal_title_edit: 'Edit Purchase Transaction',
      confirm_delete_purchase: 'Are you sure you want to delete purchase transaction ID {{purchaseId}}?',
      sales_page_title: 'Sales Transactions',
      add_new_sale: 'Add New Sale',
      option_select_customer: 'Select Customer...',
      satis_modal_title_add: 'New Sale Transaction',
      satis_modal_title_edit: 'Edit Sale Transaction',
      confirm_delete_sale: 'Are you sure you want to delete sale transaction ID {{saleId}}?',
      payments_page_title: 'Payment Transactions',
      add_new_payment: 'Add New Payment',
      table_header_transaction_type: 'Transaction Type',
      table_header_related_invoice: 'Related Invoice/Transaction',
      table_header_payment_date: 'Payment Date',
      table_header_amount: 'Amount',
      table_header_method: 'Method',
      odeme_modal_title_add: 'New Payment Record',
      odeme_modal_title_edit: 'Edit Payment Record',
      label_transaction_type: 'Transaction Type',
      option_purchase: 'Purchase',
      option_sale: 'Sale',
      label_related_transaction: 'Related Purchase/Sale',
      option_select_invoice_after_type: 'Select Transaction Type First...',
      option_select_invoice: 'Select Invoice/Transaction...',
      label_payment_date: 'Payment Date',
      label_amount: 'Amount',
      label_payment_method: 'Payment Method',
      confirm_delete_payment: 'Are you sure you want to delete payment record ID {{paymentId}}?',
      invoice_id_placeholder: 'ID: {{id}}',
      error_generic_api: 'API Error: {{error}}',
      error_loading_data: 'Error loading data.',
      option_paid_in_cash: 'Cash',
      option_installments: 'Installment',
      label_due_date: 'Due Date',
      no_report_to_export: 'No report found to export.',
      pdf_library_not_loaded: 'PDF library could not be loaded.',
      no_data_to_export: 'No data found to export.',

      // Missing translations identified during audit
      table_header_id: 'ID',
      table_header_name: 'Name',
      table_header_category: 'Category',
      table_header_default_unit: 'Default Unit',
      table_header_actions: 'Actions',
      add_new_product: 'Add New Product',
      product_modal_title_add: 'Add Product',
      product_modal_title_edit: 'Edit Product',
      label_name: 'Name',
      label_category: 'Category',
      label_default_unit: 'Default Unit',
      label_notes: 'Notes',
      confirm_delete_product: 'Are you sure you want to delete product {{productName}}?',

      // Table Factory translations
      search_ear_tag: 'Search ear tag...',
      search_name: 'Search name...',
      search_breed: 'Search breed...',
      female: 'Female',
      male: 'Male',
      birth_date: 'Birth Date',
      pregnancy_status: 'Pregnancy Status',
      pregnant: 'Pregnant',
      empty: 'Empty',
      status: 'Status',
      milking: 'Milking',
      dry: 'Dry',
      active: 'Active',
      inactive: 'Inactive',
      actions: 'Actions',
      edit: 'Edit',
      delete: 'Delete',
      no_animals_found: 'No animals found',

      // Account related
      company_name: 'Company Name',
      type: 'Type',
      customer: 'Customer',
      supplier: 'Supplier',
      phone: 'Phone',
      email: 'Email',
      address: 'Address',
      tax_number: 'Tax Number',
      tax_office: 'Tax Office',
      no_accounts_found: 'No accounts found',

      // Health related
      vaccine_name: 'Vaccine Name',
      vaccination_date: 'Vaccination Date',
      dose: 'Dose',
      veterinarian: 'Veterinarian',
      no_health_records_found: 'No health records found',

      // Milk yield related
      date: 'Date',
      time: 'Time',
      amount: 'Amount',
      fat_percentage: 'Fat %',
      protein_percentage: 'Protein %',
      no_milk_yield_records_found: 'No milk yield records found',

      // Report related
      no_data_available_for_report: 'No data available for this report',

      // Dashboard related
      accounting_summary: 'Accounting Summary',
      monthly_profit_loss_chart_title: 'Monthly Net Profit/Loss (Last 12 Months)',

      // Health page specific
      label_start_date_short: 'Start',
      label_end_date_short: 'End',

      // Reproduction page specific
      pregnancy_result_canli_dogum: 'Live Birth',
      pregnancy_result_olu_dogum: 'Stillbirth',
      pregnancy_result_abort: 'Abortion',
      pregnancy_result_miscarriage: 'Miscarriage',

      // General UI
      page_soon: '{{pageName}} page coming soon',
      label_last_vaccine: 'Last Vaccine',
      label_last_treatment: 'Last Treatment',
      label_last_insemination: 'Last Insemination',
      label_last_pregnancy: 'Last Pregnancy',
      label_type: 'Type',
      label_description: 'Description',

      // HTML file missing translations
      app_title: 'Livestock Management',
      app_name: 'LivestockManager',
      herd_management: 'Herd Management',
      accounting: 'Accounting',
      current_accounts: 'Current Accounts',
      close: 'Close',
      select_option: 'Select',
      animal_avatar: 'Animal Avatar',
      animal_name: 'Animal Name',
      tab_general_info: 'General Information',
      tab_health_repro: 'Health & Reproduction',
      tab_notes: 'Notes',
      tab_transactions: 'Transactions',
      account_name: 'Account Name',
      label_cari_kodu: 'Account Code',
      label_telefon: 'Phone',
      label_email: 'Email',
      label_adres: 'Address',
      label_vergi_no: 'Tax Number',
      label_vergi_dairesi: 'Tax Office',
      balance_status: 'Balance Status',
      label_total_purchases: 'Total Purchases',
      label_total_sales: 'Total Sales',
      label_total_payments_made: 'Payments Made',
      label_total_payments_received: 'Payments Received',
      recent_transactions: 'Recent Transactions',

      // Vaccine modal missing translations
      vaccine_record: 'Vaccine Record',
      vaccine_name_label: 'Vaccine Name',
      date_label: 'Date',
      dose_label: 'Dose',
      veterinarian_label: 'Veterinarian',
      notes_label: 'Notes',
      save_button: 'Save',

      // Missing account-related translations
      add_new_account: 'Add New Account',
      account_type_tedarikci: 'Supplier',
      account_type_musteri: 'Customer',
      table_header_unvan: 'Title',
      table_header_type: 'Type',
      table_header_phone: 'Phone',
      table_header_email: 'Email',
      table_header_tax_no: 'Tax No',
      table_header_tax_office: 'Tax Office',
      table_header_address: 'Address',
      table_header_notes: 'Notes',
      table_header_balance: 'Balance',
      table_header_total_purchases: 'Total Purchases',
      table_header_total_sales: 'Total Sales',
      table_header_date: 'Date',
      table_header_time: 'Time',
      table_header_quantity: 'Quantity',
      table_header_unit_price: 'Unit Price',
      table_header_total_price: 'Total Price',
      table_header_invoice_no: 'Invoice No',
      table_header_description: 'Description',
      account_modal_title_add: 'Add New Account',
      account_modal_title_edit: 'Edit Account',

      // Additional table headers for accounting modules
      table_header_account: 'Account',
      table_header_product: 'Product',
      table_header_invoice_number: 'Invoice No',
      table_header_unit: 'Unit',
      table_header_paid_amount: 'Paid Amount',
      table_header_remaining_amount: 'Remaining Amount',
      table_header_payment_status: 'Payment Status',
      table_header_payment_type: 'Payment Type',
      table_header_transaction_status: 'Transaction Status',
      table_header_total_amount: 'Total Amount',
      table_header_collected_amount: 'Collected Amount',
      table_header_remaining_receivable: 'Remaining Receivable',
      table_header_payment_method: 'Payment Method',
      table_header_payment_date: 'Payment Date',
      table_header_transaction_type: 'Transaction Type',
      table_header_related_invoice: 'Related Invoice',
      table_header_method: 'Method',


      estimated_cost: 'Estimated Cost',
      use_template: 'Use Template',
      confirm_delete_template: 'Are you sure you want to delete this template?',
      template_deleted_successfully: 'Template deleted successfully',
      error_deleting_template: 'Error deleting template',
      template_has_no_ingredients: 'Template has no ingredients',
      template_applied_successfully: 'Template applied successfully',
      error_using_template: 'Error using template',

      // Toast Notification System
      toast_confirm: 'Confirm',
      toast_cancel: 'Cancel',
      toast_close: 'Close',
      toast_success_save: 'Record saved successfully',
      toast_success_update: 'Update completed successfully',
      toast_success_delete: 'Delete operation completed successfully',
      toast_success_backup: 'Backup completed successfully',
      toast_success_restore: 'Restore completed successfully',
      toast_error_save: 'Error occurred while saving',
      toast_error_update: 'Error occurred while updating',
      toast_error_delete: 'Error occurred while deleting',
      toast_error_load: 'Error occurred while loading data',
      toast_error_validation: 'Form validation error',
      toast_error_network: 'Network connection error',
      toast_warning_unsaved: 'There are unsaved changes',
      toast_warning_validation: 'Please fill in the required fields',
      toast_info_loading: 'Loading...',
      toast_info_processing: 'Processing...',
      toast_confirm_delete: 'Are you sure you want to delete this record?',
      toast_confirm_update: 'Are you sure you want to update this record?',
      toast_confirm_restore: 'Are you sure you want to restore the database?',

      // Accounting page titles
      accounting_accounts_page_title: 'Current Accounts',
      accounting_products_page_title: 'Products',
      accounting_sales_page_title: 'Sales',
      accounting_purchases_page_title: 'Purchases',
      accounting_payments_page_title: 'Payments/Collections',
      accounting_reports_page_title: 'Accounting Reports',

      // Reports common
      report_criteria: 'Report Criteria',
      report_output: 'Report Output',

      // Short table headers for narrow tables
      table_header_id_short: 'ID',
      table_header_account_short: 'Account',
      table_header_product_short: 'Product',
      table_header_date_short: 'Date',
      table_header_invoice_no_short: 'Invoice',
      table_header_quantity_short: 'Qty',
      table_header_unit_price_short: 'U.Price',
      table_header_total_amount_short: 'Total',
      table_header_paid_amount_short: 'Paid',
      table_header_collected_amount_short: 'Collected',
      table_header_remaining_amount_short: 'Remaining',
      table_header_remaining_receivable_short: 'R.Receivable',
      table_header_payment_type_short: 'P.Type',
      table_header_transaction_status_short: 'Status',
      table_header_actions_short: 'Actions',
    }
  };
  let currentLang = localStorage.getItem('livestock_lang') || 'tr';

  function setLanguage(lang) {
    if (translations[lang]) {
      currentLang = lang;
      localStorage.setItem('livestock_lang', lang);
      // Optionally, you could dispatch an event here if components need to react instantly
      // document.dispatchEvent(new CustomEvent('languageChanged'));
    }
  }
  const getLanguage = () => currentLang;

  function t(key, params = null) {
    let translation = translations[currentLang]?.[key] || key;
    if (params && typeof translation === 'string') {
      Object.keys(params).forEach(paramKey => {
        translation = translation.replace(new RegExp(`{{${paramKey}}}`, 'g'), params[paramKey]);
      });
    }
    return translation;
  }
  return { setLanguage, getLanguage, t };
})();
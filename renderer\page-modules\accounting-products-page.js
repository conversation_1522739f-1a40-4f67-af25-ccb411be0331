// Standardized Accounting Products Page Module
// Following the new standardization guidelines

import { IconSystem } from '../utils/icon-system.js';
import { BadgeSystem } from '../utils/badge-system.js';
import { AccountingHeader } from '../components/accounting-header.js';
import { TableAnimations } from '../utils/table-animations.js';

// Standardized globals for this module
let passedI18n, passedContentArea, passedDefaultAvatarSVG;

let allProducts = [];
let currentProducts = [];
const itemsPerPage = 10;
let currentPage = 1;
let sortColumn = 'Ad'; // Default sort column
let sortDirection = 1; // Default sort direction (asc for name)

// DOM element references
let productsTableBody;
let productModal;
let productForm;
let productModalTitle;
let currentEditingProductId = null;
let pageInfo;
let prevButton;
let nextButton;

function sortProductsList(list, column, direction) {
  return [...list].sort((a, b) => {
    let valA = a[column];
    let valB = b[column];

    if (valA === null || valA === undefined) valA = '';
    if (valB === null || valB === undefined) valB = '';

    if (column === 'Id') {
      valA = Number(valA);
      valB = Number(valB);
      return (valA - valB) * direction;
    }
    if (typeof valA === 'string' && typeof valB === 'string') {
      return valA.localeCompare(valB, undefined, { sensitivity: 'base' }) * direction;
    }
    if (valA < valB) return -1 * direction;
    if (valA > valB) return 1 * direction;
    return 0;
  });
}

export async function renderAccountingProductsPage(contentArea, i18n, defaultAvatarSVG) {
  // Store passed parameters using standardized naming
  passedI18n = i18n;
  passedContentArea = contentArea;
  passedDefaultAvatarSVG = defaultAvatarSVG;
  const t = passedI18n.t;

  // Initialize accounting header immediately to prevent jumping
  const accountingHeader = new AccountingHeader({
    containerId: 'accounting-header',
    pageTitle: t('accounting_products_page_title') || 'Ürünler',
    pageIcon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/>
      <line x1="3" y1="6" x2="21" y2="6"/>
      <path d="M16 10a4 4 0 0 1-8 0"/>
    </svg>`
  });

  contentArea.innerHTML = `
    <div id="accounting-header"></div>
    <div id="products-controls">
      <button id="add-product-button" class="btn btn-primary">
        <svg viewBox='0 0 20 20' fill='none' style="width:20px; height:20px; margin-right: 8px;"><circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/><path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/></svg>
        ${t('add_new_product') || 'Add New Product'}
      </button><br></br>
    </div>
    <table id="products-table" class="modern-table">
      <thead>
        <tr>
          <th data-column-key="Id" class="sortable">${t('table_header_id') || 'ID'}</th>
          <th data-column-key="Ad" class="sortable">${t('table_header_name') || 'Product Name'}</th>
          <th data-column-key="Kategori" class="sortable">${t('table_header_category') || 'Category'}</th>
          <th data-column-key="VarsayilanBirim" class="sortable">${t('table_header_default_unit') || 'Default Unit'}</th>
          <th class="col-actions">${t('table_header_actions') || 'Actions'}</th>
        </tr>
        <tr class="filter-row">
          <th><input type="number" id="filter-products-id" class="filter-input" placeholder="${t('table_header_id')}"></th>
          <th><input type="text" id="filter-products-name" class="filter-input" placeholder="${t('table_header_name')}"></th>
          <th><input type="text" id="filter-products-category" class="filter-input" placeholder="${t('table_header_category')}"></th>
          <th><input type="text" id="filter-products-unit" class="filter-input" placeholder="${t('table_header_default_unit')}"></th>
          <th></th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>
    <div id="products-pagination-controls" class="pagination-controls">
      <button id="prev-page-products" disabled>&laquo; ${t('btn_previous') || 'Previous'}</button>
      <span id="page-info-products">Page 1 of 1</span>
      <button id="next-page-products" disabled>&raquo; ${t('btn_next') || 'Next'}</button>
    </div>

    <div id="product-modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="product-modal-title">${t('product_modal_title_add') || 'Add New Product'}</h2>
        </div>
        <form id="product-form">
          <div class="modal-body">
          <!-- Basic Product Information Section -->
          <div class="form-section">
            <h3 class="section-title">${t('section_basic_info') || 'Temel Bilgiler'}</h3>
            <div class="form-grid">
              <div class="form-row">
                <label for="product-name">${t('label_name') || 'Name'}</label>
                <input type="text" name="Ad" id="product-name" required>
              </div>
              <div class="form-row">
                <label for="product-category">${t('label_category') || 'Category'}</label>
                <select name="Kategori" id="product-category">
                  <option value="">${t('option_select') || 'Seçiniz'}</option>
                  <option value="Süt Ürünleri">${t('category_dairy') || 'Süt Ürünleri'}</option>
                  <option value="Et Ürünleri">${t('category_meat') || 'Et Ürünleri'}</option>
                  <option value="Yem">${t('category_feed') || 'Yem'}</option>
                  <option value="İlaç">${t('category_medicine') || 'İlaç'}</option>
                  <option value="Ekipman">${t('category_equipment') || 'Ekipman'}</option>
                  <option value="Diğer">${t('category_other') || 'Diğer'}</option>
                </select>
              </div>
              <div class="form-row">
                <label for="product-default-unit">${t('label_default_unit') || 'Default Unit'}</label>
                <select name="VarsayilanBirim" id="product-default-unit">
                  <option value="">${t('option_select') || 'Seçiniz'}</option>
                  <option value="kg">${t('unit_kg') || 'kg'}</option>
                  <option value="L">${t('unit_liter') || 'L'}</option>
                  <option value="adet">${t('unit_piece') || 'adet'}</option>
                  <option value="ton">${t('unit_ton') || 'ton'}</option>
                  <option value="m">${t('unit_meter') || 'm'}</option>
                  <option value="m²">${t('unit_square_meter') || 'm²'}</option>
                  <option value="paket">${t('unit_package') || 'paket'}</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Additional Information Section -->
          <div class="form-section">
            <h3 class="section-title">${t('section_additional_info') || 'Ek Bilgiler'}</h3>
            <div class="form-row full">
              <label for="product-notes">${t('label_notes') || 'Notes'}</label>
              <textarea name="Notlar" id="product-notes" rows="3"></textarea>
            </div>
          </div>

          <input type="hidden" name="Id">
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('product-modal').classList.add('hidden')">${t('btn_cancel') || 'İptal'}</button>
              <button type="submit" class="btn btn-primary">${t('btn_save') || 'Save'}</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  `;

  // Initialize accounting header immediately after HTML is created
  accountingHeader.init();

  productsTableBody = contentArea.querySelector('#products-table tbody');
  productModal = contentArea.querySelector('#product-modal');
  productForm = contentArea.querySelector('#product-form');
  productModalTitle = contentArea.querySelector('#product-modal-title');

  pageInfo = contentArea.querySelector('#page-info-products');
  prevButton = contentArea.querySelector('#prev-page-products');
  nextButton = contentArea.querySelector('#next-page-products');

  const addProductButton = contentArea.querySelector('#add-product-button');
  addProductButton.addEventListener('click', () => openProductModal(null));

  const closeBtn = productModal.querySelector('.close-btn');
  closeBtn.addEventListener('click', () => productModal.classList.add('hidden'));
  productModal.addEventListener('click', (e) => {
    if (e.target === productModal) productModal.classList.add('hidden');
  });

  productForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    const formData = new FormData(productForm);
    const productData = Object.fromEntries(formData.entries());

    try {
      if (currentEditingProductId) {
        await window.api.invoke('products:update', { id: currentEditingProductId, data: productData });
        window.toast.success(t('toast_success_update'));
      } else {
        await window.api.invoke('products:add', productData);
        window.toast.success(t('toast_success_save'));
      }
      productModal.classList.add('hidden');
      allProducts = await window.api.invoke('products:list');
      applyFiltersAndSortProducts();
    } catch (error) {
      console.error('Product save/update error:', error);
      window.toast.error(t('error_generic_api', { error: error.message || 'Unknown error' }));
    }
  });

  allProducts = await window.api.invoke('products:list');
  currentProducts = [...allProducts];
  applyFiltersAndSortProducts();

  const filterInputs = [
    '#filter-products-id', '#filter-products-name', '#filter-products-category', '#filter-products-unit'
  ];
  filterInputs.forEach(selector => {
    const inputElement = contentArea.querySelector(selector);
    if (inputElement) {
      inputElement.addEventListener('input', applyFiltersAndSortProducts);
    }
  });

  prevButton.addEventListener('click', () => {
    if (currentPage > 1) {
      currentPage--;
      renderProductsTablePage();
      updateProductsPaginationControls();
    }
  });
  nextButton.addEventListener('click', () => {
    const totalPages = Math.ceil(currentProducts.length / itemsPerPage);
    if (currentPage < totalPages) {
      currentPage++;
      renderProductsTablePage();
      updateProductsPaginationControls();
    }
  });

  // Event listeners for sorting
  const ths = contentArea.querySelectorAll('#products-table thead th[data-column-key]');

  ths.forEach(th => {
    const columnKey = th.dataset.columnKey;
    if (!columnKey) return;

    th.classList.add('sortable');
    th.addEventListener('click', () => {
      if (sortColumn === columnKey) {
        sortDirection *= -1;
      } else {
        sortColumn = columnKey;
        sortDirection = 1;
      }

      // Clear all sorting classes
      ths.forEach(headerTh => {
        headerTh.classList.remove('sorted-asc', 'sorted-desc');
      });

      // Add sorting class to current header
      th.classList.add(sortDirection === 1 ? 'sorted-asc' : 'sorted-desc');

      applyFiltersAndSortProducts();
    });
  });



  // Ensure modals are properly structured
  setTimeout(() => {
    if (window.reinitializeModals) {
      window.reinitializeModals();
    }
  }, 100);
}

function applyFiltersAndSortProducts() {
  let filtered = [...allProducts];

  const idFilter = document.getElementById('filter-products-id')?.value;
  const nameFilter = document.getElementById('filter-products-name')?.value.trim().toLowerCase();
  const categoryFilter = document.getElementById('filter-products-category')?.value.trim().toLowerCase();
  const unitFilter = document.getElementById('filter-products-unit')?.value.trim().toLowerCase();

  if (idFilter) filtered = filtered.filter(p => p.Id == idFilter);
  if (nameFilter) filtered = filtered.filter(p => (p.Ad || '').toLowerCase().includes(nameFilter));
  if (categoryFilter) filtered = filtered.filter(p => (p.Kategori || '').toLowerCase().includes(categoryFilter));
  if (unitFilter) filtered = filtered.filter(p => (p.VarsayilanBirim || '').toLowerCase().includes(unitFilter));

  if (sortColumn) {
    filtered = sortProductsList(filtered, sortColumn, sortDirection);
  }

  currentProducts = filtered;
  currentPage = 1;
  renderProductsTablePage();
  updateProductsPaginationControls();
}

function renderProductsTablePage() {
  const t = passedI18n.t;
  productsTableBody.innerHTML = '';

  const totalPages = Math.ceil(currentProducts.length / itemsPerPage);
  currentPage = Math.max(1, Math.min(currentPage, totalPages || 1));

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const pageItems = currentProducts.slice(startIndex, endIndex);

  if (pageItems.length === 0) {
    productsTableBody.innerHTML = `<tr><td colspan="5"><div class="empty-state-container"><div class="empty-state-icon">${passedDefaultAvatarSVG ? passedDefaultAvatarSVG(48) : ''}</div><p>${t('no_records_found')}</p></div></td></tr>`;
    return;
  }

  pageItems.forEach(product => {
    const row = productsTableBody.insertRow();
    row.innerHTML = `
      <td class="cell-products-id">${product.Id}</td>
      <td class="cell-products-name">${product.Ad || ''}</td>
      <td class="cell-products-category">${product.Kategori || ''}</td>
      <td class="cell-products-unit">${product.VarsayilanBirim || ''}</td>
      <td class="col-actions actions">
        ${IconSystem.createActionsWrapper(product.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
      </td>
    `;
    row.querySelector('.edit-btn').addEventListener('click', () => openProductModal(product));
    row.querySelector('.delete-btn').addEventListener('click', () => deleteProduct(product.Id, product.Ad));
  });
  
  // Initialize table animations
  setTimeout(() => {
    TableAnimations.initializeTable('#products-table');
  }, 100);
}

function updateProductsPaginationControls() {
  const t = passedI18n.t;
  const totalItems = currentProducts.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
  pageInfo.textContent = t('page_info_text', { currentPage, totalPages, totalItems }) || `Page ${currentPage} of ${totalPages} (${totalItems} items)`;
  prevButton.disabled = currentPage === 1;
  nextButton.disabled = currentPage === totalPages;
}

function openProductModal(productData = null) {
  const t = passedI18n.t;
  productForm.reset();
  if (productData) {
    currentEditingProductId = productData.Id;
    productModalTitle.textContent = t('product_modal_title_edit') || 'Edit Product';
    productForm.elements['Id'].value = productData.Id;
    productForm.elements['Ad'].value = productData.Ad || '';
    productForm.elements['Kategori'].value = productData.Kategori || '';
    productForm.elements['VarsayilanBirim'].value = productData.VarsayilanBirim || '';
    productForm.elements['Notlar'].value = productData.Notlar || '';
  } else {
    currentEditingProductId = null;
    productModalTitle.textContent = t('product_modal_title_add') || 'Add New Product';
    productForm.elements['Id'].value = '';
  }
  productModal.classList.remove('hidden');
}

async function deleteProduct(id, productName) {
  const t = passedI18n.t;
  const confirmed = await window.toast.confirm(t('confirm_delete_product', { productName: productName || id }) || `Are you sure you want to delete ${productName || id}?`, {
    confirmText: t('toast_confirm'),
    cancelText: t('toast_cancel')
  });

  if (confirmed) {
    try {
      await window.api.invoke('products:delete', id);
      allProducts = await window.api.invoke('products:list');
      applyFiltersAndSortProducts();
      window.toast.success(t('toast_success_delete'));
    } catch (error) {
      console.error('Product delete error:', error);
      window.toast.error(t('error_generic_api', { error: error.message || 'Unknown error' }));
    }
  }
}

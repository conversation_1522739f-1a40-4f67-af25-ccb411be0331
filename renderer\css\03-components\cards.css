/* =================================
   Modern Card System
   ================================= */

/* =================================
   Base Card
   ================================= */
.card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

/* =================================
   Stat Cards
   ================================= */
.stat-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

/* Legacy support for dashboard cards */
.dashboard-cards .stat-card {
  flex-direction: column;
  text-align: center;
  padding: var(--space-4);
}

.dashboard-cards .stat-card .card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.dashboard-cards .stat-card .card-header .icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-50);
  color: var(--primary-600);
}

.dashboard-cards .stat-card .card-header .value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
}

.dashboard-cards .stat-card .info .title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin: 0;
}

.dashboard-cards .stat-card .info .description {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin: var(--space-1) 0 0 0;
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-card .icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-card .icon svg {
  width: 24px;
  height: 24px;
}

.stat-card .content {
  flex: 1;
  min-width: 0;
}

.stat-card .title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
}

.stat-card .value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: var(--leading-tight);
  margin-bottom: var(--space-1);
}

.stat-card .subtitle {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

/* Stat Card Variants */
.stat-card.total .icon { 
  background: var(--primary-50); 
  color: var(--primary-600); 
}

.stat-card.active .icon { 
  background: var(--success-50); 
  color: var(--success-600); 
}

.stat-card.pregnant .icon { 
  background: var(--error-50); 
  color: var(--error-600); 
}

.stat-card.milking .icon { 
  background: var(--info-50); 
  color: var(--info-600); 
}

/* =================================
   Action Cards
   ================================= */
.action-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  color: var(--text-primary);
  min-width: 200px;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--interactive-primary);
  color: var(--text-primary);
  text-decoration: none;
}

.action-card .icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  background: var(--primary-50);
  color: var(--interactive-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.action-card .icon svg {
  width: 20px;
  height: 20px;
}

.action-card .content h3 {
  margin: 0 0 var(--space-1) 0;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.action-card .content p {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* =================================
   Recent Activity Cards
   ================================= */
.activity-card,
.recent-activity-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.activity-card:hover,
.recent-activity-card:hover {
  background: var(--primary-50);
  border-color: var(--interactive-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.activity-main {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  flex: 1;
}

.activity-info {
  flex: 1;
}

.activity-title {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-1);
  line-height: var(--leading-tight);
}

.activity-subtitle {
  color: var(--text-secondary);
  font-size: var(--text-xs);
  line-height: var(--leading-tight);
}

.activity-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-1);
}

.activity-date {
  color: var(--text-secondary);
  font-size: var(--text-xs);
  white-space: nowrap;
}

.activity-amount {
  color: var(--text-primary);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.activity-badge {
  margin-left: var(--space-3);
}

/* =================================
   Recent Activities Section
   ================================= */
.recent-activities-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-top: var(--space-6);
}

.recent-activities-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
}

.recent-activities-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.recent-activities-icon {
  width: 16px;
  height: 16px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

.recent-activities-view-all {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  background: transparent;
  border: none;
  color: var(--text-link);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.recent-activities-view-all:hover {
  background: var(--primary-50);
  color: var(--text-link-hover);
}

.recent-activities-view-all svg {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.recent-activities-content {
  padding: var(--space-6);
}

.recent-activities-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

/* Loading and Empty States */
.recent-activities-loading,
.recent-activities-empty,
.recent-activities-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-4);
  text-align: center;
  color: var(--text-secondary);
}

.recent-activities-loading .loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-primary);
  border-top: 2px solid var(--interactive-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-3);
}

.recent-activities-empty .empty-icon,
.recent-activities-error .error-icon {
  width: 32px;
  height: 32px;
  color: var(--text-secondary);
  opacity: 0.6;
  margin-bottom: var(--space-2);
}

.recent-activities-loading span,
.recent-activities-empty p,
.recent-activities-error p {
  font-size: var(--text-sm);
  margin: 0;
  color: var(--text-secondary);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-lg);
  background: var(--primary-50);
  color: var(--interactive-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon svg {
  width: 16px;
  height: 16px;
}

.activity-details {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-subtitle {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-1);
  flex-shrink: 0;
}

.activity-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  white-space: nowrap;
}

/* =================================
   Chart Containers
   ================================= */
.chart-container {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.chart-container h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.chart-container canvas {
  max-height: 300px;
  width: 100%;
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  .stat-card {
    padding: var(--space-4);
    gap: var(--space-3);
  }

  .stat-card .icon {
    width: 40px;
    height: 40px;
  }

  .stat-card .icon svg {
    width: 20px;
    height: 20px;
  }

  .stat-card .value {
    font-size: var(--text-xl);
  }

  .action-card {
    min-width: auto;
    padding: var(--space-3);
  }

  .action-card .icon {
    width: 36px;
    height: 36px;
  }

  .action-card .icon svg {
    width: 18px;
    height: 18px;
  }

  .chart-container {
    padding: var(--space-4);
  }

  .activity-card {
    padding: var(--space-2) var(--space-3);
  }

  .activity-main {
    gap: var(--space-3);
  }

  .activity-icon {
    width: 28px;
    height: 28px;
  }

  .activity-icon svg {
    width: 14px;
    height: 14px;
  }
}

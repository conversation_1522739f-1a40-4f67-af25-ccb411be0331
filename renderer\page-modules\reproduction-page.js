// reproduction-page.js

import { IconSystem } from '../utils/icon-system.js';
import { BadgeSystem } from '../utils/badge-system.js';
import { RecentActivities, formatActivityDate, truncateText } from '../components/recent-activities.js';
import { TableAnimations } from '../utils/table-animations.js';

// Globals for this module
let passedI18nRepro;
let passedDefaultAvatarSVGRepro;

// State for Inseminations Tab
let allInseminationsForAnimal = [];
let currentInseminationsForAnimal = [];
const itemsPerPageInseminations = 5; // Example, can be adjusted
let currentPageInseminations = 1;
let sortColumnInseminations = 'TohumlamaTarihi'; // Default
let sortDirectionInseminations = -1; // Default: newest first

// State for Pregnancies Tab
let allPregnanciesForAnimal = [];
let currentPregnanciesForAnimal = [];
const itemsPerPagePregnancies = 5; // Example
let currentPagePregnancies = 1;
let sortColumnPregnancies = 'BaslangicTarihi'; // Default
let sortDirectionPregnancies = -1; // Default: newest first

// Common elements
let animalSelectElRepro;
let reproContentEl;
let reproRecentActivities;

// Modals (assuming global) - these will be initialized
let inseminationModal, inseminationForm, inseminationModalTitle, editingInseminationId;
let pregnancyModal, pregnancyForm, pregnancyModalTitle, editingPregnancyId;


export async function renderReproductionPage(contentArea, i18n, defaultAvatarSVG) {
  passedI18nRepro = i18n;
  passedDefaultAvatarSVGRepro = defaultAvatarSVG;
  const t = passedI18nRepro.t;

  contentArea.innerHTML = `
    <!-- Reproduction Header Section -->
    <div class="reproduction-header-section">
      <div class="reproduction-header-content">
        <h2 class="reproduction-page-title">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
          </svg>
          ${t('reproduction_management')}
        </h2>
      </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="reproduction-navigation-section">
      <div id="repro-tabs" class="reproduction-nav-tabs">
        <button class="repro-tab active" data-tab="tohumlamalar">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
            <circle cx="12" cy="12" r="3"/>
          </svg>
          <span>${t('label_tohumlamalar')}</span>
        </button>
        <button class="repro-tab" data-tab="gebelikler">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
            <line x1="9" y1="9" x2="9.01" y2="9"/>
            <line x1="15" y1="9" x2="15.01" y2="9"/>
          </svg>
          <span>${t('label_gebelikler')}</span>
        </button>
      </div>

      <!-- Animal Selection Bar -->
      <div class="animal-selection-bar">
        <div class="animal-selection-bar-content">
          <div class="animal-selection-info">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M19 7.5C19 11.09 16.09 14 12.5 14S6 11.09 6 7.5 8.91 1 12.5 1s6.5 2.91 6.5 6.5z"/>
              <path d="M12.5 14v7"/>
              <path d="M8 21h9"/>
            </svg>
            <span>${t('select_animal')}</span>
          </div>
          <select id="repro-animal-select" data-placeholder="${t('please_select_animal')}">
            <option value="">${t('please_select_animal')}</option>
          </select>
        </div>
      </div>
    </div>

    <div id="repro-content"><div class='empty-message'>${t('please_select_animal')}</div></div>

    <div id="repro-recent-activities"></div>
  `;

  animalSelectElRepro = contentArea.querySelector('#repro-animal-select');
  reproContentEl = contentArea.querySelector('#repro-content');
  const tabs = contentArea.querySelectorAll('.repro-tab');

  const animals = await window.api.invoke('hayvanlar:list');
  animalSelectElRepro.innerHTML = `<option value="" selected disabled>${t('please_select_animal')}</option>`;
  animals.forEach(a => {
    const option = document.createElement('option');
    option.value = a.Id;
    option.textContent = `${a.KupeNo} ${a.Isim ? ' - ' + a.Isim : ''}`;
    animalSelectElRepro.appendChild(option);
  });

  tabs.forEach(tab => {
    tab.addEventListener('click', async () => {
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');
      // Reset sort column when changing tabs
      if (tab.getAttribute('data-tab') === 'tohumlamalar') {
          sortColumnInseminations = 'TohumlamaTarihi'; sortDirectionInseminations = -1;
      } else {
          sortColumnPregnancies = 'BaslangicTarihi'; sortDirectionPregnancies = -1;
      }
      await renderActiveReproTab();
    });
  });

  animalSelectElRepro.addEventListener('change', async () => {
    await renderActiveReproTab();
  });

  // Initialize Recent Activities
  reproRecentActivities = new RecentActivities({
    containerId: 'repro-recent-activities',
    title: t('recent_reproduction_activities') || 'Recent Reproduction Activities',
    maxItems: 5,

    onViewAll: () => {
      // Scroll to main table
      reproContentEl.scrollIntoView({ behavior: 'smooth' });
    },
    getRecords: async () => {
      try {
        // Get all animals first to map names
        const animals = await window.api.invoke('hayvanlar:list');
        const animalMap = {};
        animals.forEach(animal => {
          animalMap[animal.Id] = animal.KupeNo || animal.Isim || `#${animal.Id}`;
        });

        // Get all recent inseminations and pregnancies (not filtered by animal)
        const [allInseminations, allPregnancies] = await Promise.all([
          window.api.invoke('tohumlamalar:listAll') || [],
          window.api.invoke('gebelikler:listAll') || []
        ]);

        // Combine and sort by date
        const allRecords = [
          ...allInseminations.map(i => ({ ...i, type: 'insemination', animalName: animalMap[i.HayvanId] })),
          ...allPregnancies.map(p => ({ ...p, type: 'pregnancy', animalName: animalMap[p.HayvanId] }))
        ];

        return allRecords.sort((a, b) => {
          const dateA = new Date(a.TohumlamaTarihi || a.BaslangicTarihi);
          const dateB = new Date(b.TohumlamaTarihi || b.BaslangicTarihi);
          return dateB - dateA;
        });
      } catch (error) {
        console.error('Error fetching reproduction records:', error);
        return [];
      }
    },
    formatRecord: (record) => {
      const isInsemination = record.type === 'insemination';
      const date = record.TohumlamaTarihi || record.BaslangicTarihi;

      return {
        title: isInsemination ?
          (record.TohumlamaTipi || t('insemination')) :
          (record.GebelikSonucu || t('pregnancy')),
        subtitle: `${record.animalName || t('unknown_animal') || 'Bilinmeyen Hayvan'} - ${isInsemination ?
          t('insemination_record') :
          t('pregnancy_record')}`,
        date: formatActivityDate(date),
        badge: isInsemination ?
          BadgeSystem.createBadge(t('insemination') || 'Insemination', BadgeSystem.TYPES.INFO) :
          BadgeSystem.createBadge(t('pregnancy') || 'Pregnancy', BadgeSystem.TYPES.PREGNANT)
      };
    },
    emptyMessage: t('no_recent_reproduction_activities') || 'No recent reproduction activities'
  });

  initializeInseminationModalRepro();
  initializePregnancyModalRepro();

  // Initialize recent activities
  reproRecentActivities.init();

  renderActiveReproTab(); // Initial call
}

// --- MODAL HANDLING ---
function initializeInseminationModalRepro() {
    inseminationModal = document.getElementById('insemination-modal');
    inseminationForm = document.getElementById('insemination-form');
    inseminationModalTitle = document.getElementById('insemination-modal-title');
    if(!inseminationModal || !inseminationForm || !inseminationModalTitle) {
        console.warn("Insemination modal elements not found. CRUD for inseminations will not work.");
        return;
    }
    inseminationModal.querySelector('.close-btn').onclick = closeInseminationModalRepro;
    inseminationModal.onclick = e => { if (e.target === inseminationModal) closeInseminationModalRepro(); };
    inseminationForm.onsubmit = submitInseminationFormRepro;
    if (!inseminationForm.elements['HayvanId']) {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'HayvanId';
        inseminationForm.appendChild(hiddenInput);
     }
     if (!inseminationForm.elements['Id']) {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'Id';
        inseminationForm.appendChild(hiddenInput);
     }

     // Setup sperma kodu dropdown change handler
     const spermaKoduSelect = document.getElementById('sperma-kodu-select');
     const spermaKoduOther = document.getElementById('sperma-kodu-other');

     if (spermaKoduSelect && spermaKoduOther) {
         spermaKoduSelect.addEventListener('change', function() {
             if (this.value === 'Diğer') {
                 spermaKoduOther.style.display = 'block';
                 spermaKoduOther.required = true;
             } else {
                 spermaKoduOther.style.display = 'none';
                 spermaKoduOther.required = false;
                 spermaKoduOther.value = '';
             }
         });
     }
}
function openInseminationModalRepro(insem = null) {
    const t = passedI18nRepro.t;
    inseminationModal.classList.remove('hidden');
    inseminationForm.reset();
    editingInseminationId = insem ? insem.Id : null;
    inseminationForm.elements['HayvanId'].value = animalSelectElRepro.value;

    // Reset sperma kodu dropdown and other input
    const spermaKoduSelect = document.getElementById('sperma-kodu-select');
    const spermaKoduOther = document.getElementById('sperma-kodu-other');

    if (spermaKoduOther) {
        spermaKoduOther.style.display = 'none';
        spermaKoduOther.required = false;
        spermaKoduOther.value = '';
    }

    if (insem) {
        inseminationModalTitle.textContent = t('tohumlama_modal_title_edit');

        // Handle sperma kodu - check if it's a predefined option
        const predefinedCodes = ['HOL001', 'HOL002', 'SIM001', 'ANG001'];
        if (insem.SpermaKodu && predefinedCodes.includes(insem.SpermaKodu)) {
            spermaKoduSelect.value = insem.SpermaKodu;
        } else if (insem.SpermaKodu) {
            // Custom sperma kodu
            spermaKoduSelect.value = 'Diğer';
            spermaKoduOther.style.display = 'block';
            spermaKoduOther.required = true;
            spermaKoduOther.value = insem.SpermaKodu;
        }

        // Set other fields
        Object.keys(insem).forEach(key => {
            if (inseminationForm.elements[key] && key !== 'SpermaKodu') {
                inseminationForm.elements[key].value = insem[key] ?? '';
            }
        });

        inseminationForm.elements['Id'].value = insem.Id;
    } else {
        inseminationModalTitle.textContent = t('tohumlama_modal_title_add');
        inseminationForm.elements['Id'].value = '';
    }
}
function closeInseminationModalRepro() { inseminationModal.classList.add('hidden'); editingInseminationId = null; }
async function submitInseminationFormRepro(e) {
    e.preventDefault();
    const t = passedI18nRepro.t;

    // Validate form before submission
    const isValid = await validateModalForm('insemination-form');
    if (!isValid) {
        return; // Stop submission if validation fails
    }

    const formData = Object.fromEntries(new FormData(inseminationForm).entries());
    if (!formData.HayvanId) formData.HayvanId = animalSelectElRepro.value;
    formData.HayvanId = parseInt(formData.HayvanId);

    // Handle "Diğer" sperma kodu selection
    if (formData.SpermaKodu === 'Diğer' && formData.SpermaKoduDiger) {
        formData.SpermaKodu = formData.SpermaKoduDiger;
    }
    // Remove the temporary field
    delete formData.SpermaKoduDiger;

    try {
        if (editingInseminationId) {
            formData.Id = parseInt(editingInseminationId);
            await window.api.invoke('tohumlamalar:update', formData);
            window.toast.success(t('toast_success_update'));
        } else {
            delete formData.Id;
            await window.api.invoke('tohumlamalar:add', formData);
            window.toast.success(t('toast_success_save'));
        }
        closeInseminationModalRepro();
        await loadAndRenderActiveReproData();
        // Refresh recent activities
        if (reproRecentActivities) {
            reproRecentActivities.refresh();
        }
    } catch (error) {
        console.error('Error saving insemination data:', error);
        window.toast.error(t('toast_error_save') + ': ' + error.message);
    }
}

function initializePregnancyModalRepro() {
    pregnancyModal = document.getElementById('pregnancy-modal');
    pregnancyForm = document.getElementById('pregnancy-form');
    pregnancyModalTitle = document.getElementById('pregnancy-modal-title');
    if(!pregnancyModal || !pregnancyForm || !pregnancyModalTitle) {
        console.warn("Pregnancy modal elements not found. CRUD for pregnancies will not work.");
        return;
    }
    pregnancyModal.querySelector('.close-btn').onclick = closePregnancyModalRepro;
    pregnancyModal.onclick = e => { if (e.target === pregnancyModal) closePregnancyModalRepro(); };
    pregnancyForm.onsubmit = submitPregnancyFormRepro;
    if (!pregnancyForm.elements['HayvanId']) { /* Add HayvanId if missing */ }
    if (!pregnancyForm.elements['Id']) { /* Add Id if missing */ }
}
function openPregnancyModalRepro(preg = null) {
    const t = passedI18nRepro.t;
    pregnancyModal.classList.remove('hidden');
    pregnancyForm.reset();
    editingPregnancyId = preg ? preg.Id : null;
    pregnancyForm.elements['HayvanId'].value = animalSelectElRepro.value;
    if (preg) {
        pregnancyModalTitle.textContent = t('pregnancy_modal_title_edit');
        Object.keys(preg).forEach(key => {
            if (pregnancyForm.elements[key]) pregnancyForm.elements[key].value = preg[key] ?? '';
        });
        pregnancyForm.elements['Id'].value = preg.Id;
    } else {
        pregnancyModalTitle.textContent = t('pregnancy_modal_title_add');
        pregnancyForm.elements['Id'].value = '';
    }
}
function closePregnancyModalRepro() { pregnancyModal.classList.add('hidden'); editingPregnancyId = null; }
async function submitPregnancyFormRepro(e) {
    e.preventDefault();
    const t = passedI18nRepro.t;

    // Validate form before submission
    const isValid = await validateModalForm('pregnancy-form');
    if (!isValid) {
        return; // Stop submission if validation fails
    }

    const formData = Object.fromEntries(new FormData(pregnancyForm).entries());
    if (!formData.HayvanId) formData.HayvanId = animalSelectElRepro.value;
    formData.HayvanId = parseInt(formData.HayvanId);

    try {
        if (editingPregnancyId) {
            formData.Id = parseInt(editingPregnancyId);
            await window.api.invoke('gebelikler:update', formData);
            window.toast.success(t('toast_success_update'));
        } else {
            delete formData.Id;
            await window.api.invoke('gebelikler:add', formData);
            window.toast.success(t('toast_success_save'));
        }
        closePregnancyModalRepro();
        await loadAndRenderActiveReproData();
        // Refresh recent activities
        if (reproRecentActivities) {
            reproRecentActivities.refresh();
        }
    } catch (error) {
        console.error('Error saving pregnancy data:', error);
        window.toast.error(t('toast_error_save') + ': ' + error.message);
    }
}

// --- TAB RENDERING LOGIC ---
async function renderActiveReproTab() {
    const activeTabElement = document.querySelector('#repro-tabs .repro-tab.active');
    if (activeTabElement) {
        const tabName = activeTabElement.getAttribute('data-tab');
        await loadAndRenderActiveReproData(tabName);
    } else { // Should not happen if a tab is active by default
        await loadAndRenderActiveReproData('tohumlamalar');
    }
}

async function loadAndRenderActiveReproData(tabNameOverride = null) {
    const t = passedI18nRepro.t;
    const selectedAnimalId = animalSelectElRepro.value;
    const activeTab = tabNameOverride || document.querySelector('#repro-tabs .repro-tab.active').getAttribute('data-tab');

    if (!selectedAnimalId || selectedAnimalId === "") {
        reproContentEl.innerHTML = `<div class='empty-message'>${passedDefaultAvatarSVGRepro(48)}<br>${t('please_select_animal')}</div>`;
        return;
    }

    if (activeTab === 'tohumlamalar') {
        allInseminationsForAnimal = await window.api.invoke('tohumlamalar:list', selectedAnimalId);
        // First render the full table structure, then apply filters
        renderInseminationsTablePage();
    } else if (activeTab === 'gebelikler') {
        allPregnanciesForAnimal = await window.api.invoke('gebelikler:list', selectedAnimalId);
        // First render the full table structure, then apply filters
        renderPregnanciesTablePage();
    }
}

// --- INSEMINATIONS TAB ---
function applyFiltersAndSortInseminations() {
    let filteredData = [...allInseminationsForAnimal];
    const t = passedI18nRepro.t;
    const dateFilter = reproContentEl.querySelector('#filter-insemination-date')?.value;
    const typeFilter = reproContentEl.querySelector('#filter-insemination-type')?.value.trim().toLowerCase();
    const codeFilter = reproContentEl.querySelector('#filter-insemination-code')?.value.trim().toLowerCase();

    if (dateFilter) filteredData = filteredData.filter(i => i.TohumlamaTarihi === dateFilter);
    if (typeFilter) filteredData = filteredData.filter(i => (i.TohumlamaTipi || '').toLowerCase().includes(typeFilter));
    if (codeFilter) filteredData = filteredData.filter(i => (i.SpermaKodu || '').toLowerCase().includes(codeFilter));

    currentInseminationsForAnimal = filteredData;

    if (sortColumnInseminations) {
        currentInseminationsForAnimal = sortReproList(currentInseminationsForAnimal, sortColumnInseminations, sortDirectionInseminations);
    }
    currentPageInseminations = 1;

    // Only update table body, not the entire table structure
    updateInseminationsTableBody();
}

function updateInseminationsTableBody() {
    const t = passedI18nRepro.t;
    const tableBody = reproContentEl.querySelector('#insemination-table tbody');
    if (!tableBody) return;

    const pageItems = paginateItems(currentInseminationsForAnimal, currentPageInseminations, itemsPerPageInseminations);

    // Clear existing rows
    tableBody.innerHTML = '';

    if (pageItems.length === 0) {
        tableBody.innerHTML = `<tr><td colspan="5"><div class="empty-state-container">${passedDefaultAvatarSVGRepro(48)}<p>${t('no_records_found')}</p></div></td></tr>`;
    } else {
        pageItems.forEach(i => {
            const row = tableBody.insertRow();
            row.innerHTML = `
                <td class="col-date">${i.TohumlamaTarihi || ''}</td><td>${i.TohumlamaTipi || ''}</td>
                <td>${i.SpermaKodu || ''}</td><td>${i.Notlar || ''}</td>
                <td class="col-actions">
                    ${IconSystem.createActionsWrapper(i.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
                </td>`;
            row.querySelector('.edit-btn').onclick = () => openInseminationModalRepro(i);
            row.querySelector('.delete-btn').onclick = async () => {
                const confirmed = await window.toast.confirm(t('confirm_delete_insemination'), {
                    confirmText: t('toast_confirm'),
                    cancelText: t('toast_cancel')
                });

                if (confirmed) {
                    try {
                        await window.api.invoke('tohumlamalar:delete', i.Id);
                        await loadAndRenderActiveReproData();
                        window.toast.success(t('toast_success_delete'));
                    } catch (error) {
                        console.error('Error deleting insemination:', error);
                        window.toast.error(t('toast_error_delete') + ': ' + error.message);
                    }
                }
            };
        });
    }
}

function renderInseminationsTablePage() {
    const t = passedI18nRepro.t;
    reproContentEl.innerHTML = `
        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
            <h3>${t('label_tohumlamalar')}</h3>
            <button class="btn btn-primary" id="add-insemination-btn"><svg viewBox='0 0 20 20' fill='none'><circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/><path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/></svg> ${t('btn_add_insemination')}</button>
        </div>
        <table id="insemination-table" class="modern-table">
            <thead>
                <tr>
                    <th data-column-key="TohumlamaTarihi" class="col-date sortable">${t('label_tohumlama_tarihi')}</th>
                    <th data-column-key="TohumlamaTipi" class="sortable">${t('label_tohumlama_tipi')}</th>
                    <th data-column-key="SpermaKodu" class="sortable">${t('label_sperma_kodu')}</th>
                    <th data-column-key="Notlar" class="sortable">${t('label_notlar')}</th>
                    <th class="col-actions">${t('label_actions')}</th>
                </tr>
                <tr class="filter-row">
                    <th><input type="date" id="filter-insemination-date" class="filter-input"></th>
                    <th><input type="text" id="filter-insemination-type" class="filter-input" placeholder="${t('label_tohumlama_tipi')}"></th>
                    <th><input type="text" id="filter-insemination-code" class="filter-input" placeholder="${t('label_sperma_kodu')}"></th>
                    <th></th> <!-- Notes filter can be added if needed -->
                    <th></th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
        <div id="inseminations-pagination-controls" class="pagination-controls"></div>`; // Pagination controls will be filled by its updater

    reproContentEl.querySelector('#add-insemination-btn').onclick = () => openInseminationModalRepro();
    setupTableSortingRepro('#insemination-table', ['TohumlamaTarihi', 'TohumlamaTipi', 'SpermaKodu', 'Notlar'], 'insemination', applyFiltersAndSortInseminations);
    setupFilterListenersRepro('insemination', applyFiltersAndSortInseminations);
    setupPaginationListenersRepro('insemination', renderInseminationsTablePage, updateInseminationsPaginationControls, () => currentPageInseminations, val => currentPageInseminations = val, itemsPerPageInseminations, () => currentInseminationsForAnimal);

    // Reset filters and prepare data for display
    currentInseminationsForAnimal = [...allInseminationsForAnimal];
    currentPageInseminations = 1;

    // Update table body and pagination
    updateInseminationsTableBody();
    updateInseminationsPaginationControls();
    
    // Initialize table animations
    setTimeout(() => {
        TableAnimations.initializeTable('#insemination-table');
    }, 100);
}
function updateInseminationsPaginationControls() {
    updatePaginationControlsGeneric(passedI18nRepro.t, reproContentEl, '#inseminations-pagination-controls', currentPageInseminations, currentInseminationsForAnimal.length, itemsPerPageInseminations);
}

// --- PREGNANCIES TAB ---
function applyFiltersAndSortPregnancies() {
    let filteredData = [...allPregnanciesForAnimal];
    const t = passedI18nRepro.t;
    const startDateFilter = reproContentEl.querySelector('#filter-pregnancy-start-date')?.value;
    const expectedDateFilter = reproContentEl.querySelector('#filter-pregnancy-expected-date')?.value;
    const outcomeFilter = reproContentEl.querySelector('#filter-pregnancy-outcome')?.value;

    if (startDateFilter) filteredData = filteredData.filter(p => p.BaslangicTarihi >= startDateFilter);
    if (expectedDateFilter) filteredData = filteredData.filter(p => p.BeklenenDogumTarihi && p.BeklenenDogumTarihi <= expectedDateFilter);
    if (outcomeFilter) filteredData = filteredData.filter(p => p.GebelikSonucu === outcomeFilter);

    currentPregnanciesForAnimal = filteredData;

    if (sortColumnPregnancies) {
        currentPregnanciesForAnimal = sortReproList(currentPregnanciesForAnimal, sortColumnPregnancies, sortDirectionPregnancies);
    }
    currentPagePregnancies = 1;

    // Only update table body, not the entire table structure
    updatePregnanciesTableBody();
}

function updatePregnanciesTableBody() {
    const t = passedI18nRepro.t;
    const tableBody = reproContentEl.querySelector('#pregnancy-table tbody');
    if (!tableBody) return;

    const pageItems = paginateItems(currentPregnanciesForAnimal, currentPagePregnancies, itemsPerPagePregnancies);

    // Clear existing rows
    tableBody.innerHTML = '';

    if (pageItems.length === 0) {
        tableBody.innerHTML = `<tr><td colspan="5"><div class="empty-state-container">${passedDefaultAvatarSVGRepro(48)}<p>${t('no_records_found')}</p></div></td></tr>`;
    } else {
        pageItems.forEach(p => {
            const row = tableBody.insertRow();
            row.innerHTML = `
                <td class="col-date">${p.BaslangicTarihi || ''}</td>
                <td class="col-date">${p.BeklenenDogumTarihi || ''}</td>
                <td>${p.GebelikSonucu || ''}</td>
                <td>${p.Notlar || ''}</td>
                <td class="col-actions">
                    ${IconSystem.createActionsWrapper(p.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
                </td>`;
            row.querySelector('.edit-btn').onclick = () => openPregnancyModalRepro(p);
            row.querySelector('.delete-btn').onclick = async () => {
                const confirmed = await window.toast.confirm(t('confirm_delete_pregnancy'), {
                    confirmText: t('toast_confirm'),
                    cancelText: t('toast_cancel')
                });

                if (confirmed) {
                    try {
                        await window.api.invoke('gebelikler:delete', p.Id);
                        await loadAndRenderActiveReproData();
                        window.toast.success(t('toast_success_delete'));
                    } catch (error) {
                        console.error('Error deleting pregnancy:', error);
                        window.toast.error(t('toast_error_delete') + ': ' + error.message);
                    }
                }
            };
        });
    }
}
function renderPregnanciesTablePage() {
    const t = passedI18nRepro.t;
    reproContentEl.innerHTML = `
        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
            <h3>${t('label_gebelikler')}</h3>
            <button class="btn btn-primary" id="add-pregnancy-btn"><svg viewBox='0 0 20 20' fill='none'><circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/><path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/></svg> ${t('btn_add_pregnancy')}</button>
        </div>
        <table id="pregnancy-table" class="modern-table">
            <thead>
                <tr>
                    <th data-column-key="BaslangicTarihi" class="sortable">${t('label_gebelik_baslangic_tarihi')}</th>
                    <th data-column-key="BeklenenDogumTarihi" class="sortable">${t('label_beklenen_dogum_tarihi')}</th>
                    <th data-column-key="GebelikSonucu" class="sortable">${t('label_gebelik_sonucu')}</th>
                    <th data-column-key="Notlar" class="sortable">${t('label_notlar')}</th>
                    <th class="col-actions">${t('label_actions')}</th>
                </tr>
                <tr class="filter-row">
                    <th><input type="date" id="filter-pregnancy-start-date" class="filter-input"></th>
                    <th><input type="date" id="filter-pregnancy-expected-date" class="filter-input"></th>
                    <th>
                        <select id="filter-pregnancy-outcome" class="filter-input">
                            <option value="">${t('option_all')}</option>
                            <option value="Canlı Doğum">${t('pregnancy_result_canli_dogum') || 'Canlı Doğum'}</option>
                            <option value="Ölü Doğum">${t('pregnancy_result_olu_dogum') || 'Ölü Doğum'}</option>
                            <option value="Abort">${t('pregnancy_result_abort') || 'Abort'}</option>
                            <option value="Devam Ediyor">${t('pregnancy_result_ongoing') || 'Devam Ediyor'}</option>
                        </select>
                    </th>
                    <th></th>
                    <th></th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
        <div id="pregnancies-pagination-controls" class="pagination-controls"></div>`;
    reproContentEl.querySelector('#add-pregnancy-btn').onclick = () => openPregnancyModalRepro();
    setupTableSortingRepro('#pregnancy-table', ['BaslangicTarihi', 'BeklenenDogumTarihi', 'GebelikSonucu', 'Notlar'], 'pregnancy', applyFiltersAndSortPregnancies);
    setupFilterListenersRepro('pregnancy', applyFiltersAndSortPregnancies);
    setupPaginationListenersRepro('pregnancy', renderPregnanciesTablePage, updatePregnanciesPaginationControls, () => currentPagePregnancies, val => currentPagePregnancies = val, itemsPerPagePregnancies, () => currentPregnanciesForAnimal);

    // Reset filters and prepare data for display
    currentPregnanciesForAnimal = [...allPregnanciesForAnimal];
    currentPagePregnancies = 1;

    // Update table body and pagination
    updatePregnanciesTableBody();
    updatePregnanciesPaginationControls();
    
    // Initialize table animations
    setTimeout(() => {
        TableAnimations.initializeTable('#pregnancy-table');
    }, 100);
}
function updatePregnanciesPaginationControls() {
    updatePaginationControlsGeneric(passedI18nRepro.t, reproContentEl, '#pregnancies-pagination-controls', currentPagePregnancies, currentPregnanciesForAnimal.length, itemsPerPagePregnancies);
}

// --- COMMON HELPERS ---
function sortReproList(list, column, direction) { // Combined from health-page's sortHealthList
    return [...list].sort((a, b) => {
        let valA = a[column];
        let valB = b[column];
        const dateColumns = ['TohumlamaTarihi', 'BaslangicTarihi', 'BeklenenDogumTarihi'];
        if (dateColumns.includes(column)) {
            valA = valA ? new Date(valA) : null;
            valB = valB ? new Date(valB) : null;
            if (valA === null && valB === null) return 0;
            if (valA === null) return 1 * direction; // nulls last
            if (valB === null) return -1 * direction;
            return (valA - valB) * direction;
        }
        // For other types like string, number, boolean
        if (typeof valA === 'number' && typeof valB === 'number') {
             return (valA - valB) * direction;
        }
        if (typeof valA === 'boolean' && typeof valB === 'boolean') {
             return (valA === valB ? 0 : valA ? -1 : 1) * direction;
        }
        valA = (valA || '').toString().toLowerCase();
        valB = (valB || '').toString().toLowerCase();
        return valA.localeCompare(valB) * direction;
    });
}

function paginateItems(items, currentPage, itemsPerPage) {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return items.slice(startIndex, endIndex);
}

function updatePaginationControlsGeneric(t, container, paginationControlsSelector, currentPage, totalItems, itemsPerPage) {
    const controlsContainer = container.querySelector(paginationControlsSelector);
    if (!controlsContainer) return;

    const totalPages = Math.ceil(totalItems / itemsPerPage) || 1;

    controlsContainer.innerHTML = `
        <button id="prev-page-${paginationControlsSelector.substring(1, paginationControlsSelector.indexOf('-pagination'))}" ${currentPage === 1 ? 'disabled' : ''}>&laquo; ${t('btn_previous')}</button>
        <span id="page-info-${paginationControlsSelector.substring(1, paginationControlsSelector.indexOf('-pagination'))}">${t('page_info_text', { currentPage, totalPages, totalItems })}</span>
        <button id="next-page-${paginationControlsSelector.substring(1, paginationControlsSelector.indexOf('-pagination'))}" ${currentPage === totalPages ? 'disabled' : ''}>&raquo; ${t('btn_next')}</button>
    `;
}


function setupTableSortingRepro(tableSelector, columnKeys, type, applyFunc) {
    const ths = reproContentEl.querySelectorAll(`${tableSelector} thead th[data-column-key]`);
    ths.forEach(th => {
        const columnKey = th.dataset.columnKey;
        if (!columnKey) return;
        th.classList.add('sortable');
        
        let currentSortCol = type === 'insemination' ? sortColumnInseminations : sortColumnPregnancies;
        let currentSortDir = type === 'insemination' ? sortDirectionInseminations : sortDirectionPregnancies;

        // Set initial sorting state
        if (columnKey === currentSortCol) {
            th.classList.add(currentSortDir === 1 ? 'sorted-asc' : 'sorted-desc');
        }

        th.onclick = () => { // Re-assign onclick to ensure it's fresh
            // Clear all sorting classes first
            ths.forEach(headerTh => {
                headerTh.classList.remove('sorted-asc', 'sorted-desc');
                headerTh.querySelector('.sort-arrow')?.remove();
            });

            if (type === 'insemination') {
                if (sortColumnInseminations === columnKey) sortDirectionInseminations *= -1;
                else { sortColumnInseminations = columnKey; sortDirectionInseminations = 1; }
                th.classList.add(sortDirectionInseminations === 1 ? 'sorted-asc' : 'sorted-desc');
            } else { // pregnancy
                if (sortColumnPregnancies === columnKey) sortDirectionPregnancies *= -1;
                else { sortColumnPregnancies = columnKey; sortDirectionPregnancies = 1; }
                th.classList.add(sortDirectionPregnancies === 1 ? 'sorted-asc' : 'sorted-desc');
            }
            applyFunc();
        };
    });
}

function setupFilterListenersRepro(type, applyFunc) {
    let filterSelectors = [];
    if (type === 'insemination') {
        filterSelectors = ['#filter-insemination-date', '#filter-insemination-type', '#filter-insemination-code'];
    } else { // pregnancy
        filterSelectors = ['#filter-pregnancy-start-date', '#filter-pregnancy-expected-date', '#filter-pregnancy-outcome'];
    }
    filterSelectors.forEach(selector => {
        const inputElement = reproContentEl.querySelector(selector);
        if (inputElement) {
            // Remove any existing listeners first
            inputElement.removeEventListener('input', applyFunc);
            inputElement.removeEventListener('change', applyFunc);

            // Add new listeners using addEventListener
            inputElement.addEventListener('input', applyFunc);
            if (inputElement.tagName === 'SELECT' || inputElement.type === 'date') {
                inputElement.addEventListener('change', applyFunc);
            }
        }
    });
}

function setupPaginationListenersRepro(type, renderFunc, updateControlsFunc, getCurrentPageFunc, setCurrentPageFunc, itemsPerPage, getCurrentDataArrayFunc) {
    const controlsContainer = reproContentEl.querySelector(`#${type}s-pagination-controls`);
    if(!controlsContainer) return;

    const prevBtn = controlsContainer.querySelector(`#prev-page-${type}s`);
    const nextBtn = controlsContainer.querySelector(`#next-page-${type}s`);

    if(prevBtn) prevBtn.onclick = () => {
        if (getCurrentPageFunc() > 1) {
            setCurrentPageFunc(getCurrentPageFunc() - 1);
            renderFunc();
        }
    };
    if(nextBtn) nextBtn.onclick = () => {
        const totalPages = Math.ceil(getCurrentDataArrayFunc().length / itemsPerPage);
        if (getCurrentPageFunc() < totalPages) {
            setCurrentPageFunc(getCurrentPageFunc() + 1);
            renderFunc();
        }
    };
}

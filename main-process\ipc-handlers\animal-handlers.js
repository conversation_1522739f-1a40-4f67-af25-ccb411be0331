const { ipcMain } = require('electron');
const { getDb } = require('../services/database.js');
const { getCurrentProfile } = require('./auth-handlers.js');

const db = getDb(); // Get the initialized DB instance

// --- HAYVANLAR CRUD ---
ipcMain.handle('hayvanlar:list', async () => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const query = `
      SELECT
        H.*,
        CASE
          WHEN EXISTS (SELECT 1 FROM Gebelikler G WHERE G.HayvanId = H.Id AND G.ProfilId = ? AND (G.GebelikSonucu IS NULL OR G.GebelikSonucu = '' OR G.GebelikSonucu = 'Devam Ediyor')) THEN 'Gebe'
          ELSE 'Boş'
        END AS GebelikDurumu,
        CASE
          WHEN EXISTS (SELECT 1 FROM Laktasyonlar L WHERE L.HayvanId = H.Id AND L.ProfilId = ? AND (L.BitisTarihi IS NULL OR L.BitisTarihi >= date('now'))) THEN 'Sağmal'
          ELSE 'Kuruda'
        END AS Durum,
        (
          SELECT CAST(SUM(sv.Miktar) AS REAL) / COUNT(DISTINCT sv.Tarih)
          FROM Laktasyonlar l
          JOIN SagimVerileri sv ON sv.LaktasyonId = l.Id
          WHERE l.HayvanId = H.Id AND l.ProfilId = ? AND sv.ProfilId = ?
        ) AS GunlukOrtalamaSut
      FROM Hayvanlar H
      WHERE H.ProfilId = ?`;

    db.all(query, [currentProfile.id, currentProfile.id, currentProfile.id, currentProfile.id, currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
});

ipcMain.handle('hayvanlar:add', async (event, data) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const stmt = db.prepare(`INSERT INTO Hayvanlar (
      ProfilId, KupeNo, IsletmeNo, Isim, Tur, Irk, Cinsiyet, DogumTarihi, DogumAgirligi, DogumTipi, AnneKupe, BabaKupe, BoynuzDurumu, IsletmeyeGirisTarihi, IsletmedenCikisTarihi, CikisSebebi, FotografUrl, Notlar, AktifMi
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`);
    stmt.run([
      currentProfile.id, data.KupeNo, data.IsletmeNo, data.Isim, data.Tur, data.Irk, data.Cinsiyet, data.DogumTarihi, data.DogumAgirligi, data.DogumTipi, data.AnneKupe, data.BabaKupe, data.BoynuzDurumu, data.IsletmeyeGirisTarihi, data.IsletmedenCikisTarihi, data.CikisSebebi, data.FotografUrl, data.Notlar, data.AktifMi
    ], function(err) {
      stmt.finalize();
      if (err) reject(err);
      else resolve({ id: this.lastID });
    });
  });
});

ipcMain.handle('hayvanlar:update', async (event, data) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const stmt = db.prepare(`UPDATE Hayvanlar SET
      KupeNo=?, IsletmeNo=?, Isim=?, Tur=?, Irk=?, Cinsiyet=?, DogumTarihi=?, DogumAgirligi=?, DogumTipi=?, AnneKupe=?, BabaKupe=?, BoynuzDurumu=?, IsletmeyeGirisTarihi=?, IsletmedenCikisTarihi=?, CikisSebebi=?, FotografUrl=?, Notlar=?, AktifMi=?
      WHERE Id=? AND ProfilId=?`);
    stmt.run([
      data.KupeNo, data.IsletmeNo, data.Isim, data.Tur, data.Irk, data.Cinsiyet, data.DogumTarihi, data.DogumAgirligi, data.DogumTipi, data.AnneKupe, data.BabaKupe, data.BoynuzDurumu, data.IsletmeyeGirisTarihi, data.IsletmedenCikisTarihi, data.CikisSebebi, data.FotografUrl, data.Notlar, data.AktifMi, data.Id, currentProfile.id
    ], function(err) {
      stmt.finalize();
      if (err) reject(err);
      else resolve({ changes: this.changes });
    });
  });
});

ipcMain.handle('hayvanlar:getById', async (event, id) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    const query = `
      SELECT
        H.*,
        CASE
          WHEN EXISTS (SELECT 1 FROM Gebelikler G WHERE G.HayvanId = H.Id AND G.ProfilId = ? AND (G.GebelikSonucu IS NULL OR G.GebelikSonucu = '' OR G.GebelikSonucu = 'Devam Ediyor')) THEN 'Gebe'
          ELSE 'Boş'
        END AS GebelikDurumu,
        CASE
          WHEN EXISTS (SELECT 1 FROM Laktasyonlar L WHERE L.HayvanId = H.Id AND L.ProfilId = ? AND (L.BitisTarihi IS NULL OR L.BitisTarihi >= date('now'))) THEN 'Sağmal'
          ELSE 'Kuruda'
        END AS Durum
      FROM Hayvanlar H
      WHERE H.Id = ? AND H.ProfilId = ?`;

    db.get(query, [currentProfile.id, currentProfile.id, id, currentProfile.id], (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
});

ipcMain.handle('hayvanlar:delete', async (event, id) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.serialize(() => {
      db.run('BEGIN TRANSACTION;', (err) => { if (err) { reject(err); return; }});

      // Delete related data with profile context
      const tablesToDeleteFrom = ['Asilamalar', 'Tedaviler', 'Tohumlamalar', 'Gebelikler'];
      for (const table of tablesToDeleteFrom) {
        db.run(`DELETE FROM ${table} WHERE HayvanId = ? AND ProfilId = ?`, [id, currentProfile.id], (err) => { if (err) { db.run('ROLLBACK;'); reject(err); return; }});
      }

      db.run('DELETE FROM SagimVerileri WHERE LaktasyonId IN (SELECT Id FROM Laktasyonlar WHERE HayvanId = ? AND ProfilId = ?) AND ProfilId = ?', [id, currentProfile.id, currentProfile.id], (err) => { if (err) { db.run('ROLLBACK;'); reject(err); return; }});
      db.run('DELETE FROM Laktasyonlar WHERE HayvanId = ? AND ProfilId = ?', [id, currentProfile.id], (err) => { if (err) { db.run('ROLLBACK;'); reject(err); return; }});

      db.run('DELETE FROM Hayvanlar WHERE Id = ? AND ProfilId = ?', [id, currentProfile.id], function(err) {
        if (err) {
          db.run('ROLLBACK;', () => reject(err));
        } else {
          const changes = this.changes;
          db.run('COMMIT;', (commitErr) => {
            if (commitErr) reject(commitErr);
            else resolve({ changes });
          });
        }
      });
    });
  });
});

ipcMain.handle('hayvanlar:get-details', async (event, animalId) => {
  return new Promise((resolve, reject) => {
    if (!animalId) {
      return reject(new Error('Animal ID is required.'));
    }

    const details = {};
    const animalQuery = `
      SELECT
        H.*,
        CASE
          WHEN EXISTS (SELECT 1 FROM Gebelikler G WHERE G.HayvanId = H.Id AND (G.GebelikSonucu IS NULL OR G.GebelikSonucu = '' OR G.GebelikSonucu = 'Devam Ediyor')) THEN 'Gebe'
          ELSE 'Boş'
        END AS GebelikDurumu,
        CASE
          WHEN EXISTS (SELECT 1 FROM Laktasyonlar L WHERE L.HayvanId = H.Id AND (L.BitisTarihi IS NULL OR L.BitisTarihi >= date('now'))) THEN 'Sağmal'
          ELSE 'Kuruda'
        END AS Durum,
        (
          SELECT CAST(SUM(sv.Miktar) AS REAL) / COUNT(DISTINCT sv.Tarih)
          FROM Laktasyonlar l
          JOIN SagimVerileri sv ON sv.LaktasyonId = l.Id
          WHERE l.HayvanId = H.Id
        ) AS GunlukOrtalamaSut
      FROM Hayvanlar H
      WHERE H.Id = ?`;

    db.get(animalQuery, [animalId], (err, animalRow) => {
      if (err) return reject(err);
      if (!animalRow) return reject(new Error('Animal not found.'));
      details.animal = animalRow;

      const queries = [
        { key: 'lastVaccine', query: 'SELECT * FROM Asilamalar WHERE HayvanId = ? ORDER BY AsilamaTarihi DESC LIMIT 1' },
        { key: 'lastTreatment', query: 'SELECT * FROM Tedaviler WHERE HayvanId = ? ORDER BY TedaviBaslangicTarihi DESC LIMIT 1' },
        { key: 'lastInsemination', query: 'SELECT * FROM Tohumlamalar WHERE HayvanId = ? ORDER BY TohumlamaTarihi DESC LIMIT 1' },
        { key: 'lastPregnancy', query: 'SELECT * FROM Gebelikler WHERE HayvanId = ? ORDER BY BaslangicTarihi DESC LIMIT 1' }
      ];

      let completedQueries = 0;
      if (queries.length === 0) { // Should not happen with current queries array
          resolve(details);
          return;
      }
      queries.forEach(q => {
        db.get(q.query, [animalId], (err, row) => {
          if (err) {
            console.error(`Error fetching ${q.key} for animal ${animalId}:`, err);
            details[q.key] = null;
          } else {
            details[q.key] = row || null;
          }
          completedQueries++;
          if (completedQueries === queries.length) {
            resolve(details);
          }
        });
      });
    });
  });
});

console.log('Animal IPC handlers registered.');

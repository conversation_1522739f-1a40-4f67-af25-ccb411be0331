const sqlite3 = require('sqlite3');
const path = require('path');

// Günlük besin ihtiyaçlarını kg/gün'den %KM'ye dönüştürme fonksiyonu
function convertToPercentageDM(dailyRequirement, dryMatterIntake) {
    return (dailyRequirement / dryMatterIntake) * 100;
}

// Veritabanı bağlantısını başlat
const dbPath = path.join(__dirname, '..', 'livestock.db');
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('Veritabanına bağlanırken hata:', err.message);
        process.exit(1);
    }
    console.log('SQLite veritabanına bağlandı');
});

// Günlük kuru madde tüketimi
const dryMatterIntake = 24.5; // kg/gün

// İdeal yem değerlerini hesapla
const idealFeed = {
    ProfilId: 1,
    YemAdi: 'İdeal Rasyon',
    KuruMadde: 100, // %
    HamProtein: convertToPercentageDM(2.64, dryMatterIntake), // kg/gün -> %KM
    RDP: convertToPercentageDM(1.72, dryMatterIntake), // kg/gün -> %KM
    RUP: convertToPercentageDM(0.92, dryMatterIntake), // kg/gün -> %KM
    MetabolikEnerji: 224 / dryMatterIntake, // MJ/gün -> MJ/kg KM
    NEL: 34.2 / dryMatterIntake, // Mcal/gün -> Mcal/kg KM
    HamYag: convertToPercentageDM(0.98, dryMatterIntake), // kg/gün -> %KM
    Nisasta: convertToPercentageDM(6.126, dryMatterIntake), // kg/gün -> %KM
    Seker: convertToPercentageDM(1.225, dryMatterIntake), // kg/gün -> %KM
    Kul: convertToPercentageDM(1.96, dryMatterIntake), // kg/gün -> %KM
    NDF: convertToPercentageDM(6.8607, dryMatterIntake), // kg/gün -> %KM
    ADF: convertToPercentageDM(5.1455, dryMatterIntake), // kg/gün -> %KM
    Hemiseluloz: convertToPercentageDM(1.7152, dryMatterIntake), // kg/gün -> %KM
    Seluloz: convertToPercentageDM(4.2879, dryMatterIntake), // kg/gün -> %KM
    Lignin: convertToPercentageDM(0.8576, dryMatterIntake), // kg/gün -> %KM
    Kalsiyum: convertToPercentageDM(0.069, dryMatterIntake), // kg/gün -> %KM
    Fosfor: convertToPercentageDM(0.041, dryMatterIntake), // kg/gün -> %KM
    Magnezyum: convertToPercentageDM(0.0293, dryMatterIntake), // kg/gün -> %KM
    Potasyum: convertToPercentageDM(0.113, dryMatterIntake), // kg/gün -> %KM
    Sodyum: convertToPercentageDM(0.044, dryMatterIntake), // kg/gün -> %KM
    Klorur: convertToPercentageDM(0.066, dryMatterIntake), // kg/gün -> %KM
    sulfur: convertToPercentageDM(0.0258, dryMatterIntake), // kg/gün -> %KM
    Demir: (1225 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Cinko: (980 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Bakir: (245 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Manganez: (980 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Selenyum: (7.4 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Iyot: (12.3 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Kobalt: (2.5 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Molibden: (3.7 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    VitaminA: 66000 / dryMatterIntake, // IU/gün -> IU/kg KM
    VitaminD: 18000 / dryMatterIntake, // IU/gün -> IU/kg KM
    VitaminE: 368 / dryMatterIntake, // IU/gün -> IU/kg KM
    VitaminK: convertToPercentageDM(0.0245, dryMatterIntake), // kg/gün -> %KM
    Thiamin: (73.5 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Riboflavin: (143.5 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Niacin: (368 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Biotin: (2.4 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Folat: (12.3 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Cobalamin: (0.03 / dryMatterIntake) * 1000, // mg/gün -> mg/kg KM
    Tanen: convertToPercentageDM(0.49, dryMatterIntake), // kg/gün -> %KM
    Fitat: convertToPercentageDM(0.245, dryMatterIntake), // kg/gün -> %KM
    Saponin: convertToPercentageDM(0.1225, dryMatterIntake), // kg/gün -> %KM
    Oksalat: convertToPercentageDM(0.49, dryMatterIntake), // kg/gün -> %KM
};

// SQL sorgusunu hazırla
const columns = Object.keys(idealFeed).join(', ');
const placeholders = Object.keys(idealFeed).map(() => '?').join(', ');
const values = Object.values(idealFeed);

const sql = `INSERT INTO Yemler (${columns}) VALUES (${placeholders})`;

// Yemi veritabanına ekle
db.run(sql, values, function(err) {
    if (err) {
        console.error('Yem eklenirken hata:', err.message);
        process.exit(1);
    }
    console.log('İdeal yem başarıyla eklendi. ID:', this.lastID);
    db.close();
});
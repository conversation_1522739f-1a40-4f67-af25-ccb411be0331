const sqlite3 = require('sqlite3');
const path = require('path');

// Veritabanı bağlantısını başlat
const dbPath = path.join(__dirname, '..', 'livestock.db');
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('Veritabanına bağlanırken hata:', err.message);
        process.exit(1);
    }
    console.log('SQLite veritabanına bağlandı');
    
    // Check total feeds
    db.get("SELECT COUNT(*) as total_feeds FROM Yemler WHERE AktifMi = 1", (err, row) => {
        if (err) {
            console.error('Hata:', err.message);
        } else {
            console.log(`Toplam aktif yem sayısı: ${row.total_feeds}`);
        }
        
        // List all feeds
        db.all("SELECT YemAdi, YemTipi, BirimFiyat FROM Yemler WHERE AktifMi = 1 ORDER BY YemAdi", (err, rows) => {
            if (err) {
                console.error('Hata:', err.message);
            } else {
                console.log('\nEklenen yemler:');
                rows.forEach((row, index) => {
                    console.log(`${index + 1}. ${row.YemAdi} (${row.YemTipi}) - ${row.BirimFiyat} TL/kg`);
                });
            }
            
            db.close();
        });
    });
});

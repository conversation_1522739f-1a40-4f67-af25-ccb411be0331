// Feed Ration Page Module
// Expects contentArea and i18n

// AI optimization imports removed

// Global function for calculating overall ration score
function calculateOverallRationScore(validationResults) {
  if (!validationResults || validationResults.length === 0) {
    return 0;
  }

  let totalScore = 0;
  let totalWeight = 0;

  // Kritik besinler için ağırlıklı skorlama
  const criticalNutrients = {
    dryMatter: 0.20,    // %20 ağırlık - En kritik
    protein: 0.15,      // %15 ağırlık
    energy: 0.15,       // %15 ağırlık
    nel: 0.10,          // %10 ağırlık
    calcium: 0.08,      // %8 ağırlık
    phosphorus: 0.08,   // %8 ağırlık
    ndf: 0.08,          // %8 ağırlık
    adf: 0.06,          // %6 ağırlık
    rdp: 0.05,          // %5 ağırlık
    rup: 0.05           // %5 ağırlık
  };

  validationResults.forEach(result => {
    const nutrientWeight = criticalNutrients[result.nutrient] || 0.01; // Diğer besinler %1

    let score = 0;
    const ratio = result.provided / result.required;

    // 🚨 EXPONANSIYEL CEZA SİSTEMİ - Progress Bar Skorlaması
    const isCritical = criticalNutrients[result.nutrient] ? true : false;
    score = calculateExponentialPenaltyScoreForProgressBar(ratio, isCritical);

    // Kritik eksiklikler için ekstra ceza
    if (criticalNutrients[result.nutrient] && ratio < 0.85) {
      score = score * 0.5; // %50 ceza
    }

    totalScore += score * nutrientWeight;
    totalWeight += nutrientWeight;


  });

  const finalScore = totalWeight > 0 ? totalScore / totalWeight : 0;


  return finalScore;
}

// Exponansiyel ceza sistemi - Progress Bar için
function calculateExponentialPenaltyScoreForProgressBar(ratio, isCritical = false) {
  // Optimal aralık belirleme
  const optimalMin = 0.95;
  const optimalMax = 1.05;
  const goodMin = 0.90;
  const goodMax = 1.10;

  // Optimal aralıkta ise tam puan
  if (ratio >= optimalMin && ratio <= optimalMax) {
    return 100;
  }

  // İyi aralıkta ise hafif ceza
  if (ratio >= goodMin && ratio <= goodMax) {
    const deviation = Math.abs(ratio - 1);
    return Math.max(85, 100 - deviation * 150);
  }

  // Exponansiyel ceza hesaplama
  let score;

  if (ratio > goodMax) {
    // FAZLALIK EXPONANSIYEL CEZA
    const excessRatio = ratio - 1;

    if (isCritical) {
      // Kritik besinler için sert ceza
      if (ratio > 2.5) {
        score = 0;
      } else if (ratio > 1.8) {
        const exponent = Math.pow(excessRatio - 0.8, 2.2);
        score = Math.max(0, 20 * Math.exp(-exponent * 1.8));
      } else if (ratio > 1.3) {
        const exponent = Math.pow(excessRatio - 0.3, 1.9);
        score = Math.max(0, 50 * Math.exp(-exponent * 1.4));
      } else {
        const exponent = Math.pow(excessRatio - 0.1, 1.6);
        score = Math.max(0, 80 * Math.exp(-exponent * 1.1));
      }
    } else {
      // Normal besinler için yumuşak ceza
      if (ratio > 3.5) {
        score = 0;
      } else if (ratio > 2.2) {
        const exponent = Math.pow(excessRatio - 1.2, 2.0);
        score = Math.max(0, 25 * Math.exp(-exponent * 1.6));
      } else if (ratio > 1.5) {
        const exponent = Math.pow(excessRatio - 0.5, 1.8);
        score = Math.max(0, 60 * Math.exp(-exponent * 1.2));
      } else {
        const exponent = Math.pow(excessRatio - 0.1, 1.5);
        score = Math.max(0, 85 * Math.exp(-exponent * 0.9));
      }
    }
  } else {
    // EKSİKLİK EXPONANSIYEL CEZA
    const deficitRatio = 1 - ratio;

    if (isCritical) {
      // Kritik besinler için sert eksiklik cezası
      if (ratio < 0.6) {
        score = 0;
      } else if (ratio < 0.75) {
        const exponent = Math.pow(deficitRatio - 0.25, 2.1);
        score = Math.max(0, 30 * Math.exp(-exponent * 2.0));
      } else if (ratio < 0.85) {
        const exponent = Math.pow(deficitRatio - 0.15, 1.8);
        score = Math.max(0, 60 * Math.exp(-exponent * 1.5));
      } else {
        const exponent = Math.pow(deficitRatio - 0.05, 1.6);
        score = Math.max(0, 85 * Math.exp(-exponent * 1.0));
      }
    } else {
      // Normal besinler için yumuşak eksiklik cezası
      if (ratio < 0.4) {
        score = 0;
      } else if (ratio < 0.65) {
        const exponent = Math.pow(deficitRatio - 0.35, 1.9);
        score = Math.max(0, 40 * Math.exp(-exponent * 1.7));
      } else if (ratio < 0.8) {
        const exponent = Math.pow(deficitRatio - 0.2, 1.7);
        score = Math.max(0, 70 * Math.exp(-exponent * 1.1));
      } else {
        const exponent = Math.pow(deficitRatio - 0.1, 1.4);
        score = Math.max(0, 90 * Math.exp(-exponent * 0.8));
      }
    }
  }

  return Math.round(score * 100) / 100;
}

// Global Ration Evaluation Progress Bar Functions
function updateRationEvaluationProgress(score) {
  const scoreContainer = document.getElementById('ration-evaluation-container');
  const progressRing = document.querySelector('.progress-ring-progress-evaluation');
  const scoreValue = document.getElementById('score-value-evaluation');
  const circularProgress = document.querySelector('.circular-progress-evaluation');

  if (!scoreContainer || !progressRing || !scoreValue) {

    return;
  }

  // Progress bar'ı göster
  scoreContainer.style.display = 'block';

  // Score değerini güncelle
  const normalizedScore = Math.max(0, Math.min(100, score));
  scoreValue.textContent = Math.round(normalizedScore);

  // Circular progress'i güncelle
  updateCircularProgressEvaluation(progressRing, normalizedScore);

  // Animation ekle
  if (circularProgress) {
    circularProgress.classList.add('updating');
    setTimeout(() => {
      circularProgress.classList.remove('updating');
    }, 600);
  }


}

function updateCircularProgressEvaluation(progressRing, score) {
  const radius = 32;
  const circumference = 2 * Math.PI * radius;

  // Ensure score is between 0 and 100
  const normalizedScore = Math.max(0, Math.min(100, score));

  // Calculate offset (start from full circle and reduce based on score)
  const offset = circumference - (normalizedScore / 100) * circumference;

  // Update stroke-dasharray and stroke-dashoffset
  progressRing.style.strokeDasharray = `${circumference}`;
  progressRing.style.strokeDashoffset = `${offset}`;

  // Update color based on score
  progressRing.classList.remove('low-score', 'medium-score', 'high-score', 'excellent-score');

  let colorClass = '';
  if (normalizedScore >= 90) {
    colorClass = 'excellent-score';
    progressRing.classList.add('excellent-score');
  } else if (normalizedScore >= 75) {
    colorClass = 'high-score';
    progressRing.classList.add('high-score');
  } else if (normalizedScore >= 50) {
    colorClass = 'medium-score';
    progressRing.classList.add('medium-score');
  } else {
    colorClass = 'low-score';
    progressRing.classList.add('low-score');
  }

  console.log(`🎨 Progress ring rengi güncellendi: ${colorClass} (${normalizedScore}%)`);
}

function hideRationEvaluationProgress() {
  const scoreContainer = document.getElementById('ration-evaluation-container');
  if (scoreContainer) {
    scoreContainer.style.display = 'none';
    console.log('📊 Rasyon değerlendirme progress bar gizlendi');
  }
}

/**
 * Yem türüne göre gerçekçi vitamin A varsayılan değeri döndürür
 * @param {string} feedName - Yem adı
 * @returns {number} - Vitamin A değeri (IU/kg DM)
 */
function getRealisticVitaminADefault(feedName) {
  const name = (feedName || '').toLowerCase();

  // Kaba yemler (yüksek vitamin A)
  if (name.includes('alfalfa') || name.includes('yonca')) {
    return 12000; // Alfalfa kuru
  }
  if (name.includes('çayır') || name.includes('ot') || name.includes('saman')) {
    return 5000; // Çayır otu kuru
  }
  if (name.includes('silaj') || name.includes('mısır silaj')) {
    return 3000; // Mısır silajı
  }
  if (name.includes('sap') || name.includes('saman')) {
    return 1000; // Sap/saman
  }

  // Konsantre yemler (düşük vitamin A)
  if (name.includes('arpa')) {
    return 25; // Arpa
  }
  if (name.includes('buğday')) {
    return 15; // Buğday
  }
  if (name.includes('mısır') && !name.includes('silaj')) {
    return 200; // Mısır tanesi
  }
  if (name.includes('soya') || name.includes('küspe')) {
    return 50; // Küspeler
  }
  if (name.includes('kepek')) {
    return 100; // Kepekler
  }

  // Mineral/vitamin premiksleri
  if (name.includes('vitamin') || name.includes('premiks')) {
    return 1000000; // Vitamin premiksi
  }
  if (name.includes('mineral')) {
    return 50000; // Mineral premiks (vitamin A eklenmiş)
  }

  // Varsayılan (bilinmeyen yemler için)
  return 1000; // Güvenli düşük değer
}

export async function renderFeedRationPage(contentArea, i18n) {

  contentArea.innerHTML = `
    <div class="feed-ration-page">
      <!-- Feed Ration Header Section -->
      <div class="feed-ration-header-section">
        <div class="feed-ration-header-content">
          <h2 class="feed-ration-page-title">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
            <span data-i18n="feed_ration">Rasyon Yönetimi</span>
          </h2>
        </div>
      </div>

      <!-- Navigation Tabs Section -->
      <div class="feed-ration-navigation-section">
        <div id="feed-ration-tabs" class="feed-ration-nav-tabs">
          <button class="feed-ration-tab active" data-tab="calculator">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="9" x2="15" y2="15"></line>
              <line x1="15" y1="9" x2="9" y2="15"></line>
            </svg>
            <span>Rasyon Oluşturma</span>
          </button>
          <button class="feed-ration-tab" data-tab="feeds">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
            <span>Yem Yönetimi</span>
          </button>
          <button class="feed-ration-tab" data-tab="saved">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
            </svg>
            <span>Kaydedilen Rasyonlar</span>
          </button>
        </div>
      </div>

      <!-- Tab Content Container -->
      <div class="feed-ration-tab-container" id="feed-ration-content">
        <!-- Calculator Tab Content -->
        <div class="feed-ration-content-section active" id="calculator-section">
          <!-- Content Area for Calculator -->
          <div class="feed-ration-content">
            <!-- Feed Ration Managers -->
            <div class="feed-ration-managers">
            <!-- Top Row: Calculator and Nutritional Needs (2 cards side by side) -->
            <div class="feed-ration-top-row">
              <!-- Manager 1: Ration Calculator -->
              <div class="feed-ration-manager">
                <div class="feed-ration-manager-header">
                  <h3 class="feed-ration-manager-title">Rasyon Hesaplayıcı</h3>
                  <div class="feed-ration-manager-actions">
                    <button class="btn btn-sm btn-primary" id="calculate-nutrition-btn">Hesapla</button>
                  </div>
                </div>
                <div class="feed-ration-manager-body">
                  <div class="feed-ration-content">
                    <div class="feed-ration-form">
                      <div class="feed-ration-form-group">
                        <label for="animal-category">Hayvan Kategorisi</label>
                        <select id="animal-category" name="animalCategory">
                          <option value="">Seçiniz</option>
                          <option value="dairy-cow">Süt İneği</option>
                          <option value="calf">Buzağı</option>
                          <option value="heifer">Düve</option>
                          <option value="bull">Boğa</option>
                        </select>
                      </div>
                      <div class="feed-ration-form-group">
                        <label for="live-weight">Canlı Ağırlık (kg)</label>
                        <input type="number" id="live-weight" name="liveWeight" min="50" max="1000" placeholder="500">
                      </div>
                      <div class="feed-ration-form-group">
                        <label for="milk-yield">Günlük Süt Verimi (L)</label>
                        <input type="number" id="milk-yield" name="milkYield" min="0" max="80" placeholder="25">
                      </div>
                      <div class="feed-ration-form-group">
                        <label for="milk-fat">Süt Yağ Oranı (%)</label>
                        <input type="number" id="milk-fat" name="milkFat" min="2.5" max="6.0" step="0.1" placeholder="3.5">
                      </div>
                      <div class="feed-ration-form-group">
                        <label for="pregnancy-status">Gebelik Durumu</label>
                        <select id="pregnancy-status" name="pregnancyStatus">
                          <option value="not-pregnant">Gebe Değil</option>
                          <option value="early">1-3 Ay Gebe</option>
                          <option value="mid">4-6 Ay Gebe</option>
                          <option value="late">7-9 Ay Gebe</option>
                        </select>
                      </div>
                      <div class="feed-ration-form-group">
                        <label for="activity-level">Aktivite Seviyesi</label>
                        <select id="activity-level" name="activityLevel">
                          <option value="normal">Normal</option>
                          <option value="high">Yüksek</option>
                          <option value="low">Düşük</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Manager 2: Nutritional Needs -->
              <div class="feed-ration-manager">
                <div class="feed-ration-manager-header">
                  <h3 class="feed-ration-manager-title">Günlük Besin İhtiyaçları</h3>
                  <div class="feed-ration-manager-actions">
                    <button class="btn btn-sm btn-secondary" id="export-nutrition-btn">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                      </svg>
                      Dışa Aktar
                    </button>
                    <button class="btn btn-sm btn-outline" id="print-nutrition-btn">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="6,9 6,2 18,2 18,9"></polyline>
                        <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                        <rect x="6" y="14" width="12" height="8"></rect>
                      </svg>
                      Yazdır
                    </button>
                  </div>
                </div>
                <div class="feed-ration-manager-body">
                      <div class="table-container">
                        <table class="feed-ration-table">
                        <thead>
                          <tr>
                            <th>Besin Öğesi</th>
                            <th>İhtiyaç</th>
                            <th>Birim</th>
                            <th>Kaynak</th>
                          </tr>
                        </thead>
                        <tbody id="nutritional-needs-tbody">
                          <tr>
                            <td><strong>Makro Besinler</strong></td>
                            <td colspan="3" style="background: var(--bg-elevated); font-weight: bold; text-align: center;">Temel Gereksinimler</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Kuru Madde
                                <div class="tooltip">
                                  <span class="tooltip-title">Kuru Madde İhtiyacı (DMI)</span>
                                  <div class="tooltip-formula">DMI = (0.372 × FCM + 0.0968 × BW^0.75) × (1 - e^(-0.192×(WOL+1)))</div>
                                  <strong>Açıklama:</strong> Hayvanın günlük tüketeceği toplam kuru madde miktarı. FCM (4% Yağ Düzeltmeli Süt), BW (Canlı Ağırlık), WOL (Laktasyon Haftası).
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 15-18</div>
                                </div>
                              </div>
                            </td>
                            <td id="dm-requirement">-</td>
                            <td>kg/gün</td>
                            <td id="dm-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Ham Protein
                                <div class="tooltip">
                                  <span class="tooltip-title">Ham Protein İhtiyacı (CP)</span>
                                  <div class="tooltip-formula">CP = (MP_yaşama + MP_süt + MP_gebelik) × 1.6</div>
                                  <strong>Açıklama:</strong> Metabolik protein ihtiyacının ham proteine çevrilmesi. Yaşama payı: 3.8 × BW^0.75, süt payı: 34g/L süt.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 43-48</div>
                                </div>
                              </div>
                            </td>
                            <td id="protein-requirement">-</td>
                            <td>kg/gün</td>
                            <td id="protein-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                RDP (Rumen Parçalanabilir Protein)
                                <div class="tooltip">
                                  <span class="tooltip-title">RDP İhtiyacı</span>
                                  <div class="tooltip-formula">RDP = CP × 0.65-0.70 (optimal: 68%)</div>
                                  <strong>Açıklama:</strong> Rumende mikroorganizmalar tarafından parçalanan protein. Rumen mikroorganizma sentezi için gerekli. Fazlası amonyak olarak kaybedilir.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 48-53</div>
                                </div>
                              </div>
                            </td>
                            <td id="rdp-requirement">-</td>
                            <td>kg/gün</td>
                            <td id="rdp-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                RUP (Rumen Parçalanmayan Protein)
                                <div class="tooltip">
                                  <span class="tooltip-title">RUP İhtiyacı</span>
                                  <div class="tooltip-formula">RUP = CP × 0.30-0.35 (optimal: 32%)</div>
                                  <strong>Açıklama:</strong> Rumeni geçerek ince bağırsakta emilen protein. Yoğun süt üretiminde kritik. Amino asit profili önemli.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 53-58</div>
                                </div>
                              </div>
                            </td>
                            <td id="rup-requirement">-</td>
                            <td>kg/gün</td>
                            <td id="rup-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                MCP (Mikrobiyal Protein)
                                <div class="tooltip">
                                  <span class="tooltip-title">Mikrobiyal Protein Sentezi (Türetilen Değer)</span>
                                  <div class="tooltip-formula">MCP = TDN × 0.13 (TDN'den otomatik hesaplanır)</div>
                                  <strong>Açıklama:</strong> Rumen mikroorganizmaları tarafından sentezlenen protein. TDN miktarından otomatik hesaplanır. Amino asit kalitesi yüksek.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 48-53</div>
                                </div>
                              </div>
                            </td>
                            <td id="mcp-requirement">-</td>
                            <td>kg/gün</td>
                            <td id="mcp-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                MP (Metabolik Protein)
                                <div class="tooltip">
                                  <span class="tooltip-title">Metabolik Protein İhtiyacı (Türetilen Değer)</span>
                                  <div class="tooltip-formula">MP = (RUP × 0.8) + (MCP × 0.64) (RUP ve MCP'den otomatik hesaplanır)</div>
                                  <strong>Açıklama:</strong> Hayvana ulaşan gerçek protein. RUP'un %80'i + Mikrobiyal protein'in %64'ü. Gerçek protein ihtiyacı.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 58-63</div>
                                </div>
                              </div>
                            </td>
                            <td id="mp-requirement">-</td>
                            <td>kg/gün</td>
                            <td id="mp-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Metabolik Enerji
                                <div class="tooltip">
                                  <span class="tooltip-title">Metabolik Enerji İhtiyacı (ME)</span>
                                  <div class="tooltip-formula">ME = NEL ÷ 0.64, sonra MJ'ye çevir: ME × 4.184</div>
                                  <strong>Açıklama:</strong> NEL'den ME hesaplanır (%64 verimlilik), sonra MJ'ye çevrilir. NEL yaşama: 0.08 × BW^0.75, süt: 0.7 × FCM.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 25-30</div>
                                </div>
                              </div>
                            </td>
                            <td id="energy-requirement">-</td>
                            <td>MJ/gün</td>
                            <td id="energy-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                NEL (Net Enerji Laktasyon)
                                <div class="tooltip">
                                  <span class="tooltip-title">Net Enerji Laktasyon İhtiyacı</span>
                                  <div class="tooltip-formula">NEL = NEL_yaşama + NEL_süt + NEL_gebelik</div>
                                  <strong>Açıklama:</strong> Süt üretimi için kullanılan net enerji. Yaşama payı: 0.08 × BW^0.75, süt payı: 0.7 × FCM. En verimli enerji formu.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 30-35</div>
                                </div>
                              </div>
                            </td>
                            <td id="nel-requirement">-</td>
                            <td>Mcal/gün</td>
                            <td id="nel-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                NEM (Net Enerji Yaşama)
                                <div class="tooltip">
                                  <span class="tooltip-title">Net Enerji Yaşama İhtiyacı (Hayvan İhtiyacı)</span>
                                  <div class="tooltip-formula">NEM = 0.08 × BW^0.75 × aktivite_faktörü (Hayvan özelliklerinden hesaplanır)</div>
                                  <strong>Açıklama:</strong> Temel yaşam fonksiyonları için gereken net enerji. Solunum, dolaşım, sindirim, vücut ısısı için. Aktivite ile artar.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 35-40</div>
                                </div>
                              </div>
                            </td>
                            <td id="nem-requirement">-</td>
                            <td>Mcal/gün</td>
                            <td id="nem-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                NEL Süt (Net Enerji Süt Üretimi)
                                <div class="tooltip">
                                  <span class="tooltip-title">Süt Üretimi Enerji İhtiyacı</span>
                                  <div class="tooltip-formula">NEL_süt = FCM × 0.7 (FCM = süt × (0.4324 + 0.1624 × yağ%))</div>
                                  <strong>Açıklama:</strong> Süt üretimi için gerekli net enerji. %4 yağ düzeltmeli süt (FCM) üzerinden hesaplanır.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 20-25</div>
                                </div>
                              </div>
                            </td>
                            <td id="nel-milk-requirement">-</td>
                            <td>Mcal/gün</td>
                            <td id="nel-milk-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                NEL Gebelik (Net Enerji Gebelik)
                                <div class="tooltip">
                                  <span class="tooltip-title">Gebelik Enerji İhtiyacı</span>
                                  <div class="tooltip-formula">NEL_gebelik = Son trimester: 1.5 Mcal/gün</div>
                                  <strong>Açıklama:</strong> Gebelik için ek enerji ihtiyacı. Son trimesterde en yüksek, erken gebelikte minimal.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 25-30</div>
                                </div>
                              </div>
                            </td>
                            <td id="nel-pregnancy-requirement">-</td>
                            <td>Mcal/gün</td>
                            <td id="nel-pregnancy-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                TDN (Toplam Sindirilebilir Besin)
                                <div class="tooltip">
                                  <span class="tooltip-title">TDN İhtiyacı (Türetilen Değer)</span>
                                  <div class="tooltip-formula">TDN (% DM) = (NEL / 1.54) × 100 (NEL'den otomatik hesaplanır)</div>
                                  <strong>Açıklama:</strong> Toplam sindirilebilir besin maddesi. NEL değerinden otomatik hesaplanır. Yem kalitesinin önemli göstergesi.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 40-45</div>
                                </div>
                              </div>
                            </td>
                            <td id="tdn-requirement">-</td>
                            <td>% KM</td>
                            <td id="tdn-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                NFC (Non-Fiber Carbohydrate)
                                <div class="tooltip">
                                  <span class="tooltip-title">NFC İhtiyacı (Türetilen Değer)</span>
                                  <div class="tooltip-formula">NFC = 100 - (NDF + CP + EE + Ash) (Otomatik hesaplanır)</div>
                                  <strong>Açıklama:</strong> Fiber olmayan karbohidratlar. Nişasta ve şeker içerir. Hızlı enerji kaynağı.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 35-40</div>
                                </div>
                              </div>
                            </td>
                            <td id="nfc-requirement">-</td>
                            <td>% KM</td>
                            <td id="nfc-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                DE (Sindirilebilir Enerji)
                                <div class="tooltip">
                                  <span class="tooltip-title">Sindirilebilir Enerji İhtiyacı (Türetilen Değer)</span>
                                  <div class="tooltip-formula">DE = TDN × 4.4 Mcal/kg (TDN'den otomatik hesaplanır)</div>
                                  <strong>Açıklama:</strong> Brüt enerji - dışkı enerjisi. ME'den %18 daha yüksek. Enerji değerlendirme sisteminin ikinci basamağı.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 45-50</div>
                                </div>
                              </div>
                            </td>
                            <td id="de-requirement">-</td>
                            <td>Mcal/gün</td>
                            <td id="de-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Ham Yağ
                                <div class="tooltip">
                                  <span class="tooltip-title">Ham Yağ İhtiyacı (EE)</span>
                                  <div class="tooltip-formula">EE = DMI × 0.04 (4% KM)</div>
                                  <strong>Açıklama:</strong> Optimal enerji yoğunluğu ve süt yağı üretimi için. Maksimum %6, minimum %2 olmalı. Rumen asidozunu önlemek için kontrollü.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 54-56</div>
                                </div>
                              </div>
                            </td>
                            <td id="fat-requirement">-</td>
                            <td>g/gün</td>
                            <td id="fat-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Nişasta
                                <div class="tooltip">
                                  <span class="tooltip-title">Nişasta İhtiyacı</span>
                                  <div class="tooltip-formula">Nişasta = 20-30% KM (optimal: 25%)</div>
                                  <strong>Açıklama:</strong> Hızlı enerji kaynağı. Rumen mikroorganizmaları tarafından hızla fermente edilir. Aşırı miktarda asidoza neden olabilir.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 230-235</div>
                                </div>
                              </div>
                            </td>
                            <td id="starch-requirement">-</td>
                            <td>g/gün</td>
                            <td id="starch-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Şeker
                                <div class="tooltip">
                                  <span class="tooltip-title">Şeker İhtiyacı</span>
                                  <div class="tooltip-formula">Şeker = 3-8% KM (optimal: 5%)</div>
                                  <strong>Açıklama:</strong> Basit karbohidratlar. Hızlı enerji sağlar. Rumen mikroorganizmaları için kolay fermente edilebilir substrat.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 35-40</div>
                                </div>
                              </div>
                            </td>
                            <td id="sugar-requirement">-</td>
                            <td>g/gün</td>
                            <td id="sugar-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Kül (Ash)
                                <div class="tooltip">
                                  <span class="tooltip-title">Kül İhtiyacı</span>
                                  <div class="tooltip-formula">Kül = 6-10% KM (optimal: 8%)</div>
                                  <strong>Açıklama:</strong> Toplam mineral madde miktarı. Makro ve mikro minerallerin toplamını gösterir. Yem kalitesinin göstergesi.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 144-150</div>
                                </div>
                              </div>
                            </td>
                            <td id="ash-requirement">-</td>
                            <td>g/gün</td>
                            <td id="ash-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                NDF (Nötr Deterjan Fiber)
                                <div class="tooltip">
                                  <span class="tooltip-title">Nötr Deterjan Fiber (NDF)</span>
                                  <div class="tooltip-formula">NDF = 25-35% KM (optimal: 28%)</div>
                                  <strong>Açıklama:</strong> Rumen sağlığı için kritik fiber fraksiyonu. Çiğneme aktivitesini artırır, rumen pH'ını dengeler, asidozu önler.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 62-65</div>
                                </div>
                              </div>
                            </td>
                            <td id="ndf-requirement">-</td>
                            <td>g/gün</td>
                            <td id="ndf-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                ADF (Asit Deterjan Fiber)
                                <div class="tooltip">
                                  <span class="tooltip-title">Asit Deterjan Fiber (ADF)</span>
                                  <div class="tooltip-formula">ADF = 19-25% KM (optimal: 21%)</div>
                                  <strong>Açıklama:</strong> Sindirilebilirlik göstergesi. Selüloz + lignin içerir. Düşük ADF = yüksek sindirilebilirlik. Yem kalitesi belirleyicisi.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 65-70</div>
                                </div>
                              </div>
                            </td>
                            <td id="adf-requirement">-</td>
                            <td>g/gün</td>
                            <td id="adf-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Hemiselüloz
                                <div class="tooltip">
                                  <span class="tooltip-title">Hemiselüloz İçeriği</span>
                                  <div class="tooltip-formula">Hemiselüloz = NDF - ADF</div>
                                  <strong>Açıklama:</strong> Kolay sindirilebilir fiber fraksiyonu. Rumen mikroorganizmaları tarafından hızla fermente edilir. Enerji kaynağı.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 70-75</div>
                                </div>
                              </div>
                            </td>
                            <td id="hemicellulose-requirement">-</td>
                            <td>g/gün</td>
                            <td id="hemicellulose-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Selüloz
                                <div class="tooltip">
                                  <span class="tooltip-title">Selüloz İçeriği</span>
                                  <div class="tooltip-formula">Selüloz = ADF - Lignin</div>
                                  <strong>Açıklama:</strong> Orta sindirilebilir fiber. Rumen mikroorganizmaları tarafından yavaş fermente edilir. Yapısal karbohidrat.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 75-80</div>
                                </div>
                              </div>
                            </td>
                            <td id="cellulose-requirement">-</td>
                            <td>g/gün</td>
                            <td id="cellulose-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Lignin
                                <div class="tooltip">
                                  <span class="tooltip-title">Lignin İçeriği</span>
                                  <div class="tooltip-formula">Lignin = 2-8% KM (düşük = kaliteli yem)</div>
                                  <strong>Açıklama:</strong> Sindirilemeyen fiber fraksiyonu. Yem kalitesini düşürür. Yaşlı bitkilerde artar. Sindirilebilirliği sınırlar.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 80-85</div>
                                </div>
                              </div>
                            </td>
                            <td id="lignin-requirement">-</td>
                            <td>g/gün</td>
                            <td id="lignin-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td><strong>Mineraller</strong></td>
                            <td colspan="3" style="background: var(--bg-elevated); font-weight: bold; text-align: center;">Makro Mineraller</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Kalsiyum
                                <div class="tooltip">
                                  <span class="tooltip-title">Kalsiyum İhtiyacı (Ca)</span>
                                  <div class="tooltip-formula">Ca = (MP_yaşama × 1.54 + Süt_L × 1.22 + Gebelik) × 1000</div>
                                  <strong>Açıklama:</strong> Kemik sağlığı, kas kasılması ve süt üretimi için kritik. Sütte 1.22g/L Ca kaybı. Gebelikte fetal kemik gelişimi için ek ihtiyaç.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 150-155</div>
                                </div>
                              </div>
                            </td>
                            <td id="calcium-requirement">-</td>
                            <td>g/gün</td>
                            <td id="calcium-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Fosfor
                                <div class="tooltip">
                                  <span class="tooltip-title">Fosfor İhtiyacı (P)</span>
                                  <div class="tooltip-formula">P = (MP_yaşama × 0.77 + Süt_L × 0.90 + Gebelik) × 1000</div>
                                  <strong>Açıklama:</strong> Enerji metabolizması (ATP), kemik sağlığı ve süt üretimi. Ca:P oranı 1.5-2.0:1 olmalı. Sütte 0.90g/L P kaybı.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 155-160</div>
                                </div>
                              </div>
                            </td>
                            <td id="phosphorus-requirement">-</td>
                            <td>g/gün</td>
                            <td id="phosphorus-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Magnezyum
                                <div class="tooltip">
                                  <span class="tooltip-title">Magnezyum İhtiyacı (Mg)</span>
                                  <div class="tooltip-formula">Mg = BW × 0.04 + Süt_L × 0.15</div>
                                  <strong>Açıklama:</strong> Çayır tetanisi (hipomagnesemi) önlemi için kritik. Sinir-kas fonksiyonları, enzim aktivasyonu. Emilimi K ve NH4 ile azalır.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 160-165</div>
                                </div>
                              </div>
                            </td>
                            <td id="magnesium-requirement">-</td>
                            <td>g/gün</td>
                            <td id="magnesium-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Potasyum
                                <div class="tooltip">
                                  <span class="tooltip-title">Potasyum İhtiyacı (K)</span>
                                  <div class="tooltip-formula">K = BW × 0.1 + Süt_L × 1.5</div>
                                  <strong>Açıklama:</strong> Elektrolit dengesi, sinir iletimi, kas kasılması. Sütte yüksek kayıp (1.5g/L). Aşırı K, Mg emilimini engeller.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 165-170</div>
                                </div>
                              </div>
                            </td>
                            <td id="potassium-requirement">-</td>
                            <td>g/gün</td>
                            <td id="potassium-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Sodyum
                                <div class="tooltip">
                                  <span class="tooltip-title">Sodyum İhtiyacı (Na)</span>
                                  <div class="tooltip-formula">Na = BW × 0.05 + Süt_L × 0.4</div>
                                  <strong>Açıklama:</strong> Su dengesi, ozmotik basınç, sinir iletimi. Süt üretiminde kayıp. Tuz (NaCl) olarak verilir. K ile birlikte elektrolit dengesi.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 170-175</div>
                                </div>
                              </div>
                            </td>
                            <td id="sodium-requirement">-</td>
                            <td>g/gün</td>
                            <td id="sodium-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Klorür
                                <div class="tooltip">
                                  <span class="tooltip-title">Klorür İhtiyacı (Cl)</span>
                                  <div class="tooltip-formula">Cl = Na × 1.5</div>
                                  <strong>Açıklama:</strong> Elektrolit dengesi, asit-baz dengesi, mide asidi (HCl) üretimi. Sodyum ile birlikte ozmotik basıncı düzenler.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 170-175</div>
                                </div>
                              </div>
                            </td>
                            <td id="chlorine-requirement">-</td>
                            <td>g/gün</td>
                            <td id="chlorine-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td><strong>Trace Mineraller</strong></td>
                            <td colspan="3" style="background: var(--bg-elevated); font-weight: bold; text-align: center;">Mikro Mineraller</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Demir
                                <div class="tooltip">
                                  <span class="tooltip-title">Demir İhtiyacı (Fe)</span>
                                  <div class="tooltip-formula">Fe = DMI × 50 ppm</div>
                                  <strong>Açıklama:</strong> Hemoglobin ve miyoglobin sentezi, oksijen taşıma, elektron transferi. Eksikliği: anemi, yorgunluk, düşük performans.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 185-190</div>
                                </div>
                              </div>
                            </td>
                            <td id="iron-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="iron-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Çinko
                                <div class="tooltip">
                                  <span class="tooltip-title">Çinko İhtiyacı (Zn)</span>
                                  <div class="tooltip-formula">Zn = DMI × 40 ppm</div>
                                  <strong>Açıklama:</strong> Bağışıklık sistemi, yara iyileşmesi, üreme fonksiyonları, keratin sentezi. Eksikliği: dermatitis, düşük fertilite, büyüme geriliği.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 175-180</div>
                                </div>
                              </div>
                            </td>
                            <td id="zinc-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="zinc-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Bakır
                                <div class="tooltip">
                                  <span class="tooltip-title">Bakır İhtiyacı (Cu)</span>
                                  <div class="tooltip-formula">Cu = DMI × 10 ppm</div>
                                  <strong>Açıklama:</strong> Hemoglobin sentezi, kolajen oluşumu, enzim aktivasyonu, pigmentasyon. Mo ve S ile antagonizma. Eksikliği: anemi, kemik bozuklukları.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 180-185</div>
                                </div>
                              </div>
                            </td>
                            <td id="copper-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="copper-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Manganez
                                <div class="tooltip">
                                  <span class="tooltip-title">Manganez İhtiyacı (Mn)</span>
                                  <div class="tooltip-formula">Mn = DMI × 40 ppm</div>
                                  <strong>Açıklama:</strong> Kemik gelişimi, üreme fonksiyonları, karbohidrat metabolizması, antioksidan enzimler. Eksikliği: kemik deformasyonları, üreme sorunları.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 185-190</div>
                                </div>
                              </div>
                            </td>
                            <td id="manganese-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="manganese-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Selenyum
                                <div class="tooltip">
                                  <span class="tooltip-title">Selenyum İhtiyacı (Se)</span>
                                  <div class="tooltip-formula">Se = DMI × 0.3 ppm</div>
                                  <strong>Açıklama:</strong> Antioksidan (Vitamin E ile sinerjik), kas fonksiyonları, bağışıklık. Eksikliği: beyaz kas hastalığı, retained placenta, mastitis riski.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 190-195</div>
                                </div>
                              </div>
                            </td>
                            <td id="selenium-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="selenium-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                İyot
                                <div class="tooltip">
                                  <span class="tooltip-title">İyot İhtiyacı (I)</span>
                                  <div class="tooltip-formula">I = DMI × 0.5 ppm (gebelikte 0.8 ppm)</div>
                                  <strong>Açıklama:</strong> Tiroid hormonları (T3, T4) sentezi, metabolizma düzenleme, üreme fonksiyonları. Eksikliği: guatr, üreme sorunları, düşük süt verimi.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 195-200</div>
                                </div>
                              </div>
                            </td>
                            <td id="iodine-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="iodine-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Kobalt
                                <div class="tooltip">
                                  <span class="tooltip-title">Kobalt İhtiyacı (Co)</span>
                                  <div class="tooltip-formula">Co = DMI × 0.1 ppm</div>
                                  <strong>Açıklama:</strong> Vitamin B12 sentezi için gerekli, rumen mikroorganizmaları tarafından kullanılır. Eksikliği: B12 eksikliği, anemi, iştahsızlık.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 200-205</div>
                                </div>
                              </div>
                            </td>
                            <td id="cobalt-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="cobalt-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Molibden
                                <div class="tooltip">
                                  <span class="tooltip-title">Molibden İhtiyacı (Mo)</span>
                                  <div class="tooltip-formula">Mo = DMI × 0.15 ppm (maksimum 2 ppm - Cu antagonisti)</div>
                                  <strong>Açıklama:</strong> Enzim kofaktörü, ancak bakır antagonisti. Aşırı Mo bakır eksikliğine neden olur. Sülfat ile birlikte Cu emilimini engeller.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 205-210</div>
                                </div>
                              </div>
                            </td>
                            <td id="molybdenum-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="molybdenum-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Kükürt
                                <div class="tooltip">
                                  <span class="tooltip-title">Kükürt İhtiyacı (S)</span>
                                  <div class="tooltip-formula">S = DMI × 0.2% (maksimum 0.4% - Cu antagonisti)</div>
                                  <strong>Açıklama:</strong> Protein sentezi, amino asitler (metionin, sistein), ancak bakır antagonisti. Aşırı S bakır eksikliğine ve H2S zehirlenmesine neden olur.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 210-215</div>
                                </div>
                              </div>
                            </td>
                            <td id="sulfur-requirement">-</td>
                            <td>g/gün</td>
                            <td id="sulfur-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td><strong>Vitaminler</strong></td>
                            <td colspan="3" style="background: var(--bg-elevated); font-weight: bold; text-align: center;">Yağda Çözünen Vitaminler</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Vitamin A
                                <div class="tooltip">
                                  <span class="tooltip-title">Vitamin A İhtiyacı</span>
                                  <div class="tooltip-formula">Vit A = BW × 110 IU/kg</div>
                                  <strong>Açıklama:</strong> Görme, bağışıklık sistemi, üreme fonksiyonları, epitel doku sağlığı. Beta-karoten prekursörü. Eksikliği: gece körlüğü, üreme sorunları.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 195-200</div>
                                </div>
                              </div>
                            </td>
                            <td id="vitamin-a-requirement">-</td>
                            <td>IU/gün</td>
                            <td id="vitamin-a-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Vitamin D
                                <div class="tooltip">
                                  <span class="tooltip-title">Vitamin D İhtiyacı</span>
                                  <div class="tooltip-formula">Vit D = BW × 30 IU/kg</div>
                                  <strong>Açıklama:</strong> Kalsiyum ve fosfor emilimi, kemik mineralizasyonu. Güneş ışığı ile sentez. Eksikliği: rikets, osteomalacia, süt humması.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 200-205</div>
                                </div>
                              </div>
                            </td>
                            <td id="vitamin-d-requirement">-</td>
                            <td>IU/gün</td>
                            <td id="vitamin-d-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Vitamin E
                                <div class="tooltip">
                                  <span class="tooltip-title">Vitamin E İhtiyacı</span>
                                  <div class="tooltip-formula">Vit E = DMI × 15 IU/kg</div>
                                  <strong>Açıklama:</strong> Antioksidan (Se ile sinerjik), kas fonksiyonları, bağışıklık. Hücre zarı koruması. Eksikliği: beyaz kas hastalığı, retained placenta.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 205-210</div>
                                </div>
                              </div>
                            </td>
                            <td id="vitamin-e-requirement">-</td>
                            <td>IU/gün</td>
                            <td id="vitamin-e-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td><strong>B-Kompleks Vitaminler</strong></td>
                            <td colspan="3" style="background: var(--bg-elevated); font-weight: bold; text-align: center;">Suda Çözünen Vitaminler</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Thiamin (B1)
                                <div class="tooltip">
                                  <span class="tooltip-title">Thiamin (Vitamin B1) İhtiyacı</span>
                                  <div class="tooltip-formula">B1 = DMI × 2 mg/kg (stres durumunda 3-4 mg/kg)</div>
                                  <strong>Açıklama:</strong> Karbohidrat metabolizması, sinir sistemi fonksiyonları. Rumen mikroorganizmaları tarafından sentezlenir ancak yoğun üretimde yetersiz kalabilir.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 210-215</div>
                                </div>
                              </div>
                            </td>
                            <td id="thiamin-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="thiamin-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Riboflavin (B2)
                                <div class="tooltip">
                                  <span class="tooltip-title">Riboflavin (Vitamin B2) İhtiyacı</span>
                                  <div class="tooltip-formula">B2 = DMI × 3 mg/kg + Süt_L × 2 mg/L</div>
                                  <strong>Açıklama:</strong> Enerji metabolizması, FAD/FMN koenzimlerinin bileşeni. Süt üretiminde kayıp yüksek. Rumen sentezi genellikle yeterli.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 215-220</div>
                                </div>
                              </div>
                            </td>
                            <td id="riboflavin-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="riboflavin-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Niacin (B3)
                                <div class="tooltip">
                                  <span class="tooltip-title">Niacin (Vitamin B3) İhtiyacı</span>
                                  <div class="tooltip-formula">B3 = DMI × 15 mg/kg (yoğun üretimde 20-25 mg/kg)</div>
                                  <strong>Açıklama:</strong> NAD/NADP koenzimlerinin bileşeni, enerji metabolizması. Rumen mikroorganizmaları sentezler ancak yoğun süt üretiminde takviye gerekebilir.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 220-225</div>
                                </div>
                              </div>
                            </td>
                            <td id="niacin-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="niacin-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Biotin (B7)
                                <div class="tooltip">
                                  <span class="tooltip-title">Biotin (Vitamin B7) İhtiyacı</span>
                                  <div class="tooltip-formula">Biotin = BW × 0.4 mg/100kg (topallık önlemi için 20 mg/gün)</div>
                                  <strong>Açıklama:</strong> Tırnak sağlığı, topallık önlemi, yağ asidi sentezi. Rumen sentezi var ancak tırnak problemlerinde takviye kritik.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 225-230</div>
                                </div>
                              </div>
                            </td>
                            <td id="biotin-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="biotin-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Folat (B9)
                                <div class="tooltip">
                                  <span class="tooltip-title">Folat (Vitamin B9) İhtiyacı</span>
                                  <div class="tooltip-formula">Folat = DMI × 0.5 mg/kg (gebelikte 0.8 mg/kg)</div>
                                  <strong>Açıklama:</strong> DNA sentezi, hücre bölünmesi, amino asit metabolizması. Gebelik ve hızlı büyümede kritik. Rumen mikroorganizmaları sentezler.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 230-235</div>
                                </div>
                              </div>
                            </td>
                            <td id="folate-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="folate-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Cobalamin (B12)
                                <div class="tooltip">
                                  <span class="tooltip-title">Cobalamin (Vitamin B12) İhtiyacı</span>
                                  <div class="tooltip-formula">B12 = BW × 0.5 μg/kg (kobalt yeterli ise rumen sentezi)</div>
                                  <strong>Açıklama:</strong> DNA sentezi, sinir sistemi, kobalt gerektirir. Rumen mikroorganizmaları kobalttan sentezler. Kobalt eksikliğinde B12 eksikliği.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 235-240</div>
                                </div>
                              </div>
                            </td>
                            <td id="cobalamin-requirement">-</td>
                            <td>μg/gün</td>
                            <td id="cobalamin-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Vitamin K
                                <div class="tooltip">
                                  <span class="tooltip-title">Vitamin K İhtiyacı</span>
                                  <div class="tooltip-formula">Vit K = DMI × 1 mg/kg (kanama durumunda 2-3 mg/kg)</div>
                                  <strong>Açıklama:</strong> Kan pıhtılaşması, kemik metabolizması. Rumen mikroorganizmaları sentezler ancak antibiyotik kullanımında takviye gerekebilir.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 240-245</div>
                                </div>
                              </div>
                            </td>
                            <td id="vitamin-k-requirement">-</td>
                            <td>mg/gün</td>
                            <td id="vitamin-k-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td><strong>Anti-besin Faktörleri</strong></td>
                            <td colspan="3" style="background: var(--bg-elevated); font-weight: bold; text-align: center;">Besin Emilimini Engelleyen Faktörler</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Tanen
                                <div class="tooltip">
                                  <span class="tooltip-title">Tanen İçeriği</span>
                                  <div class="tooltip-formula">Tanen = <2% KM (güvenli seviye)</div>
                                  <strong>Açıklama:</strong> Protein ve mineral emilimini engeller. Ağızda buruk tat. Yüksek tanen: protein sindirimi ↓, Fe/Zn emilimi ↓. Eski yapraklar ve kabukta yüksek.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 220-225</div>
                                </div>
                              </div>
                            </td>
                            <td id="tannin-requirement">-</td>
                            <td>g/gün</td>
                            <td id="tannin-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Fitat (Fitik Asit)
                                <div class="tooltip">
                                  <span class="tooltip-title">Fitat İçeriği</span>
                                  <div class="tooltip-formula">Fitat = <1% KM (güvenli seviye)</div>
                                  <strong>Açıklama:</strong> Mineral şelatörü. Ca, P, Zn, Fe, Mg emilimini engeller. Tahıl ve baklagillerde yüksek. Fitaz enzimi ile parçalanabilir.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 225-230</div>
                                </div>
                              </div>
                            </td>
                            <td id="phytate-requirement">-</td>
                            <td>g/gün</td>
                            <td id="phytate-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Nişasta Yapısı (Amiloz/Amilopektin)
                                <div class="tooltip">
                                  <span class="tooltip-title">Nişasta Sindirilebilirliği</span>
                                  <div class="tooltip-formula">Sindirilebilir Nişasta = 25-30% KM (optimal)</div>
                                  <strong>Açıklama:</strong> Amiloz (yavaş) vs Amilopektin (hızlı) oranı. Hızlı nişasta: asidoz riski. Yavaş nişasta: daha güvenli enerji. İşleme yöntemi kritik.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 230-235</div>
                                </div>
                              </div>
                            </td>
                            <td id="starch-digestibility">-</td>
                            <td>% KM</td>
                            <td id="starch-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Saponin
                                <div class="tooltip">
                                  <span class="tooltip-title">Saponin İçeriği</span>
                                  <div class="tooltip-formula">Saponin = <0.5% KM (güvenli seviye)</div>
                                  <strong>Açıklama:</strong> Hücre zarı geçirgenliğini artırır. Yüksek dozda: hemoliz, sindirim bozukluğu. Yonca ve bazı baklagillerde bulunur.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 235-240</div>
                                </div>
                              </div>
                            </td>
                            <td id="saponin-requirement">-</td>
                            <td>g/gün</td>
                            <td id="saponin-source">Hesaplama yapılmadı</td>
                          </tr>
                          <tr>
                            <td>
                              <div class="tooltip-container">
                                Oksalat
                                <div class="tooltip">
                                  <span class="tooltip-title">Oksalat İçeriği</span>
                                  <div class="tooltip-formula">Oksalat = <2% KM (güvenli seviye)</div>
                                  <strong>Açıklama:</strong> Kalsiyum şelatörü, böbrek taşı riski. Ca-oksalat kompleksi emilmez. Ispanak, pancar yaprakları, bazı otlarda yüksek.
                                  <div class="tooltip-source">Kaynak: NRC 2001 Dairy Cattle, Sayfa 240-245</div>
                                </div>
                              </div>
                            </td>
                            <td id="oxalate-requirement">-</td>
                            <td>g/gün</td>
                            <td id="oxalate-source">Hesaplama yapılmadı</td>
                          </tr>
                        </tbody>
                      </table>
                      </div>
                </div>
              </div>
            </div>

            <!-- Middle Row: Ration Builder (Full Width) -->
                <div class="feed-ration-bottom-row">
                  <div class="feed-ration-manager">
                    <div class="feed-ration-manager-header">
                      <div class="header-left">
                        <h3 class="feed-ration-manager-title">Rasyon Oluşturma</h3>
                      </div>
                      <div class="header-center">
                      </div>
                      <div class="header-right">
                        <div class="feed-ration-manager-actions">
                            <button class="btn btn-sm btn-danger" id="clear-ration-btn">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6m3 0V4c0-1 1-2 2-2h4c0 1 1 2 2 2v2"></path>
                                <line x1="10" y1="11" x2="10" y2="17"></line>
                                <line x1="14" y1="11" x2="14" y2="17"></line>
                              </svg>
                              Tabloyu Temizle
                            </button>
                          <button class="btn btn-sm btn-success" id="save-ration-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                              <polyline points="17,21 17,13 7,13 7,21"></polyline>
                              <polyline points="7,3 7,8 15,8"></polyline>
                            </svg>
                            Rasyonu Kaydet
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="feed-ration-manager-body">
                      <div class="feed-ration-content">
                        <div class="ration-builder-container">
                          <div class="table-container">
                            <table class="modern-table" id="ration-builder-table">
                              <thead>
                                <tr>
                                  <th>Yem</th>
                                  <th>Mik. (kg)</th>
                                  <th>KM (%)</th>
                                  <th>HP (%)</th>
                                  <th>ME (MJ/kg)</th>
                                  <th>Fiyat (₺/kg)</th>
                                  <th>T.Maliyet (₺)</th>
                                  <th>T.Protein (g)</th>
                                  <th>T.Enerji (MJ)</th>
                                  <th>İşlem</th>
                                </tr>
                              </thead>
                              <tbody id="ration-builder-tbody">
                                <!-- Dinamik olarak eklenecek satırlar -->
                              </tbody>
                              <tfoot>
                                <tr class="totals-row">
                                  <td><strong>TOPLAM</strong></td>
                                  <td><strong id="total-amount">0.0</strong></td>
                                  <td>-</td>
                                  <td>-</td>
                                  <td>-</td>
                                  <td>-</td>
                                  <td><strong id="total-cost">0.0</strong></td>
                                  <td><strong id="total-protein">0</strong></td>
                                  <td><strong id="total-energy">0.0</strong></td>
                                  <td>-</td>
                                </tr>
                              </tfoot>
                            </table>
                          </div>

                          <!-- Yem Ekleme Footer -->
                          <div class="ration-builder-footer">
                            <div class="add-feed-form">
                              <div class="form-group">
                                <label for="feed-select">Yem Seçin:</label>
                                <select id="feed-select" class="form-control">
                                  <option value="">Yem seçiniz...</option>
                                  <!-- Yemler dinamik olarak yüklenecek -->
                                </select>
                              </div>
                              <div class="form-group">
                                <label for="feed-amount">Miktar (kg):</label>
                                <input type="number" id="feed-amount" class="form-control" min="0" step="0.1" placeholder="0.0">
                              </div>
                              <div class="form-group">
                                <button type="button" id="add-feed-btn" class="btn btn-primary">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                  </svg>
                                  Yem Ekle
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Ration Validation Row (Full Width) -->
                <div class="feed-ration-bottom-row" id="ration-validation-section" style="display: none;">
                  <div class="feed-ration-manager">
                    <div class="feed-ration-manager-header">
                      <h3 class="feed-ration-manager-title">Rasyon Değerlendirmesi</h3>
                      <div class="feed-ration-manager-actions">
                        <!-- Rasyon Değerlendirme Progress Bar -->
                        <div class="ration-evaluation-container" id="ration-evaluation-container" style="display: none; margin-right: 16px;">
                          <div class="circular-progress-evaluation" id="circular-progress-evaluation">
                            <svg class="progress-ring-evaluation" width="80" height="80" viewBox="0 0 80 80">
                              <circle class="progress-ring-background-evaluation" cx="40" cy="40" r="32"/>
                              <circle class="progress-ring-progress-evaluation" cx="40" cy="40" r="32"/>
                            </svg>
                            <div class="progress-text-evaluation">
                              <span class="score-value-evaluation" id="score-value-evaluation">0</span>
                              <span class="score-unit-evaluation">%</span>
                              <div class="score-label-evaluation">Karşılanma</div>
                            </div>
                          </div>
                        </div>
                        <button class="btn btn-sm btn-info" id="validate-ration-btn">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M9 12l2 2 4-4"></path>
                            <circle cx="12" cy="12" r="10"></circle>
                          </svg>
                          Rasyonu Değerlendir
                        </button>
                        <button class="btn btn-sm btn-secondary" id="detailed-analysis-btn" style="display: none;">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                          </svg>
                          Detaylı Analiz (47 Besin Değeri)
                        </button>
                      </div>
                    </div>
                    <div class="feed-ration-manager-body">
                      <div class="feed-ration-content">
                        <div class="ration-validation-container">
                          <!-- Validation Results Table -->
                          <div class="table-container">
                            <table class="modern-table" id="validation-results-table">
                              <thead>
                                <tr>
                                  <th>Besin Öğesi</th>
                                  <th>Gerekli</th>
                                  <th>Sağlanan</th>
                                  <th>Fark</th>
                                  <th>Durum</th>
                                  <th>Öneri</th>
                                </tr>
                              </thead>
                              <tbody id="validation-results-tbody">
                                <!-- Validation results will be populated here -->
                              </tbody>
                            </table>
                          </div>

                          <!-- Overall Assessment -->
                          <div class="validation-summary" id="validation-summary">
                            <div class="summary-card">
                              <h4>Genel Değerlendirme</h4>
                              <div class="summary-content" id="summary-content">
                                <p>Rasyon değerlendirmesi için önce besin ihtiyaçlarını hesaplayın ve rasyon oluşturun.</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>


              </div>
            </div>
          </div>
        </div>

        <!-- Feeds Tab Content -->
        <div class="feed-ration-content-section" id="feeds-section">
          <div class="card">
            <div class="card-header">
              <div class="card-header-content">
                <h3 class="card-title">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                    <path d="M2 17l10 5 10-5"></path>
                    <path d="M2 12l10 5 10-5"></path>
                  </svg>
                  Yem Kütüphanesi
                </h3>
                <button class="btn btn-primary" id="add-feed-modal-btn">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                  Yeni Yem Ekle
                </button>
              </div>
            </div>
            <div class="card-body">
              <div class="table-container">
                <table class="modern-table">
                  <thead>
                    <tr>
                      <th>Yem Adı</th>
                      <th>Kuru Madde (%)</th>
                      <th>Ham Protein (%)</th>
                      <th>Metabolik Enerji (MJ/kg)</th>
                      <th>Fiyat (₺/kg)</th>
                      <th>İşlemler</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Yemler dinamik olarak yüklenecek -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- Saved Rations Tab Content -->
        <div class="feed-ration-content-section" id="saved-section">
          <div class="card">
            <div class="card-header">
              <h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                </svg>
                Kaydedilen Rasyonlar
              </h3>
              <button class="btn btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14,2 14,8 20,8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                </svg>
                Rapor Al
              </button>
            </div>
            <div class="card-body">
              <div class="table-container">
                <table class="modern-table">
                  <thead>
                    <tr>
                      <th>Rasyon Adı</th>
                      <th>Hayvan Kategorisi</th>
                      <th>Oluşturma Tarihi</th>
                      <th>Toplam Maliyet (₺/gün)</th>
                      <th>Durum</th>
                      <th>İşlemler</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Süt İneği Rasyonu #1</td>
                      <td>Süt İneği</td>
                      <td>15.12.2024</td>
                      <td>42.50</td>
                      <td><span class="badge badge-success">Aktif</span></td>
                      <td>
                        <button class="btn btn-sm btn-primary">Görüntüle</button>
                        <button class="btn btn-sm btn-outline">Düzenle</button>
                        <button class="btn btn-sm btn-danger">Sil</button>
                      </td>
                    </tr>
                    <tr>
                      <td>Buzağı Rasyonu #1</td>
                      <td>Buzağı</td>
                      <td>10.12.2024</td>
                      <td>18.30</td>
                      <td><span class="badge badge-warning">Taslak</span></td>
                      <td>
                        <button class="btn btn-sm btn-primary">Görüntüle</button>
                        <button class="btn btn-sm btn-outline">Düzenle</button>
                        <button class="btn btn-sm btn-danger">Sil</button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Feed Modal -->
    <div class="modal-overlay" id="add-feed-modal" style="display: none;">
      <div class="modal-container">
        <div class="modal-header">
          <h3 class="modal-title">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
            Yeni Yem Ekle
          </h3>
          <button class="modal-close" id="close-feed-modal">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="modal-body">
          <form id="add-feed-form" class="feed-form">
            <!-- Temel Bilgiler -->
            <div class="form-section">
              <h4 class="form-section-title">Temel Bilgiler</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label for="feed-name">Yem Adı *</label>
                  <input type="text" id="feed-name" name="feedName" required placeholder="Örn: Kuru Ot">
                </div>
                <div class="form-group">
                  <label for="feed-type">Yem Tipi</label>
                  <select id="feed-type" name="feedType">
                    <option value="">Seçiniz</option>
                    <option value="kaba-yem">Kaba Yem</option>
                    <option value="konsantre">Konsantre Yem</option>
                    <option value="silaj">Silaj</option>
                    <option value="mineral">Mineral Karışım</option>
                    <option value="vitamin">Vitamin Karışım</option>
                    <option value="diger">Diğer</option>
                  </select>
                </div>
                <div class="form-group full-width">
                  <label for="feed-description">Açıklama</label>
                  <textarea id="feed-description" name="feedDescription" rows="2" placeholder="Yem hakkında açıklama..."></textarea>
                </div>
              </div>
            </div>

            <!-- Temel Besin Değerleri -->
            <div class="form-section">
              <h4 class="form-section-title">Temel Besin Değerleri</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label for="dry-matter">Kuru Madde (% DM)</label>
                  <input type="number" id="dry-matter" name="dryMatter" step="0.1" min="0" max="100" placeholder="88.5" title="Kuru madde yüzdesi (% DM)">
                </div>
                <div class="form-group">
                  <label for="crude-protein">Ham Protein (% DM)</label>
                  <input type="number" id="crude-protein" name="crudeProtein" step="0.1" min="0" max="100" placeholder="12.5" title="Ham protein yüzdesi (% DM)">
                </div>
                <div class="form-group">
                  <label for="rdp">RDP (% CP)</label>
                  <input type="number" id="rdp" name="rdp" step="0.1" min="0" max="100" placeholder="68.0" title="Rumen parçalanabilir protein (% Ham Protein)">
                </div>
                <div class="form-group">
                  <label for="rup">RUP (% CP)</label>
                  <input type="number" id="rup" name="rup" step="0.1" min="0" max="100" placeholder="32.0" title="Rumen parçalanmayan protein (% Ham Protein)">
                </div>

                <div class="form-group">
                  <label for="metabolic-energy">Metabolik Enerji (MJ/kg DM)</label>
                  <input type="number" id="metabolic-energy" name="metabolicEnergy" step="0.1" min="0" placeholder="9.2" title="Metabolik enerji (MJ/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="nel">NEL (Mcal/kg DM)</label>
                  <input type="number" id="nel" name="nel" step="0.01" min="0" placeholder="1.45" title="Net enerji laktasyon (Mcal/kg kuru madde)">
                </div>



                <div class="form-group">
                  <label for="crude-fat">Ham Yağ (% DM)</label>
                  <input type="number" id="crude-fat" name="crudeFat" step="0.1" min="0" max="100" placeholder="3.8" title="Ham yağ yüzdesi (% DM)">
                </div>
              </div>
            </div>

            <!-- Karbohidrat Fraksiyonları -->
            <div class="form-section">
              <h4 class="form-section-title">Karbohidrat Fraksiyonları</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label for="starch">Nişasta (% DM)</label>
                  <input type="number" id="starch" name="starch" step="0.1" min="0" max="100" placeholder="25.0" title="Nişasta yüzdesi (% DM)">
                </div>
                <div class="form-group">
                  <label for="sugar">Şeker (% DM)</label>
                  <input type="number" id="sugar" name="sugar" step="0.1" min="0" max="50" placeholder="8.0" title="Şeker yüzdesi (% DM)">
                </div>
                <div class="form-group">
                  <label for="ash">Kül (% DM)</label>
                  <input type="number" id="ash" name="ash" step="0.1" min="0" max="20" placeholder="8.0" title="Kül (mineral madde) yüzdesi (% DM)">
                </div>
              </div>
            </div>

            <!-- Fiber Fraksiyonları -->
            <div class="form-section">
              <h4 class="form-section-title">Fiber Fraksiyonları</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label for="ndf">NDF (% DM)</label>
                  <input type="number" id="ndf" name="ndf" step="0.1" min="0" max="100" placeholder="45.0" title="Nötr deterjan fiber (% DM)">
                </div>
                <div class="form-group">
                  <label for="adf">ADF (% DM)</label>
                  <input type="number" id="adf" name="adf" step="0.1" min="0" max="100" placeholder="28.0" title="Asit deterjan fiber (% DM)">
                </div>
                <div class="form-group">
                  <label for="hemicellulose">Hemiselüloz (% DM)</label>
                  <input type="number" id="hemicellulose" name="hemicellulose" step="0.1" min="0" max="100" placeholder="17.0" title="Hemiselüloz (% DM)">
                </div>
                <div class="form-group">
                  <label for="cellulose">Selüloz (% DM)</label>
                  <input type="number" id="cellulose" name="cellulose" step="0.1" min="0" max="100" placeholder="22.0" title="Selüloz (% DM)">
                </div>
                <div class="form-group">
                  <label for="lignin">Lignin (% DM)</label>
                  <input type="number" id="lignin" name="lignin" step="0.1" min="0" max="100" placeholder="6.0" title="Lignin (% DM)">
                </div>
              </div>
            </div>

            <!-- Makro Mineraller -->
            <div class="form-section">
              <h4 class="form-section-title">Makro Mineraller</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label for="calcium">Kalsiyum (% DM)</label>
                  <input type="number" id="calcium" name="calcium" step="0.01" min="0" max="10" placeholder="0.65" title="Kalsiyum (% kuru madde)">
                </div>
                <div class="form-group">
                  <label for="phosphorus">Fosfor (% DM)</label>
                  <input type="number" id="phosphorus" name="phosphorus" step="0.01" min="0" max="10" placeholder="0.45" title="Fosfor (% kuru madde)">
                </div>
                <div class="form-group">
                  <label for="magnesium">Magnezyum (% DM)</label>
                  <input type="number" id="magnesium" name="magnesium" step="0.01" min="0" max="10" placeholder="0.25" title="Magnezyum (% kuru madde)">
                </div>
                <div class="form-group">
                  <label for="potassium">Potasyum (% DM)</label>
                  <input type="number" id="potassium" name="potassium" step="0.01" min="0" max="10" placeholder="1.2" title="Potasyum (% kuru madde)">
                </div>
                <div class="form-group">
                  <label for="sodium">Sodyum (% DM)</label>
                  <input type="number" id="sodium" name="sodium" step="0.01" min="0" max="10" placeholder="0.18" title="Sodyum (% kuru madde)">
                </div>
                <div class="form-group">
                  <label for="chloride">Klorür (% DM)</label>
                  <input type="number" id="chloride" name="chloride" step="0.01" min="0" max="10" placeholder="0.25" title="Klorür (% kuru madde)">
                </div>
                <div class="form-group">
                  <label for="sulfur">Kükürt (% DM)</label>
                  <input type="number" id="sulfur" name="sulfur" step="0.01" min="0" max="10" placeholder="0.20" title="Kükürt (% kuru madde)">
                </div>
              </div>
            </div>

            <!-- Mikro Mineraller -->
            <div class="form-section">
              <h4 class="form-section-title">Mikro Mineraller (mg/kg DM)</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label for="iron">Demir (mg/kg DM)</label>
                  <input type="number" id="iron" name="iron" step="1" min="0" placeholder="150" title="Demir (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="zinc">Çinko (mg/kg DM)</label>
                  <input type="number" id="zinc" name="zinc" step="1" min="0" placeholder="40" title="Çinko (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="copper">Bakır (mg/kg DM)</label>
                  <input type="number" id="copper" name="copper" step="1" min="0" placeholder="10" title="Bakır (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="manganese">Manganez (mg/kg DM)</label>
                  <input type="number" id="manganese" name="manganese" step="1" min="0" placeholder="40" title="Manganez (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="selenium">Selenyum (mg/kg DM)</label>
                  <input type="number" id="selenium" name="selenium" step="0.1" min="0" placeholder="0.3" title="Selenyum (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="iodine">İyot (mg/kg DM)</label>
                  <input type="number" id="iodine" name="iodine" step="0.1" min="0" placeholder="0.5" title="İyot (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="cobalt">Kobalt (mg/kg DM)</label>
                  <input type="number" id="cobalt" name="cobalt" step="0.1" min="0" placeholder="0.1" title="Kobalt (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="molybdenum">Molibden (mg/kg DM)</label>
                  <input type="number" id="molybdenum" name="molybdenum" step="0.1" min="0" placeholder="1.0" title="Molibden (mg/kg kuru madde)">
                </div>
              </div>
            </div>

            <!-- Vitaminler -->
            <div class="form-section">
              <h4 class="form-section-title">Vitaminler</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label for="vitamin-a">Vitamin A (IU/kg DM)</label>
                  <input type="number" id="vitamin-a" name="vitaminA" step="100" min="0" placeholder="Yem türüne göre: Kaba yem 5000-15000, Konsantre 0-300" title="Vitamin A (IU/kg kuru madde) - Kaba yemler: 5000-15000, Konsantre yemler: 0-300">
                </div>
                <div class="form-group">
                  <label for="vitamin-d">Vitamin D (IU/kg DM)</label>
                  <input type="number" id="vitamin-d" name="vitaminD" step="100" min="0" placeholder="3000" title="Vitamin D (IU/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="vitamin-e">Vitamin E (IU/kg DM)</label>
                  <input type="number" id="vitamin-e" name="vitaminE" step="10" min="0" placeholder="50" title="Vitamin E (IU/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="vitamin-k">Vitamin K (mg/kg DM)</label>
                  <input type="number" id="vitamin-k" name="vitaminK" step="0.1" min="0" placeholder="2.0" title="Vitamin K (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="thiamin">Thiamin/B1 (mg/kg DM)</label>
                  <input type="number" id="thiamin" name="thiamin" step="0.1" min="0" placeholder="4.0" title="Thiamin/B1 (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="riboflavin">Riboflavin/B2 (mg/kg DM)</label>
                  <input type="number" id="riboflavin" name="riboflavin" step="0.1" min="0" placeholder="6.0" title="Riboflavin/B2 (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="niacin">Niacin/B3 (mg/kg DM)</label>
                  <input type="number" id="niacin" name="niacin" step="0.1" min="0" placeholder="30" title="Niacin/B3 (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="biotin">Biotin/B7 (mg/kg DM)</label>
                  <input type="number" id="biotin" name="biotin" step="0.01" min="0" placeholder="0.20" title="Biotin/B7 (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="folate">Folat/B9 (mg/kg DM)</label>
                  <input type="number" id="folate" name="folate" step="0.1" min="0" placeholder="1.0" title="Folat/B9 (mg/kg kuru madde)">
                </div>
                <div class="form-group">
                  <label for="cobalamin">Cobalamin/B12 (μg/kg DM)</label>
                  <input type="number" id="cobalamin" name="cobalamin" step="0.1" min="0" placeholder="15" title="Cobalamin/B12 (μg/kg kuru madde)">
                </div>
              </div>
            </div>

            <!-- Anti-besin Faktörleri -->
            <div class="form-section">
              <h4 class="form-section-title">Anti-besin Faktörleri</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label for="tannin">Tanen (% DM)</label>
                  <input type="number" id="tannin" name="tannin" step="0.01" min="0" max="10" placeholder="0.5" title="Tanen (% kuru madde)">
                </div>
                <div class="form-group">
                  <label for="phytate">Fitat (% DM)</label>
                  <input type="number" id="phytate" name="phytate" step="0.01" min="0" max="10" placeholder="0.3" title="Fitat (% kuru madde)">
                </div>
                <div class="form-group">
                  <label for="saponin">Saponin (% DM)</label>
                  <input type="number" id="saponin" name="saponin" step="0.01" min="0" max="10" placeholder="0.2" title="Saponin (% kuru madde)">
                </div>
                <div class="form-group">
                  <label for="oxalate">Oksalat (% DM)</label>
                  <input type="number" id="oxalate" name="oxalate" step="0.01" min="0" max="10" placeholder="0.1" title="Oksalat (% kuru madde)">
                </div>
              </div>
            </div>

            <!-- Ekonomik Bilgiler -->
            <div class="form-section">
              <h4 class="form-section-title">Ekonomik Bilgiler</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label for="unit-price">Birim Fiyat (₺/kg)</label>
                  <input type="number" id="unit-price" name="unitPrice" step="0.01" min="0" placeholder="1.50">
                </div>
                <div class="form-group">
                  <label for="supplier">Tedarikçi</label>
                  <select id="supplier" name="supplier">
                    <option value="">Seçiniz</option>
                    <!-- Tedarikçiler dinamik olarak yüklenecek -->
                  </select>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" id="cancel-feed-modal">İptal</button>
          <button type="submit" form="add-feed-form" class="btn btn-primary" id="save-feed-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
              <polyline points="17,21 17,13 7,13 7,21"></polyline>
              <polyline points="7,3 7,8 15,8"></polyline>
            </svg>
            Yemi Kaydet
          </button>
        </div>
      </div>
    </div>
  `;

  // Tab functionality
  setupTabFunctionality();

  // Ration builder functionality
  setupRationBuilder();

  // Ration validation functionality
  setupRationValidation();

  // Feed management functionality
  setupFeedManagement();

  // Nutritional calculation functionality
  setupNutritionalCalculation();

  // Initialize tooltip positioning for the nutritional needs table
  setTimeout(() => {
    setupTooltipPositioning();
  }, 100);

  // Setup export and print functionality
  setupExportPrintFunctionality();

  // Load saved rations when page loads
  await loadSavedRations();
}

function setupTabFunctionality() {
  const navTabButtons = document.querySelectorAll('.feed-ration-tab');
  const contentSections = document.querySelectorAll('.feed-ration-content-section');

  navTabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const targetTab = button.getAttribute('data-tab');

      // Remove active class from all nav tabs
      navTabButtons.forEach(tab => tab.classList.remove('active'));

      // Add active class to clicked tab
      button.classList.add('active');

      // Hide all content sections
      contentSections.forEach(section => section.classList.remove('active'));

      // Show target content section
      const targetSection = document.getElementById(targetTab + '-section');
      if (targetSection) {
        targetSection.classList.add('active');
        
        // Load saved rations when saved tab is activated
        if (targetTab === 'saved') {
          loadSavedRations();
        }
      }
    });
  });

  // Setup manager tabs functionality (for tabs within manager cards)
  const managerTabButtons = document.querySelectorAll('.feed-ration-manager .feed-ration-tab');

  managerTabButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remove active class from all tabs in the same manager
      const manager = button.closest('.feed-ration-manager');
      const managerTabs = manager.querySelectorAll('.feed-ration-tab');
      managerTabs.forEach(tab => tab.classList.remove('active'));

      // Add active class to clicked tab
      button.classList.add('active');
    });
  });
}

function setupRationBuilder() {
  const addFeedBtn = document.getElementById('add-feed-btn');
  const feedSelect = document.getElementById('feed-select');
  const feedAmount = document.getElementById('feed-amount');
  const rationTbody = document.getElementById('ration-builder-tbody');
  const saveRationBtn = document.getElementById('save-ration-btn');
  // Smart suggestion button removed

  let rationItems = [];
  let availableFeeds = [];

  // Load feeds from database for ration builder
  async function loadFeedsForRation() {
    try {
      const feeds = await window.api.invoke('get-feeds', 1);
      availableFeeds = feeds;
      populateFeedSelect();
    } catch (error) {
      console.error('Error loading feeds for ration:', error);
      // Fallback to default feeds if database fails
      loadDefaultFeeds();
    }
  }





  function populateFeedSelect() {
    // Clear existing options except the first one
    feedSelect.innerHTML = '<option value="">Yem seçiniz...</option>';

    availableFeeds.forEach(feed => {
      const option = document.createElement('option');
      option.value = feed.Id || feed.id;
      option.textContent = feed.YemAdi || feed.name;

      // Set data attributes for nutritional values using correct database field names
      option.setAttribute('data-dm', feed.KuruMadde || feed.dryMatter || 88);
      option.setAttribute('data-protein', feed.HamProtein || feed.crudeProtein || 12);
      option.setAttribute('data-energy', feed.MetabolikEnerji || feed.metabolicEnergy || 10);
      option.setAttribute('data-calcium', feed.Kalsiyum || feed.calcium || 5);
      option.setAttribute('data-phosphorus', feed.Fosfor || feed.phosphorus || 3);
      option.setAttribute('data-magnesium', feed.Magnezyum || feed.magnesium || 2);
      option.setAttribute('data-potassium', feed.Potasyum || feed.potassium || 10);
      option.setAttribute('data-sodium', feed.Sodyum || feed.sodium || 1);

      feedSelect.appendChild(option);
    });
  }

  function loadDefaultFeeds() {
    // Fallback default feeds if database is not available
    const defaultFeeds = [
      { id: 'kuru-ot', name: 'Kuru Ot', dryMatter: 88.5, crudeProtein: 12.5, metabolicEnergy: 9.2 },
      { id: 'konsantre', name: 'Konsantre Yem', dryMatter: 90.0, crudeProtein: 18.0, metabolicEnergy: 12.8 },
      { id: 'misir-silaji', name: 'Mısır Silajı', dryMatter: 35.0, crudeProtein: 8.5, metabolicEnergy: 10.5 },
      { id: 'arpa', name: 'Arpa', dryMatter: 89.0, crudeProtein: 11.5, metabolicEnergy: 12.5 },
      { id: 'misir', name: 'Mısır', dryMatter: 87.0, crudeProtein: 9.0, metabolicEnergy: 13.2 },
      { id: 'soya-kuspesi', name: 'Soya Küspesi', dryMatter: 88.0, crudeProtein: 44.0, metabolicEnergy: 13.8 }
    ];

    availableFeeds = defaultFeeds;
    populateFeedSelect();
  }

  // Initialize feeds
  loadFeedsForRation();

  // Expose function to reload feeds when feed management updates
  window.reloadRationFeeds = loadFeedsForRation;

  // Add feed to ration
  addFeedBtn?.addEventListener('click', () => {
    const selectedFeedId = feedSelect.value;
    const selectedOption = feedSelect.options[feedSelect.selectedIndex];
    const amount = parseFloat(feedAmount.value);

    if (!selectedFeedId || !amount || amount <= 0) {
      window.toast?.warning('Lütfen yem seçin ve geçerli bir miktar girin.');
      return;
    }

    // Find the feed data from availableFeeds
    const selectedFeed = availableFeeds.find(feed => (feed.Id || feed.id) == selectedFeedId);
    if (!selectedFeed) {
      window.toast?.error('Seçilen yem bulunamadı.');
      return;
    }

    const feedData = {
      id: Date.now(), // Simple ID for removal
      feedId: selectedFeedId,
      name: selectedFeed.YemAdi || selectedFeed.name,
      value: selectedFeedId,
      amount: amount,
      dryMatter: selectedFeed.KuruMadde || selectedFeed.dryMatter || parseFloat(selectedOption.dataset.dm) || 88,
      protein: selectedFeed.HamProtein || selectedFeed.crudeProtein || parseFloat(selectedOption.dataset.protein) || 12,
      energy: selectedFeed.MetabolikEnerji || selectedFeed.metabolicEnergy || parseFloat(selectedOption.dataset.energy) || 10,
      price: selectedFeed.BirimFiyat || selectedFeed.price || parseFloat(selectedOption.dataset.price) || 0,

      // Protein fraksiyonları - veritabanından al
      RDP: selectedFeed.RDP || 68.0, // % CP
      RUP: selectedFeed.RUP || 32.0, // % CP

      // Enerji fraksiyonları
      NEL: selectedFeed.NEL || null,
      NEM: selectedFeed.NEM || null,
      TDN: selectedFeed.TDN || null,
      DE: selectedFeed.DE || null,
      HamYag: selectedFeed.HamYag || null,

      // Karbohidrat fraksiyonları - veritabanından al
      Nisasta: selectedFeed.Nisasta || 0.0, // % DM
      Seker: selectedFeed.Seker || 0.0, // % DM
      Kul: selectedFeed.Kul || 0.0, // % DM

      // Fiber fraksiyonları
      NDF: selectedFeed.NDF || null,
      ADF: selectedFeed.ADF || null,
      Hemiseluloz: selectedFeed.Hemiseluloz || null,
      Seluloz: selectedFeed.Seluloz || null,
      Lignin: selectedFeed.Lignin || null,

      // Makro mineraller
      calcium: selectedFeed.Kalsiyum || selectedFeed.calcium || parseFloat(selectedOption.dataset.calcium) || 5,
      phosphorus: selectedFeed.Fosfor || selectedFeed.phosphorus || parseFloat(selectedOption.dataset.phosphorus) || 3,
      magnesium: selectedFeed.Magnezyum || selectedFeed.magnesium || parseFloat(selectedOption.dataset.magnesium) || 2,
      potassium: selectedFeed.Potasyum || selectedFeed.potassium || parseFloat(selectedOption.dataset.potassium) || 10,
      sodium: selectedFeed.Sodyum || selectedFeed.sodium || parseFloat(selectedOption.dataset.sodium) || 1,
      Klorur: selectedFeed.Klorur || null,
      sulfur: selectedFeed.sulfur || selectedFeed.Sulfur || selectedFeed.Kukurt || null,

      // Mikro mineraller
      Demir: selectedFeed.Demir || null,
      Cinko: selectedFeed.Cinko || null,
      Bakir: selectedFeed.Bakir || null,
      Manganez: selectedFeed.Manganez || null,
      Selenyum: selectedFeed.Selenyum || null,
      Iyot: selectedFeed.Iyot || null,
      Kobalt: selectedFeed.Kobalt || null,
      Molibden: selectedFeed.Molibden || null,

      // Vitaminler
      VitaminA: selectedFeed.VitaminA || null,
      VitaminD: selectedFeed.VitaminD || null,
      VitaminE: selectedFeed.VitaminE || null,
      VitaminK: selectedFeed.VitaminK || null,
      Thiamin: selectedFeed.Thiamin || null,
      Riboflavin: selectedFeed.Riboflavin || null,
      Niacin: selectedFeed.Niacin || null,
      Biotin: selectedFeed.Biotin || null,
      Folat: selectedFeed.Folat || null,
      Cobalamin: selectedFeed.Cobalamin || null,

      // Anti-besin faktörleri
      Tanen: selectedFeed.Tanen || null,
      Fitat: selectedFeed.Fitat || null,
      Saponin: selectedFeed.Saponin || null,
      Oksalat: selectedFeed.Oksalat || null
    };

    rationItems.push(feedData);
    renderRationTable();

    // Show success toast
    window.toast?.success(`${feedData.name} rasyona eklendi (${amount} kg)`);

    // Reset form
    feedSelect.value = '';
    feedAmount.value = '';
  });

  // Save ration
  saveRationBtn?.addEventListener('click', () => {
    if (rationItems.length === 0) {
      window.toast?.warning('Rasyona en az bir yem ekleyin.');
      return;
    }

    // Show modal for ration name input
    showRationNameModal((rationName) => {
      if (!rationName) return;

      const ration = {
        name: rationName,
        items: rationItems,
        totalAmount: calculateTotal('amount'),
        totalProtein: calculateTotal('protein'),
        totalEnergy: calculateTotal('energy'),
        createdAt: new Date().toLocaleDateString('tr-TR')
      };

      // Save to database
      saveRationToDatabase(ration, rationName);
    });
  });



  // Clear ration table button
  const clearRationBtn = document.getElementById('clear-ration-btn');
  clearRationBtn?.addEventListener('click', async () => {
    if (rationItems.length === 0) {
      window.toast?.info('Tablo zaten boş.');
      return;
    }

    // Uygulama standardı onay modalı kullan
    const confirmed = await window.toast?.confirm(
      `Tablodaki ${rationItems.length} yemi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      {
        confirmText: 'Evet, Temizle',
        cancelText: 'İptal',
        title: 'Rasyon Tablosunu Temizle'
      }
    );

    if (confirmed) {
      rationItems.length = 0; // Array'i temizle
      renderRationTable();
      hideRationEvaluationProgress(); // Progress bar'ı gizle
      window.toast?.success('Rasyon tablosu temizlendi.');
      console.log('🧹 Rasyon tablosu temizlendi');
    }
  });











  // AI optimization functionality removed



  // Optimization functions removed

  function renderRationTable() {
    rationTbody.innerHTML = '';

    rationItems.forEach(item => {
      const totalProtein = (item.amount * item.protein * item.dryMatter / 100 / 100 * 1000).toFixed(0);
      const totalEnergy = (item.amount * item.energy * item.dryMatter / 100).toFixed(1);
      const unitPrice = (item.price || 0).toFixed(2);
      const totalCost = (item.amount * (item.price || 0)).toFixed(2);

      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${item.name}</td>
        <td>${item.amount.toFixed(1)}</td>
        <td>${item.dryMatter.toFixed(1)}</td>
        <td>${item.protein.toFixed(1)}</td>
        <td>${item.energy.toFixed(1)}</td>
        <td>${unitPrice}</td>
        <td>${totalCost}</td>
        <td>${totalProtein}</td>
        <td>${totalEnergy}</td>
        <td>
          <button class="btn btn-sm btn-danger remove-feed-btn" data-id="${item.id}">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2"></path>
            </svg>
          </button>
        </td>
      `;

      // Add remove functionality with debug
      const removeBtn = row.querySelector('.remove-feed-btn');
      removeBtn.addEventListener('click', () => {
        const clickedId = removeBtn.dataset.id;
        console.log('Silme işlemi başlatıldı:');
        console.log(`  Tıklanan ID: ${clickedId} (tip: ${typeof clickedId})`);
        console.log(`  Mevcut rationItems:`, rationItems.map(r => ({ id: r.id, name: r.name, idType: typeof r.id })));

        // ID'yi hem string hem number olarak dene
        const removedItem = rationItems.find(r => r.id == clickedId || r.id === parseInt(clickedId) || r.id === parseFloat(clickedId));

        if (removedItem) {
          console.log(`  Silinecek öğe bulundu: ${removedItem.name}`);
          rationItems = rationItems.filter(r => r.id != clickedId && r.id !== parseInt(clickedId) && r.id !== parseFloat(clickedId));
          renderRationTable();
          window.toast?.success(`${removedItem.name} rasyondan çıkarıldı`);
        } else {
          console.log(`  Silinecek öğe bulunamadı!`);
          console.log(`  Aranan ID: ${clickedId}`);
          console.log(`  Mevcut ID'ler:`, rationItems.map(r => r.id));
          window.toast?.error(`Yem silinemedi - ID bulunamadı`);
        }
      });

      rationTbody.appendChild(row);
    });

    updateTotals();

    // Update validation system with current ration
    if (window.storeRationItems) {
      window.storeRationItems(rationItems);
    }
  }

  function updateTotals() {
    document.getElementById('total-amount').textContent = calculateTotal('amount').toFixed(1);
    document.getElementById('total-cost').textContent = calculateTotal('cost').toFixed(2);
    document.getElementById('total-protein').textContent = calculateTotal('protein').toFixed(0);
    document.getElementById('total-energy').textContent = calculateTotal('energy').toFixed(1);
  }

  function calculateTotal(type) {
    return rationItems.reduce((total, item) => {
      switch (type) {
        case 'amount':
          return total + item.amount;
        case 'cost':
          return total + (item.amount * (item.price || 0));
        case 'protein':
          return total + (item.amount * item.protein * item.dryMatter / 100 / 100 * 1000);
        case 'energy':
          return total + (item.amount * item.energy * item.dryMatter / 100);
        default:
          return total;
      }
    }, 0);
  }


}

function setupRationValidation() {
  const validateBtn = document.getElementById('validate-ration-btn');
  const validationSection = document.getElementById('ration-validation-section');
  const validationTbody = document.getElementById('validation-results-tbody');
  const summaryContent = document.getElementById('summary-content');

  let currentRequirements = null;
  let currentRationItems = [];

  // Store requirements when calculated
  window.storeNutritionalRequirements = function(requirements) {
    currentRequirements = requirements;
    checkValidationAvailability();
  };

  // Store ration items when updated
  window.storeRationItems = function(rationItems) {
    currentRationItems = [...rationItems];
    checkValidationAvailability();
  };

  function checkValidationAvailability() {
    const detailedBtn = document.getElementById('detailed-analysis-btn');

    if (currentRequirements && currentRationItems.length > 0) {
      validationSection.style.display = 'block';
      validateBtn.disabled = false;

      // Update button text to show it's ready
      validateBtn.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M9 12l2 2 4-4"></path>
          <circle cx="12" cy="12" r="10"></circle>
        </svg>
        Rasyonu Değerlendir (${currentRationItems.length} yem)
      `;
    } else {
      validationSection.style.display = 'none';
      validateBtn.disabled = true;

      // Hide detailed analysis button when validation is not available
      if (detailedBtn) {
        detailedBtn.style.display = 'none';
      }

      // Show what's missing
      let missingText = 'Rasyon Değerlendirmesi - ';
      if (!currentRequirements && currentRationItems.length === 0) {
        missingText += 'İhtiyaç hesabı ve rasyon gerekli';
      } else if (!currentRequirements) {
        missingText += 'İhtiyaç hesabı gerekli';
      } else {
        missingText += 'Rasyon gerekli';
      }

      validateBtn.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M9 12l2 2 4-4"></path>
          <circle cx="12" cy="12" r="10"></circle>
        </svg>
        ${missingText}
      `;
    }
  }

  validateBtn?.addEventListener('click', () => {
    if (!currentRequirements || currentRationItems.length === 0) {
      window.toast?.warning('Önce besin ihtiyaçlarını hesaplayın ve rasyon oluşturun.');
      return;
    }

    performRationValidation();
  });

  // Initialize validation availability check
  checkValidationAvailability();

  function performRationValidation() {
    const providedNutrients = calculateProvidedNutrients(currentRationItems);
    const validationResults = compareNutrients(currentRequirements, providedNutrients);

    // Ana tabloda sadece temel besin değerlerini göster
    renderValidationResults(validationResults.basic);
    renderValidationSummary(validationResults.all);

    // 📊 PROGRESS BAR GÜNCELLEMESİ
    const overallScore = calculateOverallRationScore(validationResults.all);
    updateRationEvaluationProgress(overallScore);
    console.log(`Rasyon değerlendirme skoru: ${overallScore.toFixed(1)}%`);

    // Detaylı analiz butonunu aktif et
    setupDetailedAnalysisModal(validationResults);
  }

  function calculateProvidedNutrients(rationItems) {
    // Calculate total dry matter first for fiber percentage calculations
    const totalDM = rationItems.reduce((sum, item) => sum + (item.amount * item.dryMatter / 100), 0);

    // Initialize all 44 nutrients from database
    const nutrients = {
      // Temel Besin Değerleri (10) - Doğrudan Ölçülen
      dryMatter: 0,        // KuruMadde
      protein: 0,          // HamProtein
      rdp: 0,              // RDP
      rup: 0,              // RUP
      energy: 0,           // MetabolikEnerji (legacy)
      me: 0,               // MetabolikEnerji (MJ/day)
      nel: 0,              // NEL (Mcal/day)
      fat: 0,              // HamYag
      starch: 0,           // Nisasta
      sugar: 0,            // Seker
      ash: 0,              // Kul

      // Türetilen Değerler (6) - Otomatik Hesaplanan
      tdn: 0,              // TDN (NEL'den)
      mcp: 0,              // MCP (TDN'den)
      mp: 0,               // MP (RUP+MCP'den)
      de: 0,               // DE (TDN'den)
      nem: 0,              // NEM (NEL'den)
      nfc: 0,              // NFC (100 - NDF - CP - EE - Ash)

      // Fiber Fraksiyonları (5)
      ndf: 0,              // NDF
      adf: 0,              // ADF
      hemicellulose: 0,    // Hemiseluloz
      cellulose: 0,        // Seluloz
      lignin: 0,           // Lignin

      // Makro Mineraller (7)
      calcium: 0,          // Kalsiyum
      phosphorus: 0,       // Fosfor
      magnesium: 0,        // Magnezyum
      potassium: 0,        // Potasyum
      sodium: 0,           // Sodyum
      chlorine: 0,         // Klorur
      sulfur: 0,           // Kükurt

      // Mikro Mineraller (8)
      iron: 0,             // Demir
      zinc: 0,             // Cinko
      copper: 0,           // Bakir
      manganese: 0,        // Manganez
      selenium: 0,         // Selenyum
      iodine: 0,           // Iyot
      cobalt: 0,           // Kobalt
      molybdenum: 0,       // Molibden

      // Vitaminler (10)
      vitaminA: 0,         // VitaminA
      vitaminD: 0,         // VitaminD
      vitaminE: 0,         // VitaminE
      vitaminK: 0,         // VitaminK
      thiamin: 0,          // Thiamin (B1)
      riboflavin: 0,       // Riboflavin (B2)
      niacin: 0,           // Niacin (B3)
      biotin: 0,           // Biotin (B7)
      folate: 0,           // Folat (B9)
      cobalamin: 0,        // Cobalamin (B12)

      // Anti-besin Faktörleri (4)
      tannin: 0,           // Tanen
      phytate: 0,          // Fitat
      saponin: 0,          // Saponin
      oxalate: 0           // Oksalat
    };

    rationItems.forEach(item => {
      const dmAmount = item.amount * (item.dryMatter / 100);

      // Temel hesaplamalar
      nutrients.dryMatter += dmAmount;
      nutrients.protein += (item.amount * item.protein * item.dryMatter / 100 / 100) * 1000; // g
      nutrients.energy += (item.amount * item.energy * item.dryMatter / 100);

      // Tüm 44 besin değerini hesapla (veritabanı field adlarıyla)
      const feedNutrients = {
        // Protein fraksiyonları - RDP ve RUP ham protein üzerinden yüzde olarak hesaplanır
        rdp: item.RDP || item.rdp || 68.0, // % CP (default: 68%)
        rup: item.RUP || item.rup || 32.0, // % CP (default: 32%)

        // Enerji fraksiyonları - NEL ve ME doğru dönüşümle
        nel: item.NEL || item.nel || (item.MetabolikEnerji ? (item.MetabolikEnerji / 4.184 * 0.64) : 1.5), // Mcal/kg DM (ME'den: MJ->Mcal, sonra NEL)
        me: item.MetabolikEnerji || item.me || (item.NEL ? (item.NEL / 0.64 * 4.184) : 6.3), // MJ/kg DM (NEL'den: NEL->ME, sonra MJ)
        fat: item.HamYag || item.fat || 3.5, // % default fat

        // Karbohidrat fraksiyonları - use 0 if no data (no misleading defaults)
        starch: item.Nisasta || item.starch || 0.0, // % DM - use actual data or 0
        sugar: item.Seker || item.sugar || 0.0, // % DM - use actual data or 0
        ash: item.Kul || item.ash || 0.0, // % DM - use actual data or 0

        // Fiber fraksiyonları
        ndf: item.NDF || item.ndf || 28, // % default NDF
        adf: item.ADF || item.adf || 21, // % default ADF
        hemicellulose: item.Hemiseluloz || item.hemicellulose || 7, // % default
        cellulose: item.Seluloz || item.cellulose || 14, // % default
        lignin: item.Lignin || item.lignin || 4, // % default

        // Makro mineraller (% DM -> g/kg DM for calculation)
        calcium: (item.Kalsiyum || item.calcium || 0.5) * 10, // % DM -> g/kg DM
        phosphorus: (item.Fosfor || item.phosphorus || 0.3) * 10, // % DM -> g/kg DM
        magnesium: (item.Magnezyum || item.magnesium || 0.2) * 10, // % DM -> g/kg DM
        potassium: (item.Potasyum || item.potassium || 1.0) * 10, // % DM -> g/kg DM
        sodium: (item.Sodyum || item.sodium || 0.1) * 10, // % DM -> g/kg DM
        chlorine: (item.Klorur || item.chlorine || 0.2) * 10, // % DM -> g/kg DM
        sulfur: (item.Sulfur || item.sulfur || 0.2) * 10, // % DM -> g/kg DM

        // Mikro mineraller (mg/kg DM - same as ppm)
        iron: item.Demir || item.iron || 100, // mg/kg DM
        zinc: item.Cinko || item.zinc || 40, // mg/kg DM
        copper: item.Bakir || item.copper || 10, // mg/kg DM
        manganese: item.Manganez || item.manganese || 40, // mg/kg DM
        selenium: item.Selenyum || item.selenium || 0.3, // mg/kg DM
        iodine: item.Iyot || item.iodine || 0.5, // mg/kg DM
        cobalt: item.Kobalt || item.cobalt || 0.1, // mg/kg DM
        molybdenum: item.Molibden || item.molybdenum || 0.15, // mg/kg DM

        // Vitaminler (Input is already per kg DM as per standardized forms)
        vitaminA: item.VitaminA || item.vitaminA || getRealisticVitaminADefault(item.YemAdi || item.name || ''), // IU/kg DM
        vitaminD: item.VitaminD || item.vitaminD || 1000, // IU/kg DM
        vitaminE: item.VitaminE || item.vitaminE || 15, // IU/kg DM
        vitaminK: item.VitaminK || item.vitaminK || 1, // mg/kg DM
        thiamin: item.Thiamin || item.thiamin || 2, // mg/kg DM (B1)
        riboflavin: item.Riboflavin || item.riboflavin || 3, // mg/kg DM (B2)
        niacin: item.Niacin || item.niacin || 15, // mg/kg DM (B3)
        biotin: item.Biotin || item.biotin || 0.1, // mg/kg DM (B7)
        folate: item.Folat || item.folate || 0.5, // mg/kg DM (B9)
        cobalamin: item.Cobalamin || item.cobalamin || 0.01, // μg/kg DM (B12)

        // Anti-besin faktörleri
        tannin: item.Tanen || item.tannin || 0.5, // % DM
        phytate: item.Fitat || item.phytate || 0.3, // % DM
        saponin: item.Saponin || item.saponin || 0.1, // % DM
        oxalate: item.Oksalat || item.oxalate || 0.2 // % DM
      };



      // STANDARDIZED: Proper unit conversions for each nutrient category
      Object.keys(feedNutrients).forEach(nutrient => {
        if (['ndf', 'adf', 'hemicellulose', 'cellulose', 'lignin'].includes(nutrient)) {
          // Fiber fractions - % DM -> g/day: (kg DM/day) * (% DM) * 10
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient] * 10; // g/day
        } else if (['tdn'].includes(nutrient)) {
          // TDN - % DM (weighted average) - keep as percentage for special calculation
          nutrients[nutrient] += (dmAmount * feedNutrients[nutrient]) / totalDM; // % DM
        } else if (['rdp', 'rup'].includes(nutrient)) {
          // RDP ve RUP: % CP -> g/day: (kg DM/day) * (% CP/100) * (CP g/kg DM) / 1000
          const cpPercentage = item.protein; // % DM
          const cpAmount = dmAmount * cpPercentage * 10; // g CP/day
          nutrients[nutrient] += cpAmount * feedNutrients[nutrient] / 100; // g/day
        } else if (['fat', 'starch', 'sugar', 'ash'].includes(nutrient)) {
          // Percentage values - % DM -> g/day: (kg DM/day) * (% / 100) * 1000 g/kg
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient] * 10; // g/day
        } else if (['tannin', 'phytate', 'saponin', 'oxalate'].includes(nutrient)) {
          // Anti-nutritional factors - % DM -> g/day: (kg DM/day) * (% / 100) * 1000 g/kg
          // CORRECTED: Use proper conversion for low percentage values
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient] * 10; // g/day (but values are typically <1% so results are small)
        } else if (['calcium', 'phosphorus', 'magnesium', 'potassium', 'sodium', 'chlorine', 'sulfur'].includes(nutrient)) {
          // Macro minerals - g/kg DM -> g/day: (kg DM/day) * (g/kg DM)
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient]; // g/day (corrected: no division)
        } else if (['iron', 'zinc', 'copper', 'manganese', 'selenium', 'iodine', 'cobalt', 'molybdenum'].includes(nutrient)) {
          // Micro minerals - mg/kg DM -> mg/day: (kg DM/day) * (mg/kg DM)
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient]; // mg/day (corrected: no division)
        } else if (['vitaminK', 'thiamin', 'riboflavin', 'niacin', 'biotin', 'folate'].includes(nutrient)) {
          // B vitamins and K vitamin - mg/kg DM -> mg/day: (kg DM/day) * (mg/kg DM)
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient]; // mg/day (corrected: no division)
        } else if (nutrient === 'cobalamin') {
          // B12 - μg/kg DM -> μg/day: (kg DM/day) * (μg/kg DM)
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient]; // μg/day (corrected: no division)
        } else if (['vitaminA', 'vitaminD', 'vitaminE'].includes(nutrient)) {
          // Fat-soluble vitamins - IU/kg DM -> IU/day: (kg DM/day) * (IU/kg DM)
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient]; // IU/day (corrected: no division)
        } else if (['nel'].includes(nutrient)) {
          // NEL energy - Mcal/kg DM -> Mcal/day: (kg DM/day) * (Mcal/kg DM)
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient]; // Mcal/day
        } else if (['me'].includes(nutrient)) {
          // ME energy - MJ/kg DM -> MJ/day: (kg DM/day) * (MJ/kg DM)
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient]; // MJ/day
        } else if (['starch', 'sugar', 'ash'].includes(nutrient)) {
          // Carbohydrate fractions - % DM -> g/day: (kg DM/day) * (% DM) * 10
          const contribution = dmAmount * feedNutrients[nutrient] * 10;
          nutrients[nutrient] += contribution; // g/day


        } else {
          // Other nutrients - fallback calculation
          nutrients[nutrient] += dmAmount * feedNutrients[nutrient] * 10; // g/day
        }
      });
    });

    // Türetilen değerleri hesapla
    if (nutrients.nel > 0 && nutrients.dryMatter > 0) {
      // 1. TDN hesaplama (NEL'den)
      const nelConcentration = nutrients.nel / nutrients.dryMatter; // Mcal NEL per kg DM
      nutrients.tdn = (nelConcentration / 1.54) * 100; // % DM (TDN as percentage of dry matter)

      // 2. MCP hesaplama (TDN'den)
      const tdnAmount = nutrients.dryMatter * nutrients.tdn / 100; // kg TDN per day
      nutrients.mcp = tdnAmount * 0.13 * 1000; // g MCP per day (13g MCP per 100g TDN)

      // 3. MP hesaplama (RUP + MCP'den)
      const rupAmount = nutrients.rup; // g/day (already calculated)
      const mcpAmount = nutrients.mcp; // g/day (just calculated)
      const mpFromRUP = rupAmount * 0.8; // 80% of RUP is absorbed
      const mpFromMCP = mcpAmount * 0.64; // 64% of MCP is true protein
      nutrients.mp = mpFromRUP + mpFromMCP; // g MP per day

      // 4. DE hesaplama (TDN'den)
      nutrients.de = tdnAmount * 4.4; // Mcal DE per day (1 kg TDN = 4.4 Mcal DE)

      // 5. NEM hesaplama (NEL'den - yaklaşık)
      nutrients.nem = nutrients.nel * 0.85; // Mcal NEM per day (NEL'in ~85%'i NEM)

      // 6. NFC hesaplama (Non-Fiber Carbohydrate)
      // NFC = 100 - (NDF + CP + EE + Ash) - % DM olarak
      if (nutrients.dryMatter > 0) {
        const ndfPercent = (nutrients.ndf / nutrients.dryMatter) * 100; // % DM
        const cpPercent = (nutrients.protein / nutrients.dryMatter) * 100; // % DM
        const eePercent = (nutrients.fat / nutrients.dryMatter) * 100; // % DM (EE = Ether Extract = Fat)
        const ashPercent = (nutrients.ash / nutrients.dryMatter) * 100; // % DM
        const nfcPercent = 100 - (ndfPercent + cpPercent + eePercent + ashPercent); // % DM
        nutrients.nfc = Math.max(0, nfcPercent); // % DM (negative değerleri 0 yap)
      }
    }



    return nutrients;
  }

  function compareNutrients(required, provided) {
    // STANDARDIZED: All units match between requirements and provided values
    // Temel besin değerleri (ana tabloda gösterilecek)
    const basicNutrients = [
      {
        name: 'Kuru Madde',
        unit: 'kg/gün',
        required: required.dryMatter,
        provided: provided.dryMatter,
        tolerance: 0.1,
        category: 'basic'
      },
      {
        name: 'Ham Protein',
        unit: 'g/gün',
        required: required.protein * 1000, // Convert kg to g
        provided: provided.protein, // Already in g/day from calculateProvidedNutrients
        tolerance: 0.05,
        category: 'basic'
      },
      {
        name: 'Metabolik Enerji',
        unit: 'MJ/gün',
        required: required.energy,
        provided: provided.energy,
        tolerance: 0.1,
        category: 'basic'
      },
      {
        name: 'Kalsiyum',
        unit: 'g/gün',
        required: required.calcium,
        provided: provided.calcium,
        tolerance: 0.15,
        category: 'basic'
      },
      {
        name: 'Fosfor',
        unit: 'g/gün',
        required: required.phosphorus,
        provided: provided.phosphorus,
        tolerance: 0.15,
        category: 'basic'
      }
    ];

    // Modal için ayrı basic kategorisi (sadece Kuru Madde ve Ham Protein)
    const modalBasicNutrients = [
      {
        name: 'Kuru Madde',
        unit: 'kg/gün',
        required: required.dryMatter,
        provided: provided.dryMatter,
        tolerance: 0.1,
        category: 'basic'
      },
      {
        name: 'Ham Protein',
        unit: 'g/gün',
        required: required.protein * 1000, // Convert kg to g
        provided: provided.protein, // Already in g/day from calculateProvidedNutrients
        tolerance: 0.05,
        category: 'basic'
      }
    ];

    // Detaylı besin değerleri - 42 doğrudan ölçülen besin değeri (basic hariç, çünkü ayrı tab'da)
    const detailedNutrients = [
      // Protein fraksiyonları (2)
      {
        name: 'RDP (Rumen Parçalanabilir Protein)',
        unit: 'g/gün',
        required: (required.rdp || 0) * 1000, // Convert kg to g
        provided: provided.rdp || 0, // Already in g/day
        tolerance: 0.1,
        category: 'protein'
      },
      {
        name: 'RUP (Rumen Parçalanmayan Protein)',
        unit: 'g/gün',
        required: (required.rup || 0) * 1000, // Convert kg to g
        provided: provided.rup || 0, // Already in g/day
        tolerance: 0.1,
        category: 'protein'
      },


      // Enerji fraksiyonları (6)
      {
        name: 'Metabolik Enerji',
        unit: 'MJ/gün',
        required: required.energy || 0, // Already in MJ/day
        provided: provided.energy || 0, // Already in MJ/day
        tolerance: 0.1,
        category: 'energy'
      },
      {
        name: 'NEL (Net Enerji Laktasyon)',
        unit: 'Mcal/gün',
        required: required.nel || 0, // Already in Mcal/day
        provided: provided.nel || 0, // Already in Mcal/day
        tolerance: 0.1,
        category: 'energy'
      },
      {
        name: 'Ham Yağ',
        unit: 'g/gün',
        required: required.fat || 0,
        provided: provided.fat || 0,
        tolerance: 0.15,
        category: 'energy'
      },

      // Karbohidrat fraksiyonları (3) - Optimal hedef değerler
      {
        name: 'Nişasta (Optimal)',
        unit: 'g/gün',
        required: required.starch || 0,
        provided: provided.starch || 0,
        tolerance: 0.25, // More flexible for carbohydrates
        category: 'carbohydrate'
      },
      {
        name: 'Şeker (Optimal)',
        unit: 'g/gün',
        required: required.sugar || 0,
        provided: provided.sugar || 0,
        tolerance: 0.30, // More flexible for sugars
        category: 'carbohydrate'
      },
      {
        name: 'Kül (Optimal)',
        unit: 'g/gün',
        required: required.ash || 0,
        provided: provided.ash || 0,
        tolerance: 0.20, // More flexible for ash
        category: 'macro-minerals'
      },
      // Fiber fraksiyonları (5) - g/gün units for consistency
      {
        name: 'NDF (Nötr Deterjan Fiber)',
        unit: 'g/gün',
        required: required.ndf || 0,
        provided: provided.ndf || 0,
        tolerance: 0.15,
        category: 'fiber'
      },
      {
        name: 'ADF (Asit Deterjan Fiber)',
        unit: 'g/gün',
        required: required.adf || 0,
        provided: provided.adf || 0,
        tolerance: 0.15,
        category: 'fiber'
      },
      {
        name: 'Hemiseluloz',
        unit: 'g/gün',
        required: required.hemicellulose || 0,
        provided: provided.hemicellulose || 0,
        tolerance: 0.20,
        category: 'fiber'
      },
      {
        name: 'Seluloz',
        unit: 'g/gün',
        required: required.cellulose || 0,
        provided: provided.cellulose || 0,
        tolerance: 0.20,
        category: 'fiber'
      },
      {
        name: 'Lignin',
        unit: 'g/gün',
        required: required.lignin || 0,
        provided: provided.lignin || 0,
        tolerance: 0.25,
        category: 'fiber'
      },

      // Ana ekranda gösterilen makro mineraller (2)
      {
        name: 'Kalsiyum',
        unit: 'g/gün',
        required: required.calcium,
        provided: provided.calcium,
        tolerance: 0.15,
        category: 'macro-minerals'
      },
      {
        name: 'Fosfor',
        unit: 'g/gün',
        required: required.phosphorus,
        provided: provided.phosphorus,
        tolerance: 0.15,
        category: 'macro-minerals'
      },

      // Diğer makro mineraller
      {
        name: 'Magnezyum',
        unit: 'g/gün',
        required: required.magnesium,
        provided: provided.magnesium,
        tolerance: 0.2,
        category: 'macro-minerals'
      },
      {
        name: 'Potasyum',
        unit: 'g/gün',
        required: required.potassium,
        provided: provided.potassium,
        tolerance: 0.2,
        category: 'macro-minerals'
      },
      {
        name: 'Sodyum',
        unit: 'g/gün',
        required: required.sodium,
        provided: provided.sodium,
        tolerance: 0.25,
        category: 'macro-minerals'
      },
      {        name: 'Klorür',        unit: 'g/gün',        required: required.chlorine || 0,        provided: provided.chlorine || 0,        tolerance: 0.25,        category: 'macro-minerals'      },
      {        name: 'Kükürt',        unit: 'g/gün',        required: required.sulfur || 0,        provided: provided.sulfur || 0,        tolerance: 0.25,        category: 'macro-minerals'      },

      // Mikro mineraller (8)
      {
        name: 'Demir',
        unit: 'mg/gün',
        required: required.iron || 0,
        provided: provided.iron || 0,
        tolerance: 0.2,
        category: 'micro-minerals'
      },
      {
        name: 'Çinko',
        unit: 'mg/gün',
        required: required.zinc || 0,
        provided: provided.zinc || 0,
        tolerance: 0.2,
        category: 'micro-minerals'
      },
      {
        name: 'Bakır',
        unit: 'mg/gün',
        required: required.copper || 0,
        provided: provided.copper || 0,
        tolerance: 0.2,
        category: 'micro-minerals'
      },
      {
        name: 'Manganez',
        unit: 'mg/gün',
        required: required.manganese || 0,
        provided: provided.manganese || 0,
        tolerance: 0.2,
        category: 'micro-minerals'
      },
      {
        name: 'Selenyum',
        unit: 'mg/gün',
        required: required.selenium || 0,
        provided: provided.selenium || 0,
        tolerance: 0.15,
        category: 'micro-minerals'
      },
      {
        name: 'İyot',
        unit: 'mg/gün',
        required: required.iodine || 0,
        provided: provided.iodine || 0,
        tolerance: 0.2,
        category: 'micro-minerals'
      },
      {
        name: 'Kobalt',
        unit: 'mg/gün',
        required: required.cobalt || 0,
        provided: provided.cobalt || 0,
        tolerance: 0.25,
        category: 'micro-minerals'
      },
      {
        name: 'Molibden',
        unit: 'mg/gün',
        required: required.molybdenum || 0,
        provided: provided.molybdenum || 0,
        tolerance: 0.3,
        category: 'micro-minerals'
      },

      // Vitaminler (10)
      {
        name: 'Vitamin A',
        unit: 'IU/gün',
        required: required.vitaminA || 0,
        provided: provided.vitaminA || 0,
        tolerance: 0.2,
        category: 'vitamins'
      },
      {
        name: 'Vitamin D',
        unit: 'IU/gün',
        required: required.vitaminD || 0,
        provided: provided.vitaminD || 0,
        tolerance: 0.2,
        category: 'vitamins'
      },
      {
        name: 'Vitamin E',
        unit: 'IU/gün',
        required: required.vitaminE || 0,
        provided: provided.vitaminE || 0,
        tolerance: 0.2,
        category: 'vitamins'
      },
      {
        name: 'Vitamin K',
        unit: 'mg/gün',
        required: required.vitaminK || 0,
        provided: provided.vitaminK || 0,
        tolerance: 0.25,
        category: 'vitamins'
      },
      {
        name: 'Thiamin (B1)',
        unit: 'mg/gün',
        required: required.thiamin || 0,
        provided: provided.thiamin || 0,
        tolerance: 0.2,
        category: 'vitamins'
      },
      {
        name: 'Riboflavin (B2)',
        unit: 'mg/gün',
        required: required.riboflavin || 0,
        provided: provided.riboflavin || 0,
        tolerance: 0.2,
        category: 'vitamins'
      },
      {
        name: 'Niacin (B3)',
        unit: 'mg/gün',
        required: required.niacin || 0,
        provided: provided.niacin || 0,
        tolerance: 0.2,
        category: 'vitamins'
      },
      {
        name: 'Biotin (B7)',
        unit: 'mg/gün',
        required: required.biotin || 0,
        provided: provided.biotin || 0,
        tolerance: 0.25,
        category: 'vitamins'
      },
      {
        name: 'Folat (B9)',
        unit: 'mg/gün',
        required: required.folate || 0,
        provided: provided.folate || 0,
        tolerance: 0.2,
        category: 'vitamins'
      },
      {
        name: 'Cobalamin (B12)',
        unit: 'μg/gün',
        required: required.cobalamin || 0,
        provided: provided.cobalamin || 0,
        tolerance: 0.3,
        category: 'vitamins'
      },

      // Anti-besin faktörleri (4)
      {
        name: 'Tanen',
        unit: 'g/gün',
        required: required.tannin || 0,
        provided: provided.tannin || 0,
        tolerance: 0.5,
        category: 'anti-nutrients'
      },
      {
        name: 'Fitat',
        unit: 'g/gün',
        required: required.phytate || 0,
        provided: provided.phytate || 0,
        tolerance: 0.5,
        category: 'anti-nutrients'
      },
      {
        name: 'Saponin',
        unit: 'g/gün',
        required: required.saponin || 0,
        provided: provided.saponin || 0,
        tolerance: 0.5,
        category: 'anti-nutrients'
      },
      {
        name: 'Oksalat',
        unit: 'g/gün',
        required: required.oxalate || 0,
        provided: provided.oxalate || 0,
        tolerance: 0.5,
        category: 'anti-nutrients'
      }
    ];

    // Tüm besin değerlerini birleştir
    const allNutrients = [...basicNutrients, ...detailedNutrients];

    // Besin değerlerini işle
    const processNutrients = (nutrientList) => {
      return nutrientList.map(nutrient => {
        // Provided value'yu nutrient objesinden al
        let providedValue = nutrient.provided;

        const difference = providedValue - nutrient.required;
        const percentDiff = (difference / nutrient.required) * 100;

        let status, statusClass, recommendation, isAdequate;

        // Anti-besin faktörleri için özel mantık (fazla olması kötü)
        if (nutrient.category === 'anti-nutrients') {
          isAdequate = providedValue <= nutrient.required; // Maksimum güvenli seviyenin altında olmalı

          if (isAdequate) {
            status = 'Güvenli';
            statusClass = 'adequate';
            recommendation = 'Güvenli seviyede';
          } else {
            status = 'Yüksek';
            statusClass = 'excess';
            const excessPercent = ((providedValue - nutrient.required) / nutrient.required) * 100;
            if (excessPercent > 50) {
              recommendation = 'Tehlikeli seviye - derhal azaltın';
            } else if (excessPercent > 20) {
              recommendation = 'Yüksek seviye - azaltın';
            } else {
              recommendation = 'Hafif yüksek - kontrol edin';
            }
          }
        } else {
          // Normal besin değerleri için standart mantık
          isAdequate = Math.abs(percentDiff) <= (nutrient.tolerance * 100);

          if (isAdequate) {
            status = 'Yeterli';
            statusClass = 'adequate';
            recommendation = 'Mevcut seviye uygun';
          } else if (percentDiff > 0) {
            status = 'Fazla';
            statusClass = 'excess';
            if (percentDiff > 20) {
              recommendation = 'Önemli fazlalık - azaltın';
            } else {
              recommendation = 'Hafif fazlalık - kontrol edin';
            }
          } else {
            status = 'Eksik';
            statusClass = 'deficient';
            if (percentDiff < -20) {
              recommendation = 'Kritik eksiklik - artırın';
            } else {
              recommendation = 'Hafif eksiklik - artırın';
            }
          }
        }

        return {
          ...nutrient,
          difference: difference,
          percentDiff: percentDiff,
          status: status,
          statusClass: statusClass,
          recommendation: recommendation
        };
      });
    };

    // Ana tabloda gösterilecek temel besin değerleri
    const basicResults = processNutrients(basicNutrients);

    // Modal'da gösterilecek temel besin değerleri (sadece Kuru Madde ve Ham Protein)
    const modalBasicResults = processNutrients(modalBasicNutrients);

    // Detaylı analiz için tüm besin değerleri
    const detailedResults = processNutrients(detailedNutrients);

    // Karşılaştırma için benzersiz verileri oluştur (tekrarları filtrele)
    const uniqueResults = [...basicResults];

    // detailedResults'tan sadece basicResults'ta olmayan verileri ekle
    const duplicates = [];
    detailedResults.forEach(detailed => {
      const isDuplicate = basicResults.some(basic => basic.name === detailed.name);
      if (!isDuplicate) {
        uniqueResults.push(detailed);
      } else {
        duplicates.push(detailed.name);
      }
    });



    // Sonuçları döndür
    return {
      basic: basicResults,
      modalBasic: modalBasicResults,
      detailed: detailedResults,
      all: uniqueResults // Artık benzersiz 44 veri
    };
  }

  function renderValidationResults(validationResults) {
    validationTbody.innerHTML = '';

    validationResults.forEach(result => {
      const row = document.createElement('tr');
      row.className = `validation-row ${result.statusClass}`;

      const differenceText = result.difference >= 0
        ? `+${result.difference.toFixed(1)}`
        : result.difference.toFixed(1);

      const percentText = result.percentDiff >= 0
        ? `+${result.percentDiff.toFixed(1)}%`
        : `${result.percentDiff.toFixed(1)}%`;

      row.innerHTML = `
        <td><strong>${result.name}</strong></td>
        <td>${result.required.toFixed(1)} ${result.unit}</td>
        <td>${result.provided.toFixed(1)} ${result.unit}</td>
        <td>
          <span class="difference-value ${result.statusClass}">
            ${differenceText} (${percentText})
          </span>
        </td>
        <td>
          <span class="status-badge ${result.statusClass}">
            ${result.status}
          </span>
        </td>
        <td class="recommendation-cell">
          ${result.recommendation}
        </td>
      `;

      validationTbody.appendChild(row);
    });
  }

  function renderValidationSummary(validationResults) {
    const adequate = validationResults.filter(r => r.statusClass === 'adequate').length;
    const deficient = validationResults.filter(r => r.statusClass === 'deficient').length;
    const excess = validationResults.filter(r => r.statusClass === 'excess').length;
    const total = validationResults.length;

    let overallStatus, statusClass, statusIcon;

    if (deficient === 0 && excess === 0) {
      overallStatus = 'Mükemmel';
      statusClass = 'excellent';
      statusIcon = '';
    } else if (deficient === 0 && excess <= 1) {
      overallStatus = 'İyi';
      statusClass = 'good';
      statusIcon = '';
    } else if (deficient <= 1 && excess <= 1) {
      overallStatus = 'Kabul Edilebilir';
      statusClass = 'acceptable';
      statusIcon = '';
    } else {
      overallStatus = 'Düzeltme Gerekli';
      statusClass = 'needs-improvement';
      statusIcon = '';
    }

    const criticalIssues = validationResults.filter(r =>
      Math.abs(r.percentDiff) > 20 && r.statusClass !== 'adequate'
    );

    let summaryHTML = `
      <div class="overall-status ${statusClass}">
        <div class="status-header">
          <span class="status-icon">${statusIcon}</span>
          <h5>Genel Durum: ${overallStatus}</h5>
        </div>
        <div class="status-breakdown">
          <div class="status-item adequate">
            <span class="count">${adequate}</span>
            <span class="label">Yeterli</span>
          </div>
          <div class="status-item deficient">
            <span class="count">${deficient}</span>
            <span class="label">Eksik</span>
          </div>
          <div class="status-item excess">
            <span class="count">${excess}</span>
            <span class="label">Fazla</span>
          </div>
        </div>
      </div>
    `;

    if (criticalIssues.length > 0) {
      summaryHTML += `
        <div class="critical-issues">
          <h6>Kritik Sorunlar:</h6>
          <ul>
            ${criticalIssues.map(issue =>
              `<li><strong>${issue.name}:</strong> ${issue.recommendation}</li>`
            ).join('')}
          </ul>
        </div>
      `;
    }

    summaryHTML += `
      <div class="recommendations">
        <h6>Genel Öneriler:</h6>
        <ul>
    `;

    if (deficient > 0) {
      summaryHTML += '<li>Eksik besin öğeleri için yem miktarlarını artırın veya daha zengin yemler ekleyin</li>';
    }
    if (excess > 0) {
      summaryHTML += '<li>Fazla besin öğeleri için yem miktarlarını azaltın veya daha dengeli yemler kullanın</li>';
    }
    if (adequate === total) {
      summaryHTML += '<li>Rasyon dengeli görünüyor, mevcut formülasyonu koruyabilirsiniz</li>';
    }

    summaryHTML += `
        </ul>
      </div>
    `;

    summaryContent.innerHTML = summaryHTML;
  }

  function setupDetailedAnalysisModal(validationResults) {
    const detailedBtn = document.getElementById('detailed-analysis-btn');

    // Detaylı analiz butonunu göster
    detailedBtn.style.display = 'inline-flex';

    // Modal HTML'ini oluştur (eğer yoksa)
    if (!document.getElementById('detailed-analysis-modal')) {
      const modalHTML = `
        <div class="modal hidden" id="detailed-analysis-modal">
          <div class="detail-modal-content">
            <div class="detail-modal-header">
              <div class="detail-title-section">
                <h2 class="detail-title">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px;">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                  </svg>
                  Detaylı Besin Değeri Analizi
                </h2>
                <p class="detail-subtitle">47 Doğrudan Ölçülen Besin Değerinin Kapsamlı Analizi</p>
              </div>
              <button class="close-btn" id="close-detailed-analysis-modal">&times;</button>
            </div>

            <div class="detail-modal-body">
              <div class="detail-tabs">
                <button class="detail-tab-link active" data-tab="basic-content">
                  <span class="tab-icon"></span>
                  Temel Besin Değerleri
                </button>
                <button class="detail-tab-link" data-tab="protein-content">
                  <span class="tab-icon"></span>
                  Protein Fraksiyonları
                </button>
                <button class="detail-tab-link" data-tab="energy-content">
                  <span class="tab-icon"></span>
                  Enerji Fraksiyonları
                </button>
                <button class="detail-tab-link" data-tab="carbohydrate-content">
                  <span class="tab-icon"></span>
                  Karbohidrat Fraksiyonları
                </button>
                <button class="detail-tab-link" data-tab="fiber-content">
                  <span class="tab-icon"></span>
                  Fiber Fraksiyonları
                </button>
                <button class="detail-tab-link" data-tab="minerals-content">
                  <span class="tab-icon"></span>
                  Mineraller
                </button>
                <button class="detail-tab-link" data-tab="vitamins-content">
                  <span class="tab-icon"></span>
                  Vitaminler
                </button>
                <button class="detail-tab-link" data-tab="anti-nutrients-content">
                  <span class="tab-icon"></span>
                  Anti-besin Faktörleri
                </button>
              </div>

              <div class="detail-tab-content active" id="basic-content">
                <div class="table-container">
                  <table class="modern-table" id="basic-analysis-table">
                    <thead>
                      <tr>
                        <th>Temel Besin Değeri</th>
                        <th>Gerekli</th>
                        <th>Sağlanan</th>
                        <th>Fark</th>
                        <th>Durum</th>
                        <th>Öneri</th>
                      </tr>
                    </thead>
                    <tbody id="basic-analysis-tbody"></tbody>
                  </table>
                </div>
              </div>

              <div class="detail-tab-content" id="protein-content">
                <div class="table-container">
                  <table class="modern-table" id="protein-analysis-table">
                    <thead>
                      <tr>
                        <th>Protein Fraksiyonu</th>
                        <th>Gerekli</th>
                        <th>Sağlanan</th>
                        <th>Fark</th>
                        <th>Durum</th>
                        <th>Öneri</th>
                      </tr>
                    </thead>
                    <tbody id="protein-analysis-tbody"></tbody>
                  </table>
                </div>
              </div>

              <div class="detail-tab-content" id="energy-content">
                <div class="table-container">
                  <table class="modern-table" id="energy-analysis-table">
                    <thead>
                      <tr>
                        <th>Enerji Fraksiyonu</th>
                        <th>Gerekli</th>
                        <th>Sağlanan</th>
                        <th>Fark</th>
                        <th>Durum</th>
                        <th>Öneri</th>
                      </tr>
                    </thead>
                    <tbody id="energy-analysis-tbody"></tbody>
                  </table>
                </div>
              </div>

              <div class="detail-tab-content" id="carbohydrate-content">
                <div class="table-container">
                  <table class="modern-table" id="carbohydrate-analysis-table">
                    <thead>
                      <tr>
                        <th>Karbohidrat Fraksiyonu</th>
                        <th>Gerekli</th>
                        <th>Sağlanan</th>
                        <th>Fark</th>
                        <th>Durum</th>
                        <th>Öneri</th>
                      </tr>
                    </thead>
                    <tbody id="carbohydrate-analysis-tbody"></tbody>
                  </table>
                </div>
              </div>

              <div class="detail-tab-content" id="fiber-content">
                <div class="table-container">
                  <table class="modern-table" id="fiber-analysis-table">
                    <thead>
                      <tr>
                        <th>Fiber Fraksiyonu</th>
                        <th>Gerekli</th>
                        <th>Sağlanan</th>
                        <th>Fark</th>
                        <th>Durum</th>
                        <th>Öneri</th>
                      </tr>
                    </thead>
                    <tbody id="fiber-analysis-tbody"></tbody>
                  </table>
                </div>
              </div>

              <div class="detail-tab-content" id="minerals-content">
                <div class="table-container">
                  <table class="modern-table" id="minerals-analysis-table">
                    <thead>
                      <tr>
                        <th>Mineral</th>
                        <th>Gerekli</th>
                        <th>Sağlanan</th>
                        <th>Fark</th>
                        <th>Durum</th>
                        <th>Öneri</th>
                      </tr>
                    </thead>
                    <tbody id="minerals-analysis-tbody"></tbody>
                  </table>
                </div>
              </div>

              <div class="detail-tab-content" id="vitamins-content">
                <div class="table-container">
                  <table class="modern-table" id="vitamins-analysis-table">
                    <thead>
                      <tr>
                        <th>Vitamin</th>
                        <th>Gerekli</th>
                        <th>Sağlanan</th>
                        <th>Fark</th>
                        <th>Durum</th>
                        <th>Öneri</th>
                      </tr>
                    </thead>
                    <tbody id="vitamins-analysis-tbody"></tbody>
                  </table>
                </div>
              </div>

              <div class="detail-tab-content" id="anti-nutrients-content">
                <div class="table-container">
                  <table class="modern-table" id="anti-nutrients-analysis-table">
                    <thead>
                      <tr>
                        <th>Anti-besin Faktörü</th>
                        <th>Maks. Güvenli</th>
                        <th>Sağlanan</th>
                        <th>Fark</th>
                        <th>Durum</th>
                        <th>Öneri</th>
                      </tr>
                    </thead>
                    <tbody id="anti-nutrients-analysis-tbody"></tbody>
                  </table>
                </div>
              </div>
            </div>

            <div class="detail-modal-footer">
              <div class="footer-actions">
                <button type="button" class="btn btn-secondary" id="close-detailed-modal">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 6L6 18"></path>
                    <path d="M6 6l12 12"></path>
                  </svg>
                  Kapat
                </button>
                <button type="button" class="btn btn-primary" id="export-detailed-analysis">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7,10 12,15 17,10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                  Raporu İndir
                </button>
              </div>
            </div>
          </div>
        </div>
      `;

      document.body.insertAdjacentHTML('beforeend', modalHTML);

      // Modal close event listeners
      const modal = document.getElementById('detailed-analysis-modal');
      const closeBtn = modal.querySelector('#close-detailed-analysis-modal');
      const closeFooterBtn = modal.querySelector('#close-detailed-modal');

      closeBtn.onclick = () => modal.classList.add('hidden');
      closeFooterBtn.onclick = () => modal.classList.add('hidden');

      // Close modal when clicking outside
      modal.onclick = (e) => {
        if (e.target === modal) {
          modal.classList.add('hidden');
        }
      };

      // Setup tab functionality for this modal
      setupDetailedAnalysisTabSystem(modal);
    }

    // Detaylı analiz butonuna event listener ekle
    detailedBtn.onclick = () => {
      populateDetailedAnalysisModal(validationResults.detailed, validationResults.modalBasic);
      const modal = document.getElementById('detailed-analysis-modal');
      modal.classList.remove('hidden');
    };
  }

  function populateDetailedAnalysisModal(detailedResults, modalBasicResults) {
    // Kategorilere göre sonuçları grupla
    const categories = {
      basic: modalBasicResults, // Modal için ayrı basic kategorisi (sadece Kuru Madde ve Ham Protein)
      protein: detailedResults.filter(r => r.category === 'protein'),
      energy: detailedResults.filter(r => r.category === 'energy'),
      carbohydrate: detailedResults.filter(r => r.category === 'carbohydrate'),
      fiber: detailedResults.filter(r => r.category === 'fiber'),
      'macro-minerals': detailedResults.filter(r => r.category === 'macro-minerals'),
      'micro-minerals': detailedResults.filter(r => r.category === 'micro-minerals'),
      vitamins: detailedResults.filter(r => r.category === 'vitamins'),
      'anti-nutrients': detailedResults.filter(r => r.category === 'anti-nutrients')
    };

    // Her kategori için tabloları doldur
    Object.keys(categories).forEach(category => {
      let tableId;
      switch(category) {
        case 'basic':
          tableId = 'basic-analysis-tbody';
          break;
        case 'protein':
          tableId = 'protein-analysis-tbody';
          break;
        case 'energy':
          tableId = 'energy-analysis-tbody';
          break;
        case 'carbohydrate':
          tableId = 'carbohydrate-analysis-tbody';
          break;
        case 'fiber':
          tableId = 'fiber-analysis-tbody';
          break;
        case 'macro-minerals':
          tableId = 'minerals-analysis-tbody';
          break;
        case 'micro-minerals':
          // Micro-minerals'ı macro-minerals ile birleştir
          tableId = null; // Bu kategoriyi ayrı işleyeceğiz
          break;
        case 'vitamins':
          tableId = 'vitamins-analysis-tbody';
          break;
        case 'anti-nutrients':
          tableId = 'anti-nutrients-analysis-tbody';
          break;
      }

      if (tableId) {
        const tbody = document.getElementById(tableId);
        if (tbody) {
          tbody.innerHTML = renderDetailedTableRows(categories[category]);
        }
      }
    });

    // Mineraller tablosunu özel olarak işle (macro + micro birleştir)
    const mineralsTableBody = document.getElementById('minerals-analysis-tbody');
    if (mineralsTableBody) {
      const allMinerals = [...categories['macro-minerals'], ...categories['micro-minerals']];
      mineralsTableBody.innerHTML = renderDetailedTableRows(allMinerals);
    }
  }

  function renderDetailedTableRows(results) {
    return results.map(result => {
      const differenceText = result.difference >= 0
        ? `+${result.difference.toFixed(2)}`
        : result.difference.toFixed(2);

      const percentText = result.percentDiff >= 0
        ? `+${result.percentDiff.toFixed(1)}%`
        : `${result.percentDiff.toFixed(1)}%`;

      return `
        <tr class="validation-row ${result.statusClass}">
          <td><strong>${result.name}</strong></td>
          <td>${result.required.toFixed(2)} ${result.unit}</td>
          <td>${result.provided.toFixed(2)} ${result.unit}</td>
          <td>
            <span class="difference-value ${result.statusClass}">
              ${differenceText} (${percentText})
            </span>
          </td>
          <td>
            <span class="status-badge ${result.statusClass}">
              ${result.status}
            </span>
          </td>
          <td class="recommendation-cell">
            ${result.recommendation}
          </td>
        </tr>
      `;
    }).join('');
  }

  function setupDetailedAnalysisTabSystem(modal) {
    const tabLinks = modal.querySelectorAll('.detail-tab-link');
    const tabContents = modal.querySelectorAll('.detail-tab-content');

    function switchTab(activeTabLink) {
      tabLinks.forEach(link => link.classList.remove('active'));
      activeTabLink.classList.add('active');
      const targetTabId = activeTabLink.dataset.tab;
      tabContents.forEach(content => {
        content.classList.remove('active');
        if (content.id === targetTabId) {
          content.classList.add('active');
        }
      });
    }

    tabLinks.forEach(link => {
      link.onclick = () => switchTab(link);
    });

    // Activate the first tab by default
    if (tabLinks.length > 0) switchTab(tabLinks[0]);
  }
}

// Global variables for feed management
let feedsData = [];
let editingFeedId = null;
let isModalOpen = false;

function setupFeedManagement() {


  // Load feeds from database
  async function loadFeeds() {
    try {

      const currentProfile = JSON.parse(localStorage.getItem('currentProfile') || '{}');
      const feeds = await window.api.invoke('get-feeds', currentProfile.id || 1);
      feedsData = feeds;

      renderFeedsTable();

      // Also update ration builder dropdown
      if (window.reloadRationFeeds) {
        window.reloadRationFeeds();
      }
    } catch (error) {

      window.toast?.error('Yemler yüklenirken hata oluştu');
    }
  }

  // Render feeds table
  function renderFeedsTable() {
    const tableBody = document.querySelector('#feeds-section tbody');
    if (!tableBody) {

      return;
    }

    if (feedsData.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="6" class="text-center text-muted">
            Henüz yem eklenmemiş. "Yeni Yem Ekle" butonunu kullanarak yem ekleyebilirsiniz.
          </td>
        </tr>
      `;
      return;
    }

    tableBody.innerHTML = feedsData.map(feed => `
      <tr>
        <td>
          <div class="feed-name">
            <strong>${feed.YemAdi}</strong>
            ${feed.YemTipi ? `<span class="feed-type-badge">${getFeedTypeLabel(feed.YemTipi)}</span>` : ''}
          </div>
        </td>
        <td class="text-center">${feed.KuruMadde ? feed.KuruMadde.toFixed(1) : '-'}</td>
        <td class="text-center">${feed.HamProtein ? feed.HamProtein.toFixed(1) : '-'}</td>
        <td class="text-center">${feed.MetabolikEnerji ? feed.MetabolikEnerji.toFixed(1) : '-'}</td>
        <td class="text-center">${feed.BirimFiyat ? feed.BirimFiyat.toFixed(2) + ' ₺' : '-'}</td>
        <td class="actions">
          <button class="btn btn-sm btn-outline" onclick="editFeed(${feed.Id})" title="Düzenle">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
          </button>
          <button class="btn btn-sm btn-danger" onclick="deleteFeed(${feed.Id})" title="Sil">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            </svg>
          </button>
        </td>
      </tr>
    `).join('');
  }

  function getFeedTypeLabel(type) {
    const types = {
      'kaba-yem': 'Kaba Yem',
      'konsantre': 'Konsantre',
      'silaj': 'Silaj',
      'mineral': 'Mineral',
      'vitamin': 'Vitamin',
      'diger': 'Diğer'
    };
    return types[type] || type;
  }

  // Load suppliers for dropdown
  async function loadSuppliers() {
    try {
      const currentProfile = JSON.parse(localStorage.getItem('currentProfile'));
      if (!currentProfile) return;

      const suppliers = await window.api.invoke('get-cariler', currentProfile.id);
      const supplierSelect = document.getElementById('supplier');

      if (supplierSelect) {
        supplierSelect.innerHTML = '<option value="">Seçiniz</option>' +
          suppliers.map(supplier =>
            `<option value="${supplier.Id}">${supplier.Unvan}</option>`
          ).join('');
      }
    } catch (error) {
      console.error('Error loading suppliers:', error);
    }
  }

  // Open modal for adding new feed
  function openAddFeedModal() {

    editingFeedId = null;
    isModalOpen = true;

    const addFeedModal = document.getElementById('add-feed-modal');
    const addFeedForm = document.getElementById('add-feed-form');
    const modalTitle = document.querySelector('#add-feed-modal .modal-title');

    // Reset form and set title
    if (addFeedForm) {
      addFeedForm.reset();
    }

    if (modalTitle) {
      modalTitle.textContent = 'Yeni Yem Ekle';
    }

    // Show modal
    if (addFeedModal) {
      addFeedModal.style.display = 'flex';
    }
    document.body.style.overflow = 'hidden';

    // Load suppliers for dropdown
    loadSuppliers();


  }

  // Close modal
  function closeFeedModal() {

    const addFeedModal = document.getElementById('add-feed-modal');
    const addFeedForm = document.getElementById('add-feed-form');

    if (addFeedModal) {
      addFeedModal.style.display = 'none';
    }
    document.body.style.overflow = 'auto';

    if (addFeedForm) {
      addFeedForm.reset();
    }

    editingFeedId = null;
    isModalOpen = false;

  }

  // Save feed
  async function saveFeed(formData) {


    try {
      let currentProfile = JSON.parse(localStorage.getItem('currentProfile'));

      // Try alternative profile keys if currentProfile is null
      if (!currentProfile) {
        currentProfile = JSON.parse(localStorage.getItem('selectedProfile'));
      }
      if (!currentProfile) {
        currentProfile = JSON.parse(localStorage.getItem('userProfile'));
      }

      if (!currentProfile) {
        // For testing purposes, use a default profile
        currentProfile = { id: 1 };
      }



      const feedData = {
        profilId: currentProfile.id,
        yemAdi: formData.get('feedName'),
        yemTipi: formData.get('feedType'),
        aciklama: formData.get('feedDescription'),

        // Temel besin değerleri (restored rdp, rup, nisasta, seker, kul)
        kuruMadde: parseFloat(formData.get('dryMatter')) || null,
        hamProtein: parseFloat(formData.get('crudeProtein')) || null,
        rdp: parseFloat(formData.get('rdp')) || null,
        rup: parseFloat(formData.get('rup')) || null,
        metabolikEnerji: parseFloat(formData.get('metabolicEnergy')) || null,
        nel: parseFloat(formData.get('nel')) || null,
        hamYag: parseFloat(formData.get('crudeFat')) || null,

        // Karbohidrat fraksiyonları
        nisasta: parseFloat(formData.get('starch')) || null,
        seker: parseFloat(formData.get('sugar')) || null,
        kul: parseFloat(formData.get('ash')) || null,

        // Fiber fraksiyonları
        ndf: parseFloat(formData.get('ndf')) || null,
        adf: parseFloat(formData.get('adf')) || null,
        hemiseluloz: parseFloat(formData.get('hemicellulose')) || null,
        seluloz: parseFloat(formData.get('cellulose')) || null,
        lignin: parseFloat(formData.get('lignin')) || null,

        // Makro mineraller
        kalsiyum: parseFloat(formData.get('calcium')) || null,
        fosfor: parseFloat(formData.get('phosphorus')) || null,
        magnezyum: parseFloat(formData.get('magnesium')) || null,
        potasyum: parseFloat(formData.get('potassium')) || null,
        sodyum: parseFloat(formData.get('sodium')) || null,
        klorur: parseFloat(formData.get('chloride')) || null,
        sulfur: parseFloat(formData.get('sulfur')) || null,

        // Mikro mineraller
        demir: parseFloat(formData.get('iron')) || null,
        cinko: parseFloat(formData.get('zinc')) || null,
        bakir: parseFloat(formData.get('copper')) || null,
        manganez: parseFloat(formData.get('manganese')) || null,
        selenyum: parseFloat(formData.get('selenium')) || null,
        iyot: parseFloat(formData.get('iodine')) || null,
        kobalt: parseFloat(formData.get('cobalt')) || null,
        molibden: parseFloat(formData.get('molybdenum')) || null,

        // Vitaminler
        vitaminA: parseFloat(formData.get('vitaminA')) || null,
        vitaminD: parseFloat(formData.get('vitaminD')) || null,
        vitaminE: parseFloat(formData.get('vitaminE')) || null,
        vitaminK: parseFloat(formData.get('vitaminK')) || null,
        thiamin: parseFloat(formData.get('thiamin')) || null,
        riboflavin: parseFloat(formData.get('riboflavin')) || null,
        niacin: parseFloat(formData.get('niacin')) || null,
        biotin: parseFloat(formData.get('biotin')) || null,
        folat: parseFloat(formData.get('folate')) || null,
        cobalamin: parseFloat(formData.get('cobalamin')) || null,

        // Anti-besin faktörleri
        tanen: parseFloat(formData.get('tannin')) || null,
        fitat: parseFloat(formData.get('phytate')) || null,
        saponin: parseFloat(formData.get('saponin')) || null,
        oksalat: parseFloat(formData.get('oxalate')) || null,

        // Ekonomik bilgiler
        birimFiyat: parseFloat(formData.get('unitPrice')) || null,
        tedarikciId: formData.get('supplier') ? parseInt(formData.get('supplier')) : null
      };



      // Validate feedData
      if (!feedData || typeof feedData !== 'object') {

        window.toast?.error('Form verisi geçersiz');
        return;
      }

      if (!feedData.yemAdi || feedData.yemAdi.trim() === '') {

        window.toast?.error('Yem adı gereklidir');
        return;
      }



      let result;
      if (editingFeedId) {


        // Call update-feed handler with combined object
        const updateData = { feedId: editingFeedId, ...feedData };

        result = await window.api.invoke('update-feed', updateData);
        window.toast?.success('Yem başarıyla güncellendi (test mode)');
      } else {

        result = await window.api.invoke('add-feed', feedData);
        window.toast?.success('Yem başarıyla eklendi');
      }

      if (result.success) {
        closeFeedModal();
        loadFeeds();
      } else {

      }
    } catch (error) {

      window.toast?.error('Yem kaydedilirken hata oluştu');
    }
  }

  // Event listeners setup
  function setupEventListeners() {


    // Add feed button
    const addFeedModalBtn = document.getElementById('add-feed-modal-btn');
    if (addFeedModalBtn) {
      addFeedModalBtn.addEventListener('click', openAddFeedModal);

    }

    // Form submission
    const addFeedForm = document.getElementById('add-feed-form');
    if (addFeedForm) {
      addFeedForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        console.log('Form submitted via submit event!');
        console.log('Form target:', e.target);
        console.log('Form target elements:', e.target.elements.length);
        const formData = new FormData(e.target);
        console.log('FormData from submit event:', formData);
        await saveFeed(formData);
      });
      console.log('Form submit listener attached');
    }

    // Modal close buttons
    const closeBtn = document.getElementById('close-feed-modal');
    const cancelBtn = document.getElementById('cancel-feed-modal');

    if (closeBtn) {
      closeBtn.addEventListener('click', closeFeedModal);
      console.log('Close button listener attached');
    }

    if (cancelBtn) {
      cancelBtn.addEventListener('click', closeFeedModal);

    }

    // Save button
    const saveBtn = document.getElementById('save-feed-btn');
    if (saveBtn) {
      saveBtn.addEventListener('click', async (e) => {
        e.preventDefault();
        const form = document.getElementById('add-feed-form');
        if (form) {
          const formData = new FormData(form);
          await saveFeed(formData);
        }
      });

    }
  }

  // Global functions for table actions
  window.editFeed = async function(feedId) {
    try {
      const feed = feedsData.find(f => f.Id === feedId);
      if (!feed) {
        return;
      }

      editingFeedId = feedId;
      isModalOpen = true;

      // Fill form with feed data
      document.getElementById('feed-name').value = feed.YemAdi || '';
      document.getElementById('feed-type').value = feed.YemTipi || '';
      document.getElementById('feed-description').value = feed.Aciklama || '';

      // Temel besin değerleri
      document.getElementById('dry-matter').value = feed.KuruMadde || '';
      document.getElementById('crude-protein').value = feed.HamProtein || '';
      document.getElementById('rdp').value = feed.RDP || '';
      document.getElementById('rup').value = feed.RUP || '';
      document.getElementById('metabolic-energy').value = feed.MetabolikEnerji || '';
      document.getElementById('nel').value = feed.NEL || '';
      document.getElementById('crude-fat').value = feed.HamYag || '';

      // Karbohidrat fraksiyonları
      document.getElementById('starch').value = feed.Nisasta || '';
      document.getElementById('sugar').value = feed.Seker || '';
      document.getElementById('ash').value = feed.Kul || '';

      // Fiber fraksiyonları
      document.getElementById('ndf').value = feed.NDF || '';
      document.getElementById('adf').value = feed.ADF || '';
      document.getElementById('hemicellulose').value = feed.Hemiseluloz || '';
      document.getElementById('cellulose').value = feed.Seluloz || '';
      document.getElementById('lignin').value = feed.Lignin || '';

      // Makro mineraller
      document.getElementById('calcium').value = feed.Kalsiyum || '';
      document.getElementById('phosphorus').value = feed.Fosfor || '';
      document.getElementById('magnesium').value = feed.Magnezyum || '';
      document.getElementById('potassium').value = feed.Potasyum || '';
      document.getElementById('sodium').value = feed.Sodyum || '';
      document.getElementById('chloride').value = feed.Klorur || '';
      document.getElementById('sulfur').value = feed.sulfur || '';

      // Mikro mineraller
      document.getElementById('iron').value = feed.Demir || '';
      document.getElementById('zinc').value = feed.Cinko || '';
      document.getElementById('copper').value = feed.Bakir || '';
      document.getElementById('manganese').value = feed.Manganez || '';
      document.getElementById('selenium').value = feed.Selenyum || '';
      document.getElementById('iodine').value = feed.Iyot || '';
      document.getElementById('cobalt').value = feed.Kobalt || '';
      document.getElementById('molybdenum').value = feed.Molibden || '';

      // Vitaminler
      document.getElementById('vitamin-a').value = feed.VitaminA || '';
      document.getElementById('vitamin-d').value = feed.VitaminD || '';
      document.getElementById('vitamin-e').value = feed.VitaminE || '';
      document.getElementById('vitamin-k').value = feed.VitaminK || '';
      document.getElementById('thiamin').value = feed.Thiamin || '';
      document.getElementById('riboflavin').value = feed.Riboflavin || '';
      document.getElementById('niacin').value = feed.Niacin || '';
      document.getElementById('biotin').value = feed.Biotin || '';
      document.getElementById('folate').value = feed.Folat || '';
      document.getElementById('cobalamin').value = feed.Cobalamin || '';

      // Anti-besin faktörleri
      document.getElementById('tannin').value = feed.Tanen || '';
      document.getElementById('phytate').value = feed.Fitat || '';
      document.getElementById('saponin').value = feed.Saponin || '';
      document.getElementById('oxalate').value = feed.Oksalat || '';

      // Ekonomik bilgiler
      document.getElementById('unit-price').value = feed.BirimFiyat || '';
      document.getElementById('supplier').value = feed.TedarikciId || '';

      const modalTitle = document.querySelector('#add-feed-modal .modal-title');
      if (modalTitle) {
        modalTitle.textContent = 'Yem Düzenle';
      }

      const addFeedModal = document.getElementById('add-feed-modal');
      if (addFeedModal) {
        addFeedModal.style.display = 'flex';
      }
      document.body.style.overflow = 'hidden';
      loadSuppliers();


    } catch (error) {

      window.toast?.error('Yem düzenlenirken hata oluştu');
    }
  };

  window.deleteFeed = async function(feedId) {
    // Show confirmation modal instead of confirm()
    showConfirmationModal(
      'Yem Silme Onayı',
      'Bu yemi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
      async () => {
        try {
          const result = await window.api.invoke('delete-feed', feedId);
          if (result.success) {
            window.toast?.success('Yem başarıyla silindi');
            loadFeeds();
          }
        } catch (error) {

          window.toast?.error('Yem silinirken hata oluştu');
        }
      }
    );
  };

  // Initialize
  setupEventListeners();
  loadFeeds();
}



// Modal functions to replace alert(), confirm(), and prompt()
function showRationNameModal(callback) {
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10002;
  `;

  const modalContent = document.createElement('div');
  modalContent.className = 'modal-content';
  modalContent.style.cssText = `
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 90%;
  `;

  modalContent.innerHTML = `
    <h3 style="margin: 0 0 16px 0; color: #1f2937;">Rasyon Adı</h3>
    <p style="margin: 0 0 16px 0; color: #6b7280;">Kaydedilecek rasyonun adını girin:</p>
    <input type="text" id="ration-name-input" placeholder="Rasyon adı..." style="
      width: 100%;
      padding: 12px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 14px;
      margin-bottom: 20px;
      box-sizing: border-box;
    ">
    <div style="display: flex; gap: 12px; justify-content: flex-end;">
      <button id="cancel-ration-btn" style="
        padding: 10px 20px;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
      ">İptal</button>
      <button id="save-ration-btn-modal" style="
        padding: 10px 20px;
        border: none;
        background: #10b981;
        color: white;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
      ">Kaydet</button>
    </div>
  `;

  modal.appendChild(modalContent);
  document.body.appendChild(modal);

  const input = modal.querySelector('#ration-name-input');
  const saveBtn = modal.querySelector('#save-ration-btn-modal');
  const cancelBtn = modal.querySelector('#cancel-ration-btn');

  // Focus input
  setTimeout(() => input.focus(), 100);

  // Handle save
  const handleSave = () => {
    const name = input.value.trim();
    if (name) {
      document.body.removeChild(modal);
      callback(name);
    } else {
      window.toast?.warning('Lütfen rasyon adını girin.');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    document.body.removeChild(modal);
  };

  // Event listeners
  saveBtn.addEventListener('click', handleSave);
  cancelBtn.addEventListener('click', handleCancel);
  input.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') handleSave();
    if (e.key === 'Escape') handleCancel();
  });

  // Close on overlay click
  modal.addEventListener('click', (e) => {
    if (e.target === modal) handleCancel();
  });
}

function showConfirmationModal(title, message, onConfirm) {
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10002;
  `;

  const modalContent = document.createElement('div');
  modalContent.className = 'modal-content';
  modalContent.style.cssText = `
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 90%;
  `;

  modalContent.innerHTML = `
    <h3 style="margin: 0 0 16px 0; color: #1f2937;">${title}</h3>
    <p style="margin: 0 0 24px 0; color: #6b7280; line-height: 1.5;">${message}</p>
    <div style="display: flex; gap: 12px; justify-content: flex-end;">
      <button id="cancel-confirm-btn" style="
        padding: 10px 20px;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
      ">İptal</button>
      <button id="confirm-btn" style="
        padding: 10px 20px;
        border: none;
        background: #ef4444;
        color: white;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
      ">Onayla</button>
    </div>
  `;

  modal.appendChild(modalContent);
  document.body.appendChild(modal);

  const confirmBtn = modal.querySelector('#confirm-btn');
  const cancelBtn = modal.querySelector('#cancel-confirm-btn');

  // Handle confirm
  const handleConfirm = () => {
    document.body.removeChild(modal);
    onConfirm();
  };

  // Handle cancel
  const handleCancel = () => {
    document.body.removeChild(modal);
  };

  // Event listeners
  confirmBtn.addEventListener('click', handleConfirm);
  cancelBtn.addEventListener('click', handleCancel);

  // Close on overlay click
  modal.addEventListener('click', (e) => {
    if (e.target === modal) handleCancel();
  });

  // Keyboard support
  document.addEventListener('keydown', function escapeHandler(e) {
    if (e.key === 'Escape') {
      handleCancel();
      document.removeEventListener('keydown', escapeHandler);
    }
  });
}

function setupNutritionalCalculation() {
  const calculateBtn = document.getElementById('calculate-nutrition-btn');
  const animalCategorySelect = document.getElementById('animal-category');
  const milkYieldInput = document.getElementById('milk-yield');

  // Add event listeners for form validation
  animalCategorySelect?.addEventListener('change', toggleMilkFields);

  calculateBtn?.addEventListener('click', () => {
    calculateNutritionalNeeds();
  });

  // Initial setup
  toggleMilkFields();
}

function toggleMilkFields() {
  const animalCategory = document.getElementById('animal-category').value;
  const milkYieldInput = document.getElementById('milk-yield');
  const milkFatInput = document.getElementById('milk-fat');

  // Disable milk fields for non-dairy animals
  if (animalCategory === 'calf' || animalCategory === 'bull') {
    milkYieldInput.disabled = true;
    milkFatInput.disabled = true;
    milkYieldInput.value = '0';
    milkFatInput.value = '0';
    milkYieldInput.style.backgroundColor = 'var(--bg-tertiary)';
    milkFatInput.style.backgroundColor = 'var(--bg-tertiary)';
  } else {
    milkYieldInput.disabled = false;
    milkFatInput.disabled = false;
    milkYieldInput.style.backgroundColor = '';
    milkFatInput.style.backgroundColor = '';
    if (milkYieldInput.value === '0') milkYieldInput.value = '';
    if (milkFatInput.value === '0') milkFatInput.value = '';
  }
}

function calculateNutritionalNeeds() {
  // Get form values
  const animalCategory = document.getElementById('animal-category').value;
  const liveWeight = parseFloat(document.getElementById('live-weight').value);
  const milkYield = parseFloat(document.getElementById('milk-yield').value) || 0;
  const milkFat = parseFloat(document.getElementById('milk-fat').value) || 3.5;
  const pregnancyStatus = document.getElementById('pregnancy-status').value;
  const activityLevel = document.getElementById('activity-level').value;

  // Validate inputs
  if (!animalCategory || !liveWeight) {
    window.toast?.warning('Lütfen hayvan kategorisi ve canlı ağırlık bilgilerini girin.');
    return;
  }

  if (liveWeight < 50 || liveWeight > 1000) {
    window.toast?.error('Canlı ağırlık 50-1000 kg arasında olmalıdır.');
    return;
  }

  // Calculate nutritional requirements based on NRC guidelines
  const requirements = calculateRequirements(animalCategory, liveWeight, milkYield, milkFat, pregnancyStatus, activityLevel);

  // Update the table
  updateNutritionalNeedsTable(requirements);
}

function calculateRequirements(category, weight, milkYield, milkFat, pregnancy, activity) {
  let requirements = {
    dryMatter: 0,
    protein: 0,
    rdp: 0,
    rup: 0,
    mp: 0,
    energy: 0,
    nel: 0,
    nem: 0,
    tdn: 0,
    de: 0,
    fat: 0,
    ndf: 0,
    adf: 0,
    hemicellulose: 0,
    cellulose: 0,
    lignin: 0,
    calcium: 0,
    phosphorus: 0,
    magnesium: 0,
    potassium: 0,
    sodium: 0,
    zinc: 0,
    copper: 0,
    manganese: 0,
    selenium: 0,
    iodine: 0,
    cobalt: 0,
    molybdenum: 0,
    sulfur: 0,
    vitaminA: 0,
    vitaminD: 0,
    vitaminE: 0,
    thiamin: 0,
    riboflavin: 0,
    niacin: 0,
    biotin: 0,
    folate: 0,
    cobalamin: 0,
    vitaminK: 0,
    tannin: 0,
    phytate: 0,
    starchDigestibility: 0,
    saponin: 0,
    oxalate: 0,
    sources: {
      dryMatter: '',
      protein: '',
      rdp: '',
      rup: '',
      mp: '',
      energy: '',
      nel: '',
      nem: '',
      tdn: '',
      de: '',
      fat: '',
      ndf: '',
      adf: '',
      hemicellulose: '',
      cellulose: '',
      lignin: '',
      calcium: '',
      phosphorus: '',
      magnesium: '',
      potassium: '',
      sodium: '',
      zinc: '',
      copper: '',
      manganese: '',
      selenium: '',
      iodine: '',
      cobalt: '',
      molybdenum: '',
      sulfur: '',
      vitaminA: '',
      vitaminD: '',
      vitaminE: '',
      thiamin: '',
      riboflavin: '',
      niacin: '',
      biotin: '',
      folate: '',
      cobalamin: '',
      vitaminK: '',
      tannin: '',
      phytate: '',
      starchDigestibility: '',
      saponin: '',
      oxalate: ''
    }
  };

  // Activity level multipliers
  const activityMultiplier = {
    'low': 0.9,
    'normal': 1.0,
    'high': 1.1
  };

  const actMult = activityMultiplier[activity] || 1.0;

  switch (category) {
    case 'dairy-cow':
      requirements = calculateDairyCowRequirements(weight, milkYield, milkFat, pregnancy, actMult);
      break;
    case 'calf':
      requirements = calculateCalfRequirements(weight, actMult);
      break;
    case 'heifer':
      requirements = calculateHeiferRequirements(weight, pregnancy, actMult);
      break;
    case 'bull':
      requirements = calculateBullRequirements(weight, actMult);
      break;
    default:
      window.toast?.error('Lütfen geçerli bir hayvan kategorisi seçin.');
      return null;
  }

  return requirements;
}

function calculateDairyCowRequirements(weight, milkYield, milkFat, pregnancy, actMult) {
  // NRC 2001 Dairy Cattle guidelines

  // Maintenance requirements
  const maintenanceEnergy = 0.08 * Math.pow(weight, 0.75) * actMult; // Mcal NEL
  const maintenanceProtein = 3.8 * Math.pow(weight, 0.75) / 1000; // kg MP

  // Milk production requirements
  const fatCorrectedMilk = milkYield * (0.4324 + 0.1624 * milkFat); // 4% FCM
  const milkEnergy = fatCorrectedMilk * 0.7; // Mcal NEL per kg 4% FCM
  const milkProtein = milkYield * 0.034; // kg crude protein per L milk

  // Pregnancy requirements (last trimester)
  let pregnancyEnergy = 0;
  let pregnancyProtein = 0;

  if (pregnancy === 'late') {
    pregnancyEnergy = 2.0; // Mcal NEL
    pregnancyProtein = 0.15; // kg crude protein
  } else if (pregnancy === 'mid') {
    pregnancyEnergy = 1.0;
    pregnancyProtein = 0.08;
  } else if (pregnancy === 'early') {
    pregnancyEnergy = 0.3;
    pregnancyProtein = 0.03;
  }

  // Energy fractions (NRC 2001)
  const totalNEL = maintenanceEnergy + milkEnergy + pregnancyEnergy; // Mcal NEL
  const totalNEM = maintenanceEnergy * actMult; // Mcal NEM (maintenance only)

  // Convert NEL to ME using proper efficiency (NEL = ME × 0.64-0.66)
  const nelToMeEfficiency = 0.64; // NRC 2001: NEL efficiency from ME
  const totalME = totalNEL / nelToMeEfficiency; // Mcal ME
  const totalEnergy = totalME * 4.184; // Convert ME to MJ

  // Dry matter intake (NRC prediction) - CORRECTED: Use realistic weeks in milk
  const weeksInMilk = 20; // Assume mid-lactation (can be made dynamic later)
  const dmiBase = (0.372 * fatCorrectedMilk + 0.0968 * Math.pow(weight, 0.75)) * (1 - Math.exp(-0.192 * (weeksInMilk + 3.67)));
  const dryMatter = dmiBase * actMult;

  // TDN calculation (NEL to TDN conversion) - CORRECTED: Use proper NRC conversion
  // TDN (% DM) = NEL (Mcal/kg DM) / 1.54 * 100 (NRC 2001: 1 kg TDN = 1.54 Mcal NEL)
  const nelConcentration = totalNEL / dryMatter; // Mcal NEL per kg DM
  const totalTDN = (nelConcentration / 1.54) * 100; // % DM (TDN as percentage of dry matter)

  // Digestible Energy calculation - CORRECTED: Use TDN amount, not percentage
  const tdnAmount = dryMatter * totalTDN / 100; // kg TDN per day
  const totalDE = tdnAmount * 4.4; // Mcal DE per day
  const totalMP = maintenanceProtein + milkProtein + pregnancyProtein; // Metabolizable protein
  const totalProtein = totalMP * 1.6; // Convert MP to crude protein

  // Protein fractions (NRC 2001)
  const rdpRatio = milkYield > 30 ? 0.65 : 0.68; // Lower RDP for high producers
  const rupRatio = 1 - rdpRatio;

  const rdp = totalProtein * rdpRatio; // Rumen degradable protein
  const rup = totalProtein * rupRatio; // Rumen undegradable protein

  // Microbial protein synthesis (13g MCP per 100g TDN)
  const microbialProtein = tdnAmount * 0.13; // kg MCP per day

  // Metabolizable protein from both sources
  const mpFromRUP = rup * 0.8; // 80% of RUP is absorbed
  const mpFromMCP = microbialProtein * 0.64; // 64% of MCP is true protein
  const calculatedMP = mpFromRUP + mpFromMCP;

  // Mineral requirements (NRC 2001) - Standardized to exact NRC units
  const calcium = weight * 0.043 + milkYield * 1.22 + (pregnancy === 'late' ? 7.1 : 0); // g/day
  const phosphorus = weight * 0.016 + milkYield * 0.90 + (pregnancy === 'late' ? 3.7 : 0); // g/day

  // Additional macro mineral requirements (NRC 2001) - g/day
  const magnesium = weight * 0.04 + milkYield * 0.15; // g/day
  const potassium = weight * 0.1 + milkYield * 1.5; // g/day
  const sodium = weight * 0.05 + milkYield * 0.4; // g/day
  const chlorine = sodium * 1.5; // g/day (Cl:Na ratio ~1.5:1)
  const sulfur = weight * 0.04 + milkYield * 0.05; // g/day (NRC 2001: 0.2% of DM)

  // Fat requirements (NRC 2001: 2-6% of DM) - g/day
  const fat = dryMatter * 40; // g/day (4% of DM, dryMatter in kg/day)

  // Carbohydrate fractions (NRC 2001) - g/day
  const starch = dryMatter * 250; // g/day (25% of DM, optimal for dairy cows)
  const sugar = dryMatter * 50; // g/day (5% of DM, optimal for rumen health)
  const ash = dryMatter * 80; // g/day (8% of DM, total mineral content)

  // Fiber fractions (NRC 2001) - converted to g/day for consistency
  const ndfPercent = milkYield > 35 ? 25 : 28; // % of DM (lower for high producers)
  const adfPercent = milkYield > 35 ? 19 : 21; // % of DM (lower for high producers)
  const ligninPercent = 3.5; // % of DM (typical for quality forages)

  // Convert fiber fractions to g/day
  const ndf = dryMatter * ndfPercent * 10; // g/day
  const adf = dryMatter * adfPercent * 10; // g/day
  const lignin = dryMatter * ligninPercent * 10; // g/day

  // Calculated fiber fractions - g/day
  const hemicellulose = ndf - adf; // g/day
  const cellulose = adf - lignin; // g/day

  // NFC calculation (Non-Fiber Carbohydrate) - % DM
  // NFC = 100 - (NDF + CP + EE + Ash) - All values must be in % DM
  const cpPercent = (totalProtein / dryMatter / 1000) * 100; // Convert g/day to kg/day, then to % DM
  const eePercent = (fat / dryMatter / 1000) * 100; // Convert g/day to kg/day, then to % DM
  const ashPercent = (ash / dryMatter / 1000) * 100; // Convert g/day to kg/day, then to % DM
  const nfcPercent = Math.max(0, 100 - (ndfPercent + cpPercent + eePercent + ashPercent)); // % DM

  // Trace mineral requirements (NRC 2001) - mg/day
  const iron = dryMatter * 50; // mg/day (50 mg/kg DM)
  const zinc = dryMatter * 40; // mg/day (40 mg/kg DM)
  const copper = dryMatter * 10; // mg/day (10 mg/kg DM)
  const manganese = dryMatter * 40; // mg/day (40 mg/kg DM)
  const selenium = dryMatter * 0.3; // mg/day (0.3 mg/kg DM)

  // Additional trace minerals (NRC 2001) - mg/day
  const iodine = dryMatter * (pregnancy !== 'not-pregnant' ? 0.8 : 0.5); // mg/day
  const cobalt = dryMatter * 0.1; // mg/day (0.1 mg/kg DM for B12 synthesis)
  const molybdenum = dryMatter * 0.15; // mg/day (0.15 mg/kg DM)

  // Vitamin requirements (NRC 2001)
  const vitaminA = weight * 110; // IU/day (110 IU/kg BW)
  const vitaminD = weight * 30; // IU/day (30 IU/kg BW)
  const vitaminE = dryMatter * 15; // IU/day (15 IU/kg DM)

  // B-Complex vitamins (NRC 2001) - Standardized daily requirements
  const thiamin = dryMatter * (milkYield > 30 ? 3 : 2); // mg/day (stress condition for high producers)
  const riboflavin = dryMatter * 3 + milkYield * 2; // mg/day (3 mg/kg DM + 2 mg/L milk)
  const niacin = dryMatter * (milkYield > 35 ? 25 : 15); // mg/day (higher for intensive production)
  const biotin = weight * 0.004; // mg/day (0.4 mg/100kg BW = 0.004 mg/kg BW)
  const folate = dryMatter * (pregnancy !== 'not-pregnant' ? 0.8 : 0.5); // mg/day (higher in pregnancy)
  const cobalamin = weight * 0.05; // μg/day (0.05 μg/kg BW - rumen synthesis covers most needs)
  const vitaminK = dryMatter * 1; // mg/day (1 mg/kg DM)

  // Anti-nutritional factors (NRC 2001 maximum safe levels) - converted to g/day
  // Formula: (% DM / 100) * kg DM/day * 1000 g/kg = g/day
  const tannin = (2.0 / 100) * dryMatter * 1000; // g/day (max safe: 2% of DM)
  const phytate = (1.0 / 100) * dryMatter * 1000; // g/day (max safe: 1% of DM)
  const starchDigestibility = milkYield > 30 ? 28 : 25; // % of DM (optimal 25-30%)
  const saponin = (0.5 / 100) * dryMatter * 1000; // g/day (max safe: 0.5% of DM)
  const oxalate = (2.0 / 100) * dryMatter * 1000; // g/day (max safe: 2% of DM)

  let sources = {
    dryMatter: 'Yaşama + Süt Üretimi',
    protein: 'Yaşama + Süt Üretimi',
    rdp: 'Rumen Mikroorganizmaları',
    rup: 'İnce Bağırsak Emilimi',
    mcp: 'TDN × 0.13 (Rumen Sentezi)',
    mp: 'RUP + Mikrobiyal Protein',
    energy: 'Yaşama + Süt Üretimi',
    nel: 'Süt Üretimi + Yaşama',
    nem: 'Temel Yaşam Fonksiyonları',
    nelMilk: 'FCM × 0.7 (Süt Üretimi)',
    nelPregnancy: 'Gebelik Dönemi (Trimester)',
    tdn: 'NEL Konsantrasyonu/1.54 (% KM)',
    nfc: '100 - (NDF + CP + EE + Ash)',
    de: 'Brüt - Dışkı Enerjisi',
    fat: 'Enerji + Süt Yağı',
    starch: 'Hızlı Enerji Kaynağı',
    sugar: 'Basit Karbohidrat',
    ash: 'Toplam Mineral Madde',
    ndf: 'Rumen Sağlığı',
    adf: 'Sindirilebilirlik Göstergesi',
    hemicellulose: 'Kolay Sindirilebilir Fiber',
    cellulose: 'Orta Sindirilebilir Fiber',
    lignin: 'Sindirilemeyen Fiber',
    calcium: 'Yaşama + Süt + Kemik',
    phosphorus: 'Yaşama + Süt + Kemik',
    magnesium: 'Çayır Tetanisi Önlemi',
    potassium: 'Elektrolit Dengesi',
    sodium: 'Su Dengesi + Süt',
    chlorine: 'Elektrolit + Asit-Baz Dengesi',
    iron: 'Hemoglobin + Oksijen Taşıma',
    zinc: 'Bağışıklık + Üreme',
    copper: 'Hemoglobin + Enzimler',
    manganese: 'Kemik + Üreme',
    selenium: 'Antioksidan + Kas',
    iodine: 'Tiroid Hormonları',
    cobalt: 'Vitamin B12 Sentezi',
    molybdenum: 'Enzim Kofaktörü',
    sulfur: 'Protein Sentezi',
    vitaminA: 'Görme + Bağışıklık',
    vitaminD: 'Kalsiyum Emilimi',
    vitaminE: 'Antioksidan + Kas',
    thiamin: 'Karbohidrat Metabolizması',
    riboflavin: 'Enerji Metabolizması',
    niacin: 'NAD/NADP Koenzimi',
    biotin: 'Tırnak Sağlığı',
    folate: 'DNA Sentezi',
    cobalamin: 'Sinir Sistemi',
    vitaminK: 'Kan Pıhtılaşması',
    tannin: 'Protein Emilimi Engeli',
    phytate: 'Mineral Şelatörü',
    starchDigestibility: 'Rumen Asidoz Kontrolü',
    saponin: 'Hücre Zarı Etkisi',
    oxalate: 'Kalsiyum Şelatörü'
  };

  if (pregnancy !== 'not-pregnant') {
    sources.dryMatter += ' + Gebelik';
    sources.protein += ' + Gebelik';
    sources.energy += ' + Gebelik';
    sources.calcium += ' + Gebelik';
    sources.phosphorus += ' + Gebelik';
    sources.magnesium += ' + Gebelik';
    sources.vitaminA += ' + Gebelik';
    sources.vitaminE += ' + Gebelik';
  }

  return {
    dryMatter: Math.max(dryMatter, weight * 0.025), // Minimum 2.5% of body weight
    protein: totalProtein,
    rdp: rdp,
    rup: rup,
    mcp: microbialProtein, // Mikrobiyal protein
    mp: calculatedMP,
    energy: totalEnergy,
    nel: totalNEL,
    nem: totalNEM,
    nelMilk: milkEnergy, // Süt üretimi enerjisi
    nelPregnancy: pregnancyEnergy, // Gebelik enerjisi
    tdn: totalTDN,
    nfc: nfcPercent,
    de: totalDE,
    fat: fat,
    starch: starch,
    sugar: sugar,
    ash: ash,
    ndf: ndf,
    adf: adf,
    hemicellulose: hemicellulose,
    cellulose: cellulose,
    lignin: lignin,
    calcium: calcium,
    phosphorus: phosphorus,
    magnesium: magnesium,
    potassium: potassium,
    sodium: sodium,
    chlorine: chlorine,
    iron: iron,
    zinc: zinc,
    copper: copper,
    manganese: manganese,
    selenium: selenium,
    iodine: iodine,
    cobalt: cobalt,
    molybdenum: molybdenum,
    sulfur: sulfur,
    vitaminA: vitaminA,
    vitaminD: vitaminD,
    vitaminE: vitaminE,
    thiamin: thiamin,
    riboflavin: riboflavin,
    niacin: niacin,
    biotin: biotin,
    folate: folate,
    cobalamin: cobalamin,
    vitaminK: vitaminK,
    tannin: tannin,
    phytate: phytate,
    starchDigestibility: starchDigestibility,
    saponin: saponin,
    oxalate: oxalate,
    sources: sources
  };
}

function calculateCalfRequirements(weight, actMult) {
  // Young calf requirements (NRC 2001)
  const dryMatter = weight * 0.028 * actMult; // 2.8% of body weight
  const protein = weight * 0.0045; // 0.45% of body weight as crude protein
  const energy = weight * 0.2 * 4.184; // Convert to MJ

  // Mineral requirements for calves (NRC 2001) - g/day
  const calcium = weight * 0.8; // g/day (higher for growth)
  const phosphorus = weight * 0.5; // g/day
  const magnesium = weight * 0.06; // g/day
  const potassium = weight * 0.12; // g/day
  const sodium = weight * 0.08; // g/day
  const chlorine = sodium * 1.5; // g/day
  const sulfur = weight * 0.05; // g/day (NRC 2001: 0.2% of DM)

  const fat = dryMatter * 50; // g/day (5% of DM for growth)

  // Fiber fractions for calves (NRC 2001) - % of DM
  const ndf = 22; // % of DM (lower for young calves)
  const adf = 15; // % of DM (lower for digestibility)
  const lignin = 2.5; // % of DM (lower lignin for young animals)
  const hemicellulose = ndf - adf; // % of DM
  const cellulose = adf - lignin; // % of DM

  // TDN for calves (NRC 2001) - % of DM
  const tdn = 75; // % DM (typical for calf starter feeds)

  // NFC calculation for calves (Non-Fiber Carbohydrate) - % DM
  // NFC = 100 - (NDF + CP + EE + Ash) - All values must be in % DM
  const cpPercent = (protein / dryMatter / 1000) * 100; // Convert g/day to kg/day, then to % DM
  const eePercent = (fat / dryMatter / 1000) * 100; // Convert g/day to kg/day, then to % DM
  const ashPercent = 8; // % DM (typical for calf feeds)
  const nfcPercent = Math.max(0, 100 - (ndf + cpPercent + eePercent + ashPercent)); // % DM

  // Protein fractions for calves (NRC 2001)
  const rdpRatio = 0.70; // Higher RDP for young calves
  const rupRatio = 1 - rdpRatio;

  const rdp = protein * rdpRatio; // Rumen degradable protein
  const rup = protein * rupRatio; // Rumen undegradable protein

  // Microbial protein synthesis for calves (13g MCP per 100g TDN)
  const tdnAmount = dryMatter * tdn / 100; // kg TDN per day
  const microbialProtein = tdnAmount * 0.13; // kg MCP per day

  // Metabolizable protein from both sources
  const mpFromRUP = rup * 0.8; // 80% of RUP is absorbed
  const mpFromMCP = microbialProtein * 0.64; // 64% of MCP is true protein
  const calculatedMP = mpFromRUP + mpFromMCP;

  // Trace mineral requirements for calves (NRC 2001) - mg/day
  const iron = dryMatter * 60; // mg/day (60 mg/kg DM for growth)
  const zinc = dryMatter * 50; // mg/day (50 mg/kg DM)
  const copper = dryMatter * 12; // mg/day (12 mg/kg DM)
  const manganese = dryMatter * 45; // mg/day (45 mg/kg DM)
  const selenium = dryMatter * 0.35; // mg/day (0.35 mg/kg DM)
  const iodine = dryMatter * 0.6; // mg/day (0.6 mg/kg DM)
  const cobalt = dryMatter * 0.12; // mg/day (0.12 mg/kg DM)
  const molybdenum = dryMatter * 0.2; // mg/day (0.2 mg/kg DM)

  // Higher vitamin needs for growth
  const vitaminA = weight * 150; // IU/day
  const vitaminD = weight * 40; // IU/day
  const vitaminE = dryMatter * 20; // IU/day

  // B-Complex vitamins for calves
  const thiamin = dryMatter * 4; // mg/day (higher for growth)
  const riboflavin = dryMatter * 4; // mg/day (higher for growth)
  const niacin = dryMatter * 20; // mg/day
  const biotin = weight * 0.006; // mg/day (0.6 mg/100kg BW = 0.006 mg/kg BW, higher for growth)
  const folate = dryMatter * 1.0; // mg/day (higher for growth)
  const cobalamin = weight * 0.08; // μg/day (0.08 μg/kg BW, higher for growth but still low due to rumen synthesis)
  const vitaminK = dryMatter * 1.5; // mg/day (higher for growth)

  // Anti-nutritional factors (NRC 2001 maximum safe levels) - converted to g/day
  const tannin = (2.0 / 100) * dryMatter * 1000; // g/day (max safe: 2% of DM)
  const phytate = (1.0 / 100) * dryMatter * 1000; // g/day (max safe: 1% of DM)
  const starchDigestibility = 25; // % of DM (optimal for calves)
  const saponin = (0.5 / 100) * dryMatter * 1000; // g/day (max safe: 0.5% of DM)
  const oxalate = (2.0 / 100) * dryMatter * 1000; // g/day (max safe: 2% of DM)

  return {
    dryMatter: dryMatter,
    protein: protein,
    rdp: rdp,
    rup: rup,
    mcp: microbialProtein,
    mp: calculatedMP,
    energy: energy,
    nelMilk: 0, // Buzağılar süt üretmez
    nelPregnancy: 0, // Buzağılar gebe olmaz
    fat: fat,
    ndf: ndf,
    adf: adf,
    hemicellulose: hemicellulose,
    cellulose: cellulose,
    lignin: lignin,
    tdn: tdn,
    nfc: nfcPercent,
    calcium: calcium,
    phosphorus: phosphorus,
    magnesium: magnesium,
    potassium: potassium,
    sodium: sodium,
    chlorine: chlorine,
    sulfur: sulfur,
    iron: iron,
    zinc: zinc,
    copper: copper,
    manganese: manganese,
    selenium: selenium,
    iodine: iodine,
    cobalt: cobalt,
    molybdenum: molybdenum,
    vitaminA: vitaminA,
    vitaminD: vitaminD,
    vitaminE: vitaminE,
    thiamin: thiamin,
    riboflavin: riboflavin,
    niacin: niacin,
    biotin: biotin,
    folate: folate,
    cobalamin: cobalamin,
    vitaminK: vitaminK,
    tannin: tannin,
    phytate: phytate,
    starchDigestibility: starchDigestibility,
    saponin: saponin,
    oxalate: oxalate,
    sources: {
      dryMatter: 'Büyüme + Yaşama',
      protein: 'Büyüme + Yaşama',
      rdp: 'Rumen Mikroorganizmaları',
      rup: 'İnce Bağırsak Emilimi',
      mcp: 'TDN × 0.13 (Rumen Sentezi)',
      mp: 'RUP + Mikrobiyal Protein',
      energy: 'Büyüme + Yaşama',
      nelMilk: 'Süt üretimi yok',
      nelPregnancy: 'Gebe olmaz',
      fat: 'Büyüme + Enerji',
      tdn: 'Starter Yem Kalitesi (% KM)',
      nfc: '100 - (NDF + CP + EE + Ash)',
      ndf: 'Rumen Gelişimi',
      calcium: 'Kemik Gelişimi',
      phosphorus: 'Kemik Gelişimi',
      magnesium: 'Kemik + Kas',
      potassium: 'Büyüme + Elektrolit',
      sodium: 'Büyüme + Su Dengesi',
      zinc: 'Büyüme + Bağışıklık',
      copper: 'Büyüme + Kan',
      manganese: 'Kemik + Büyüme',
      selenium: 'Kas + Bağışıklık',
      vitaminA: 'Büyüme + Görme',
      vitaminD: 'Kemik Gelişimi',
      vitaminE: 'Kas + Bağışıklık',
      thiamin: 'Büyüme + Metabolizma',
      riboflavin: 'Büyüme + Enerji',
      niacin: 'Büyüme + NAD/NADP',
      biotin: 'Büyüme + Tırnak',
      folate: 'Büyüme + DNA',
      cobalamin: 'Büyüme + Sinir',
      vitaminK: 'Büyüme + Kan',
      tannin: 'Protein Emilimi Engeli',
      phytate: 'Mineral Şelatörü',
      starchDigestibility: 'Rumen Gelişimi',
      saponin: 'Hücre Zarı Etkisi',
      oxalate: 'Kalsiyum Şelatörü'
    }
  };
}

function calculateHeiferRequirements(weight, pregnancy, actMult) {
  // Growing heifer requirements (NRC 2001)
  const targetGain = 0.7; // kg/day target weight gain

  const maintenanceEnergy = 0.08 * Math.pow(weight, 0.75) * actMult;
  const growthEnergy = targetGain * 4.92; // Mcal NEL per kg gain

  let pregnancyEnergy = 0;
  let pregnancyProtein = 0;

  if (pregnancy === 'late') {
    pregnancyEnergy = 1.5;
    pregnancyProtein = 0.12;
  } else if (pregnancy === 'mid') {
    pregnancyEnergy = 0.8;
    pregnancyProtein = 0.06;
  } else if (pregnancy === 'early') {
    pregnancyEnergy = 0.2;
    pregnancyProtein = 0.02;
  }

  const totalEnergy = (maintenanceEnergy + growthEnergy + pregnancyEnergy) * 4.184;
  const totalProtein = (weight * 0.0035 + targetGain * 0.16 + pregnancyProtein) * 1.6;

  const dryMatter = weight * 0.026 * actMult; // 2.6% of body weight

  // Protein fractions for heifers (NRC 2001)
  const rdpRatio = 0.68; // Standard RDP ratio for heifers
  const rupRatio = 1 - rdpRatio;

  const rdp = totalProtein * rdpRatio; // Rumen degradable protein
  const rup = totalProtein * rupRatio; // Rumen undegradable protein

  // TDN calculation for heifers
  const totalNEL = maintenanceEnergy + growthEnergy + pregnancyEnergy;
  const nelConcentration = totalNEL / dryMatter; // Mcal NEL per kg DM
  const tdnPercentage = (nelConcentration / 1.54) * 100; // % DM (NRC 2001 conversion)
  const tdnAmount = dryMatter * tdnPercentage / 100; // kg TDN per day

  // Microbial protein synthesis for heifers (13g MCP per 100g TDN)
  const microbialProtein = tdnAmount * 0.13; // kg MCP per day

  // Metabolizable protein from both sources
  const mpFromRUP = rup * 0.8; // 80% of RUP is absorbed
  const mpFromMCP = microbialProtein * 0.64; // 64% of MCP is true protein
  const calculatedMP = mpFromRUP + mpFromMCP;

  // Enhanced requirements for heifers
  const calcium = weight * 0.4 + targetGain * 15; // g/day
  const phosphorus = weight * 0.25 + targetGain * 8; // g/day
  const magnesium = weight * 0.05; // g/day
  const potassium = weight * 0.11; // g/day
  const sodium = weight * 0.06; // g/day
  const chlorine = sodium * 1.5; // g/day

  const fat = dryMatter * 35; // g/day (3.5% of DM) - CORRECTED

  // Fiber fractions for heifers (% of DM)
  const ndf = 30; // % of DM
  const adf = 20; // % of DM (moderate for growing animals)
  const lignin = 3.0; // % of DM (moderate lignin)
  const hemicellulose = ndf - adf; // % of DM
  const cellulose = adf - lignin; // % of DM

  // TDN for heifers (NRC 2001) - % of DM (use the calculated value)
  const tdn = tdnPercentage; // % DM (already calculated above)

  const iron = dryMatter * 55; // mg/day (higher for growth)
  const zinc = dryMatter * 45; // mg/day
  const copper = dryMatter * 11; // mg/day
  const manganese = dryMatter * 42; // mg/day
  const selenium = dryMatter * 0.32; // mg/day

  const vitaminA = weight * 130; // IU/day
  const vitaminD = weight * 35; // IU/day
  const vitaminE = dryMatter * 18; // IU/day

  let sources = {
    dryMatter: 'Yaşama + Büyüme',
    protein: 'Yaşama + Büyüme',
    rdp: 'Rumen Mikroorganizmaları',
    rup: 'İnce Bağırsak Emilimi',
    mcp: 'TDN × 0.13 (Rumen Sentezi)',
    mp: 'RUP + Mikrobiyal Protein',
    energy: 'Yaşama + Büyüme',
    nelMilk: 'Süt üretimi yok',
    nelPregnancy: 'Gebelik Dönemi',
    fat: 'Büyüme + Enerji',
    tdn: 'NEL Konsantrasyonu/1.54 (% KM)',
    ndf: 'Rumen Sağlığı',
    calcium: 'Yaşama + Büyüme',
    phosphorus: 'Yaşama + Büyüme',
    magnesium: 'Büyüme + Kemik',
    potassium: 'Büyüme + Elektrolit',
    sodium: 'Büyüme + Su Dengesi',
    zinc: 'Büyüme + Üreme',
    copper: 'Büyüme + Kan',
    manganese: 'Kemik + Büyüme',
    selenium: 'Kas + Üreme',
    vitaminA: 'Büyüme + Üreme',
    vitaminD: 'Kemik Gelişimi',
    vitaminE: 'Kas + Üreme'
  };

  if (pregnancy !== 'not-pregnant') {
    sources.dryMatter += ' + Gebelik';
    sources.protein += ' + Gebelik';
    sources.energy += ' + Gebelik';
    sources.calcium += ' + Gebelik';
    sources.phosphorus += ' + Gebelik';
    sources.vitaminA += ' + Gebelik';
    sources.vitaminE += ' + Gebelik';
  }

  return {
    dryMatter: dryMatter,
    protein: totalProtein,
    rdp: rdp,
    rup: rup,
    mcp: microbialProtein,
    mp: calculatedMP,
    energy: totalEnergy,
    nelMilk: 0, // Düveler süt üretmez
    nelPregnancy: pregnancyEnergy, // Gebelik enerjisi
    fat: fat,
    ndf: ndf,
    adf: adf,
    hemicellulose: hemicellulose,
    cellulose: cellulose,
    lignin: lignin,
    tdn: tdn,
    calcium: calcium,
    phosphorus: phosphorus,
    magnesium: magnesium,
    potassium: potassium,
    sodium: sodium,
    chlorine: chlorine,
    iron: iron,
    zinc: zinc,
    copper: copper,
    manganese: manganese,
    selenium: selenium,
    vitaminA: vitaminA,
    vitaminD: vitaminD,
    vitaminE: vitaminE,
    sources: sources
  };
}

function calculateBullRequirements(weight, actMult) {
  // Mature bull requirements (NRC 2001)
  const dryMatter = weight * 0.024 * actMult; // 2.4% of body weight
  const protein = weight * 0.0032; // 0.32% of body weight as crude protein
  const energy = 0.08 * Math.pow(weight, 0.75) * actMult * 4.184; // Convert to MJ

  // Enhanced requirements for bulls
  const calcium = weight * 0.3; // g/day
  const phosphorus = weight * 0.2; // g/day
  const magnesium = weight * 0.03; // g/day
  const potassium = weight * 0.08; // g/day
  const sodium = weight * 0.04; // g/day
  const chlorine = sodium * 1.5; // g/day

  const fat = dryMatter * 30; // g/day (3% of DM) - CORRECTED

  // Fiber fractions for bulls (% of DM)
  const ndf = 32; // % of DM (higher fiber for bulls)
  const adf = 22; // % of DM (higher for maintenance)
  const lignin = 4.0; // % of DM (higher lignin tolerance)
  const hemicellulose = ndf - adf; // % of DM
  const cellulose = adf - lignin; // % of DM

  // Protein fractions for bulls (NRC 2001)
  const rdpRatio = 0.68; // Standard RDP ratio for bulls
  const rupRatio = 1 - rdpRatio;

  const rdp = protein * rdpRatio; // Rumen degradable protein
  const rup = protein * rupRatio; // Rumen undegradable protein

  // TDN for bulls (NRC 2001) - % of DM
  const maintenanceEnergyMcal = 0.08 * Math.pow(weight, 0.75) * actMult; // Mcal NEL
  const nelConcentration = maintenanceEnergyMcal / dryMatter; // Mcal NEL per kg DM
  const tdn = (nelConcentration / 1.54) * 100; // % DM (NRC 2001 conversion)
  const tdnAmount = dryMatter * tdn / 100; // kg TDN per day

  // Microbial protein synthesis for bulls (13g MCP per 100g TDN)
  const microbialProtein = tdnAmount * 0.13; // kg MCP per day

  // Metabolizable protein from both sources
  const mpFromRUP = rup * 0.8; // 80% of RUP is absorbed
  const mpFromMCP = microbialProtein * 0.64; // 64% of MCP is true protein
  const calculatedMP = mpFromRUP + mpFromMCP;

  const iron = dryMatter * 45; // mg/day
  const zinc = dryMatter * 35; // mg/day
  const copper = dryMatter * 8; // mg/day
  const manganese = dryMatter * 35; // mg/day
  const selenium = dryMatter * 0.25; // mg/day

  const vitaminA = weight * 100; // IU/day
  const vitaminD = weight * 25; // IU/day
  const vitaminE = dryMatter * 12; // IU/day

  return {
    dryMatter: dryMatter,
    protein: protein,
    rdp: rdp,
    rup: rup,
    mcp: microbialProtein,
    mp: calculatedMP,
    energy: energy,
    nelMilk: 0, // Boğalar süt üretmez
    nelPregnancy: 0, // Boğalar gebe olmaz
    fat: fat,
    ndf: ndf,
    adf: adf,
    hemicellulose: hemicellulose,
    cellulose: cellulose,
    lignin: lignin,
    tdn: tdn,
    calcium: calcium,
    phosphorus: phosphorus,
    magnesium: magnesium,
    potassium: potassium,
    sodium: sodium,
    chlorine: chlorine,
    iron: iron,
    zinc: zinc,
    copper: copper,
    manganese: manganese,
    selenium: selenium,
    vitaminA: vitaminA,
    vitaminD: vitaminD,
    vitaminE: vitaminE,
    sources: {
      dryMatter: 'Yaşama + Aktivite',
      protein: 'Yaşama + Aktivite',
      rdp: 'Rumen Mikroorganizmaları',
      rup: 'İnce Bağırsak Emilimi',
      mcp: 'TDN × 0.13 (Rumen Sentezi)',
      mp: 'RUP + Mikrobiyal Protein',
      energy: 'Yaşama + Aktivite',
      nelMilk: 'Süt üretimi yok',
      nelPregnancy: 'Gebe olmaz',
      fat: 'Yaşama + Enerji',
      tdn: 'NEL Konsantrasyonu/1.54 (% KM)',
      ndf: 'Rumen Sağlığı',
      adf: 'Sindirilebilirlik',
      hemicellulose: 'Fiber Dengesi',
      cellulose: 'Yapısal Fiber',
      lignin: 'Sindirilemez Fiber',
      calcium: 'Yaşama + Kemik',
      phosphorus: 'Yaşama + Kemik',
      magnesium: 'Kas + Sinir',
      potassium: 'Elektrolit Dengesi',
      sodium: 'Su Dengesi',
      zinc: 'Üreme + Bağışıklık',
      copper: 'Kan + Enzimler',
      manganese: 'Kemik + Üreme',
      selenium: 'Kas + Antioksidan',
      vitaminA: 'Üreme + Bağışıklık',
      vitaminD: 'Kemik Sağlığı',
      vitaminE: 'Kas + Üreme'
    }
  };
}

function updateNutritionalNeedsTable(requirements) {
  if (!requirements) return;

  // Update macro nutrients
  document.getElementById('dm-requirement').textContent = requirements.dryMatter.toFixed(1);
  document.getElementById('protein-requirement').textContent = requirements.protein.toFixed(2);
  document.getElementById('rdp-requirement').textContent = requirements.rdp.toFixed(2);
  document.getElementById('rup-requirement').textContent = requirements.rup.toFixed(2);
  document.getElementById('mcp-requirement').textContent = requirements.mcp.toFixed(2);
  document.getElementById('mp-requirement').textContent = requirements.mp.toFixed(2);
  document.getElementById('energy-requirement').textContent = requirements.energy.toFixed(0);
  document.getElementById('nel-requirement').textContent = requirements.nel.toFixed(1);
  document.getElementById('nem-requirement').textContent = requirements.nem.toFixed(1);
  document.getElementById('nel-milk-requirement').textContent = (requirements.nelMilk || 0).toFixed(1);
  document.getElementById('nel-pregnancy-requirement').textContent = (requirements.nelPregnancy || 0).toFixed(1);
  document.getElementById('tdn-requirement').textContent = requirements.tdn.toFixed(1);
  document.getElementById('nfc-requirement').textContent = requirements.nfc.toFixed(1);
  document.getElementById('de-requirement').textContent = requirements.de.toFixed(0);
  document.getElementById('fat-requirement').textContent = requirements.fat.toFixed(0);
  document.getElementById('starch-requirement').textContent = requirements.starch.toFixed(0);
  document.getElementById('sugar-requirement').textContent = requirements.sugar.toFixed(0);
  document.getElementById('ash-requirement').textContent = requirements.ash.toFixed(0);
  document.getElementById('ndf-requirement').textContent = requirements.ndf.toFixed(1);
  document.getElementById('adf-requirement').textContent = requirements.adf.toFixed(1);
  document.getElementById('hemicellulose-requirement').textContent = requirements.hemicellulose.toFixed(1);
  document.getElementById('cellulose-requirement').textContent = requirements.cellulose.toFixed(1);
  document.getElementById('lignin-requirement').textContent = requirements.lignin.toFixed(1);

  // Update macro minerals
  document.getElementById('calcium-requirement').textContent = requirements.calcium.toFixed(0);
  document.getElementById('phosphorus-requirement').textContent = requirements.phosphorus.toFixed(0);
  document.getElementById('magnesium-requirement').textContent = requirements.magnesium.toFixed(1);
  document.getElementById('potassium-requirement').textContent = requirements.potassium.toFixed(0);
  document.getElementById('sodium-requirement').textContent = requirements.sodium.toFixed(1);
  document.getElementById('chlorine-requirement').textContent = requirements.chlorine.toFixed(1);

  // Update trace minerals
  document.getElementById('iron-requirement').textContent = requirements.iron.toFixed(0);
  document.getElementById('zinc-requirement').textContent = requirements.zinc.toFixed(0);
  document.getElementById('copper-requirement').textContent = requirements.copper.toFixed(0);
  document.getElementById('manganese-requirement').textContent = requirements.manganese.toFixed(0);
  document.getElementById('selenium-requirement').textContent = requirements.selenium.toFixed(1);
  document.getElementById('iodine-requirement').textContent = requirements.iodine.toFixed(1);
  document.getElementById('cobalt-requirement').textContent = requirements.cobalt.toFixed(1);
  document.getElementById('molybdenum-requirement').textContent = requirements.molybdenum.toFixed(1);
  document.getElementById('sulfur-requirement').textContent = requirements.sulfur.toFixed(1);

  // Update vitamins
  document.getElementById('vitamin-a-requirement').textContent = requirements.vitaminA.toFixed(0);
  document.getElementById('vitamin-d-requirement').textContent = requirements.vitaminD.toFixed(0);
  document.getElementById('vitamin-e-requirement').textContent = requirements.vitaminE.toFixed(0);

  // Update B-complex vitamins
  document.getElementById('thiamin-requirement').textContent = requirements.thiamin.toFixed(1);
  document.getElementById('riboflavin-requirement').textContent = requirements.riboflavin.toFixed(1);
  document.getElementById('niacin-requirement').textContent = requirements.niacin.toFixed(0);
  document.getElementById('biotin-requirement').textContent = requirements.biotin.toFixed(1);
  document.getElementById('folate-requirement').textContent = requirements.folate.toFixed(1);
  document.getElementById('cobalamin-requirement').textContent = requirements.cobalamin.toFixed(0);
  document.getElementById('vitamin-k-requirement').textContent = requirements.vitaminK.toFixed(1);

  // Update anti-nutritional factors
  document.getElementById('tannin-requirement').textContent = requirements.tannin.toFixed(1);
  document.getElementById('phytate-requirement').textContent = requirements.phytate.toFixed(1);
  document.getElementById('starch-digestibility').textContent = requirements.starchDigestibility.toFixed(1);
  document.getElementById('saponin-requirement').textContent = requirements.saponin.toFixed(1);
  document.getElementById('oxalate-requirement').textContent = requirements.oxalate.toFixed(1);

  // Update sources
  document.getElementById('dm-source').textContent = requirements.sources.dryMatter;
  document.getElementById('protein-source').textContent = requirements.sources.protein;
  document.getElementById('rdp-source').textContent = requirements.sources.rdp;
  document.getElementById('rup-source').textContent = requirements.sources.rup;
  document.getElementById('mcp-source').textContent = requirements.sources.mcp;
  document.getElementById('mp-source').textContent = requirements.sources.mp;
  document.getElementById('energy-source').textContent = requirements.sources.energy;
  document.getElementById('nel-source').textContent = requirements.sources.nel;
  document.getElementById('nem-source').textContent = requirements.sources.nem;
  document.getElementById('nel-milk-source').textContent = requirements.sources.nelMilk || 'Süt üretimi yok';
  document.getElementById('nel-pregnancy-source').textContent = requirements.sources.nelPregnancy || 'Gebe değil';
  document.getElementById('tdn-source').textContent = requirements.sources.tdn;
  document.getElementById('nfc-source').textContent = requirements.sources.nfc;
  document.getElementById('de-source').textContent = requirements.sources.de;
  document.getElementById('fat-source').textContent = requirements.sources.fat;
  document.getElementById('starch-source').textContent = requirements.sources.starch;
  document.getElementById('sugar-source').textContent = requirements.sources.sugar;
  document.getElementById('ash-source').textContent = requirements.sources.ash;
  document.getElementById('ndf-source').textContent = requirements.sources.ndf;
  document.getElementById('adf-source').textContent = requirements.sources.adf;
  document.getElementById('hemicellulose-source').textContent = requirements.sources.hemicellulose;
  document.getElementById('cellulose-source').textContent = requirements.sources.cellulose;
  document.getElementById('lignin-source').textContent = requirements.sources.lignin;
  document.getElementById('calcium-source').textContent = requirements.sources.calcium;
  document.getElementById('phosphorus-source').textContent = requirements.sources.phosphorus;
  document.getElementById('magnesium-source').textContent = requirements.sources.magnesium;
  document.getElementById('potassium-source').textContent = requirements.sources.potassium;
  document.getElementById('sodium-source').textContent = requirements.sources.sodium;
  document.getElementById('chlorine-source').textContent = requirements.sources.chlorine;
  document.getElementById('iron-source').textContent = requirements.sources.iron;
  document.getElementById('zinc-source').textContent = requirements.sources.zinc;
  document.getElementById('copper-source').textContent = requirements.sources.copper;
  document.getElementById('manganese-source').textContent = requirements.sources.manganese;
  document.getElementById('selenium-source').textContent = requirements.sources.selenium;
  document.getElementById('iodine-source').textContent = requirements.sources.iodine;
  document.getElementById('cobalt-source').textContent = requirements.sources.cobalt;
  document.getElementById('molybdenum-source').textContent = requirements.sources.molybdenum;
  document.getElementById('sulfur-source').textContent = requirements.sources.sulfur;
  document.getElementById('vitamin-a-source').textContent = requirements.sources.vitaminA;
  document.getElementById('vitamin-d-source').textContent = requirements.sources.vitaminD;
  document.getElementById('vitamin-e-source').textContent = requirements.sources.vitaminE;
  document.getElementById('thiamin-source').textContent = requirements.sources.thiamin;
  document.getElementById('riboflavin-source').textContent = requirements.sources.riboflavin;
  document.getElementById('niacin-source').textContent = requirements.sources.niacin;
  document.getElementById('biotin-source').textContent = requirements.sources.biotin;
  document.getElementById('folate-source').textContent = requirements.sources.folate;
  document.getElementById('cobalamin-source').textContent = requirements.sources.cobalamin;
  document.getElementById('vitamin-k-source').textContent = requirements.sources.vitaminK;
  document.getElementById('tannin-source').textContent = requirements.sources.tannin;
  document.getElementById('phytate-source').textContent = requirements.sources.phytate;
  document.getElementById('starch-source').textContent = requirements.sources.starchDigestibility;
  document.getElementById('saponin-source').textContent = requirements.sources.saponin;
  document.getElementById('oxalate-source').textContent = requirements.sources.oxalate;

  // Add visual feedback
  const tbody = document.getElementById('nutritional-needs-tbody');
  tbody.style.backgroundColor = 'var(--bg-success-subtle)';
  setTimeout(() => {
    tbody.style.backgroundColor = '';
  }, 1000);

  // Show success message with more details
  const animalCategory = document.getElementById('animal-category').value;
  const weight = document.getElementById('live-weight').value;
  const milkYield = document.getElementById('milk-yield').value || 0;

  let message = `Gelişmiş besin ihtiyaçları hesaplandı!\n\n`;
  message += `Hesaplama Detayları:\n`;
  message += `• Hayvan: ${getAnimalCategoryText(animalCategory)}\n`;
  message += `• Ağırlık: ${weight} kg\n`;
  if (milkYield > 0) message += `• Süt Verimi: ${milkYield} L/gün\n`;
  message += `\nNRC 2001 standartlarına göre:\n`;
  message += `• ${Object.keys(requirements.sources).length} farklı besin öğesi\n`;
  message += `• Protein fraksiyonları (RDP/RUP/MP)\n`;
  message += `• Enerji fraksiyonları (NEL/NEM/TDN/DE)\n`;
  message += `• Fiber fraksiyonları (NDF/ADF/Lignin)\n`;
  message += `• Makro + mikro mineraller + antagonistler\n`;
  message += `• B-kompleks + yağda çözünen vitaminler\n`;
  message += `• Anti-besin faktörleri (Tanen/Fitat/Nişasta)\n`;
  message += `• Rumen sağlığı + mineral etkileşimleri`;

  window.toast?.success('Besin ihtiyaçları başarıyla hesaplandı! Detaylar tabloda görüntüleniyor.');

  // Store requirements for validation system
  if (window.storeNutritionalRequirements) {
    window.storeNutritionalRequirements(requirements);
  }

  // Store requirements globally for smart suggestions
  window.currentRequirements = requirements;


}

function getAnimalCategoryText(category) {
  const categories = {
    'dairy-cow': 'Süt İneği',
    'calf': 'Buzağı',
    'heifer': 'Düve',
    'bull': 'Boğa'
  };
  return categories[category] || category;
}

// Fix tooltip positioning in scrollable containers
function setupTooltipPositioning() {
  const scrollContainer = document.querySelector('.feed-ration-manager:nth-child(2) .table-container');
  const tooltipContainers = document.querySelectorAll('.feed-ration-manager:nth-child(2) .tooltip-container');

  // console.log('Setting up tooltip positioning');
  // console.log('Found scroll container:', scrollContainer);
  // console.log('Found tooltip containers:', tooltipContainers.length);

  if (!tooltipContainers.length) return;

  // Create a custom tooltip element that we fully control
  let customTooltip = document.getElementById('custom-nutritional-tooltip');
  if (!customTooltip) {
    customTooltip = document.createElement('div');
    customTooltip.id = 'custom-nutritional-tooltip';
    customTooltip.style.cssText = `
      position: fixed;
      background: var(--bg-elevated);
      color: var(--text-primary);
      padding: 16px 20px;
      border-radius: 12px;
      box-shadow: 0 10px 40px rgba(0,0,0,0.15);
      border: 1px solid var(--border-primary);
      font-size: 14px;
      line-height: 1.5;
      z-index: 10000;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s ease, visibility 0.2s ease;
      max-width: 480px;
      width: max-content;
      white-space: normal;
      text-align: left;
      pointer-events: none;
      word-wrap: break-word;
    `;
    document.body.appendChild(customTooltip);
  }

  // Remove any existing positioning from original tooltips
  tooltipContainers.forEach(container => {
    const tooltip = container.querySelector('.tooltip');
    if (!tooltip) return;

    // Hide original tooltip completely
    tooltip.style.cssText = `
      opacity: 0 !important;
      visibility: hidden !important;
      position: absolute !important;
      left: -9999px !important;
      top: -9999px !important;
    `;
  });

  // Simple and effective tooltip positioning using custom tooltip
  function showTooltip(container, originalTooltip) {
    const customTooltip = document.getElementById('custom-nutritional-tooltip');
    if (!customTooltip) return;

    // console.log('Showing tooltip for:', container.textContent.trim().substring(0, 20));

    // Get the original tooltip content
    const tooltipContent = originalTooltip.innerHTML;
    customTooltip.innerHTML = tooltipContent;

    // Get container position relative to viewport
    const rect = container.getBoundingClientRect();
    const margin = 20;

    // console.log('Container rect:', rect.left, rect.top, rect.width, rect.height);

    // Position tooltip off-screen first to measure it
    customTooltip.style.left = '-9999px';
    customTooltip.style.top = '-9999px';
    customTooltip.style.opacity = '1';
    customTooltip.style.visibility = 'visible';

    // Force reflow and get dimensions
    customTooltip.offsetHeight;
    const tooltipRect = customTooltip.getBoundingClientRect();
    const tooltipWidth = tooltipRect.width;
    const tooltipHeight = tooltipRect.height;

    // console.log('Tooltip size:', tooltipWidth, 'x', tooltipHeight);

    // Calculate position - center horizontally relative to container
    let left = rect.left + (rect.width / 2) - (tooltipWidth / 2);

    // Position above the container by default
    let top = rect.top - tooltipHeight - margin;

    // console.log('Initial pos:', left, top);

    // Adjust horizontal position if off-screen
    if (left < margin) {
      left = margin;
    } else if (left + tooltipWidth > window.innerWidth - margin) {
      left = window.innerWidth - tooltipWidth - margin;
    }

    // If not enough space above, position below
    if (top < margin) {
      top = rect.bottom + margin;
      // console.log('Positioning below');
    }

    // If still off-screen vertically, position at top of viewport
    if (top + tooltipHeight > window.innerHeight - margin) {
      top = margin;
      // console.log('Positioning at top');
    }

    // console.log('Final pos:', left, top);

    // Apply final position
    customTooltip.style.left = left + 'px';
    customTooltip.style.top = top + 'px';
  }

  function hideTooltip() {
    const customTooltip = document.getElementById('custom-nutritional-tooltip');
    if (customTooltip) {
      // console.log('Hiding custom tooltip');
      customTooltip.style.opacity = '0';
      customTooltip.style.visibility = 'hidden';
    }
  }

  // Add event listeners to each tooltip container
  tooltipContainers.forEach(container => {
    const tooltip = container.querySelector('.tooltip');
    if (!tooltip) return;

    // Remove any existing event listeners
    container.removeEventListener('mouseenter', container._tooltipEnterHandler);
    container.removeEventListener('mouseleave', container._tooltipLeaveHandler);

    // Create new event handlers
    container._tooltipEnterHandler = function() {
      // Hide tooltip first
      hideTooltip();

      // Show this tooltip
      setTimeout(() => {
        showTooltip(container, tooltip);
      }, 10);
    };

    container._tooltipLeaveHandler = function() {
      hideTooltip();
    };

    // Add event listeners
    container.addEventListener('mouseenter', container._tooltipEnterHandler);
    container.addEventListener('mouseleave', container._tooltipLeaveHandler);
  });

  // Hide tooltips when scrolling
  if (scrollContainer) {
    scrollContainer.addEventListener('scroll', function() {
      hideTooltip();
    }, { passive: true });
  }

  // Hide tooltips when window scrolls
  window.addEventListener('scroll', function() {
    hideTooltip();
  }, { passive: true });
}

// Initialize tooltip positioning when page loads
document.addEventListener('DOMContentLoaded', function() {
  // Wait for content to be loaded
  setTimeout(() => {
    setupTooltipPositioning();
  }, 100);
});

// Export function to reinitialize tooltips when page is dynamically loaded
window.reinitializeTooltips = function() {
  setTimeout(() => {
    setupTooltipPositioning();
  }, 100);
};

// =================================
// Export and Print Functionality
// =================================
function setupExportPrintFunctionality() {
  const exportBtn = document.getElementById('export-nutrition-btn');
  const printBtn = document.getElementById('print-nutrition-btn');

  if (exportBtn) {
    exportBtn.addEventListener('click', exportNutritionalNeeds);
  }

  if (printBtn) {
    printBtn.addEventListener('click', printNutritionalNeeds);
  }
}

function exportNutritionalNeeds() {
  try {
    // Get the nutritional needs table data
    const tableData = getNutritionalTableData();

    if (!tableData || tableData.length === 0) {
      window.toast?.error('Dışa aktarılacak veri bulunamadı. Önce besin ihtiyaçlarını hesaplayın.');
      return;
    }

    // Create CSV content
    const csvContent = createCSVContent(tableData);

    // Create and download file
    downloadCSVFile(csvContent, 'besin_ihtiyaclari.csv');

    window.toast?.success('Besin ihtiyaçları başarıyla dışa aktarıldı.');
  } catch (error) {
    console.error('Export error:', error);
    window.toast?.error('Dışa aktarma sırasında bir hata oluştu.');
  }
}

function printNutritionalNeeds() {
  try {
    // Get the nutritional needs table data
    const tableData = getNutritionalTableData();

    if (!tableData || tableData.length === 0) {
      window.toast?.error('Yazdırılacak veri bulunamadı. Önce besin ihtiyaçlarını hesaplayın.');
      return;
    }

    // Get animal information from form
    const animalInfo = getAnimalFormData();

    // Create print content with animal info
    const printContent = createPrintContent(tableData, animalInfo);

    // Open print window
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();

    window.toast?.success('Yazdırma penceresi açıldı.');
  } catch (error) {
    console.error('Print error:', error);
    window.toast?.error('Yazdırma sırasında bir hata oluştu.');
  }
}

function getNutritionalTableData() {
  const table = document.querySelector('#nutritional-needs-tbody');
  if (!table) return [];

  const rows = table.querySelectorAll('tr');
  const data = [];

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length >= 4) {
      // Skip header rows (those with colspan)
      if (cells[0].hasAttribute('colspan') || cells[1].hasAttribute('colspan')) {
        data.push({
          type: 'header',
          name: cells[0].textContent.trim(),
          requirement: '',
          unit: '',
          source: ''
        });
      } else {
        // Extract text content, removing tooltip content
        const nameElement = cells[0].querySelector('.tooltip-container') || cells[0];
        const name = nameElement.childNodes[0]?.textContent?.trim() || cells[0].textContent.trim();

        data.push({
          type: 'data',
          name: name,
          requirement: cells[1].textContent.trim(),
          unit: cells[2].textContent.trim(),
          source: cells[3].textContent.trim()
        });
      }
    }
  });

  return data;
}

function getAnimalFormData() {
  const animalCategory = document.getElementById('animal-category')?.value || '';
  const liveWeight = document.getElementById('live-weight')?.value || '';
  const milkYield = document.getElementById('milk-yield')?.value || '';
  const milkFat = document.getElementById('milk-fat')?.value || '';
  const pregnancyStatus = document.getElementById('pregnancy-status')?.value || '';
  const activityLevel = document.getElementById('activity-level')?.value || '';

  // Convert values to readable format
  const categoryLabels = {
    'dairy-cow': 'Süt İneği',
    'calf': 'Buzağı',
    'heifer': 'Düve',
    'bull': 'Boğa'
  };

  const pregnancyLabels = {
    'not-pregnant': 'Gebe Değil',
    'early': '1-3 Ay Gebe',
    'mid': '4-6 Ay Gebe',
    'late': '7-9 Ay Gebe'
  };

  const activityLabels = {
    'normal': 'Normal',
    'high': 'Yüksek',
    'low': 'Düşük'
  };

  return {
    category: categoryLabels[animalCategory] || animalCategory || 'Belirtilmemiş',
    liveWeight: liveWeight ? `${liveWeight} kg` : 'Belirtilmemiş',
    milkYield: milkYield ? `${milkYield} L/gün` : 'Belirtilmemiş',
    milkFat: milkFat ? `${milkFat}%` : 'Belirtilmemiş',
    pregnancyStatus: pregnancyLabels[pregnancyStatus] || pregnancyStatus || 'Belirtilmemiş',
    activityLevel: activityLabels[activityLevel] || activityLevel || 'Belirtilmemiş'
  };
}

function createCSVContent(data) {
  const headers = ['Besin Öğesi', 'İhtiyaç', 'Birim', 'Kaynak'];
  let csvContent = headers.join(',') + '\n';

  data.forEach(row => {
    if (row.type === 'header') {
      csvContent += `"${row.name}","","",""\n`;
    } else {
      const escapedData = [
        `"${row.name.replace(/"/g, '""')}"`,
        `"${row.requirement.replace(/"/g, '""')}"`,
        `"${row.unit.replace(/"/g, '""')}"`,
        `"${row.source.replace(/"/g, '""')}"`
      ];
      csvContent += escapedData.join(',') + '\n';
    }
  });

  return csvContent;
}

function downloadCSVFile(content, filename) {
  const blob = new Blob(['\uFEFF' + content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

function createPrintContent(data, animalInfo) {
  const currentDate = new Date().toLocaleDateString('tr-TR');

  let printHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Günlük Besin İhtiyaçları</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 20px;
          color: #333;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #333;
          padding-bottom: 10px;
        }
        .header h1 {
          margin: 0;
          color: #2563eb;
        }
        .header p {
          margin: 5px 0;
          color: #666;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        .header-row {
          background-color: #e3f2fd;
          font-weight: bold;
          text-align: center;
        }
        .animal-info {
          background-color: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 5px;
          padding: 15px;
          margin-bottom: 20px;
        }
        .animal-info h3 {
          margin: 0 0 10px 0;
          color: #2563eb;
          font-size: 16px;
        }
        .animal-info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
        }
        .animal-info-item {
          display: flex;
          justify-content: space-between;
          padding: 5px 0;
          border-bottom: 1px dotted #ccc;
        }
        .animal-info-label {
          font-weight: bold;
          color: #555;
        }
        .animal-info-value {
          color: #333;
        }
        .footer {
          margin-top: 30px;
          text-align: center;
          font-size: 12px;
          color: #666;
        }
        @media print {
          body { margin: 0; }
          .header { page-break-after: avoid; }
          .animal-info { page-break-after: avoid; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Günlük Besin İhtiyaçları</h1>
        <p>Tarih: ${currentDate}</p>
        <p>Çiftlik Yönetim Sistemi</p>
      </div>

      <div class="animal-info">
        <h3>Hayvan Bilgileri</h3>
        <div class="animal-info-grid">
          <div class="animal-info-item">
            <span class="animal-info-label">Kategori:</span>
            <span class="animal-info-value">${animalInfo.category}</span>
          </div>
          <div class="animal-info-item">
            <span class="animal-info-label">Canlı Ağırlık:</span>
            <span class="animal-info-value">${animalInfo.liveWeight}</span>
          </div>
          <div class="animal-info-item">
            <span class="animal-info-label">Günlük Süt Verimi:</span>
            <span class="animal-info-value">${animalInfo.milkYield}</span>
          </div>
          <div class="animal-info-item">
            <span class="animal-info-label">Süt Yağ Oranı:</span>
            <span class="animal-info-value">${animalInfo.milkFat}</span>
          </div>
          <div class="animal-info-item">
            <span class="animal-info-label">Gebelik Durumu:</span>
            <span class="animal-info-value">${animalInfo.pregnancyStatus}</span>
          </div>
          <div class="animal-info-item">
            <span class="animal-info-label">Aktivite Seviyesi:</span>
            <span class="animal-info-value">${animalInfo.activityLevel}</span>
          </div>
        </div>
      </div>

      <table>
        <thead>
          <tr>
            <th>Besin Öğesi</th>
            <th>İhtiyaç</th>
            <th>Birim</th>
            <th>Kaynak</th>
          </tr>
        </thead>
        <tbody>
  `;

  data.forEach(row => {
    if (row.type === 'header') {
      printHTML += `
        <tr class="header-row">
          <td colspan="4">${row.name}</td>
        </tr>
      `;
    } else {
      printHTML += `
        <tr>
          <td>${row.name}</td>
          <td>${row.requirement}</td>
          <td>${row.unit}</td>
          <td>${row.source}</td>
        </tr>
      `;
    }
  });

  printHTML += `
        </tbody>
      </table>

      <div class="footer">
        <p>Bu rapor ${currentDate} tarihinde Çiftlik Yönetim Sistemi tarafından oluşturulmuştur.</p>
      </div>
    </body>
    </html>
  `;

  return printHTML;
}

// Save ration to database
async function saveRationToDatabase(ration, rationName) {
  try {
    const currentProfile = window.sessionManager.getCurrentProfile();
    if (!currentProfile) {
      window.toast?.error('Profil bilgisi bulunamadı.');
      return;
    }

    const rationData = {
      profilId: currentProfile.Id,
      rasyonAdi: rationName,
      hayvanKategorisi: 'Genel', // Bu değer form'dan alınabilir
      hedefCanlıAgirlik: null,
      hedefSutVerimi: null,
      hedefSutYagOrani: null,
      gebelikDurumu: null,
      aktiviteSeviyesi: null,
      hedefKuruMadde: ration.totalAmount || null,
      hedefHamProtein: ration.totalProtein || null,
      hedefMetabolikEnerji: ration.totalEnergy || null,
      hedefNEL: null,
      hedefKalsiyum: null,
      hedefFosfor: null,
      notlar: `Toplam Miktar: ${ration.totalAmount}kg, Protein: ${ration.totalProtein}%, Enerji: ${ration.totalEnergy}MJ`,
      yemler: ration.items.map((item, index) => ({
        yemId: item.feedId,
        miktar: item.amount,
        yuzde: (item.amount / ration.totalAmount * 100).toFixed(2),
        sira: index + 1
      }))
    };

    console.log('Saving ration data:', rationData);
    const result = await window.api.invoke('save-ration', rationData);
    
    if (result.success) {
      window.toast?.success(`"${rationName}" rasyonu başarıyla kaydedildi!`);
      
      // Clear the ration
      if (typeof rationItems !== 'undefined') {
        rationItems.length = 0;
        renderRationTable();
      }
      
      // Reload saved rations
      await loadSavedRations();
    } else {
      window.toast?.error('Rasyon kaydedilirken hata oluştu.');
    }
  } catch (error) {
    console.error('Error saving ration:', error);
    window.toast?.error('Rasyon kaydedilirken hata oluştu: ' + error.message);
  }
}

// Load saved rations from database
async function loadSavedRations() {
  try {
    const currentProfile = window.sessionManager.getCurrentProfile();
    if (!currentProfile) {
      console.log('No profile found for loading rations');
      return;
    }

    const rations = await window.api.invoke('get-rations', currentProfile.Id);
    await renderSavedRationsTable(rations);
  } catch (error) {
    console.error('Error loading saved rations:', error);
    window.toast?.error('Kaydedilen rasyonlar yüklenirken hata oluştu.');
  }
}

// Render saved rations table
async function renderSavedRationsTable(rations) {
  const tableBody = document.querySelector('#saved-section .modern-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  if (!rations || rations.length === 0) {
    tableBody.innerHTML = `
      <tr>
        <td colspan="6" class="text-center">
          <div class="empty-state">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
            </svg>
            <p>Henüz kaydedilmiş rasyon bulunmuyor.</p>
            <small>Rasyon Oluşturucu sekmesinden yeni rasyon oluşturabilirsiniz.</small>
          </div>
        </td>
      </tr>
    `;
    return;
  }

  // Process rations sequentially to calculate costs
  for (const ration of rations) {
    const row = document.createElement('tr');
    const createdDate = new Date(ration.OlusturmaTarihi).toLocaleDateString('tr-TR');
    
    // Calculate actual cost based on feed prices
    const totalCost = await calculateRationCost(ration);
    
    row.innerHTML = `
      <td>${ration.RasyonAdi}</td>
      <td>${ration.HayvanKategorisi || 'Genel'}</td>
      <td>${createdDate}</td>
      <td>${totalCost.toFixed(2)} ₺/gün</td>
      <td><span class="badge badge-success">Aktif</span></td>
      <td>
        <button class="btn btn-sm btn-primary" onclick="viewRationDetails(${ration.Id})">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          Görüntüle
        </button>
        <button class="btn btn-sm btn-outline" onclick="editRation(${ration.Id})">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
          Düzenle
        </button>
        <button class="btn btn-sm btn-danger" onclick="deleteRation(${ration.Id})">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 6h18"></path>
            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
          </svg>
          Sil
        </button>
      </td>
    `;
    
    tableBody.appendChild(row);
  }
}

// Calculate ration cost based on actual feed prices and amounts
async function calculateRationCost(ration) {
  try {
    // Get ration details with feeds
    const details = await window.api.invoke('get-ration-details', ration.Id);
    
    if (!details || !details.feeds || details.feeds.length === 0) {
      return 0;
    }
    
    let totalCost = 0;
    
    details.feeds.forEach(feed => {
      const miktar = parseFloat(feed.Miktar) || 0;
      const birimFiyat = parseFloat(feed.BirimFiyat) || 0;
      totalCost += miktar * birimFiyat;
    });
    
    return totalCost;
  } catch (error) {
    console.error('Error calculating ration cost:', error);
    return 0;
  }
}

// Global functions for ration actions
window.viewRationDetails = async function(rationId) {
  try {
    const details = await window.api.invoke('get-ration-details', rationId);
    showRationDetailsModal(details);
  } catch (error) {
    console.error('Error loading ration details:', error);
    window.toast?.error('Rasyon detayları yüklenirken hata oluştu.');
  }
};

window.editRation = function(rationId) {
  window.toast?.info('Rasyon düzenleme özelliği yakında eklenecek.');
};

window.deleteRation = async function(rationId) {
  const confirmed = await window.toast?.confirm(
    'Bu rasyonu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
    {
      confirmText: 'Evet, Sil',
      cancelText: 'İptal',
      title: 'Rasyon Sil'
    }
  );
  
  if (confirmed) {
    try {
      const result = await window.api.invoke('delete-ration', rationId);
      if (result.success) {
        window.toast?.success('Rasyon başarıyla silindi.');
        await loadSavedRations();
      } else {
        window.toast?.error('Rasyon silinirken hata oluştu.');
      }
    } catch (error) {
      console.error('Error deleting ration:', error);
      window.toast?.error('Rasyon silinirken hata oluştu: ' + error.message);
    }
  }
};

// Show ration details modal
function showRationDetailsModal(details) {
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';
  modal.innerHTML = `
    <div class="modal-content" style="max-width: 800px;">
      <div class="modal-header">
        <h3>Rasyon Detayları: ${details.ration.RasyonAdi}</h3>
        <button class="modal-close" onclick="this.closest('.modal-overlay').remove();">&times;</button>
      </div>
      <div class="modal-body">
        <div class="ration-details-content">
          <div class="ration-info">
            <h4>Genel Bilgiler</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>Rasyon Adı:</label>
                <span>${details.ration.RasyonAdi}</span>
              </div>
              <div class="info-item">
                <label>Hayvan Kategorisi:</label>
                <span>${details.ration.HayvanKategorisi || 'Genel'}</span>
              </div>
              <div class="info-item">
                <label>Oluşturma Tarihi:</label>
                <span>${new Date(details.ration.OlusturmaTarihi).toLocaleDateString('tr-TR')}</span>
              </div>
            </div>
          </div>
          
          <div class="ration-feeds">
            <h4>Yem Bileşimi</h4>
            <table class="modern-table">
              <thead>
                <tr>
                  <th>Yem Adı</th>
                  <th>Miktar (kg)</th>
                  <th>Yüzde (%)</th>
                  <th>Birim Fiyat (₺)</th>
                </tr>
              </thead>
              <tbody>
                ${details.feeds.map(feed => `
                  <tr>
                    <td>${feed.YemAdi}</td>
                    <td>${feed.Miktar}</td>
                    <td>${feed.Yuzde}%</td>
                    <td>${feed.BirimFiyat || 'N/A'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove();">Kapat</button>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
}

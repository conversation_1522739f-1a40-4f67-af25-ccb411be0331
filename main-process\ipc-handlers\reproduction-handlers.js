const { ipcMain } = require('electron');
const { getDb, createGenericCrudHandlers } = require('../services/database.js');
const { getCurrentProfile } = require('./auth-handlers.js');

const db = getDb();

// --- TOHUMLAMALAR CRUD ---
const tohumlamalarColumns = ['TohumlamaTarihi', 'TohumlamaTipi', 'SpermaKodu', 'Notlar'];
const tohumlamalarCrud = createGenericCrudHandlers('Tohumlamalar', tohumlamalarColumns, { hasHayvanId: true, requiresProfile: true });

ipcMain.handle('tohumlamalar:list', tohumlamalarCrud.listByHayvanId);
ipcMain.handle('tohumlamalar:add', tohumlamalarCrud.add);
ipcMain.handle('tohumlamalar:update', tohumlamalarCrud.update);
ipcMain.handle('tohumlamalar:delete', tohumlamalarCrud.delete);

// Add listAll method for tohumlamalar with profile context
ipcMain.handle('tohumlamalar:listAll', async () => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.all('SELECT * FROM Tohumlamalar WHERE ProfilId = ? ORDER BY TohumlamaTarihi DESC, Id DESC', [currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
});

// --- GEBELİKLER CRUD ---
const gebeliklerColumns = ['TohumlamaId', 'BaslangicTarihi', 'BeklenenDogumTarihi', 'GebelikSonucu', 'DogumTarihi', 'Notlar'];
const gebeliklerCrud = createGenericCrudHandlers('Gebelikler', gebeliklerColumns, { hasHayvanId: true, requiresProfile: true });

ipcMain.handle('gebelikler:list', gebeliklerCrud.listByHayvanId);
ipcMain.handle('gebelikler:add', gebeliklerCrud.add);
ipcMain.handle('gebelikler:update', gebeliklerCrud.update);
ipcMain.handle('gebelikler:delete', gebeliklerCrud.delete);

// Add listAll method for gebelikler with profile context
ipcMain.handle('gebelikler:listAll', async () => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.all('SELECT * FROM Gebelikler WHERE ProfilId = ? ORDER BY BaslangicTarihi DESC, Id DESC', [currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
});

console.log('Reproduction (Tohumlamalar, Gebelikler) IPC handlers registered.');

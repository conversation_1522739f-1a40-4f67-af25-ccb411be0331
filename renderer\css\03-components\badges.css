/* =================================
   Modern Badge System
   ================================= */

/* =================================
   Base Badge
   ================================= */
.badge, .table-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  text-align: center;
  white-space: nowrap;
  min-width: 50px;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
}

/* =================================
   Badge Variants
   ================================= */

/* Success Badges */
.badge-success,
.badge-active,
.badge-pregnant,
.badge-milking,
.badge-paid,
.badge-aktif,
.badge-milk-high {
  background: var(--status-success-bg);
  color: var(--status-success);
  border-color: var(--success-200);
}

/* Warning Badges */
.badge-warning,
.badge-pending,
.badge-partial,
.badge-vadeli,
.badge-milk-low {
  background: var(--status-warning-bg);
  color: var(--status-warning);
  border-color: var(--warning-200);
}

/* Error Badges */
.badge-error,
.badge-danger,
.badge-inactive,
.badge-empty,
.badge-unpaid,
.badge-pasif {
  background: var(--status-error-bg);
  color: var(--status-error);
  border-color: var(--error-200);
}

/* Info Badges */
.badge-info,
.badge-dry,
.badge-completed {
  background: var(--status-info-bg);
  color: var(--status-info);
  border-color: var(--info-200);
}

/* Primary Badges */
.badge-primary,
.badge-musteri,
.badge-customer {
  background: var(--primary-50);
  color: var(--primary-600);
  border-color: var(--primary-200);
}

/* Secondary Badges */
.badge-secondary,
.badge-neutral,
.badge-default {
  background: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-200);
}

/* Purple Badges */
.badge-purple,
.badge-tedarikci,
.badge-supplier {
  background: #f3e8ff;
  color: #7c3aed;
  border-color: #d8b4fe;
}

/* Business Type Badges */
.badge-alis,
.badge-purchase {
  background: var(--success-50);
  color: var(--success-700);
  border-color: var(--success-200);
}

.badge-satis,
.badge-sale {
  background: var(--error-50);
  color: var(--error-700);
  border-color: var(--error-200);
}

.badge-pesin,
.badge-cash {
  background: #ecfdf5;
  color: #065f46;
  border-color: #bbf7d0;
}

/* =================================
   Badge Sizes
   ================================= */
.badge-sm {
  padding: var(--space-1) var(--space-2);
  font-size: 10px;
  min-width: 40px;
}

.badge-lg {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  min-width: 60px;
}

/* =================================
   Badge with Icons
   ================================= */
.badge-with-icon {
  gap: var(--space-1);
  padding-left: var(--space-2);
}

.badge-with-icon svg {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

.badge-with-icon.badge-lg svg {
  width: 14px;
  height: 14px;
}

/* =================================
   Dot Badges
   ================================= */
.badge-dot {
  position: relative;
  padding-left: var(--space-4);
}

.badge-dot::before {
  content: '';
  position: absolute;
  left: var(--space-2);
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* =================================
   Outline Badges
   ================================= */
.badge-outline {
  background: transparent;
  border-width: 1px;
}

.badge-outline.badge-success {
  color: var(--status-success);
  border-color: var(--status-success);
}

.badge-outline.badge-warning {
  color: var(--status-warning);
  border-color: var(--status-warning);
}

.badge-outline.badge-error {
  color: var(--status-error);
  border-color: var(--status-error);
}

.badge-outline.badge-info {
  color: var(--status-info);
  border-color: var(--status-info);
}

.badge-outline.badge-primary {
  color: var(--interactive-primary);
  border-color: var(--interactive-primary);
}

/* =================================
   Interactive Badges
   ================================= */
.badge-interactive {
  cursor: pointer;
  transition: all var(--transition-fast);
}

.badge-interactive:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.badge-interactive.badge-success:hover {
  background: var(--success-100);
}

.badge-interactive.badge-warning:hover {
  background: var(--warning-100);
}

.badge-interactive.badge-error:hover {
  background: var(--error-100);
}

.badge-interactive.badge-info:hover {
  background: var(--info-100);
}

.badge-interactive.badge-primary:hover {
  background: var(--primary-100);
}

/* =================================
   Badge Groups
   ================================= */
.badge-group {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.badge-group .badge {
  margin: 0;
}

/* =================================
   Table Badges
   ================================= */
.table-badge {
  min-width: 45px;
  padding: var(--space-1) var(--space-2);
  font-size: 10px;
  flex-shrink: 0;
}

/* =================================
   Dark Theme Adjustments
   ================================= */
[data-theme="dark"] .badge-success,
[data-theme="dark"] .badge-active,
[data-theme="dark"] .badge-pregnant,
[data-theme="dark"] .badge-milking,
[data-theme="dark"] .badge-paid,
[data-theme="dark"] .badge-aktif {
  background: rgba(34, 197, 94, 0.15);
  color: #86efac;
  border-color: rgba(34, 197, 94, 0.3);
}

[data-theme="dark"] .badge-warning,
[data-theme="dark"] .badge-pending,
[data-theme="dark"] .badge-partial,
[data-theme="dark"] .badge-vadeli {
  background: rgba(245, 158, 11, 0.15);
  color: #fcd34d;
  border-color: rgba(245, 158, 11, 0.3);
}

[data-theme="dark"] .badge-error,
[data-theme="dark"] .badge-inactive,
[data-theme="dark"] .badge-empty,
[data-theme="dark"] .badge-unpaid,
[data-theme="dark"] .badge-pasif {
  background: rgba(239, 68, 68, 0.15);
  color: #fca5a5;
  border-color: rgba(239, 68, 68, 0.3);
}

[data-theme="dark"] .badge-info,
[data-theme="dark"] .badge-dry,
[data-theme="dark"] .badge-completed {
  background: rgba(6, 182, 212, 0.15);
  color: #67e8f9;
  border-color: rgba(6, 182, 212, 0.3);
}

[data-theme="dark"] .badge-primary,
[data-theme="dark"] .badge-musteri,
[data-theme="dark"] .badge-customer {
  background: rgba(59, 130, 246, 0.15);
  color: #93c5fd;
  border-color: rgba(59, 130, 246, 0.3);
}

[data-theme="dark"] .badge-secondary,
[data-theme="dark"] .badge-neutral,
[data-theme="dark"] .badge-default {
  background: rgba(156, 163, 175, 0.15);
  color: #d1d5db;
  border-color: rgba(156, 163, 175, 0.3);
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  .badge {
    padding: var(--space-1) var(--space-2);
    font-size: 10px;
    min-width: 40px;
  }

  .badge-lg {
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    min-width: 50px;
  }

  .table-badge {
    min-width: 35px;
    padding: 2px var(--space-1);
    font-size: 9px;
  }
}

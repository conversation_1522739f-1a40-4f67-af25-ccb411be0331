/* =================================
   Modern Button System
   ================================= */

/* =================================
   Base Button
   ================================= */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-tight);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  user-select: none;
  min-height: var(--button-height-base);
  font-family: inherit;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* =================================
   Button Variants
   ================================= */

/* Primary Button */
.btn-primary {
  background: var(--interactive-primary);
  color: var(--text-inverse);
  border-color: var(--interactive-primary);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: var(--interactive-primary-hover);
  border-color: var(--interactive-primary-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:active {
  background: var(--interactive-primary-active);
  border-color: var(--interactive-primary-active);
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary {
  background: var(--interactive-secondary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover {
  background: var(--interactive-secondary-hover);
  border-color: var(--border-secondary);
}

.btn-secondary:active {
  background: var(--interactive-secondary-active);
}

/* Outline Button */
.btn-outline {
  background: transparent;
  color: var(--interactive-primary);
  border-color: var(--interactive-primary);
}

.btn-outline:hover {
  background: var(--interactive-primary);
  color: var(--text-inverse);
}

/* Ghost Button */
.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border-color: transparent;
}

.btn-ghost:hover {
  background: var(--interactive-secondary);
  color: var(--text-primary);
}

/* Danger Button */
.btn-danger {
  background: var(--status-error);
  color: var(--text-inverse);
  border-color: var(--status-error);
}

.btn-danger:hover {
  background: var(--error-600);
  border-color: var(--error-600);
}

/* Success Button */
.btn-success {
  background: var(--status-success);
  color: var(--text-inverse);
  border-color: var(--status-success);
}

.btn-success:hover {
  background: var(--success-600);
  border-color: var(--success-600);
}

/* =================================
   Button Sizes
   ================================= */

/* Small Button */
.btn-sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  min-height: var(--button-height-sm);
}

.btn-sm svg {
  width: 14px;
  height: 14px;
}

/* Large Button */
.btn-lg {
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  min-height: var(--button-height-lg);
}

.btn-lg svg {
  width: 18px;
  height: 18px;
}

/* =================================
   Special Button Types
   ================================= */

/* Add Button (Primary Action) */
.btn-add,
.add-btn,
.add-animal-btn {
  background: var(--interactive-primary);
  color: var(--text-inverse);
  border-color: var(--interactive-primary);
  box-shadow: var(--shadow-md);
  font-weight: var(--font-semibold);
}

.btn-add:hover,
.add-btn:hover,
.add-animal-btn:hover {
  background: var(--interactive-primary-hover);
  border-color: var(--interactive-primary-hover);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Action Button (Table Actions) */
.action-btn {
  background: var(--bg-elevated);
  color: var(--text-secondary);
  border-color: var(--border-primary);
  padding: var(--space-1) var(--space-2);
  min-height: 32px;
  font-size: var(--text-xs);
}

.action-btn:hover {
  background: var(--interactive-secondary-hover);
  color: var(--text-primary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.action-btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Actions Wrapper */
.actions-wrapper {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  justify-content: center;
}

.actions-wrapper .action-btn {
  min-width: 32px;
  height: 32px;
  padding: var(--space-2);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Action Button Variants */
.action-btn.edit-btn {
  color: var(--interactive-primary);
  border-color: var(--primary-200);
}

.action-btn.edit-btn:hover {
  background: var(--primary-50);
  border-color: var(--interactive-primary);
  color: var(--interactive-primary);
}

.action-btn.delete-btn {
  color: var(--status-error);
  border-color: var(--error-200);
}

.action-btn.delete-btn:hover {
  background: var(--status-error-bg);
  border-color: var(--status-error);
  color: var(--status-error);
}

.action-btn.view-btn {
  color: var(--text-tertiary);
  border-color: var(--gray-200);
}

.action-btn.view-btn:hover {
  background: var(--gray-50);
  border-color: var(--gray-300);
  color: var(--text-secondary);
}

/* =================================
   Button States
   ================================= */

/* Loading State */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* =================================
   Button Groups
   ================================= */
.btn-group {
  display: inline-flex;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-right-width: 1px;
}

.btn-group .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}

/* =================================
   Icon Only Buttons
   ================================= */
.btn-icon {
  padding: var(--space-2);
  min-width: var(--button-height-base);
  aspect-ratio: 1;
}

.btn-icon.btn-sm {
  padding: var(--space-1);
  min-width: var(--button-height-sm);
}

.btn-icon.btn-lg {
  padding: var(--space-3);
  min-width: var(--button-height-lg);
}

/* =================================
   Responsive Adjustments
   ================================= */
@media (max-width: 768px) {
  .btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }

  .btn-lg {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
  }

  .action-btn {
    padding: var(--space-1);
    min-height: 28px;
  }
}

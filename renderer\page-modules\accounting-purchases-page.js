// Standardized Accounting Purchases Page Module
// Following the new standardization guidelines

import { IconSystem } from '../utils/icon-system.js';
import { BadgeSystem } from '../utils/badge-system.js';
import { RecentActivities, formatActivityDate, truncateText } from '../components/recent-activities.js';
import { AccountingHeader } from '../components/accounting-header.js';
import { TableAnimations } from '../utils/table-animations.js';

// Standardized globals for this module
let passedI18n, passedContentArea, passedDefaultAvatarSVG;

let allPurchases = [];
let currentPurchases = [];
const itemsPerPage = 10;
let currentPage = 1;
let sortColumn = null;
let sortDirection = 1; // 1 for asc, -1 for desc

let productsCache = [];
let accountsCache = [];

// DOM element references
let purchasesTableBody;
let purchaseModal;
let purchaseForm;
let purchaseModalTitle;
let currentEditingPurchaseId = null;
let pageInfo;
let prevButton;
let nextButton;
let purchasesRecentActivities;


function sortPurchasesList(list, column, direction) {
  return [...list].sort((a, b) => {
    let valA = a[column];
    let valB = b[column];

    if (valA === null && valB === null) return 0;
    if (valA === null) return 1 * direction; // nulls last
    if (valB === null) return -1 * direction; // nulls last

    if (column === 'Tarih' || column === 'VadeTarihi') {
      valA = new Date(valA);
      valB = new Date(valB);
      return (valA - valB) * direction;
    }
    if (typeof valA === 'number' && typeof valB === 'number') {
      return (valA - valB) * direction;
    }
    if (typeof valA === 'boolean' && typeof valB === 'boolean') {
      return (valA === valB ? 0 : valA ? -1 : 1) * direction;
    }
    valA = (valA || '').toString().toLowerCase();
    valB = (valB || '').toString().toLowerCase();
    return valA.localeCompare(valB) * direction;
  });
}

export async function renderAccountingPurchasesPage(contentArea, i18n, defaultAvatarSVG) {
  // Store passed parameters using standardized naming
  passedI18n = i18n;
  passedContentArea = contentArea;
  passedDefaultAvatarSVG = defaultAvatarSVG;
  const t = passedI18n.t;

  // Initialize accounting header immediately to prevent jumping
  const accountingHeader = new AccountingHeader({
    containerId: 'accounting-header',
    pageTitle: t('accounting_purchases_page_title') || 'Alımlar',
    pageIcon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
    </svg>`
  });

  contentArea.innerHTML = `
    <div id="accounting-header"></div>
    <div id="purchases-controls">
      <button id="add-purchase-button" class="btn btn-primary">
        <svg viewBox='0 0 20 20' fill='none' style="width:20px; height:20px; margin-right: 8px;"><circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/><path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/></svg>
        ${t('add_new_purchase') || 'Add New Purchase'}
      </button><br></br>
    </div>
    <table id="purchases-table" class="modern-table">
      <thead>
        <tr>
          <th data-column-key="Id" class="sortable" title="${t('table_header_id') || 'ID'}">${t('table_header_id_short') || 'ID'}</th>
          <th data-column-key="CariUnvan" class="sortable" title="${t('table_header_account') || 'Cari Hesap'}">${t('table_header_account_short') || 'Cari'}</th>
          <th data-column-key="UrunAdi" class="sortable" title="${t('table_header_product') || 'Ürün'}">${t('table_header_product_short') || 'Ürün'}</th>
          <th data-column-key="Tarih" class="sortable" title="${t('table_header_date') || 'Tarih'}">${t('table_header_date_short') || 'Tarih'}</th>
          <th data-column-key="FaturaNo" class="sortable" title="${t('table_header_invoice_no') || 'Fatura Numarası'}">${t('table_header_invoice_no_short') || 'Fatura'}</th>
          <th data-column-key="Miktar" class="sortable" title="${t('table_header_quantity') || 'Miktar'}">${t('table_header_quantity_short') || 'Adet'}</th>
          <th data-column-key="BirimFiyat" class="sortable" title="${t('table_header_unit_price') || 'Birim Fiyat'}">${t('table_header_unit_price_short') || 'B.Fiyat'}</th>
          <th data-column-key="ToplamTutar" class="sortable" title="${t('table_header_total_amount') || 'Toplam Tutar'}">${t('table_header_total_amount_short') || 'Toplam'}</th>
          <th data-column-key="OdenenTutar" class="sortable" title="${t('table_header_paid_amount') || 'Ödenen Tutar'}">${t('table_header_paid_amount_short') || 'Ödenen'}</th>
          <th data-column-key="KalanTutar" class="sortable" title="${t('table_header_remaining_amount') || 'Kalan Tutar'}">${t('table_header_remaining_amount_short') || 'Kalan'}</th>
          <th data-column-key="OdemeDurumu" class="sortable" title="${t('table_header_payment_type') || 'Ödeme Tipi'}">${t('table_header_payment_type_short') || 'Ö.Tipi'}</th>
          <th data-column-key="IslemTamamlandi" class="sortable" title="${t('table_header_transaction_status') || 'İşlem Durumu'}">${t('table_header_transaction_status_short') || 'Durum'}</th>
          <th class="col-actions" title="${t('table_header_actions') || 'İşlemler'}">${t('table_header_actions_short') || 'İşlem'}</th>
        </tr>
        <tr class="filter-row">
          <th><input type="number" id="filter-purchases-id" class="filter-input" placeholder="${t('table_header_id')}" /></th>
          <th><input type="text" id="filter-purchases-account" class="filter-input" placeholder="${t('table_header_account')}" /></th>
          <th><input type="text" id="filter-purchases-product" class="filter-input" placeholder="${t('table_header_product')}" /></th>
          <th>
            <input type="date" id="filter-purchases-date-start" class="filter-input" />
            <input type="date" id="filter-purchases-date-end" class="filter-input" />
          </th>
          <th><input type="text" id="filter-purchases-invoice" class="filter-input" placeholder="${t('table_header_invoice_no')}" /></th>
          <th></th> <!-- Quantity filter not added, can be added if needed -->
          <th></th> <!-- Unit Price filter not added -->
          <th></th> <!-- Total Amount filter not added -->
          <th></th> <!-- Paid Amount filter not added -->
          <th></th> <!-- Remaining Amount filter not added -->
          <th>
            <select id="filter-purchases-payment-type" class="filter-input">
              <option value="">${t('option_all') || 'All'}</option>
              <option value="Pesin">${t('payment_type_cash') || 'Cash'}</option>
              <option value="Vadeli">${t('payment_type_credit') || 'Credit'}</option>
            </select>
          </th>
          <th>
            <select id="filter-purchases-transaction-status" class="filter-input">
              <option value="">${t('option_all') || 'All'}</option>
              <option value="paid">${t('transaction_status_paid') || 'Paid'}</option>
              <option value="partially_paid">${t('transaction_status_partially_paid') || 'Partially Paid'}</option>
              <option value="unpaid">${t('transaction_status_unpaid') || 'Unpaid'}</option>
            </select>
          </th>
          <th></th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>

    <div id="purchases-pagination-controls" class="pagination-controls">
      <button id="prev-page-purchases" disabled>&laquo; ${t('btn_previous') || 'Previous'}</button>
      <span id="page-info-purchases">Page 1 of 1</span>
      <button id="next-page-purchases" disabled>&raquo; ${t('btn_next') || 'Next'}</button>
    </div>

    <div id="purchases-recent-activities"></div>

    <!-- Purchase Modal -->
    <div id="purchase-modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="purchase-modal-title">${t('purchase_modal_title_add') || 'Add New Purchase'}</h2>
        </div>
        <form id="purchase-form">
          <div class="modal-body">
            <!-- Purchase Information Section -->
            <div class="form-section">
              <h3 class="section-title">${t('section_purchase_info') || 'Alış Bilgileri'}</h3>
              <div class="form-grid">
                <div class="form-row">
                  <label for="purchase-account">${t('label_account') || 'Cari'}:</label>
                  <select name="CariId" id="purchase-account" required></select>
                </div>
                <div class="form-row">
                  <label for="purchase-product">${t('label_product') || 'Ürün'}:</label>
                  <select name="UrunId" id="purchase-product" required></select>
                </div>
                <div class="form-row">
                  <label for="purchase-date">${t('label_date') || 'Tarih'}:</label>
                  <input type="date" name="Tarih" id="purchase-date" required>
                </div>
                <div class="form-row">
                  <label for="purchase-invoice-no">${t('label_invoice_no') || 'Fatura No'}:</label>
                  <input type="text" name="FaturaNo" id="purchase-invoice-no">
                </div>
                <div class="form-row">
                  <label for="purchase-quantity">${t('label_quantity') || 'Miktar'}:</label>
                  <input type="number" step="any" name="Miktar" id="purchase-quantity" required>
                </div>
                <div class="form-row">
                  <label for="alis-birim-fiyat">${t('alislar_col_birim_fiyat') || 'Birim Fiyat'}:</label>
                  <input type="number" step="any" name="BirimFiyat" id="alis-birim-fiyat" required>
                </div>
              </div>
            </div>

            <!-- Payment Information Section -->
            <div class="form-section">
              <h3 class="section-title">${t('section_payment_info') || 'Ödeme Bilgileri'}</h3>
              <div class="form-grid">
                <div class="form-row">
                  <label for="alis-odeme-durumu">${t('alislar_col_odeme_durumu') || 'Ödeme Durumu'}:</label>
                  <select name="OdemeDurumu" id="alis-odeme-durumu">
                    <option value="Pesin">${t('payment_type_pesin') || 'Peşin'}</option>
                    <option value="Vadeli">${t('payment_type_vadeli') || 'Vadeli'}</option>
                  </select>
                </div>
                <div class="form-row due-date-field" style="display: none;">
                  <label for="alis-vade-tarihi">${t('alislar_col_vade_tarihi') || 'Vade Tarihi'}:</label>
                  <input type="date" name="VadeTarihi" id="alis-vade-tarihi">
                </div>
              </div>
            </div>

            <!-- Additional Information Section -->
            <div class="form-section">
              <h3 class="section-title">${t('section_additional_info') || 'Ek Bilgiler'}</h3>
              <div class="form-row full">
                <label for="alis-notlar">${t('alislar_col_notlar') || 'Notlar'}:</label>
                <textarea name="Notlar" id="alis-notlar" rows="3"></textarea>
              </div>
            </div>

            <input type="hidden" name="Id">
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('purchase-modal').classList.add('hidden')">${t('btn_cancel') || 'İptal'}</button>
              <button type="submit" class="btn btn-primary">${t('btn_save') || 'Kaydet'}</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  `;

  // Initialize accounting header immediately after HTML is created
  accountingHeader.init();

  purchasesTableBody = contentArea.querySelector('#purchases-table tbody');
  purchaseModal = contentArea.querySelector('#purchase-modal');
  purchaseForm = contentArea.querySelector('#purchase-form');
  purchaseModalTitle = contentArea.querySelector('#purchase-modal-title');
  const closeBtn = purchaseModal.querySelector('.close-btn');
  const addPurchaseButton = contentArea.querySelector('#add-purchase-button');

  pageInfo = contentArea.querySelector('#page-info-purchases');
  prevButton = contentArea.querySelector('#prev-page-purchases');
  nextButton = contentArea.querySelector('#next-page-purchases');

  // Cache Urunler and Cariler for dropdowns
  await Promise.all([loadUrunlerForSelect(passedI18n), loadCarilerForSelect(passedI18n)]);

  addPurchaseButton.addEventListener('click', () => openPurchaseModal(null));
  closeBtn.addEventListener('click', () => purchaseModal.classList.add('hidden'));
  purchaseModal.addEventListener('click', (e) => {
    if (e.target === purchaseModal) {
      purchaseModal.classList.add('hidden');
    }
  });

  purchaseForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    const formData = new FormData(purchaseForm);
    const purchaseData = Object.fromEntries(formData.entries());
    purchaseData.CariId = parseInt(purchaseData.CariId);
    purchaseData.UrunId = parseInt(purchaseData.UrunId);
    purchaseData.Miktar = parseFloat(purchaseData.Miktar);
    purchaseData.BirimFiyat = parseFloat(purchaseData.BirimFiyat);
    // Tarih formatını kontrol et/dönüştür
    if (purchaseData.Tarih) purchaseData.Tarih = new Date(purchaseData.Tarih).toISOString().split('T')[0];
    if (purchaseData.VadeTarihi) purchaseData.VadeTarihi = new Date(purchaseData.VadeTarihi).toISOString().split('T')[0];


    try {
      if (currentEditingPurchaseId) {
        await window.api.invoke('purchases:update', { id: currentEditingPurchaseId, data: purchaseData });
        window.toast.success(passedI18n.t('toast_success_update'));
      } else {
        await window.api.invoke('purchases:add', purchaseData);
        window.toast.success(passedI18n.t('toast_success_save'));
      }
      purchaseModal.classList.add('hidden');
      // Fetch all data again and re-apply filters/sort
      allPurchases = await window.api.invoke('purchases:list');
      applyFiltersAndSortPurchases();
      // Refresh recent activities
      if (purchasesRecentActivities) {
        purchasesRecentActivities.refresh();
      }
    } catch (error) {
      console.error('Alış kaydetme/güncelleme hatası:', error);
      window.toast.error(passedI18n.t('error_generic_api', { error: error.message || 'Bilinmeyen bir hata oluştu.' }));
    }
  });

  // Initial data load
  allPurchases = await window.api.invoke('purchases:list');
  currentPurchases = [...allPurchases];
  applyFiltersAndSortPurchases(); // Initial render

  // Event listeners for filters
  const filterInputs = [
    '#filter-purchases-id', '#filter-purchases-account', '#filter-purchases-product',
    '#filter-purchases-date-start', '#filter-purchases-date-end',
    '#filter-purchases-invoice', '#filter-purchases-payment-type', '#filter-purchases-transaction-status'
  ];
  filterInputs.forEach(selector => {
    const inputElement = contentArea.querySelector(selector);
    if (inputElement) {
      inputElement.addEventListener('input', applyFiltersAndSortPurchases);
      if (inputElement.tagName === 'SELECT' || inputElement.type === 'date') {
         inputElement.addEventListener('change', applyFiltersAndSortPurchases);
      }
    }
  });

  // Event listeners for pagination
  prevButton.addEventListener('click', () => {
    if (currentPage > 1) {
      currentPage--;
      renderPurchasesTablePage();
      updatePurchasesPaginationControls();
    }
  });

  nextButton.addEventListener('click', () => {
    const totalPages = Math.ceil(currentPurchases.length / itemsPerPage);
    if (currentPage < totalPages) {
      currentPage++;
      renderPurchasesTablePage();
      updatePurchasesPaginationControls();
    }
  });

  // Event listeners for sorting
  const ths = contentArea.querySelectorAll('#purchases-table thead th[data-column-key]');

  ths.forEach(th => {
    const columnKey = th.dataset.columnKey;
    if (!columnKey) return;

    th.classList.add('sortable');
    th.addEventListener('click', () => {
      if (sortColumn === columnKey) {
        sortDirection *= -1;
      } else {
        sortColumn = columnKey;
        sortDirection = 1;
      }
      // Clear all sorting classes and arrows
      ths.forEach(headerTh => {
        headerTh.classList.remove('sorted-asc', 'sorted-desc');
        headerTh.querySelector('.sort-arrow')?.remove();
      });

      // Add sorting class to current header
      th.classList.add(sortDirection === 1 ? 'sorted-asc' : 'sorted-desc');

      applyFiltersAndSortPurchases();
    });
  });

  // Setup payment status change handler
  const paymentStatusSelect = contentArea.querySelector('#alis-odeme-durumu');
  if (paymentStatusSelect) {
    paymentStatusSelect.addEventListener('change', function() {
      const dueDateField = contentArea.querySelector('.due-date-field');
      if (dueDateField) {
        dueDateField.style.display = this.value === 'Vadeli' ? 'flex' : 'none';

        // Clear due date when hiding
        if (this.value !== 'Vadeli') {
          const dueDateInput = dueDateField.querySelector('input[type="date"]');
          if (dueDateInput) dueDateInput.value = '';
        }
      }
    });
  }

  // Initialize Recent Activities
  purchasesRecentActivities = new RecentActivities({
    containerId: 'purchases-recent-activities',
    title: t('recent_purchase_activities') || 'Recent Purchase Activities',
    maxItems: 5,
    onViewAll: () => {
      // Scroll to main table
      document.getElementById('purchases-table').scrollIntoView({ behavior: 'smooth' });
    },
    getRecords: async () => {
      try {
        const purchases = await window.api.invoke('purchases:list');
        return purchases.sort((a, b) => new Date(b.Tarih) - new Date(a.Tarih));
      } catch (error) {
        console.error('Error fetching purchase records:', error);
        return [];
      }
    },
    formatRecord: (record) => {
      const amount = record.ToplamTutar || (record.Miktar * record.BirimFiyat);
      return {
        title: truncateText(record.CariUnvan || 'Unknown Account', 25),
        subtitle: truncateText(record.UrunAdi || 'Unknown Product', 30),
        date: formatActivityDate(record.Tarih),
        amount: amount ? `₺${amount.toFixed(2)}` : null,
        badge: BadgeSystem.createPaymentMethodBadge(record.OdemeDurumu, {
          cash: t('payment_type_cash'),
          credit: t('payment_type_credit')
        })
      };
    },
    emptyMessage: t('no_recent_purchase_activities') || 'No recent purchase activities'
  });



  // Initialize recent activities
  purchasesRecentActivities.init();

  // Ensure modals are properly structured
  setTimeout(() => {
    if (window.reinitializeModals) {
      window.reinitializeModals();
    }
  }, 100);
}

function applyFiltersAndSortPurchases() {
  const t = passedI18n.t;
  let filtered = [...allPurchases];

  const idFilter = document.getElementById('filter-purchases-id')?.value;
  const cariFilter = document.getElementById('filter-purchases-account')?.value.trim().toLowerCase();
  const urunFilter = document.getElementById('filter-purchases-product')?.value.trim().toLowerCase();
  const tarihStartFilter = document.getElementById('filter-purchases-date-start')?.value;
  const tarihEndFilter = document.getElementById('filter-purchases-date-end')?.value;
  const faturaFilter = document.getElementById('filter-purchases-invoice')?.value.trim().toLowerCase();
  const odemeDurumuFilter = document.getElementById('filter-purchases-payment-type')?.value;
  const islemDurumuFilter = document.getElementById('filter-purchases-transaction-status')?.value;

  if (idFilter) filtered = filtered.filter(a => a.Id == idFilter);
  if (cariFilter) filtered = filtered.filter(a => (a.CariUnvan || '').toLowerCase().includes(cariFilter));
  if (urunFilter) filtered = filtered.filter(a => (a.UrunAdi || '').toLowerCase().includes(urunFilter));
  if (tarihStartFilter) filtered = filtered.filter(a => new Date(a.Tarih) >= new Date(tarihStartFilter));
  if (tarihEndFilter) filtered = filtered.filter(a => new Date(a.Tarih) <= new Date(tarihEndFilter));
  if (faturaFilter) filtered = filtered.filter(a => (a.FaturaNo || '').toLowerCase().includes(faturaFilter));
  if (odemeDurumuFilter) filtered = filtered.filter(a => a.OdemeDurumu === odemeDurumuFilter);

  if (islemDurumuFilter) {
    filtered = filtered.filter(a => {
      const status = getIslemDurumu(a, t); // Helper to determine status string
      if (islemDurumuFilter === 'paid') return status === (t('transaction_status_paid') || 'Ödendi');
      if (islemDurumuFilter === 'partially_paid') return status === (t('transaction_status_partially_paid') || 'Kısmi Ödendi');
      if (islemDurumuFilter === 'unpaid') return status === (t('transaction_status_unpaid') || 'Ödenmedi');
      return false;
    });
  }

  if (sortColumn) {
    filtered = sortPurchasesList(filtered, sortColumn, sortDirection);
  }

  currentPurchases = filtered;
  currentPage = 1;
  renderPurchasesTablePage();
  updatePurchasesPaginationControls();
}


function renderPurchasesTablePage() {
  const t = passedI18n.t;
  purchasesTableBody.innerHTML = ''; // Clear existing rows

  const totalPages = Math.ceil(currentPurchases.length / itemsPerPage);
  currentPage = Math.max(1, Math.min(currentPage, totalPages || 1));

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const pageItems = currentPurchases.slice(startIndex, endIndex);

  if (pageItems.length === 0) {
    purchasesTableBody.innerHTML = `<tr><td colspan="13"><div class="empty-state-container"><div class="empty-state-icon">${passedDefaultAvatarSVG ? passedDefaultAvatarSVG(48) : ''}</div><p>${t('no_records_found')}</p></div></td></tr>`;
    return;
  }

  pageItems.forEach(purchase => {
    const row = purchasesTableBody.insertRow();
    const toplamTutar = typeof purchase.ToplamTutar === 'number' ? purchase.ToplamTutar.toFixed(2) : 'N/A';
    const odenenTutar = typeof purchase.OdenenTutar === 'number' ? purchase.OdenenTutar.toFixed(2) : '0.00';
    const kalanTutar = typeof purchase.KalanTutar === 'number' ? purchase.KalanTutar.toFixed(2) : toplamTutar;

    const { badgeClass: odemeBadgeClass, badgeText: odemeBadgeText } = getOdemeDurumuBadge(purchase, t);
    const { badgeClass: islemBadgeClass, badgeText: islemBadgeText } = getIslemDurumuBadge(purchase, t);

    row.innerHTML = `
      <td class="cell-alislar-id">${purchase.Id}</td>
      <td class="cell-alislar-cari">${purchase.CariUnvan || (accountsCache.find(c => c.Id === purchase.CariId)?.Unvan) || t('unknown')}</td>
      <td class="cell-alislar-urun">${purchase.UrunAdi || (productsCache.find(u => u.Id === purchase.UrunId)?.Ad) || t('unknown')}</td>
      <td class="cell-alislar-tarih">${purchase.Tarih ? new Date(purchase.Tarih).toLocaleDateString() : ''}</td>
      <td class="cell-alislar-fatura">${purchase.FaturaNo || ''}</td>
      <td class="cell-alislar-miktar">${purchase.Miktar || ''}</td>
      <td class="cell-alislar-birimfiyat">${purchase.BirimFiyat ? purchase.BirimFiyat.toFixed(2) : ''}</td>
      <td class="cell-alislar-toplamtutar">${toplamTutar}</td>
      <td class="cell-alislar-odenentutar">${odenenTutar}</td>
      <td class="cell-alislar-kalantutar">${kalanTutar}</td>
      <td class="cell-alislar-odemedurumu">${BadgeSystem.createPaymentMethodBadge(purchase.OdemeDurumu, { cash: t('payment_type_cash'), credit: t('payment_type_credit') })}</td>
      <td class="cell-alislar-islemdurumu">${BadgeSystem.createTransactionStatusBadge(purchase, { paid: t('transaction_status_paid'), unpaid: t('transaction_status_unpaid'), partiallyPaid: t('transaction_status_partially_paid') })}</td>
      <td class="col-actions actions">
        ${IconSystem.createActionsWrapper(purchase.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
      </td>
    `;
    row.querySelector('.edit-btn').addEventListener('click', () => openPurchaseModal(purchase));
    row.querySelector('.delete-btn').addEventListener('click', () => deletePurchase(purchase.Id));
  });
  
  // Initialize table animations
  setTimeout(() => {
    TableAnimations.initializeTable('#purchases-table');
  }, 100);
}

function updatePurchasesPaginationControls() {
  const t = passedI18n.t;
  const totalItems = currentPurchases.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
  pageInfo.textContent = t('page_info_text', { currentPage, totalPages, totalItems }) || `Page ${currentPage} of ${totalPages} (${totalItems} items)`;
  prevButton.disabled = currentPage === 1;
  nextButton.disabled = currentPage === totalPages;
}

async function loadUrunlerForSelect() {
  const t = passedI18n.t;
  try {
    productsCache = await window.api.invoke('products:list');
    const productSelect = purchaseForm.elements['UrunId'];
    if (productSelect) {
      productSelect.innerHTML = `<option value="">${t('option_select_product') || 'Ürün Seçiniz'}</option>`;
      productsCache.forEach(urun => {
        const option = document.createElement('option');
        option.value = urun.Id;
        option.textContent = urun.Ad;
        productSelect.appendChild(option);
      });
    }
  } catch (error) {
    console.error('Form için ürünler yüklenirken hata:', error);
  }
}

async function loadCarilerForSelect() {
  const t = passedI18n.t;
  try {
    accountsCache = await window.api.invoke('accounts:list');
    const accountSelect = purchaseForm.elements['CariId'];
    if (accountSelect) {
      accountSelect.innerHTML = `<option value="">${t('option_select_cari') || 'Cari Seçiniz'}</option>`;
      const tedarikciler = accountsCache.filter(c => c.Tip === 'Tedarikci');
      (tedarikciler.length > 0 ? tedarikciler : accountsCache).forEach(cari => {
        const option = document.createElement('option');
        option.value = cari.Id;
        option.textContent = cari.Unvan;
        accountSelect.appendChild(option);
      });
    }
  } catch (error) {
    console.error('Form için cariler yüklenirken hata:', error);
  }
}

function getOdemeDurumuBadge(alis, t) {
    let badgeClass = 'badge';
    let badgeText = t('payment_type_unknown') || 'Bilinmiyor';

    if (alis.OdemeDurumu === 'Pesin') {
        badgeClass = 'badge badge-pesin'; // Assuming green or success
        badgeText = t('payment_type_pesin') || 'Peşin';
    } else if (alis.OdemeDurumu === 'Vadeli') {
        badgeClass = 'badge badge-vadeli'; // Assuming orange or warning
        badgeText = t('payment_type_vadeli') || 'Vadeli';
    }
    return { badgeClass, badgeText };
}

function getIslemDurumu(alis, t) { // Renamed from getIslemDurumuBadge to just getIslemDurumu for filter logic
    if (alis.IslemTamamlandi || (alis.ToplamTutar > 0 && alis.KalanTutar <= 0)) {
        return t('transaction_status_paid') || 'Ödendi';
    } else if (alis.OdenenTutar > 0 && alis.KalanTutar > 0) {
        return t('transaction_status_partially_paid') || 'Kısmi Ödendi';
    } else {
        return t('transaction_status_unpaid') || 'Ödenmedi';
    }
}


function getIslemDurumuBadge(alis, t) {
    let badgeClass = 'badge badge-pasif'; // Default to unpaid/passive style
    let badgeText = t('transaction_status_unpaid') || 'Ödenmedi';

    if (alis.IslemTamamlandi || (alis.ToplamTutar > 0 && alis.KalanTutar <= 0)) { // Check KalanTutar for explicit completion
        badgeClass = 'badge badge-aktif'; // Green / Active
        badgeText = t('transaction_status_paid') || 'Ödendi';
    } else if (alis.OdenenTutar > 0 && alis.KalanTutar > 0) {
        badgeClass = 'badge badge-vadeli'; // Orange / Pending
        badgeText = t('transaction_status_partially_paid') || 'Kısmi Ödendi';
    }
    // Default is already set for unpaid
    return { badgeClass, badgeText };
}


function openPurchaseModal(purchaseData = null) {
  const t = passedI18n.t;
  purchaseForm.reset();
  // Ensure dropdowns are populated
  if (purchaseForm.elements['UrunId'].options.length <= 1 && productsCache.length > 0) {
      productsCache.forEach(product => {
        const option = document.createElement('option');
        if (purchaseForm.elements['UrunId'].querySelector(`option[value="${product.Id}"]`)) return; // Avoid duplicates
        option.value = product.Id;
        option.textContent = product.Ad;
        purchaseForm.elements['UrunId'].appendChild(option);
      });
  }
   if (purchaseForm.elements['CariId'].options.length <= 1 && accountsCache.length > 0) {
      const suppliers = accountsCache.filter(c => c.Tip === 'Tedarikci');
      (suppliers.length > 0 ? suppliers : accountsCache).forEach(account => {
        if (purchaseForm.elements['CariId'].querySelector(`option[value="${account.Id}"]`)) return; // Avoid duplicates
        const option = document.createElement('option');
        option.value = account.Id;
        option.textContent = account.Unvan;
        purchaseForm.elements['CariId'].appendChild(option);
      });
  }

  if (purchaseData) {
    currentEditingPurchaseId = purchaseData.Id;
    purchaseModalTitle.textContent = t('purchase_modal_title_edit') || 'Edit Purchase';
    purchaseForm.elements['Id'].value = purchaseData.Id;
    purchaseForm.elements['CariId'].value = purchaseData.CariId;
    purchaseForm.elements['UrunId'].value = purchaseData.UrunId;
    // Ensure date is in YYYY-MM-DD for input type="date"
    purchaseForm.elements['Tarih'].value = purchaseData.Tarih ? new Date(purchaseData.Tarih).toISOString().split('T')[0] : '';
    purchaseForm.elements['FaturaNo'].value = purchaseData.FaturaNo || '';
    purchaseForm.elements['Miktar'].value = purchaseData.Miktar || '';
    purchaseForm.elements['BirimFiyat'].value = purchaseData.BirimFiyat || '';
    purchaseForm.elements['OdemeDurumu'].value = purchaseData.OdemeDurumu || 'Pesin';
    purchaseForm.elements['VadeTarihi'].value = purchaseData.VadeTarihi ? new Date(purchaseData.VadeTarihi).toISOString().split('T')[0] : '';
    purchaseForm.elements['Notlar'].value = purchaseData.Notlar || '';
  } else {
    currentEditingPurchaseId = null;
    purchaseModalTitle.textContent = t('purchase_modal_title_add') || 'Add New Purchase';
    purchaseForm.elements['Id'].value = '';
    purchaseForm.elements['Tarih'].valueAsDate = new Date(); // Default to today
    purchaseForm.elements['OdemeDurumu'].value = 'Pesin'; // Default
  }
  purchaseModal.classList.remove('hidden');
}

async function deletePurchase(id) {
  const t = passedI18n.t;
  const confirmed = await window.toast.confirm(t('confirm_delete_purchase', { purchaseId: id }), {
    confirmText: t('toast_confirm'),
    cancelText: t('toast_cancel')
  });

  if (confirmed) {
    try {
      await window.api.invoke('purchases:delete', id);
      allPurchases = await window.api.invoke('purchases:list');
      applyFiltersAndSortPurchases();
      window.toast.success(t('toast_success_delete'));
    } catch (error) {
      console.error('Alış silme hatası:', error);
      window.toast.error(t('error_generic_api', { error: error.message || 'Bilinmeyen bir hata oluştu.' }));
    }
  }
}

// Alias function for backward compatibility
function applyFiltersAndSortAlislar() {
  applyFiltersAndSortPurchases();
}

const db = require('./database').getDb();

// Helper function to get current profile
function getCurrentProfile() {
  try {
    const authHandlers = require('../ipc-handlers/auth-handlers.js');
    return authHandlers.getCurrentProfile();
  } catch (error) {
    console.error('Error getting current profile:', error);
    return null;
  }
}

// --- Products Services ---
async function getAllProducts() {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.all("SELECT * FROM Urunler WHERE ProfilId = ? ORDER BY Ad", [currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

async function getAccountDetails(accountId) {
  return new Promise(async (resolve, reject) => {
    try {
      const accountSql = "SELECT * FROM Cariler WHERE Id = ?";
      const account = await new Promise((res, rej) => db.get(accountSql, [accountId], (err, row) => err ? rej(err) : res(row)));

      if (!account) {
        return reject(new Error('Account not found'));
      }

      const alislarSql = `
        SELECT Tarih, 'Alış' AS Tip, UrunId, Miktar, BirimFiyat, ToplamTutar, OdenenTutar, KalanTutar
        FROM Alislar
        WHERE CariId = ?
        ORDER BY Tarih DESC`;
      const satislarSql = `
        SELECT Tarih, 'Satış' AS Tip, UrunId, Miktar, BirimFiyat, ToplamTutar, OdenenTutar, KalanTutar
        FROM Satislar
        WHERE CariId = ?
        ORDER BY Tarih DESC`;
      // Ödemeler için daha genel bir sorgu, IslemTipi'ne göre ayrıştırılabilir veya direkt tüm ödemeler listelenebilir.
      // Şimdilik sadece alış ve satışları alıyoruz, ödemeler daha sonra eklenebilir veya ayrı bir sekmede gösterilebilir.

      const alislar = await new Promise((res, rej) => db.all(alislarSql, [accountId], (err, rows) => err ? rej(err) : res(rows)));
      const satislar = await new Promise((res, rej) => db.all(satislarSql, [accountId], (err, rows) => err ? rej(err) : res(rows)));

      let toplamAlisTutari = 0;
      let toplamAlisOdenen = 0;
      alislar.forEach(a => {
        toplamAlisTutari += a.ToplamTutar;
        toplamAlisOdenen += a.OdenenTutar;
      });

      let toplamSatisTutari = 0;
      let toplamSatisOdenen = 0; // Bu, cariden alınan ödemeler
      satislar.forEach(s => {
        toplamSatisTutari += s.ToplamTutar;
        toplamSatisOdenen += s.OdenenTutar;
      });

      // Son N işlemi birleştirelim (örneğin son 10 işlem)
      const sonIslemler = [...alislar, ...satislar]
        .sort((a, b) => new Date(b.Tarih) - new Date(a.Tarih))
        .slice(0, 10) // Son 10 işlemi al
        .map(async (islem) => {
            let urunAdi = '';
            if (islem.UrunId) {
                const urun = await getProductById(islem.UrunId);
                urunAdi = urun ? urun.Ad : 'Bilinmeyen Ürün';
            }
            return {
                Tarih: islem.Tarih,
                Tip: islem.Tip,
                Aciklama: `${urunAdi} (${islem.Miktar} x ${islem.BirimFiyat.toFixed(2)})`,
                Tutar: islem.ToplamTutar
            };
        });

      const resolvedSonIslemler = await Promise.all(sonIslemler);

      // Güncel Bakiye: Carinin bize borcu (pozitif) veya bizim cariye borcumuz (negatif)
      // (Toplam Satış Kalan Tutarı) - (Toplam Alış Kalan Tutarı)
      const toplamSatisKalan = toplamSatisTutari - toplamSatisOdenen;
      const toplamAlisKalan = toplamAlisTutari - toplamAlisOdenen;
      const guncelBakiye = toplamSatisKalan - toplamAlisKalan;


      resolve({
        account,
        balance: {
          totalDebt: toplamSatisKalan,
          totalCredit: toplamAlisKalan,
          netBalance: guncelBakiye,
          totalPurchases: toplamAlisTutari,
          totalSales: toplamSatisTutari,
          totalPurchasePayments: toplamAlisOdenen,
          totalSalesPayments: toplamSatisOdenen
        },
        transactions: resolvedSonIslemler
      });

    } catch (error) {
      reject(error);
    }
  });
}

// Carilerin Net Bakiyelerini Dashboard için Hesapla (Tarih Kısıtsız)
async function getAccountsNetBalanceDashboard() {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  const sql = `
    SELECT
        C.Id,
        C.Unvan,
        (SELECT IFNULL(SUM(S2.KalanTutar),0) FROM Satislar S2 WHERE S2.CariId = C.Id AND S2.ProfilId = ?) AS ToplamKalanSatisAlacagi,
        (SELECT IFNULL(SUM(A2.KalanTutar),0) FROM Alislar A2 WHERE A2.CariId = C.Id AND A2.ProfilId = ?) AS ToplamKalanAlisBorcu
    FROM Cariler C
    WHERE C.ProfilId = ?
  `;
  return new Promise((resolve, reject) => {
    db.all(sql, [currentProfile.id, currentProfile.id, currentProfile.id], (err, rows) => {
      if (err) return reject(err);

      const result = rows.map(row => {
        const netBakiye = (row.ToplamKalanSatisAlacagi || 0) - (row.ToplamKalanAlisBorcu || 0);
        return {
          Id: row.Id,
          Unvan: row.Unvan,
          NetBakiye: netBakiye
        };
      }).filter(cari => cari.NetBakiye < -0.005 || cari.NetBakiye > 0.005); // KalanTutar'daki gibi küçük bir eşik

      // Alacak miktarı en yüksekten borç miktarı en yükseğe doğru sırala
      result.sort((a,b) => {
        // Önce mutlak değere göre büyük olanlar, sonra pozitifler negatiflerden önce
        if (Math.abs(b.NetBakiye) - Math.abs(a.NetBakiye) !== 0) {
            return Math.abs(b.NetBakiye) - Math.abs(a.NetBakiye);
        }
        return b.NetBakiye - a.NetBakiye; // Pozitifler önce (büyükten küçüğe), sonra negatifler (küçükten büyüğe)
      });
      resolve(result);
    });
  });
}


async function getProductById(id) {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.get("SELECT * FROM Urunler WHERE Id = ? AND ProfilId = ?", [id, currentProfile.id], (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

async function addProduct(product) {
  const { Ad, Kategori, VarsayilanBirim, Notlar } = product;
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.run(
      "INSERT INTO Urunler (ProfilId, Ad, Kategori, VarsayilanBirim, Notlar) VALUES (?, ?, ?, ?, ?)",
      [currentProfile.id, Ad, Kategori, VarsayilanBirim, Notlar],
      function (err) {
        if (err) reject(err);
        else resolve({ id: this.lastID });
      }
    );
  });
}

async function updateProduct(id, product) {
  const { Ad, Kategori, VarsayilanBirim, Notlar } = product;
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.run(
      "UPDATE Urunler SET Ad = ?, Kategori = ?, VarsayilanBirim = ?, Notlar = ? WHERE Id = ? AND ProfilId = ?",
      [Ad, Kategori, VarsayilanBirim, Notlar, id, currentProfile.id],
      function (err) {
        if (err) reject(err);
        else resolve({ changes: this.changes });
      }
    );
  });
}

async function deleteProduct(id) {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    db.run("DELETE FROM Urunler WHERE Id = ? AND ProfilId = ?", [id, currentProfile.id], function (err) {
      if (err) reject(err);
      else resolve({ changes: this.changes });
    });
  });
}

// --- Accounts Services ---
async function getAllAccounts({ filters, sortBy } = {}) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  let sql = "SELECT * FROM Cariler";
  const params = [];
  const conditions = ["ProfilId = ?"];
  params.push(currentProfile.id);

  if (filters) {
    if (filters.Unvan) {
      conditions.push("Unvan LIKE ?");
      params.push(`%${filters.Unvan}%`);
    }
    if (filters.Tip) {
      conditions.push("Tip = ?");
      params.push(filters.Tip);
    }
    // Gelecekte diğer filtreler buraya eklenebilir (örn: bakiye > X)
  }

  if (conditions.length > 0) {
    sql += " WHERE " + conditions.join(" AND ");
  }

  // Sıralama
  if (sortBy) {
    // sortBy formatı: "SutunAdi ASC|DESC"
    const [column, direction] = sortBy.split(" ");
    const validColumns = ["Id", "Unvan", "Tip"]; // Güvenlik için sıralanabilir sütunları beyaz listeye al
    const validDirections = ["ASC", "DESC"];

    if (validColumns.includes(column) && validDirections.includes(direction.toUpperCase())) {
      sql += ` ORDER BY ${column} ${direction.toUpperCase()}`;
    } else {
      sql += " ORDER BY Unvan ASC"; // Varsayılan sıralama
    }
  } else {
    sql += " ORDER BY Unvan ASC"; // Varsayılan sıralama
  }

  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

async function getAccountById(id) {
  return new Promise((resolve, reject) => {
    db.get("SELECT * FROM Cariler WHERE Id = ?", [id], (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

async function addAccount(account) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  const { Unvan, Tip, Telefon, Email, Adres, VergiNo, Notlar } = account;
  return new Promise((resolve, reject) => {
    db.run(
      "INSERT INTO Cariler (Unvan, Tip, Telefon, Email, Adres, VergiNo, Notlar, ProfilId) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
      [Unvan, Tip, Telefon, Email, Adres, VergiNo, Notlar, currentProfile.id],
      function (err) {
        if (err) reject(err);
        else resolve({ id: this.lastID });
      }
    );
  });
}

async function updateAccount(id, account) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  const { Unvan, Tip, Telefon, Email, Adres, VergiNo, Notlar } = account;
  return new Promise((resolve, reject) => {
    db.run(
      "UPDATE Cariler SET Unvan = ?, Tip = ?, Telefon = ?, Email = ?, Adres = ?, VergiNo = ?, Notlar = ? WHERE Id = ? AND ProfilId = ?",
      [Unvan, Tip, Telefon, Email, Adres, VergiNo, Notlar, id, currentProfile.id],
      function (err) {
        if (err) reject(err);
        else resolve({ changes: this.changes });
      }
    );
  });
}

async function deleteAccount(id) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  return new Promise((resolve, reject) => {
    db.run("DELETE FROM Cariler WHERE Id = ? AND ProfilId = ?", [id, currentProfile.id], function (err) {
      if (err) reject(err);
      else resolve({ changes: this.changes });
    });
  });
}

// --- Purchases Services ---
async function getAllPurchases({ startDate, endDate } = {}) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  let sql = `
    SELECT A.*, C.Unvan AS CariUnvan, U.Ad AS UrunAdi
    FROM Alislar A
    JOIN Cariler C ON A.CariId = C.Id
    JOIN Urunler U ON A.UrunId = U.Id
  `;
  const params = [];
  const conditions = ["A.ProfilId = ?"];
  params.push(currentProfile.id);

  if (startDate) {
    conditions.push("A.Tarih >= ?");
    params.push(startDate);
  }
  if (endDate) {
    conditions.push("A.Tarih <= ?");
    params.push(endDate);
  }

  if (conditions.length > 0) {
    sql += " WHERE " + conditions.join(" AND ");
  }

  sql += " ORDER BY A.Tarih DESC";

  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

async function addPurchase(purchase) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  const { CariId, UrunId, Tarih, FaturaNo, Miktar, BirimFiyat, OdemeDurumu, VadeTarihi, Notlar } = purchase;
  const toplamTutar = parseFloat(Miktar) * parseFloat(BirimFiyat);
  let odenenTutar = 0;
  let kalanTutar = toplamTutar;
  let islemTamamlandi = 0;

  if (OdemeDurumu === 'Pesin') {
    odenenTutar = toplamTutar;
    kalanTutar = 0;
    islemTamamlandi = 1;
  }

  return new Promise((resolve, reject) => {
    db.run(
      "INSERT INTO Alislar (CariId, UrunId, Tarih, FaturaNo, Miktar, BirimFiyat, ToplamTutar, OdenenTutar, KalanTutar, OdemeDurumu, VadeTarihi, Notlar, IslemTamamlandi, ProfilId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
      [CariId, UrunId, Tarih, FaturaNo, Miktar, BirimFiyat, toplamTutar, odenenTutar, kalanTutar, OdemeDurumu, VadeTarihi, Notlar, islemTamamlandi, currentProfile.id],
      function (err) {
        if (err) reject(err);
        else resolve({ id: this.lastID, toplamTutar, odenenTutar, kalanTutar, islemTamamlandi });
      }
    );
  });
}

async function updatePurchase(id, purchase) {
  const { CariId, UrunId, Tarih, FaturaNo, Miktar, BirimFiyat, OdemeDurumu, VadeTarihi, Notlar } = purchase;
  const toplamTutar = parseFloat(Miktar) * parseFloat(BirimFiyat);

  // Mevcut ödemeleri dikkate alarak OdenenTutar ve KalanTutar'ı yeniden hesapla.
  // Şimdilik, eğer OdemeDurumu değişirse, basitleştirilmiş bir mantık kullanalım:
  // Eğer "Peşin"e dönerse ve hiç ödeme yoksa tam ödenmiş say.
  // Eğer "Vadeli"ye dönerse, mevcut ödemeler korunur.
  // Bu kısım daha detaylı ele alınabilir: Eğer işlem "Vadeli" iken ödemeler yapıldıysa
  // ve sonra "Peşin"e çevrilirse ne olmalı? Ya da tam tersi.
  // Şimdilik varsayım: updateAlis sırasında OdenenTutar doğrudan değiştirilmiyor,
  // sadece OdemeDurumu değişikliğine göre ilk KalanTutar ayarlanıyor.
  // Gerçek ödeme takibi Odemeler tablosu üzerinden olacak.

  return new Promise((resolve, reject) => {
    db.get("SELECT OdenenTutar FROM Alislar WHERE Id = ? AND ProfilId = ?", [id, getCurrentProfile().id], (err, row) => {
      if (err) return reject(err);
      if (!row) return reject(new Error('Alış bulunamadı'));

      let odenenTutar = row.OdenenTutar; // Mevcut ödenen tutarı koru
      let kalanTutar = toplamTutar - odenenTutar;
      let islemTamamlandi = kalanTutar <= 0 ? 1 : 0;

      // Eğer OdemeDurumu "Peşin" olarak güncelleniyorsa ve daha önce hiç ödeme yapılmadıysa (veya az yapıldıysa),
      // ve bu güncelleme ile tam ödenmiş varsayılıyorsa, odenenTutar'ı toplamTutar'a eşitle.
      // Bu senaryo biraz karmaşık, çünkü updateAlis tek başına ödeme yapmaz.
      // Şimdilik OdemeDurumu değişikliğinin KalanTutar'ı doğrudan etkilemediğini, sadece bir gösterge olduğunu varsayalım.
      // Asıl ödeme durumu Odemeler tablosu ve oradan güncellenen OdenenTutar ile belirlenir.
      // Ancak, eğer bir alış "Peşin" olarak güncelleniyorsa, ve o ana kadar hiç ödeme yoksa,
      // mantıken o anda tam ödenmiş gibi davranılması beklenebilir. Bu, UI'da ayrı bir "Ödeme Yap" işlemiyle daha net olur.
      // Şimdilik, ToplamTutar değişimine göre KalanTutar'ı güncelleyelim. IslemTamamlandi de buna göre ayarlanır.

      db.run(
        "UPDATE Alislar SET CariId = ?, UrunId = ?, Tarih = ?, FaturaNo = ?, Miktar = ?, BirimFiyat = ?, ToplamTutar = ?, KalanTutar = ?, OdemeDurumu = ?, VadeTarihi = ?, Notlar = ?, IslemTamamlandi = ? WHERE Id = ? AND ProfilId = ?",
        [CariId, UrunId, Tarih, FaturaNo, Miktar, BirimFiyat, toplamTutar, kalanTutar, OdemeDurumu, VadeTarihi, Notlar, islemTamamlandi, id, getCurrentProfile().id],
        function (err) {
          if (err) reject(err);
          else resolve({ changes: this.changes });
        }
      );
    });
  });
}

async function deletePurchase(id) {
  // İlişkili ödemeler varsa ne yapılmalı? Genellikle silinmemesi tercih edilir
  // ya da kullanıcıya sorulur. Şimdilik sadece Alis kaydını siliyoruz.
  // Eğer cascade delete aktifse (ki aktif görünüyor FOREIGN KEY tanımında),
  // Alis silindiğinde ilişkili Odemeler de silinecektir (eğer DB bunu destekliyorsa ve IslemId üzerinden bağlıysa).
  // Ancak Odemeler tablosu IslemId'yi direkt Alislar.Id'ye bağlamıyor, IslemTipi ile ayırıyor.
  // Bu yüzden manuel olarak ilişkili ödemeleri de silmek gerekebilir ya da bu işleme izin verilmemeli.
  // Planda bu detay atlanmış, şimdilik sadece Alis'ı siliyoruz.
  // ÖNEMLİ: Cascade delete Odemeler için çalışmayacak çünkü FK tanımı yok.
  // Bu nedenle, bir alış silinmeden önce ilişkili ödemelerin silinmesi veya
  // alış silme işleminin engellenmesi gerekir.
  // Şimdilik basitçe siliyoruz, bu bir sonraki adımda ele alınabilir.
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      db.run("BEGIN TRANSACTION");
      // Önce ilişkili ödemeleri sil (eğer varsa ve silinmesi isteniyorsa)
      db.run("DELETE FROM Odemeler WHERE IslemTipi = 'Alis' AND IslemId = ?", [id], function(err) {
        if (err) {
          db.run("ROLLBACK");
          return reject(err);
        }
        // Sonra Alış kaydını sil
        db.run("DELETE FROM Alislar WHERE Id = ?", [id], function (err) {
          if (err) {
            db.run("ROLLBACK");
            return reject(err);
          }
          db.run("COMMIT");
          resolve({ changes: this.changes });
        });
      });
    });
  });
}


// --- Sales Services ---
async function getAllSales({ startDate, endDate } = {}) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  let sql = `
    SELECT S.*, C.Unvan AS CariUnvan, U.Ad AS UrunAdi
    FROM Satislar S
    LEFT JOIN Cariler C ON S.CariId = C.Id
    LEFT JOIN Urunler U ON S.UrunId = U.Id
  `;
  const params = [];
  const conditions = ["S.ProfilId = ?"];
  params.push(currentProfile.id);

  if (startDate) {
    conditions.push("S.Tarih >= ?");
    params.push(startDate);
  }
  if (endDate) {
    conditions.push("S.Tarih <= ?");
    params.push(endDate);
  }

  if (conditions.length > 0) {
    sql += " WHERE " + conditions.join(" AND ");
  }

  sql += " ORDER BY S.Tarih DESC";

  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

async function addSale(sale) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  const { CariId, UrunId, Tarih, FaturaNo, Miktar, BirimFiyat, OdemeDurumu, VadeTarihi, Notlar } = sale;
  const toplamTutar = parseFloat(Miktar) * parseFloat(BirimFiyat);
  let odenenTutar = 0;
  let kalanTutar = toplamTutar;
  let islemTamamlandi = 0;

  if (OdemeDurumu === 'Pesin') {
    odenenTutar = toplamTutar;
    kalanTutar = 0;
    islemTamamlandi = 1;
  }

  return new Promise((resolve, reject) => {
    db.run(
      "INSERT INTO Satislar (CariId, UrunId, Tarih, FaturaNo, Miktar, BirimFiyat, ToplamTutar, OdenenTutar, KalanTutar, OdemeDurumu, VadeTarihi, Notlar, IslemTamamlandi, ProfilId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
      [CariId, UrunId, Tarih, FaturaNo, Miktar, BirimFiyat, toplamTutar, odenenTutar, kalanTutar, OdemeDurumu, VadeTarihi, Notlar, islemTamamlandi, currentProfile.id],
      function (err) {
        if (err) reject(err);
        else resolve({ id: this.lastID, toplamTutar, odenenTutar, kalanTutar, islemTamamlandi });
      }
    );
  });
}

async function updateSale(id, sale) {
  const { CariId, UrunId, Tarih, FaturaNo, Miktar, BirimFiyat, OdemeDurumu, VadeTarihi, Notlar } = sale;
  const toplamTutar = parseFloat(Miktar) * parseFloat(BirimFiyat);

  return new Promise((resolve, reject) => {
    db.get("SELECT OdenenTutar FROM Satislar WHERE Id = ? AND ProfilId = ?", [id, getCurrentProfile().id], (err, row) => {
      if (err) return reject(err);
      if (!row) return reject(new Error('Satış bulunamadı'));

      let odenenTutar = row.OdenenTutar;
      let kalanTutar = toplamTutar - odenenTutar;
      let islemTamamlandi = kalanTutar <= 0 ? 1 : 0;

      db.run(
        "UPDATE Satislar SET CariId = ?, UrunId = ?, Tarih = ?, FaturaNo = ?, Miktar = ?, BirimFiyat = ?, ToplamTutar = ?, KalanTutar = ?, OdemeDurumu = ?, VadeTarihi = ?, Notlar = ?, IslemTamamlandi = ? WHERE Id = ? AND ProfilId = ?",
        [CariId, UrunId, Tarih, FaturaNo, Miktar, BirimFiyat, toplamTutar, kalanTutar, OdemeDurumu, VadeTarihi, Notlar, islemTamamlandi, id, getCurrentProfile().id],
        function (err) {
          if (err) reject(err);
          else resolve({ changes: this.changes });
        }
      );
    });
  });
}

async function deleteSale(id) {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      db.run("BEGIN TRANSACTION");
      db.run("DELETE FROM Odemeler WHERE IslemTipi = 'Satis' AND IslemId = ?", [id], function(err) {
        if (err) {
          db.run("ROLLBACK");
          return reject(err);
        }
        db.run("DELETE FROM Satislar WHERE Id = ?", [id], function (err) {
          if (err) {
            db.run("ROLLBACK");
            return reject(err);
          }
          db.run("COMMIT");
          resolve({ changes: this.changes });
        });
      });
    });
  });
}

// --- Ödemeler Servisleri ---
// Helper function to update Alis/Satis after payment change
async function updateAlisSatisOdemeDurumu(islemTipi, islemId) {
  const tableName = islemTipi === 'Alis' ? 'Alislar' : 'Satislar';
  return new Promise((resolve, reject) => {
    db.get(`SELECT SUM(Tutar) as ToplamOdenmis FROM Odemeler WHERE IslemTipi = ? AND IslemId = ?`, [islemTipi, islemId], (err, odemeRow) => {
      if (err) return reject(err);
      const toplamOdenmis = odemeRow ? odemeRow.ToplamOdenmis || 0 : 0;

      db.get(`SELECT ToplamTutar FROM ${tableName} WHERE Id = ?`, [islemId], (err, islemRow) => {
        if (err) return reject(err);
        if (!islemRow) return reject(new Error(`${islemTipi} kaydı bulunamadı: ${islemId}`));

        const toplamTutar = islemRow.ToplamTutar;
        const kalanTutar = toplamTutar - toplamOdenmis;
        const islemTamamlandi = kalanTutar <= 0 ? 1 : 0;

        db.run(`UPDATE ${tableName} SET OdenenTutar = ?, KalanTutar = ?, IslemTamamlandi = ? WHERE Id = ?`,
          [toplamOdenmis, kalanTutar, islemTamamlandi, islemId], function(err) {
            if (err) return reject(err);
            resolve({ changes: this.changes });
          }
        );
      });
    });
  });
}

// --- Payments Services ---
async function getAllPayments({ startDate, endDate } = {}) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  // Bu sorgu, IslemTipi'ne göre Alislar veya Satislar tablosundan ilgili fatura no ve cari bilgisini getirebilir.
  // Örnek olarak, tüm ödemeleri ve ilişkili olabilecek temel bilgileri listeleyelim.
  // Daha detaylı birleştirme için uygulama mantığında IslemTipi'ne göre ayrı sorgular gerekebilir.
  let sql = `
    SELECT O.*,
           CASE O.IslemTipi
               WHEN 'Alis' THEN (SELECT C.Unvan FROM Alislar AL JOIN Cariler C ON AL.CariId = C.Id WHERE AL.Id = O.IslemId AND AL.ProfilId = ?)
               WHEN 'Satis' THEN (SELECT C.Unvan FROM Satislar SA JOIN Cariler C ON SA.CariId = C.Id WHERE SA.Id = O.IslemId AND SA.ProfilId = ?)
           END AS CariUnvan,
           CASE O.IslemTipi
               WHEN 'Alis' THEN (SELECT AL.FaturaNo FROM Alislar AL WHERE AL.Id = O.IslemId AND AL.ProfilId = ?)
               WHEN 'Satis' THEN (SELECT SA.FaturaNo FROM Satislar SA WHERE SA.Id = O.IslemId AND SA.ProfilId = ?)
           END AS FaturaNo
    FROM Odemeler O
    WHERE O.ProfilId = ?
  `;
  const params = [currentProfile.id, currentProfile.id, currentProfile.id, currentProfile.id, currentProfile.id];
  const conditions = [];

  if (startDate) {
    conditions.push("O.OdemeTarihi >= ?");
    params.push(startDate);
  }
  if (endDate) {
    conditions.push("O.OdemeTarihi <= ?");
    params.push(endDate);
  }

  if (conditions.length > 0) {
    sql += " AND " + conditions.join(" AND ");
  }

  sql += " ORDER BY O.OdemeTarihi DESC";

  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

async function addPayment(payment) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  const { IslemTipi, IslemId, OdemeTarihi, Tutar, Yontem, Notlar } = payment;
  return new Promise((resolve, reject) => {
    db.run(
      "INSERT INTO Odemeler (IslemTipi, IslemId, OdemeTarihi, Tutar, Yontem, Notlar, ProfilId) VALUES (?, ?, ?, ?, ?, ?, ?)",
      [IslemTipi, IslemId, OdemeTarihi, Tutar, Yontem, Notlar, currentProfile.id],
      async function (err) {
        if (err) return reject(err);
        try {
          await updateAlisSatisOdemeDurumu(IslemTipi, IslemId);
          resolve({ id: this.lastID });
        } catch (updateErr) {
          // Log the update error, but the payment was added.
          // Consider how to handle this: maybe rollback the payment if the update fails critically.
          console.error(`Ödeme eklendi (ID: ${this.lastID}) ancak ${IslemTipi} durumu güncellenirken hata oluştu:`, updateErr);
          resolve({ id: this.lastID, warning: `İlişkili ${IslemTipi} durumu güncellenemedi.` });
        }
      }
    );
  });
}

async function updatePayment(id, payment) {
  const { IslemTipi, IslemId, OdemeTarihi, Tutar, Yontem, Notlar } = payment;
  // IslemTipi ve IslemId'nin güncellenmesi genellikle karmaşıktır.
  // Bir ödemenin ilişkili olduğu faturayı değiştirmek yerine, mevcut ödemeyi silip
  // yeni bir ödeme oluşturmak daha temiz bir yaklaşımdır.
  // Bu yüzden burada IslemTipi ve IslemId'nin değiştirilmediğini varsayıyoruz.
  // Eğer değiştirilirse, eski ve yeni ilişkili faturaların durumları güncellenmeli.

  return new Promise((resolve, reject) => {
    // Önce eski ödeme bilgilerini al (eğer tutar değişiyorsa, eski faturanın durumunu da etkileyebilir)
    db.get("SELECT IslemTipi, IslemId FROM Odemeler WHERE Id = ?", [id], (err, oldOdeme) => {
        if (err) return reject(err);
        if (!oldOdeme) return reject(new Error("Güncellenecek ödeme bulunamadı."));

        db.run(
            "UPDATE Odemeler SET OdemeTarihi = ?, Tutar = ?, Yontem = ?, Notlar = ? WHERE Id = ?",
            // IslemTipi ve IslemId güncellenmiyor.
            [OdemeTarihi, Tutar, Yontem, Notlar, id],
            async function (err) {
                if (err) return reject(err);
                try {
                    // Ödeme güncellendikten sonra ilişkili Alış/Satış durumunu güncelle
                    await updateAlisSatisOdemeDurumu(oldOdeme.IslemTipi, oldOdeme.IslemId);
                    resolve({ changes: this.changes });
                } catch (updateErr) {
                    console.error(`Ödeme güncellendi (ID: ${id}) ancak ${oldOdeme.IslemTipi} durumu güncellenirken hata oluştu:`, updateErr);
                    resolve({ changes: this.changes, warning: `İlişkili ${oldOdeme.IslemTipi} durumu güncellenemedi.` });
                }
            }
        );
    });
  });
}

async function deletePayment(id) {
  return new Promise((resolve, reject) => {
    db.get("SELECT IslemTipi, IslemId FROM Odemeler WHERE Id = ?", [id], (err, odeme) => {
      if (err) return reject(err);
      if (!odeme) return reject(new Error("Silinecek ödeme bulunamadı."));

      db.run("DELETE FROM Odemeler WHERE Id = ?", [id], async function (err) {
        if (err) return reject(err);
        try {
          // Ödeme silindikten sonra ilişkili Alış/Satış durumunu güncelle
          await updateAlisSatisOdemeDurumu(odeme.IslemTipi, odeme.IslemId);
          resolve({ changes: this.changes });
        } catch (updateErr) {
          console.error(`Ödeme silindi (ID: ${id}) ancak ${odeme.IslemTipi} durumu güncellenirken hata oluştu:`, updateErr);
          resolve({ changes: this.changes, warning: `İlişkili ${odeme.IslemTipi} durumu güncellenemedi.` });
        }
      });
    });
  });
}

// Borçlu Cariler Raporu: Bizim carilere olan borçlarımız (Cariler bizden alacaklı)
// Yani: (Toplam Alış Tutarı - Alışlar İçin Yapılan Ödemeler) > 0 olan cariler
async function getDebtorAccounts(startDate, endDate) { // Accounts we owe money to
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  let dateFilter = '';
  const params = [currentProfile.id];

  if (startDate && endDate) {
    dateFilter = ` AND A.Tarih BETWEEN ? AND ?`;
    params.push(startDate, endDate);
  }

  const sql = `
    SELECT
        C.Id,
        C.Unvan,
        IFNULL(SUM(A.KalanTutar), 0) AS NetBorcumuz
    FROM Cariler C
    JOIN Alislar A ON C.Id = A.CariId
    WHERE A.KalanTutar > 0.005 AND C.ProfilId = ? ${dateFilter}
    GROUP BY C.Id, C.Unvan
    HAVING NetBorcumuz > 0
    ORDER BY NetBorcumuz DESC
  `;
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows.map(row => ({ ...row, Bakiye: -row.NetBorcumuz }) )); // Raporlamada negatif gösterim için
    });
  });
}

// Alacaklı Cariler Raporu: Carilerin bize olan borçları (Biz carilerden alacaklıyız)
// Yani: (Toplam Satış Tutarı - Satışlar İçin Alınan Ödemeler) > 0 olan cariler
async function getCreditorAccounts(startDate, endDate) { // Accounts that owe us money
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  const sql = `
    SELECT
        C.Id,
        C.Unvan,
        IFNULL(SUM(S.KalanTutar), 0) AS NetAlacagimiz
    FROM Cariler C
    JOIN Satislar S ON C.Id = S.CariId
    WHERE S.Tarih BETWEEN ? AND ? AND S.KalanTutar > 0.005 AND C.ProfilId = ? -- Küçük küsurat farklarını tolere et
    GROUP BY C.Id, C.Unvan
    HAVING NetAlacagimiz > 0
    ORDER BY NetAlacagimiz DESC
  `;
  return new Promise((resolve, reject) => {
    db.all(sql, [startDate, endDate, currentProfile.id], (err, rows) => {
      if (err) reject(err);
      else resolve(rows.map(row => ({ ...row, Bakiye: row.NetAlacagimiz }) ));
    });
  });
}

// Dönemlik Gelir-Gider Tablosu
async function getIncomeExpenseReport(startDate, endDate) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  let sqlGelir = `SELECT IFNULL(SUM(ToplamTutar), 0) AS ToplamGelir FROM Satislar WHERE ProfilId = ?`;
  let sqlGider = `SELECT IFNULL(SUM(ToplamTutar), 0) AS ToplamGider FROM Alislar WHERE ProfilId = ?`;
  let gelirParams = [currentProfile.id];
  let giderParams = [currentProfile.id];

  if (startDate && endDate) {
    sqlGelir += ` AND Tarih BETWEEN ? AND ?`;
    sqlGider += ` AND Tarih BETWEEN ? AND ?`;
    gelirParams.push(startDate, endDate);
    giderParams.push(startDate, endDate);
  }

  return new Promise((resolve, reject) => {
    db.get(sqlGelir, gelirParams, (err, gelirRow) => {
      if (err) return reject(err);
      db.get(sqlGider, giderParams, (err2, giderRow) => {
        if (err2) return reject(err2);
        const toplamGelir = gelirRow.ToplamGelir || 0;
        const toplamGider = giderRow.ToplamGider || 0;
        resolve({
          ToplamGelir: toplamGelir,
          ToplamGider: toplamGider,
          NetKarZarar: toplamGelir - toplamGider
        });
      });
    });
  });
}

// Cari Hesap Ekstresi / Net Bakiye Raporu
async function getAccountsNetBalanceReport(startDate, endDate) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  let dateFilter = '';
  const params = [];

  if (startDate && endDate) {
    dateFilter = ` AND Tarih BETWEEN ? AND ?`;
    params.push(startDate, endDate);
  }

  const sql = `
    SELECT
        C.Id,
        C.Unvan,
        (SELECT IFNULL(SUM(S.ToplamTutar), 0) FROM Satislar S WHERE S.CariId = C.Id AND S.ProfilId = ? ${dateFilter}) AS DonemSatisTutar,
        (SELECT IFNULL(SUM(A.ToplamTutar), 0) FROM Alislar A WHERE A.CariId = C.Id AND A.ProfilId = ? ${dateFilter}) AS DonemAlisTutar,
        (SELECT IFNULL(SUM(O.Tutar), 0) FROM Odemeler O JOIN Satislar S ON O.IslemId = S.Id AND O.IslemTipi = 'Satis' WHERE S.CariId = C.Id AND O.ProfilId = ? ${dateFilter.replace(/Tarih/g, 'OdemeTarihi')}) AS DonemTahsilat, -- Satışlardan Alınan
        (SELECT IFNULL(SUM(O.Tutar), 0) FROM Odemeler O JOIN Alislar A ON O.IslemId = A.Id AND O.IslemTipi = 'Alis' WHERE A.CariId = C.Id AND O.ProfilId = ? ${dateFilter.replace(/Tarih/g, 'OdemeTarihi')}) AS DonemOdeme, -- Alışlara Ödenen
        -- Genel Kalan Tutar (Tüm zamanlar)
        (SELECT IFNULL(SUM(S2.KalanTutar),0) FROM Satislar S2 WHERE S2.CariId = C.Id AND S2.ProfilId = ?) AS ToplamKalanSatisAlacagi, -- Carinin bize toplam borcu
        (SELECT IFNULL(SUM(A2.KalanTutar),0) FROM Alislar A2 WHERE A2.CariId = C.Id AND A2.ProfilId = ?) AS ToplamKalanAlisBorcu -- Bizim cariye toplam borcumuz
    FROM Cariler C
    WHERE C.ProfilId = ?
    ORDER BY C.Unvan
  `;

  // Combine parameters for all subqueries
  const allParams = [
    currentProfile.id, ...params, // For DonemSatisTutar
    currentProfile.id, ...params, // For DonemAlisTutar
    currentProfile.id, ...params, // For DonemTahsilat
    currentProfile.id, ...params, // For DonemOdeme
    currentProfile.id, // For ToplamKalanSatisAlacagi
    currentProfile.id, // For ToplamKalanAlisBorcu
    currentProfile.id  // For main WHERE clause
  ];

  return new Promise((resolve, reject) => {
    db.all(sql, allParams, (err, rows) => {
      if (err) return reject(err);

      const result = rows.map(row => {
        // Net Bakiye: Pozitif ise biz alacaklıyız, negatif ise biz borçluyuz.
        // (Carinin bize borcu) - (Bizim cariye borcumuz)
        const netBakiye = row.ToplamKalanSatisAlacagi - row.ToplamKalanAlisBorcu;
        return {
          Id: row.Id,
          Unvan: row.Unvan,
          ToplamAlis: row.DonemAlisTutar, // Sadece seçilen dönemdeki alışları
          Odenen: row.DonemOdeme,         // Sadece seçilen dönemdeki ödemeleri (alışlara yapılan)
          ToplamSatis: row.DonemSatisTutar, // Sadece seçilen dönemdeki satışları
          Alinan: row.DonemTahsilat,      // Sadece seçilen dönemdeki tahsilatları (satışlardan alınan)
          NetBakiye: netBakiye            // GENEL NET BAKİYE (tüm zamanlar)
        };
      });
      resolve(result);
    });
  });
}

// Aylık Net Kar/Zarar Trendi
async function getMonthlyNetProfitLossTrend(monthCount = 12) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  return new Promise((resolve, reject) => {
    const aylar = [];
    const bugun = new Date();

    for (let i = monthCount - 1; i >= 0; i--) {
      const tarih = new Date(bugun.getFullYear(), bugun.getMonth() - i, 1);
      const yil = tarih.getFullYear();
      const ay = (tarih.getMonth() + 1).toString().padStart(2, '0');
      aylar.push(`${yil}-${ay}`);
    }

    const promises = aylar.map(ayStr => {
      const sqlGelir = `SELECT IFNULL(SUM(ToplamTutar), 0) AS ToplamGelir FROM Satislar WHERE strftime('%Y-%m', Tarih) = ? AND ProfilId = ?`;
      const sqlGider = `SELECT IFNULL(SUM(ToplamTutar), 0) AS ToplamGider FROM Alislar WHERE strftime('%Y-%m', Tarih) = ? AND ProfilId = ?`;

      return new Promise((res, rej) => {
        db.get(sqlGelir, [ayStr, currentProfile.id], (err, gelirRow) => {
          if (err) return rej(err);
          db.get(sqlGider, [ayStr, currentProfile.id], (err2, giderRow) => {
            if (err2) return rej(err2);
            const toplamGelir = gelirRow.ToplamGelir || 0;
            const toplamGider = giderRow.ToplamGider || 0;
            res({
              ay: ayStr,
              gelir: toplamGelir,
              gider: toplamGider,
              net: toplamGelir - toplamGider
            });
          });
        });
      });
    });

    Promise.all(promises)
      .then(results => resolve(results))
      .catch(err => reject(err));
  });
}


// Belirli bir cari için detaylı hesap ekstresi
async function getAccountStatement(accountId, startDate, endDate) {
  return new Promise(async (resolve, reject) => {
    try {
      // Cari bilgilerini al
      const account = await getAccountById(accountId);
      if (!account) {
        return reject(new Error('Account not found'));
      }

      let dateFilter = '';
      const params = [accountId];

      if (startDate && endDate) {
        dateFilter = ` AND Tarih BETWEEN ? AND ?`;
        params.push(startDate, endDate);
      }

      // Tüm işlemleri birleştir (Alışlar, Satışlar, Ödemeler)
      const transactionsQuery = `
        SELECT
          'Alış' as IslemTipi,
          A.Id as IslemId,
          A.Tarih,
          A.FaturaNo,
          U.Ad as UrunAdi,
          A.Miktar,
          A.BirimFiyat,
          A.ToplamTutar,
          A.OdenenTutar,
          A.KalanTutar,
          A.ToplamTutar as Borc, -- Alış = Bizim borcumuz
          0 as Alacak,
          A.Notlar as Aciklama
        FROM Alislar A
        LEFT JOIN Urunler U ON A.UrunId = U.Id
        WHERE A.CariId = ? ${dateFilter}

        UNION ALL

        SELECT
          'Satış' as IslemTipi,
          S.Id as IslemId,
          S.Tarih,
          S.FaturaNo,
          U.Ad as UrunAdi,
          S.Miktar,
          S.BirimFiyat,
          S.ToplamTutar,
          S.OdenenTutar,
          S.KalanTutar,
          0 as Borc,
          S.ToplamTutar as Alacak, -- Satış = Bizim alacağımız
          S.Notlar as Aciklama
        FROM Satislar S
        LEFT JOIN Urunler U ON S.UrunId = U.Id
        WHERE S.CariId = ? ${dateFilter}

        UNION ALL

        SELECT
          'Ödeme-' || O.IslemTipi as IslemTipi,
          O.Id as IslemId,
          O.OdemeTarihi as Tarih,
          CASE
            WHEN O.IslemTipi = 'Alis' THEN (SELECT A.FaturaNo FROM Alislar A WHERE A.Id = O.IslemId)
            WHEN O.IslemTipi = 'Satis' THEN (SELECT S.FaturaNo FROM Satislar S WHERE S.Id = O.IslemId)
          END as FaturaNo,
          'Ödeme' as UrunAdi,
          1 as Miktar,
          O.Tutar as BirimFiyat,
          O.Tutar as ToplamTutar,
          O.Tutar as OdenenTutar,
          0 as KalanTutar,
          CASE WHEN O.IslemTipi = 'Satis' THEN O.Tutar ELSE 0 END as Borc, -- Satış ödemesi = Bizim borcumuz azalır
          CASE WHEN O.IslemTipi = 'Alis' THEN O.Tutar ELSE 0 END as Alacak, -- Alış ödemesi = Bizim alacağımız azalır
          O.Notlar as Aciklama
        FROM Odemeler O
        WHERE (
          (O.IslemTipi = 'Alis' AND EXISTS (SELECT 1 FROM Alislar A WHERE A.Id = O.IslemId AND A.CariId = ?)) OR
          (O.IslemTipi = 'Satis' AND EXISTS (SELECT 1 FROM Satislar S WHERE S.Id = O.IslemId AND S.CariId = ?))
        ) ${dateFilter.replace(/Tarih/g, 'OdemeTarihi')}

        ORDER BY Tarih ASC, IslemTipi ASC
      `;

      // Parametreleri her UNION için ekle
      // İlk iki UNION için params (accountId, startDate?, endDate?)
      // Üçüncü UNION için accountId iki kez (Alis ve Satis için) + tarih parametreleri
      const paymentParams = [accountId, accountId];
      if (startDate && endDate) {
        paymentParams.push(startDate, endDate);
      }
      const allParams = [...params, ...params, ...paymentParams];

      db.all(transactionsQuery, allParams, async (err, transactions) => {
        if (err) return reject(err);

        // Bakiye hesaplama
        let runningBalance = 0;
        const processedTransactions = transactions.map(transaction => {
          // Bakiye hesaplama: Alacak (+), Borç (-)
          runningBalance += (transaction.Alacak - transaction.Borc);

          return {
            ...transaction,
            Bakiye: runningBalance,
            FormattedDate: formatDate(transaction.Tarih),
            FormattedAmount: formatCurrency(transaction.ToplamTutar),
            FormattedBalance: formatCurrency(runningBalance)
          };
        });

        // Özet bilgiler
        const summary = {
          totalDebit: transactions.reduce((sum, t) => sum + t.Borc, 0),
          totalCredit: transactions.reduce((sum, t) => sum + t.Alacak, 0),
          finalBalance: runningBalance,
          transactionCount: transactions.length,
          dateRange: { startDate, endDate }
        };

        resolve({
          account,
          transactions: processedTransactions,
          summary
        });
      });

    } catch (error) {
      reject(error);
    }
  });
}

// Detaylı Finansal Rapor (Header kartlarıyla uyumlu)
async function getDetailedFinancialReport(startDate, endDate) {
  const currentProfile = getCurrentProfile();
  if (!currentProfile) {
    throw new Error('Aktif profil bulunamadı');
  }

  try {
    // Tüm alış, satış ve ödeme verilerini al
    const [allPurchases, allSales, allPayments] = await Promise.all([
      getAllPurchases(),
      getAllSales(),
      getAllPayments()
    ]);

    // Tarih filtresi uygula - hem işlemler hem ödemeler için
    let filteredPurchases = allPurchases;
    let filteredSales = allSales;
    let filteredPayments = allPayments;

    if (startDate && endDate) {
      filteredPurchases = allPurchases.filter(p => p.Tarih >= startDate && p.Tarih <= endDate);
      filteredSales = allSales.filter(s => s.Tarih >= startDate && s.Tarih <= endDate);
      filteredPayments = allPayments.filter(p => p.OdemeTarihi >= startDate && p.OdemeTarihi <= endDate);
    }

    // Dönem içindeki alış ve satışların toplam tutarları
    const totalPurchases = filteredPurchases.reduce((sum, p) => sum + (p.ToplamTutar || (p.Miktar * p.BirimFiyat)), 0);
    const totalSales = filteredSales.reduce((sum, s) => sum + (s.ToplamTutar || (s.Miktar * s.BirimFiyat)), 0);

    // Dönem içindeki ödemeleri hesapla
    const purchasePayments = filteredPayments
      .filter(p => p.IslemTipi === 'Alis')
      .reduce((sum, p) => sum + p.Tutar, 0);

    const salesPayments = filteredPayments
      .filter(p => p.IslemTipi === 'Satis')
      .reduce((sum, p) => sum + p.Tutar, 0);

    // Dönem içindeki alış/satışların peşin ödemelerini hesapla (OdenenTutar - bu işlemin kendi ödemeleri)
    // Not: OdenenTutar aslında toplam ödenen tutardır, bunu dönem bazında ayırmak zor
    // Basitleştirme: Peşin işlemler için OdenenTutar = ToplamTutar olduğunu varsayalım
    const totalPaidFromPurchases = filteredPurchases
      .filter(p => p.OdemeDurumu === 'Pesin')
      .reduce((sum, p) => sum + (p.ToplamTutar || 0), 0);

    const totalCollectedFromSales = filteredSales
      .filter(s => s.OdemeDurumu === 'Pesin')
      .reduce((sum, s) => sum + (s.ToplamTutar || 0), 0);

    // Toplam ödenen ve tahsil edilen para (peşin + vadeli ödemeler)
    const totalPaidMoney = totalPaidFromPurchases + purchasePayments;
    const totalCollectedMoney = totalCollectedFromSales + salesPayments;

    // Ödenmemiş borç ve tahsil edilmemiş alacak
    const unpaidDebt = totalPurchases - totalPaidMoney;
    const uncollectedReceivable = totalSales - totalCollectedMoney;
    const cashNetBalance = totalCollectedMoney - totalPaidMoney;

    // 6. veri: Net Finansal Durum
    // (Yapılmamış tahsilatlar + Yapılmış tahsilatlar) - (Ödenmemiş borçlar + Ödenmiş borçlar)
    // = Toplam Alacak - Toplam Borç
    const netFinancialPosition = (uncollectedReceivable + totalCollectedMoney) - (unpaidDebt + totalPaidMoney);

    return {
      unpaidDebt: Math.max(0, unpaidDebt),
      uncollectedReceivable: Math.max(0, uncollectedReceivable),
      paidMoney: totalPaidMoney,
      collectedMoney: totalCollectedMoney,
      cashNetBalance: cashNetBalance,
      netFinancialPosition: netFinancialPosition
    };
  } catch (error) {
    console.error('Error in getDetailedFinancialReport:', error);
    throw error;
  }
}

// Yardımcı fonksiyonlar
function formatDate(dateString) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('tr-TR');
}

function formatCurrency(amount) {
  if (amount === null || amount === undefined) return '₺0.00';
  return `₺${parseFloat(amount).toFixed(2)}`;
}

module.exports = {
  getAllProducts,
  getProductById,
  addProduct,
  updateProduct,
  deleteProduct,
  getAllAccounts,
  getAccountById,
  addAccount,
  updateAccount,
  deleteAccount,
  getAllPurchases,
  addPurchase,
  updatePurchase,
  deletePurchase,
  getAllSales,
  addSale,
  updateSale,
  deleteSale,
  getAllPayments,
  addPayment,
  updatePayment,
  deletePayment,
  getDebtorAccounts,
  getCreditorAccounts,
  getIncomeExpenseReport,
  getAccountsNetBalanceReport,
  getMonthlyNetProfitLossTrend,
  getAccountsNetBalanceDashboard,
  getAccountDetails,
  getAccountStatement,
  getDetailedFinancialReport,
};

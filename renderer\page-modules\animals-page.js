import { IconSystem } from '../utils/icon-system.js';
import { BadgeSystem } from '../utils/badge-system.js';
import { TableAnimations } from '../utils/table-animations.js';

// Importer for i18n, calculateAge, defaultAvatarSVG will be adjusted once they are in utils.
// For now, this module will expect them to be passed or available globally/via imports.

// Forward declaration for openAnimalModal which is expected to be passed or imported
// let openAnimalModal;

// Forward declaration for shared UI elements usually available in the main app scope
// let contentArea;

// Global variables for this module
let passedI18n, passedCalculateAge, passedDefaultAvatarSVG, passedOpenAnimalModal;

export async function renderAnimalsPage(passedContentArea, i18n, calculateAge, defaultAvatarSVG, openAnimalModal) {
  // Assign to global variables for use in other functions
  passedI18n = i18n;
  passedCalculateAge = calculateAge;
  passedDefaultAvatarSVG = defaultAvatarSVG;
  passedOpenAnimalModal = openAnimalModal;

  const t = passedI18n.t;
  // contentArea is where the page HTML will be injected.
  passedContentArea.innerHTML = `
    <!-- Animals Page Header -->
    <div class="animals-header-section">
      <div class="animals-header">
        <h2 class="animals-page-title">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M16 8.36364C16 7.26089 15.5469 6.20343 14.7464 5.40294C13.9459 4.60246 12.8885 4.14929 11.7857 4.14929C10.6829 4.14929 9.62548 4.60246 8.82499 5.40294C8.0245 6.20343 7.57143 7.26089 7.57143 8.36364V9.27273H16V8.36364Z"/>
            <path d="M7.57143 9.27273H16V16.3636C16 17.4664 15.5469 18.5238 14.7464 19.3243C13.9459 20.1248 12.8885 20.578 11.7857 20.578C10.6829 20.578 9.62548 20.1248 8.82499 19.3243C8.0245 18.5238 7.57143 17.4664 7.57143 16.3636V9.27273Z"/>
          </svg>
          ${t('animals_management') || 'Hayvan Yönetimi'}
        </h2>
        <div class="animals-quick-actions">
          <button class="btn btn-primary" id="header-add-animal-btn">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="16"/>
              <line x1="8" y1="12" x2="16" y2="12"/>
            </svg>
            ${t('btn_add_animal')}
          </button>
        </div>
      </div>
    </div>

    <div id="animal-table-controls" style="display: none;">
      <button class="btn btn-primary"> ${t('btn_add_animal')}</button>
    </div>
    <table id="animals-table" class="modern-table">
      <thead>
        <tr>
          <th class="col-avatar"></th>
          <th>${t('label_kupe_no')}</th>
          <th>${t('label_isim')}</th>
          <th>${t('label_irk')}</th>
          <th class="col-gender">${t('label_cinsiyet')}</th>
          <th class="col-age">${t('label_age')}</th>
          <th>${t('label_gebelik_durumu')}</th>
          <th>${t('label_durum')}</th>
          <th>${t('label_daily_milk_avg') || 'Günlük Ortalama Süt Verimi'}</th>
          <th class="col-check">${t('label_aktif_mi')}</th>
          <th class="col-actions">${t('label_actions')}</th>
        </tr>
        <tr class="filter-row">
          <th></th>
          <th><input type="text" id="filter-kupe" class="filter-input" placeholder="${t('label_kupe_no')}" /></th>
          <th><input type="text" id="filter-isim" class="filter-input" placeholder="${t('label_isim')}" /></th>
          <th><input type="text" id="filter-irk" class="filter-input" placeholder="${t('label_irk')}" /></th>
          <th>
            <select id="filter-cinsiyet" class="filter-input">
              <option value="">${t('option_all')}</option>
              <option value="Dişi">${t('option_dişi') || 'Dişi'}</option>
              <option value="Erkek">${t('option_erkek') || 'Erkek'}</option>
            </select>
          </th>
          <th><input type="number" id="filter-yas" class="filter-input" min="0" placeholder="${t('label_age')}" /></th>
          <th>
            <select id="filter-gebelik" class="filter-input">
              <option value="">${t('option_all')}</option>
              <option value="Gebe">${t('pregnancy_status_pregnant') || 'Gebe'}</option>
              <option value="Boş">${t('pregnancy_status_empty') || 'Boş'}</option>
            </select>
          </th>
          <th>
            <select id="filter-durum" class="filter-input">
              <option value="">${t('option_all')}</option>
              <option value="Sağmal">${t('lactation_status_milking') || 'Sağmal'}</option>
              <option value="Kuru">${t('lactation_status_dry') || 'Kuru'}</option>
            </select>
          </th>
          <th><input type="number" id="filter-sut" class="filter-input" min="0" step="0.1" placeholder="${t('label_daily_milk_avg')}" /></th>
          <th>
            <select id="filter-aktif" class="filter-input filter-aktif-narrow">
              <option value="">${t('option_all')}</option>
              <option value="1">${t('status_active') || 'Aktif'}</option>
              <option value="0">${t('status_inactive') || 'Pasif'}</option>
            </select>
          </th>
          <th></th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>
    <div id="animals-pagination-controls" class="pagination-controls">
      <button id="prev-page" disabled>&laquo; ${t('btn_previous') || 'Previous'}</button>
      <span id="page-info">Page 1 of 1</span>
      <button id="next-page" disabled>&raquo; ${t('btn_next') || 'Next'}</button>
    </div>
  `;
  const tableBody = passedContentArea.querySelector('#animals-table tbody');
  let allAnimals = await window.api.invoke('hayvanlar:list');
  let currentAnimals = [...allAnimals];

  const itemsPerPage = 10;
  let currentPage = 1;
  let sortColumn = null;
  let sortDirection = 1;

  const prevButton = passedContentArea.querySelector('#prev-page');
  const nextButton = passedContentArea.querySelector('#next-page');
  const pageInfo = passedContentArea.querySelector('#page-info');

  // Moved openAnimalDetailModal inside animals-page.js context
  async function openAnimalDetailModal(animalSummary) {
    if (!animalSummary || typeof animalSummary !== 'object' || !animalSummary.Id) return;
    const modal = document.getElementById('animal-detail-modal');
    const closeBtn = modal.querySelector('.detail-close-btn');

    try {
      modal.classList.remove('hidden');
      const details = await window.api.invoke('hayvanlar:get-details', animalSummary.Id);
      if (!details || !details.animal) {
        console.error("Could not load animal details.");
        document.getElementById('animal-detail-name').textContent = t('error_loading_details') || "Error loading details.";
        return;
      }

      const animal = details.animal;
      const avatarImg = document.getElementById('animal-detail-avatar');
      const avatarPlaceholder = document.getElementById('animal-detail-avatar-placeholder');
      if (animal.FotografUrl) {
        avatarImg.src = animal.FotografUrl;
        avatarImg.style.display = '';
        avatarPlaceholder.style.display = 'none';
      } else {
        avatarImg.style.display = 'none';
        avatarPlaceholder.style.display = 'flex';
        avatarPlaceholder.innerHTML = passedDefaultAvatarSVG(40); // Adjusted size
      }
      // Başlıkta küpe no, badge'de isim göster
      document.getElementById('animal-detail-name').textContent = animal.KupeNo || t('unknown_ear_tag') || 'Bilinmeyen Küpe No';

      // Update tags with appropriate CSS classes - isim artık badge içinde
      const kupeTag = document.getElementById('animal-detail-kupe-tag');
      kupeTag.textContent = `${t('label_isim') || 'İsim'}: ${animal.Isim || t('unknown_animal_name') || 'Bilinmeyen Hayvan'}`;
      kupeTag.className = 'detail-tag tag-name';

      const genderTag = document.getElementById('animal-detail-gender-tag');
      genderTag.textContent = animal.Cinsiyet ? t(`option_${animal.Cinsiyet.toLocaleLowerCase('tr-TR')}`) : '-';
      genderTag.className = 'detail-tag tag-gender';

      const ageTag = document.getElementById('animal-detail-age');
      ageTag.textContent = passedCalculateAge(animal.DogumTarihi);
      ageTag.className = 'detail-tag tag-age';

      const pregBadge = document.getElementById('animal-detail-pregnancy');
      if (animal.GebelikDurumu) {
        pregBadge.innerHTML = BadgeSystem.createAnimalStatusBadge(animal.GebelikDurumu, {
          pregnant: t('pregnancy_status_pregnant'),
          empty: t('pregnancy_status_empty')
        });
        pregBadge.style.display = '';
      } else {
        pregBadge.style.display = 'none';
      }

      const statusBadge = document.getElementById('animal-detail-status');
      if (animal.Durum) {
        statusBadge.innerHTML = BadgeSystem.createAnimalStatusBadge(animal.Durum, {
          milking: t('lactation_status_milking'),
          dry: t('lactation_status_dry')
        });
        statusBadge.style.display = '';
      } else {
        statusBadge.style.display = 'none';
      }

      // Genel Bilgiler Tabı
      document.getElementById('animal-detail-tur').textContent = animal.Tur || '-';
      document.getElementById('animal-detail-irk').textContent = animal.Irk || '-';
      document.getElementById('animal-detail-dogum').textContent = animal.DogumTarihi ? new Date(animal.DogumTarihi).toLocaleDateString() : '-';
      document.getElementById('animal-detail-dogum-agirligi').textContent = animal.DogumAgirligi || '-';
      document.getElementById('animal-detail-dogum-tipi').textContent = animal.DogumTipi || '-';
      document.getElementById('animal-detail-anne-kupe').textContent = animal.AnneKupe || '-';
      document.getElementById('animal-detail-baba-kupe').textContent = animal.BabaKupe || '-';
      document.getElementById('animal-detail-boynuz').textContent = animal.BoynuzDurumu || '-';
      document.getElementById('animal-detail-giris').textContent = animal.IsletmeyeGirisTarihi ? new Date(animal.IsletmeyeGirisTarihi).toLocaleDateString() : '-';
      document.getElementById('animal-detail-cikis').textContent = animal.IsletmedenCikisTarihi ? new Date(animal.IsletmedenCikisTarihi).toLocaleDateString() : '-';
      document.getElementById('animal-detail-cikis-sebebi').textContent = animal.CikisSebebi || '-';

      // Sağlık & Üreme Tabı
      const { lastVaccine, lastTreatment, lastInsemination, lastPregnancy } = details;
      document.getElementById('animal-detail-last-vaccine').textContent = lastVaccine ? `${lastVaccine.AsiAdi} (${new Date(lastVaccine.AsilamaTarihi).toLocaleDateString()})` : '-';
      document.getElementById('animal-detail-last-treatment').textContent = lastTreatment ? `${lastTreatment.Teshis} (${new Date(lastTreatment.TedaviBaslangicTarihi).toLocaleDateString()})` : '-';
      document.getElementById('animal-detail-last-insemination').textContent = lastInsemination ? `${new Date(lastInsemination.TohumlamaTarihi).toLocaleDateString()} (${lastInsemination.TohumlamaTipi || ''})` : '-';
      document.getElementById('animal-detail-last-pregnancy').textContent = lastPregnancy ? `${new Date(lastPregnancy.BaslangicTarihi).toLocaleDateString()} (${lastPregnancy.GebelikSonucu || t('pregnancy_result_ongoing')})` : '-';

      let dailyAvgMilk = '-';
      if (typeof animal.GunlukOrtalamaSut === 'number') {
          dailyAvgMilk = animal.GunlukOrtalamaSut.toFixed(1) + ' L';
      }
      document.getElementById('animal-detail-daily-milk').textContent = dailyAvgMilk;

      // Notlar Tabı
      document.getElementById('animal-detail-notlar').textContent = animal.Notlar || t('no_notes_available') || 'Not bulunmuyor.';

      // Apply translations to modal
      applyAnimalModalTranslations(modal, t);

      // Tab functionality
      const tabLinks = modal.querySelectorAll('.detail-tab-link');
      const tabContents = modal.querySelectorAll('.detail-tab-content');

      function switchTab(activeTabLink) {
        tabLinks.forEach(link => link.classList.remove('active'));
        activeTabLink.classList.add('active');
        const targetTabId = activeTabLink.dataset.tab;
        tabContents.forEach(content => {
          content.classList.remove('active');
          if (content.id === targetTabId) {
            content.classList.add('active');
          }
        });
      }

      tabLinks.forEach(link => {
        link.onclick = () => switchTab(link);
      });
      // Activate the first tab by default
      if(tabLinks.length > 0) switchTab(tabLinks[0]);


    } catch (error) {
      console.error('Failed to open animal detail modal:', error);
      document.getElementById('animal-detail-name').textContent = t('error_loading_details') || "Error loading details.";
    }

    function closeModal() {
      modal.classList.add('hidden');
    }
    closeBtn.onclick = closeModal;
    modal.onclick = (e) => { if (e.target === modal) closeModal(); };
  }

  function applyAnimalModalTranslations(modal, t) {
    // Apply translations to all elements with data-i18n attribute
    modal.querySelectorAll('[data-i18n]').forEach(el => {
      const key = el.getAttribute('data-i18n');
      const translation = t(key);
      if (translation !== key) {
        el.textContent = translation;
      }
    });
  }


  function applyFiltersAndSort() {
    let filtered = [...allAnimals];
    // Yeni filtre inputlarını al
    const kupe = passedContentArea.querySelector('#filter-kupe').value.trim().toLowerCase();
    const isim = passedContentArea.querySelector('#filter-isim').value.trim().toLowerCase();
    const irk = passedContentArea.querySelector('#filter-irk').value.trim().toLowerCase();
    const cinsiyet = passedContentArea.querySelector('#filter-cinsiyet').value;
    const yas = passedContentArea.querySelector('#filter-yas').value;
    const gebelik = passedContentArea.querySelector('#filter-gebelik').value;
    const durum = passedContentArea.querySelector('#filter-durum').value;
    const sut = passedContentArea.querySelector('#filter-sut').value;
    const aktif = passedContentArea.querySelector('#filter-aktif').value;

    if (kupe) filtered = filtered.filter(a => (a.KupeNo || '').toLowerCase().includes(kupe));
    if (isim) filtered = filtered.filter(a => (a.Isim || '').toLowerCase().includes(isim));
    if (irk) filtered = filtered.filter(a => (a.Irk || '').toLowerCase().includes(irk));
    if (cinsiyet) filtered = filtered.filter(a => (a.Cinsiyet || '') === cinsiyet);
    if (yas) filtered = filtered.filter(a => {
      const age = passedCalculateAge(a.DogumTarihi, true); // true: sadece yıl döndürsün
      return age == yas;
    });
    if (gebelik) filtered = filtered.filter(a => (a.GebelikDurumu || '') === (gebelik === t('pregnancy_status_pregnant') ? 'Gebe' : 'Boş'));
    if (durum) filtered = filtered.filter(a => (a.Durum || '') === (durum === t('lactation_status_milking') ? 'Sağmal' : 'Kuru'));
    if (sut) filtered = filtered.filter(a => typeof a.GunlukOrtalamaSut === 'number' && a.GunlukOrtalamaSut >= parseFloat(sut));
    if (aktif) filtered = filtered.filter(a => String(a.AktifMi ? 1 : 0) === aktif);
    if (sortColumn) {
      filtered = sortList(filtered, sortColumn, sortDirection);
    }
    currentAnimals = filtered;
    currentPage = 1;
    renderTablePage();
    updatePaginationControls();
  }



  function sortList(list, column, direction) {
    return [...list].sort((a, b) => {
      let valA = a[column];
      let valB = b[column];
      if (column === 'DogumTarihi') {
        valA = new Date(valA);
        valB = new Date(valB);
      } else if (typeof valA === 'string' && valA.match(/^\d{4}-\d{2}-\d{2}$/)) {
        valA = new Date(valA);
        valB = new Date(valB);
      } else if (typeof valA === 'number' && typeof valB === 'number') {
        // Pure numeric
      } else if (typeof valA === 'boolean' && typeof valB === 'boolean') {
        // Boolean
      } else {
        valA = (valA || '').toString().toLowerCase();
        valB = (valB || '').toString().toLowerCase();
      }
      if (valA < valB) return -1 * direction;
      if (valA > valB) return 1 * direction;
      return 0;
    });
  }

  function renderTablePage() {
    const t = passedI18n.t;
    tableBody.innerHTML = '';
    const totalPages = Math.ceil(currentAnimals.length / itemsPerPage);
    currentPage = Math.max(1, Math.min(currentPage, totalPages || 1));

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageItems = currentAnimals.slice(startIndex, endIndex);

    if (pageItems.length === 0) {
      tableBody.innerHTML = `<tr><td colspan="11"><div class="empty-state-container"><div class="empty-state-icon">${passedDefaultAvatarSVG(48)}</div><p>${t('no_records_found')}</p></div></td></tr>`;
      return;
    }

    pageItems.forEach(animal => {
      const row = tableBody.insertRow();
      row.classList.add('clickable-row');
      row.innerHTML = `
        <td class="col-avatar">
          ${animal.FotografUrl ? `<img class=\"animal-avatar\" src=\"${animal.FotografUrl}\" alt=\"\" onerror=\"this.style.display='none'\">` : `<div class=\"animal-avatar-placeholder\">${passedDefaultAvatarSVG()}</div>`}
        </td>
        <td title="${animal.KupeNo || ''}">${animal.KupeNo || ''}</td>
        <td title="${animal.Isim || ''}">${animal.Isim || ''}</td>
        <td title="${animal.Irk || ''}">${animal.Irk || ''}</td>
        <td class="col-gender">
          ${animal.Cinsiyet ? t(`option_${animal.Cinsiyet.toLocaleLowerCase('tr-TR')}`) : ''}
        </td>
        <td class="col-age">${passedCalculateAge(animal.DogumTarihi)}</td>
        <td title="${animal.GebelikDurumu ? t(animal.GebelikDurumu === 'Gebe' ? 'pregnancy_status_pregnant' : 'pregnancy_status_empty') : ''}">
          ${animal.GebelikDurumu ? BadgeSystem.createAnimalStatusBadge(animal.GebelikDurumu, { pregnant: t('pregnancy_status_pregnant'), empty: t('pregnancy_status_empty') }) : ''}
        </td>
        <td title="${animal.Durum ? t(animal.Durum === 'Sağmal' ? 'lactation_status_milking' : 'lactation_status_dry') : ''}">
          ${animal.Durum ? BadgeSystem.createAnimalStatusBadge(animal.Durum, { milking: t('lactation_status_milking'), dry: t('lactation_status_dry') }) : ''}
        </td>
        <td>
          ${typeof animal.GunlukOrtalamaSut === 'number' ? BadgeSystem.createMilkYieldBadge(animal.GunlukOrtalamaSut, 30, { dailyMilk: t('daily_milk') || 'Daily Milk' }) : '-'}
        </td>
        <td class="col-check">${BadgeSystem.createActiveStatusBadge(animal.AktifMi, { active: t('status_active') || 'Active', inactive: t('status_inactive') || 'Inactive' })}</td>
        <td class="col-actions">
          ${IconSystem.createActionsWrapper(animal.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
        </td>
      `;
      row.addEventListener('click', async (e) => {
        if (e.target.closest('.edit-btn') || e.target.closest('.delete-btn')) return;
        await openAnimalDetailModal(animal); // Uses the modal function now local to this module
      });
    });

    tableBody.querySelectorAll('.edit-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const animalToEdit = allAnimals.find(a => a.Id == btn.getAttribute('data-id'));
        passedOpenAnimalModal(animalToEdit); // Uses the passed openAnimalModal for add/edit
      });
    });
    tableBody.querySelectorAll('.delete-btn').forEach(btn => {
      btn.addEventListener('click', async () => {
        const confirmed = await window.toast.confirm(t('confirm_delete_animal'), {
          confirmText: t('toast_confirm'),
          cancelText: t('toast_cancel')
        });

        if (confirmed) {
          try {
            await window.api.invoke('hayvanlar:delete', parseInt(btn.getAttribute('data-id')));
            allAnimals = await window.api.invoke('hayvanlar:list');
            applyFiltersAndSort();
            window.toast.success(t('toast_success_delete'));
          } catch (error) {
            console.error('Error deleting animal:', error);
            window.toast.error(t('toast_error_delete') + ': ' + error.message);
          }
        }
      });
    });
  }

  function updatePaginationControls() {
    const totalItems = currentAnimals.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    pageInfo.textContent = t('page_info_text', { currentPage, totalPages, totalItems }) || `Page ${currentPage} of ${totalPages} (${totalItems} items)`;
    prevButton.disabled = currentPage === 1;
    nextButton.disabled = currentPage === totalPages || totalPages === 0;
  }

  prevButton.addEventListener('click', () => {
    if (currentPage > 1) {
      currentPage--;
      renderTablePage();
      updatePaginationControls();
    }
  });

  nextButton.addEventListener('click', () => {
    const totalPages = Math.ceil(currentAnimals.length / itemsPerPage);
    if (currentPage < totalPages) {
      currentPage++;
      renderTablePage();
      updatePaginationControls();
    }
  });

  // Filtre inputlarına event listener ekle:
  [
    '#filter-kupe', '#filter-isim', '#filter-irk', '#filter-cinsiyet', '#filter-yas', '#filter-gebelik', '#filter-durum', '#filter-sut', '#filter-aktif'
  ].forEach(sel => {
    passedContentArea.querySelector(sel).addEventListener('input', applyFiltersAndSort);
    passedContentArea.querySelector(sel).addEventListener('change', applyFiltersAndSort);
  });

  const ths = passedContentArea.querySelectorAll('#animals-table thead th');
  const columnKeys = [
    null, 'KupeNo', 'Isim', 'Irk', 'Cinsiyet', 'DogumTarihi', 'GebelikDurumu', 'Durum', 'GunlukOrtalamaSut', 'AktifMi', null
  ];

  ths.forEach((th, idx) => {
    const sortableColumnKey = columnKeys[idx];
    if (!sortableColumnKey) return;

    th.classList.add('sortable');
    th.addEventListener('click', () => {
      if (sortColumn === sortableColumnKey) {
        sortDirection *= -1;
      } else {
        sortColumn = sortableColumnKey;
        sortDirection = 1;
      }
      ths.forEach(headerTh => {
        headerTh.classList.remove('sorted-asc', 'sorted-desc');
        headerTh.querySelector('.sort-arrow')?.remove();
      });
      th.classList.add(sortDirection === 1 ? 'sorted-asc' : 'sorted-desc');
      applyFiltersAndSort();
    });
  });

  applyFiltersAndSort();

  // Initialize table animations
  setTimeout(() => {
    TableAnimations.initializeTable('#animals-table');
  }, 100);

  // Header button event listeners
  const headerAddAnimalBtn = passedContentArea.querySelector('#header-add-animal-btn');

  if (headerAddAnimalBtn) {
    headerAddAnimalBtn.addEventListener('click', () => passedOpenAnimalModal());
  }

  // Keep the old add animal button for backward compatibility
  const addBtn = passedContentArea.querySelector('.btn.btn-primary');
  if (addBtn) {
    addBtn.addEventListener('click', () => passedOpenAnimalModal()); // Uses passed openAnimalModal
  }
}

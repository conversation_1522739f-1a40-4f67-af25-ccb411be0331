/* =================================
   Milk Yield Page Styles
   ================================= */

/* =================================
   Milk Yield Header Section
   ================================= */
.milk-yield-header-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.milk-yield-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.milk-yield-page-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.milk-yield-page-title svg {
  width: 28px;
  height: 28px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

.milk-yield-quick-actions {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.milk-yield-quick-actions .btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.milk-yield-quick-actions .btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* =================================
   Milk Yield Sections Grid
   ================================= */
.milk-yield-sections-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
}

.milk-yield-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.milk-yield-section:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* =================================
   Milk Yield Animal Selection Bar
   ================================= */
.animal-selection-bar {
  background: var(--primary-50);
  border: 1px solid var(--border-primary);
  border-top: 1px solid var(--primary-200);
  border-radius: var(--radius-lg);
  padding: var(--space-4) var(--space-6);
  margin-bottom: var(--space-6);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.animal-selection-bar-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  max-width: 600px;
}

.animal-selection-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--primary-700);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.animal-selection-info svg {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.animal-selection-bar select {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--primary-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--bg-elevated);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  min-width: 200px;
}

.animal-selection-bar select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.animal-selection-bar select:hover {
  border-color: var(--primary-400);
}

/* =================================
   Milk Content Wrapper
   ================================= */
#milk-content-wrapper {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  min-height: 500px;
  transition: all var(--transition-fast);
  margin-bottom: var(--space-6);
}

#milk-content-wrapper:hover {
  box-shadow: var(--shadow-md);
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
}

.section-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.section-header h3 svg {
  width: 18px;
  height: 18px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

/* Lactations and Milking Sections */
#lactations-section,
#milking-section {
  margin-bottom: 0;
}

/* Table Containers */
#lactations-table-container,
#milking-table-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  min-height: 200px;
}

/* Empty Messages */
.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-4);
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 1024px) {
  .milk-yield-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }
  
  .milk-yield-quick-actions {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .milk-yield-header-section {
    padding: var(--space-4);
  }

  .milk-yield-page-title {
    font-size: var(--text-xl);
  }

  .milk-yield-page-title svg {
    width: 24px;
    height: 24px;
  }

  .milk-yield-quick-actions {
    flex-direction: column;
    width: 100%;
  }

  .milk-yield-quick-actions .btn {
    justify-content: center;
    width: 100%;
  }

  .milk-yield-sections-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .milk-yield-section {
    padding: var(--space-4);
  }

  .animal-selection-bar {
    padding: var(--space-3) var(--space-4);
  }

  .animal-selection-bar-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
    max-width: none;
  }

  .animal-selection-bar select {
    width: 100%;
    min-width: auto;
  }

  #milk-content-wrapper {
    padding: var(--space-4);
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .milk-yield-header-section {
    padding: var(--space-3);
  }

  .milk-yield-page-title {
    font-size: var(--text-lg);
  }

  .milk-yield-quick-actions .btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }

  .animal-selection-bar {
    padding: var(--space-2) var(--space-3);
  }

  .animal-selection-info {
    font-size: var(--text-xs);
  }

  .animal-selection-info svg {
    width: 16px;
    height: 16px;
  }

  .milk-yield-section {
    padding: var(--space-3);
  }

  .section-header h3 {
    font-size: var(--text-base);
  }

  .section-header h3 svg {
    width: 16px;
    height: 16px;
  }
}

/* =================================
   Modern Grid System
   CSS Grid based layout system
   ================================= */

/* =================================
   Base Grid Container
   ================================= */
.grid {
  display: grid;
  gap: var(--space-4);
}

/* =================================
   Grid Columns
   ================================= */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

/* Auto-fit columns */
.grid-cols-auto-fit { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-cols-auto-fit-sm { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }
.grid-cols-auto-fit-lg { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }

/* =================================
   Grid Rows
   ================================= */
.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)); }
.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)); }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)); }

/* =================================
   Column Span
   ================================= */
.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-5 { grid-column: span 5 / span 5; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-full { grid-column: 1 / -1; }

/* =================================
   Row Span
   ================================= */
.row-span-1 { grid-row: span 1 / span 1; }
.row-span-2 { grid-row: span 2 / span 2; }
.row-span-3 { grid-row: span 3 / span 3; }
.row-span-4 { grid-row: span 4 / span 4; }
.row-span-full { grid-row: 1 / -1; }

/* =================================
   Gap Utilities
   ================================= */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.gap-x-0 { column-gap: 0; }
.gap-x-1 { column-gap: var(--space-1); }
.gap-x-2 { column-gap: var(--space-2); }
.gap-x-3 { column-gap: var(--space-3); }
.gap-x-4 { column-gap: var(--space-4); }
.gap-x-6 { column-gap: var(--space-6); }

.gap-y-0 { row-gap: 0; }
.gap-y-1 { row-gap: var(--space-1); }
.gap-y-2 { row-gap: var(--space-2); }
.gap-y-3 { row-gap: var(--space-3); }
.gap-y-4 { row-gap: var(--space-4); }
.gap-y-6 { row-gap: var(--space-6); }

/* =================================
   Flexbox System
   ================================= */
.flex { display: flex; }
.inline-flex { display: inline-flex; }

/* Flex Direction */
.flex-row { flex-direction: row; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col { flex-direction: column; }
.flex-col-reverse { flex-direction: column-reverse; }

/* Flex Wrap */
.flex-wrap { flex-wrap: wrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }
.flex-nowrap { flex-wrap: nowrap; }

/* Justify Content */
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Align Items */
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

/* Align Content */
.content-start { align-content: flex-start; }
.content-end { align-content: flex-end; }
.content-center { align-content: center; }
.content-between { align-content: space-between; }
.content-around { align-content: space-around; }
.content-evenly { align-content: space-evenly; }

/* Align Self */
.self-auto { align-self: auto; }
.self-start { align-self: flex-start; }
.self-end { align-self: flex-end; }
.self-center { align-self: center; }
.self-stretch { align-self: stretch; }
.self-baseline { align-self: baseline; }

/* Flex Grow & Shrink */
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

.grow { flex-grow: 1; }
.grow-0 { flex-grow: 0; }

.shrink { flex-shrink: 1; }
.shrink-0 { flex-shrink: 0; }

/* =================================
   Responsive Grid
   ================================= */

/* Small screens (640px and up) */
@media (min-width: 640px) {
  .sm\\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .sm\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .sm\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  
  .sm\\:col-span-1 { grid-column: span 1 / span 1; }
  .sm\\:col-span-2 { grid-column: span 2 / span 2; }
  .sm\\:col-span-3 { grid-column: span 3 / span 3; }
  .sm\\:col-span-4 { grid-column: span 4 / span 4; }
  .sm\\:col-span-full { grid-column: 1 / -1; }
}

/* Medium screens (768px and up) */
@media (min-width: 768px) {
  .md\\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  
  .md\\:col-span-1 { grid-column: span 1 / span 1; }
  .md\\:col-span-2 { grid-column: span 2 / span 2; }
  .md\\:col-span-3 { grid-column: span 3 / span 3; }
  .md\\:col-span-4 { grid-column: span 4 / span 4; }
  .md\\:col-span-6 { grid-column: span 6 / span 6; }
  .md\\:col-span-full { grid-column: 1 / -1; }
}

/* Large screens (1024px and up) */
@media (min-width: 1024px) {
  .lg\\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .lg\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .lg\\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
  
  .lg\\:col-span-1 { grid-column: span 1 / span 1; }
  .lg\\:col-span-2 { grid-column: span 2 / span 2; }
  .lg\\:col-span-3 { grid-column: span 3 / span 3; }
  .lg\\:col-span-4 { grid-column: span 4 / span 4; }
  .lg\\:col-span-6 { grid-column: span 6 / span 6; }
  .lg\\:col-span-12 { grid-column: span 12 / span 12; }
  .lg\\:col-span-full { grid-column: 1 / -1; }
}

/* =================================
   Common Layout Patterns
   ================================= */

/* Dashboard grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
}

/* Form grid */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

/* Card grid */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6);
}

/* Stats grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

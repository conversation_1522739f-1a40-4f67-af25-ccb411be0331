// Expects i18n, contentArea, and modalOpeners to be passed

export async function renderDashboardPage(contentArea, i18n, modalOpeners = {}) {
  const t = i18n.t;
  contentArea.innerHTML = `
    <!-- Top Bar with Quick Actions -->
    <div class="dashboard-top-bar">
      <div class="dashboard-title">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="dashboard-icon">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <line x1="9" y1="9" x2="15" y2="9"/>
          <line x1="9" y1="15" x2="15" y2="15"/>
        </svg>
        <h1 data-i18n="dashboard_title">Dashboard</h1>
      </div>
      <div class="dashboard-quick-actions">
        <button id="quick-add-animal" class="btn btn-primary">
          <svg viewBox='0 0 20 20' fill='none'><circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/><path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/></svg>
          <span data-i18n="btn_add_animal">Hayvan Ekle</span>
        </button>
        <button id="quick-bulk-milk" class="btn btn-secondary">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 22a7 7 0 0 0 7-7c0-2-1-3.9-3-5.5s-3.5-4-3.5-4-1.5 2.5-3.5 4-3 3.5-3 5.5a7 7 0 0 0 7 7z"/>
            <path d="M8 12h8"/>
            <path d="M8 16h8"/>
          </svg>
          <span data-i18n="btn_bulk_milk_entry">Toplu Süt Verisi</span>
        </button>
        <button id="quick-add-payment" class="btn btn-success">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
            <line x1="1" y1="10" x2="23" y2="10"/>
          </svg>
          <span data-i18n="btn_add_payment">Ödeme Ekle</span>
        </button>
      </div>
    </div>

    <!-- Main Content Area with 3/4 - 1/4 Split -->
    <div class="dashboard-main-content">
      <!-- Left Side: Animal Info Cards (3/4) -->
      <div class="dashboard-left-section">
        <div class="dashboard-section">
          <h2 data-i18n="herd_management_summary" class="section-title">Sürü Yönetimi Özeti</h2>
          <div class="dashboard-cards herd-cards"></div>
          <div class="dashboard-charts herd-charts">
            <div class="chart-container">
              <h3 data-i18n="animal_distribution_chart_title">Hayvan Dağılımı</h3>
              <canvas id="animalDistributionChart"></canvas>
            </div>
            <div class="chart-container">
              <h3 data-i18n="milk_production_chart_title">Aylık Süt Üretimi (Son 6 Ay)</h3>
              <canvas id="milkProductionChart"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Side: Alerts and Notifications (1/4) -->
      <div class="dashboard-right-section">
        <div class="dashboard-section">
          <h2 data-i18n="alerts_notifications" class="section-title">Uyarılar ve Bildirimler</h2>
          <div class="alerts-table-container">
            <div class="table-container">
              <div class="table-wrapper">
                <table class="modern-table" id="alerts-table">
                  <thead>
                    <tr>
                      <th data-i18n="alert_type">Tür</th>
                      <th data-i18n="alert_message">Mesaj</th>
                      <th data-i18n="alert_date">Tarih</th>
                      <th data-i18n="alert_status">Durum</th>
                    </tr>
                  </thead>
                  <tbody id="alerts-table-body">
                    <!-- Alerts will be populated here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Section: Accounting -->
    <div class="dashboard-bottom-section">
      <div class="dashboard-section">
        <h2 data-i18n="accounting_summary" class="section-title">Muhasebe Özeti</h2>
        <div class="dashboard-cards accounting-cards">
          <!-- Muhasebe kartları buraya gelecek -->
        </div>
        <div class="dashboard-charts accounting-charts">
          <div class="chart-container">
            <h3 data-i18n="monthly_profit_loss_chart_title">Aylık Net Kar/Zarar (Son 12 Ay)</h3>
            <canvas id="monthlyProfitLossChart"></canvas>
          </div>
          <div class="chart-container">
            <h3 data-i18n="income_expense_chart_title">Gelir/Gider Dağılımı</h3>
            <canvas id="incomeExpenseChart"></canvas>
          </div>
        </div>

        <!-- Receivables and Payables Tables -->
        <div class="cariler-tables-section">
          <div class="cariler-table-container">
            <div class="cariler-table-header">
              <h3 data-i18n="receivables_summary">Alacaklı Cariler</h3>
              <div class="table-controls">
                <input type="text" id="receivables-search" placeholder="Firma ara..." class="search-input">
                <select id="receivables-filter" class="filter-select">
                  <option value="all" data-i18n="filter_all">Tümü</option>
                  <option value="high" data-i18n="filter_high_amount">Yüksek Tutar</option>
                  <option value="medium" data-i18n="filter_medium_amount">Orta Tutar</option>
                  <option value="low" data-i18n="filter_low_amount">Düşük Tutar</option>
                </select>
              </div>
            </div>
            <div class="table-container">
              <div class="table-wrapper">
                <table class="modern-table" id="receivables-table">
                  <thead>
                    <tr>
                      <th data-i18n="company_name" class="sortable" data-sort="company">
                        Firma Adı
                        <svg class="sort-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <path d="M7 10l5 5 5-5"/>
                        </svg>
                      </th>
                      <th data-i18n="amount" class="sortable" data-sort="amount">
                        Tutar
                        <svg class="sort-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <path d="M7 10l5 5 5-5"/>
                        </svg>
                      </th>
                      <th data-i18n="due_date">Vade</th>
                      <th data-i18n="actions">İşlemler</th>
                    </tr>
                  </thead>
                  <tbody id="receivables-table-body">
                    <!-- Receivables will be populated here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="cariler-table-container">
            <div class="cariler-table-header">
              <h3 data-i18n="payables_summary">Borçlu Cariler</h3>
              <div class="table-controls">
                <input type="text" id="payables-search" placeholder="Firma ara..." class="search-input">
                <select id="payables-filter" class="filter-select">
                  <option value="all" data-i18n="filter_all">Tümü</option>
                  <option value="high" data-i18n="filter_high_amount">Yüksek Tutar</option>
                  <option value="medium" data-i18n="filter_medium_amount">Orta Tutar</option>
                  <option value="low" data-i18n="filter_low_amount">Düşük Tutar</option>
                </select>
              </div>
            </div>
            <div class="table-container">
              <div class="table-wrapper">
                <table class="modern-table" id="payables-table">
                  <thead>
                    <tr>
                      <th data-i18n="company_name" class="sortable" data-sort="company">
                        Firma Adı
                        <svg class="sort-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <path d="M7 10l5 5 5-5"/>
                        </svg>
                      </th>
                      <th data-i18n="amount" class="sortable" data-sort="amount">
                        Tutar
                        <svg class="sort-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <path d="M7 10l5 5 5-5"/>
                        </svg>
                      </th>
                      <th data-i18n="due_date">Vade</th>
                      <th data-i18n="actions">İşlemler</th>
                    </tr>
                  </thead>
                  <tbody id="payables-table-body">
                    <!-- Payables will be populated here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
  const herdCardsContainer = contentArea.querySelector('.herd-cards');
  const accountingCardsContainer = contentArea.querySelector('.accounting-cards');
  const alertsTableBody = contentArea.querySelector('#alerts-table-body');
  const receivablesTableBody = contentArea.querySelector('#receivables-table-body');
  const payablesTableBody = contentArea.querySelector('#payables-table-body');

  // Quick Action button listeners
  const { openAnimalModal, openMilkingModal, openAlertsModal } = modalOpeners;

  // Add Animal Button
  const quickAddAnimalBtn = contentArea.querySelector('#quick-add-animal');
  if (quickAddAnimalBtn && typeof openAnimalModal === 'function') {
    quickAddAnimalBtn.addEventListener('click', () => {
      openAnimalModal();
    });
  } else if (quickAddAnimalBtn) {
    quickAddAnimalBtn.addEventListener('click', () => {
      console.warn('openAnimalModal function not provided to dashboard page.');
      window.toast.warning(t('error_modal_function_not_available') || 'Hayvan ekleme modalı açılamıyor.');
    });
  }

  // Bulk Milk Data Entry Button
  const quickBulkMilkBtn = contentArea.querySelector('#quick-bulk-milk');
  if (quickBulkMilkBtn) {
    quickBulkMilkBtn.addEventListener('click', () => {
      // Navigate to milk yield page or open bulk entry modal
      console.log('Bulk milk data entry button clicked');
      window.toast.info(t('feature_not_implemented_yet') || 'Bu özellik henüz eklenmedi: Toplu Süt Verisi');
    });
  }

  // Add Payment Button
  const quickAddPaymentBtn = contentArea.querySelector('#quick-add-payment');
  if (quickAddPaymentBtn) {
    quickAddPaymentBtn.addEventListener('click', () => {
      // Navigate to payments page or open payment modal
      console.log('Add payment button clicked');
      window.toast.info(t('feature_not_implemented_yet') || 'Bu özellik henüz eklenmedi: Ödeme Ekle');
    });
  }

  try {
      const stats = await window.api.invoke('dashboard:stats');
      if (stats.error) {
          herdCardsContainer.innerHTML = `<p>${t('error_loading_stats')}: ${stats.error}</p>`;
          accountingCardsContainer.innerHTML = `<p>${t('error_loading_accounting_stats')}: ${stats.error}</p>`;
          return;
      }

      // Sürü Yönetimi Kartları
      const herdCards = [
          {
              title: t('total_animals'),
              value: stats.totalAnimals,
              icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>`,
              className: 'total',
              description: t('total_animals_desc')
          },
          {
              title: t('female_animals'),
              value: stats.femaleAnimals,
              icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="10" r="8"></circle><line x1="12" y1="18" x2="12" y2="23"></line><line x1="9" y1="21" x2="15" y2="21"></line></svg>`,
              className: 'female',
              description: t('female_animals_desc')
          },
          {
              title: t('male_animals'),
              value: stats.maleAnimals,
              icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="13" r="8"></circle><line x1="20" y1="4" x2="15" y2="9"></line><line x1="20" y1="9" x2="20" y2="4"></line><line x1="15" y1="4" x2="20" y2="4"></line></svg>`,
              className: 'male',
              description: t('male_animals_desc')
          },
          {
              title: t('ongoing_pregnancies'),
              value: stats.pregnantAnimals,
              icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 16.01a2 2 0 1 1-4 0c0-1.42 1.43-3.14 2-4 1.43 2.58 2 5.42 2 4Z"></path><path d="M3.33 8c-1.15 4.33.83 9.33 5.67 12.5 3.81 2.5 8.83 2.5 12.64 0 4.84-3.17 6.82-8.17 5.67-12.5-1.17-4.42-4.95-7-9.5-7-4.55 0-8.33 2.58-9.5 7Z"></path></svg>`,
              className: 'pregnant',
              description: t('ongoing_pregnancies_desc')
          },
          {
              title: t('upcoming_births_30_days'),
              value: stats.upcomingBirths,
              icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 12 20 22 4 22 4 12"></polyline><rect x="2" y="7" width="20" height="5"></rect><line x1="12" y1="22" x2="12" y2="7"></line><path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"></path><path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"></path></svg>`,
              className: 'births',
              description: t('upcoming_births_30_days_desc')
          },
           {
              title: t('milking_animals'),
              value: `${stats.milkingAnimals} / ${stats.femaleAnimals}`,
              icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline></svg>`,
              className: 'milking',
              description: t('milking_animals_desc')
          },
          {
              title: t('daily_milk_yield_avg'),
              value: `${(stats.averageDailyMilkYield ?? 0).toFixed(1)} L`,
              icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22a7 7 0 0 0 7-7c0-2-1-3.9-3-5.5s-3.5-4-3.5-4-1.5 2.5-3.5 4-3 3.5-3 5.5a7 7 0 0 0 7 7z"></path></svg>`,
              className: 'daily-milk',
              description: t('daily_milk_yield_avg_desc')
          },
          {
              title: t('health_alerts_active'),
              value: stats.activeHealthAlerts,
              icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>`,
              className: 'alerts',
              description: t('health_alerts_active_desc')
          }
      ];

      herdCardsContainer.innerHTML = herdCards.map(card => `
          <div class="accounting-card ${card.className || ''}">
              <div class="card-icon">
                ${card.icon}
              </div>
              <div class="card-content">
                  <div class="card-title">${card.title}</div>
                  <div class="card-value">${card.value}</div>
                  ${card.description ? `<div class="card-subtitle">${card.description}</div>` : ''}
              </div>
          </div>
      `).join('');

      // Initialize Charts
      // Ensure Chart.js is loaded, perhaps via a global or specific import if using modules fully
      if (typeof Chart !== 'undefined') {
        renderAnimalDistributionChart(stats, t);
        // Placeholder for milk production data - this would come from stats
        const milkProductionData = stats.last6MonthsMilk || { labels: [], data: [] };
        renderMilkProductionChart(milkProductionData, t);
      } else {
        console.warn('Chart.js not loaded. Charts will not be rendered.');
        contentArea.querySelector('#animalDistributionChart').parentElement.innerHTML = `<p>${t('chart_library_not_loaded')}</p>`;
        contentArea.querySelector('#milkProductionChart').parentElement.innerHTML = `<p>${t('chart_library_not_loaded')}</p>`;
      }

      // Muhasebe Kartları ve Grafiği
      await renderAccountingCards(stats, accountingCardsContainer, t);
      if (typeof Chart !== 'undefined' && stats.aylikKarZararTrendi) {
        renderMonthlyProfitLossChart(stats.aylikKarZararTrendi, t);
        await renderIncomeExpenseChart(stats, t);
      } else if (stats.aylikKarZararTrendi) {
        console.warn('Chart.js not loaded. Profit/Loss chart will not be rendered.');
        contentArea.querySelector('#monthlyProfitLossChart').parentElement.innerHTML = `<p>${t('chart_library_not_loaded')}</p>`;
      }

      // Populate alerts table
      await renderAlertsTable(alertsTableBody, t);

      // Populate receivables and payables tables
      await renderReceivablesTable(receivablesTableBody, stats.carilerNetBakiye, t);
      await renderPayablesTable(payablesTableBody, stats.carilerNetBakiye, t);

      // Initialize table functionality
      initializeCarilerTableFunctionality(stats.carilerNetBakiye, t);

  } catch (error) {
      console.error('Failed to load dashboard stats:', error);
      herdCardsContainer.innerHTML = `<p>${t('error_loading_dashboard_data')}</p>`; // Hata mesajını doğru konteynerda göster
      accountingCardsContainer.innerHTML = `<p>${t('error_loading_dashboard_data')}</p>`;
      if (alertsTableBody) alertsTableBody.innerHTML = `<tr><td colspan="4">${t('error_loading_dashboard_data')}</td></tr>`;
      if (receivablesTableBody) receivablesTableBody.innerHTML = `<tr><td colspan="4">${t('error_loading_dashboard_data')}</td></tr>`;
      if (payablesTableBody) payablesTableBody.innerHTML = `<tr><td colspan="4">${t('error_loading_dashboard_data')}</td></tr>`;
  }
}

async function renderAccountingCards(stats, container, t) {
  try {
    // Use the existing AccountingHeader component to get real financial data
    const { AccountingHeader } = await import('../components/accounting-header.js');

    // Create a temporary accounting header to fetch real data
    const tempAccountingHeader = new AccountingHeader({
      containerId: 'temp-accounting-header',
      pageTitle: 'Dashboard',
      pageIcon: ''
    });

    // Fetch real financial data
    const financialData = await tempAccountingHeader.fetchFinancialSummary();

    // Use the real accounting cards structure from AccountingHeader
    container.innerHTML = `
      <div class="accounting-cards">
        <div class="accounting-card unpaid-debt-card">
          <div class="card-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="16"/>
              <line x1="8" y1="12" x2="16" y2="12"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-title">Ödenmemiş Borç</div>
            <div class="card-value ${financialData.unpaidDebt > 0 ? 'negative' : 'neutral'}">${tempAccountingHeader.formatCurrency(financialData.unpaidDebt)}</div>
            <div class="card-subtitle">Toplam Borç</div>
          </div>
        </div>

        <div class="accounting-card uncollected-receivable-card">
          <div class="card-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-title">Tahsil Edilmemiş</div>
            <div class="card-value ${financialData.uncollectedReceivable > 0 ? 'positive' : 'neutral'}">${tempAccountingHeader.formatCurrency(financialData.uncollectedReceivable)}</div>
            <div class="card-subtitle">Toplam Alacak</div>
          </div>
        </div>

        <div class="accounting-card paid-money-card">
          <div class="card-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 1v22M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
              <circle cx="18" cy="6" r="3"/>
              <path d="m16 8 2 2 4-4"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-title">Ödenmiş Para</div>
            <div class="card-value ${financialData.paidMoney > 0 ? 'negative' : 'neutral'}">${tempAccountingHeader.formatCurrency(financialData.paidMoney)}</div>
            <div class="card-subtitle">Toplam Ödeme</div>
          </div>
        </div>

        <div class="accounting-card collected-money-card">
          <div class="card-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 1v22M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
              <circle cx="18" cy="6" r="3"/>
              <path d="m16 8 2 2 4-4"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-title">Tahsil Edilmiş</div>
            <div class="card-value ${financialData.collectedMoney > 0 ? 'positive' : 'neutral'}">${tempAccountingHeader.formatCurrency(financialData.collectedMoney)}</div>
            <div class="card-subtitle">Nakit Dahil</div>
          </div>
        </div>

        <div class="accounting-card cash-balance-card">
          <div class="card-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
              <line x1="1" y1="10" x2="23" y2="10"/>
              <circle cx="8" cy="14" r="2"/>
              <path d="m15 11 1 1 3-3"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-title">Kasa Net Bakiye</div>
            <div class="card-value ${financialData.cashNetBalance >= 0 ? 'positive' : 'negative'}">${tempAccountingHeader.formatCurrency(financialData.cashNetBalance)}</div>
            <div class="card-subtitle">Toplam Bakiye</div>
          </div>
        </div>

        <div class="accounting-card net-financial-position-card">
          <div class="card-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2L2 7l10 5 10-5-10-5z"/>
              <path d="m2 17 10 5 10-5"/>
              <path d="m2 12 10 5 10-5"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-title">Net Finansal Durum</div>
            <div class="card-value ${financialData.netFinancialPosition >= 0 ? 'positive' : 'negative'}">${tempAccountingHeader.formatCurrency(financialData.netFinancialPosition)}</div>
            <div class="card-subtitle">Toplam Alacak - Toplam Borç</div>
          </div>
        </div>
      </div>
    `;

  } catch (error) {
    console.error('Error loading real financial data:', error);
    // Fallback to basic display
    container.innerHTML = `<p class="error-message">Mali veriler yüklenirken hata oluştu: ${error.message}</p>`;
  }

}

function renderMonthlyProfitLossChart(trendData, t) {
  const ctx = document.getElementById('monthlyProfitLossChart')?.getContext('2d');
  if (!ctx) return;

  const labels = trendData.map(d => d.ay);
  const netValues = trendData.map(d => d.net);

  // Çizgi renklerini belirle: net değere göre yeşil veya kırmızı
  const lineColors = netValues.map(value => value >= 0 ? 'rgba(34, 197, 94, 1)' : 'rgba(239, 68, 68, 1)');
   // Alan renklerini belirle (daha soluk)
  const backgroundColors = netValues.map(value => value >= 0 ? 'rgba(34, 197, 94, 0.2)' : 'rgba(239, 68, 68, 0.2)');


  new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: t('net_profit_loss') || 'Net Kar/Zarar',
        data: netValues,
        fill: true,
        borderColor: lineColors, // Her nokta için farklı renk olabilir veya tek bir genel renk
        backgroundColor: backgroundColors, // Alan dolgusu için
        tension: 0.3,
        pointBackgroundColor: lineColors, // Nokta iç rengi
        pointBorderColor: '#fff', // Nokta kenarlık rengi
        pointHoverRadius: 7,
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: lineColors // Hover durumunda nokta kenarlık rengi
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: false, // Kar/zarar negatif olabileceği için false
          title: {
            display: true,
            text: t('amount_try') || 'Tutar (TRY)'
          },
          ticks: {
            callback: function(value, index, values) {
              return new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(value);
            }
          }
        },
        x: {
          title: {
            display: true,
            text: t('months') || 'Aylar'
          }
        }
      },
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
            callbacks: {
                label: function(context) {
                    let label = context.dataset.label || '';
                    if (label) {
                        label += ': ';
                    }
                    if (context.parsed.y !== null) {
                        label += new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }).format(context.parsed.y);
                    }
                    return label;
                }
            }
        }
      }
    }
  });
}


function renderAnimalDistributionChart(stats, t) {
  const ctx = document.getElementById('animalDistributionChart')?.getContext('2d');
  if (!ctx) return;

  new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: [t('female_animals'), t('male_animals'), t('other_genders') || 'Diğer'], // Assuming 'other' might be needed
      datasets: [{
        label: t('animal_distribution_chart_title'),
        data: [stats.femaleAnimals, stats.maleAnimals, stats.totalAnimals - (stats.femaleAnimals + stats.maleAnimals)],
        backgroundColor: [
          'rgba(239, 68, 68, 0.7)', // female - red
          'rgba(59, 130, 246, 0.7)', // male - blue
          'rgba(245, 158, 11, 0.7)' // other - amber
        ],
        borderColor: [
          'rgba(239, 68, 68, 1)',
          'rgba(59, 130, 246, 1)',
          'rgba(245, 158, 11, 1)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
        },
        title: {
          display: false, // Already have a title above canvas
        }
      }
    }
  });
}

function renderMilkProductionChart(milkData, t) {
  const ctx = document.getElementById('milkProductionChart')?.getContext('2d');
  if (!ctx) return;

  // Ensure milkData has labels and data arrays
  const labels = milkData.labels && milkData.labels.length > 0 ? milkData.labels : [t('no_data')];
  const dataPoints = milkData.data && milkData.data.length > 0 ? milkData.data : [0];

  new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: t('milk_production_liters') || 'Süt Üretimi (L)',
        data: dataPoints,
        fill: true,
        borderColor: 'rgba(34, 197, 94, 1)', // green
        backgroundColor: 'rgba(34, 197, 94, 0.2)',
        tension: 0.3,
        pointBackgroundColor: 'rgba(34, 197, 94, 1)',
        pointBorderColor: '#fff',
        pointHoverRadius: 6,
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(34, 197, 94, 1)',
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: t('liters') || 'Litre'
          }
        },
        x: {
          title: {
            display: true,
            text: t('months') || 'Aylar'
          }
        }
      },
      plugins: {
        legend: {
          display: false, // Only one dataset, legend might be redundant
        },
      }
    }
  });
}

// New functions for dashboard redesign

async function renderAlertsTable(tableBody, t) {
  if (!tableBody) return;

  try {
    // Fetch real alert data from multiple sources
    const [upcomingVaccinations, overdueVaccinations, activeReminders, pregnantAnimals] = await Promise.all([
      window.api.invoke('asiTakvimi:getUpcoming', 30),
      window.api.invoke('asiTakvimi:getOverdue'),
      window.api.invoke('asiHatirlaticlari:getActive'),
      // Get pregnant animals approaching birth date
      window.api.invoke('dashboard:getStats').then(stats => {
        // Extract pregnant animals from stats if available
        return stats.gebeHayvanSayisi || 0;
      }).catch(() => 0)
    ]);

    const realAlerts = [];

    // Process upcoming vaccinations
    upcomingVaccinations.forEach(vaccination => {
      const daysUntil = Math.ceil((new Date(vaccination.PlanlananTarih) - new Date()) / (1000 * 60 * 60 * 24));
      realAlerts.push({
        id: `vac_${vaccination.Id}`,
        type: 'vaccination',
        message: `${vaccination.AsiAdi} - ${vaccination.HayvanIsim || vaccination.KupeNo}`,
        date: new Date(vaccination.PlanlananTarih),
        status: 'unread',
        priority: daysUntil <= 3 ? 'high' : daysUntil <= 7 ? 'medium' : 'low',
        daysUntil: daysUntil,
        animalId: vaccination.HayvanId
      });
    });

    // Process overdue vaccinations
    overdueVaccinations.forEach(vaccination => {
      realAlerts.push({
        id: `overdue_${vaccination.Id}`,
        type: 'vaccination',
        message: `GECİKTİ: ${vaccination.AsiAdi} - ${vaccination.HayvanIsim || vaccination.KupeNo}`,
        date: new Date(vaccination.PlanlananTarih),
        status: 'unread',
        priority: 'high',
        daysUntil: Math.ceil((new Date(vaccination.PlanlananTarih) - new Date()) / (1000 * 60 * 60 * 24)),
        animalId: vaccination.HayvanId
      });
    });

    // Process active reminders
    activeReminders.forEach(reminder => {
      realAlerts.push({
        id: `reminder_${reminder.Id}`,
        type: 'health',
        message: `Hatırlatıcı: ${reminder.AsiAdi} - ${reminder.HayvanIsim || reminder.KupeNo}`,
        date: new Date(reminder.HatirlaticiTarihi),
        status: 'unread',
        priority: 'medium',
        animalId: reminder.HayvanId
      });
    });

    // Add some sample breeding and milk alerts if no real data available
    if (realAlerts.length < 3) {
      // Add sample alerts to demonstrate the system
      realAlerts.push(
        {
          id: 'sample_breeding',
          type: 'breeding',
          message: 'Çiftleştirme zamanı yaklaşıyor - Kontrol gerekli',
          date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          status: 'unread',
          priority: 'medium'
        },
        {
          id: 'sample_milk',
          type: 'milk',
          message: 'Süt verimi düşüş eğiliminde - İnceleme önerilir',
          date: new Date(),
          status: 'read',
          priority: 'low'
        }
      );
    }

    // Sort alerts by priority and date
    const sortedAlerts = realAlerts.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      return new Date(a.date) - new Date(b.date);
    });

    // Limit to top 10 alerts for dashboard
    const displayAlerts = sortedAlerts.slice(0, 10);

    if (displayAlerts.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="4" class="text-center" style="padding: var(--space-6); color: var(--text-secondary);">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" style="margin-bottom: var(--space-2); opacity: 0.5;">
              <path d="M9 12l2 2 4-4"/>
              <circle cx="12" cy="12" r="10"/>
            </svg>
            <br>
            ${t('no_active_alerts') || 'Aktif uyarı bulunmuyor'}
          </td>
        </tr>
      `;
      return;
    }

    tableBody.innerHTML = displayAlerts.map(alert => `
      <tr class="alert-row alert-${alert.priority} alert-${alert.status}" data-alert-id="${alert.id}" onclick="handleAlertClick('${alert.id}')">
        <td>
          <span class="badge badge-${alert.type}">
            ${getAlertTypeIcon(alert.type)} ${t('alert_type_' + alert.type) || alert.type}
          </span>
        </td>
        <td class="alert-message" title="${alert.message}">${alert.message}</td>
        <td>${alert.date.toLocaleDateString('tr-TR')}</td>
        <td>
          <span class="badge ${alert.status === 'unread' ? 'badge-warning' : 'badge-success'}">
            ${alert.status === 'unread' ? '●' : '○'} ${t('alert_status_' + alert.status) || (alert.status === 'unread' ? 'Okunmadı' : 'Okundu')}
          </span>
        </td>
      </tr>
    `).join('');

  } catch (error) {
    console.error('Error loading real alert data:', error);
    tableBody.innerHTML = `
      <tr>
        <td colspan="4" class="text-center error-message">
          ${t('error_loading_alerts') || 'Uyarılar yüklenirken hata oluştu'}: ${error.message}
        </td>
      </tr>
    `;
  }
}

// Handle alert click to mark as read - moved to end of file

async function renderReceivablesTable(tableBody, carilerData, t) {
  if (!tableBody) return;

  try {
    // If carilerData is not provided, fetch it from the API
    if (!carilerData) {
      const stats = await window.api.invoke('dashboard:getStats');
      carilerData = stats.carilerNetBakiye || [];
    }

    const alacakliCariler = carilerData.filter(cari => cari.NetBakiye > 0).sort((a,b) => b.NetBakiye - a.NetBakiye);

    if (alacakliCariler.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="4" class="text-center" style="padding: var(--space-4); color: var(--text-secondary);">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" style="margin-bottom: var(--space-2); opacity: 0.5;">
              <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="8.5" cy="7" r="4"/>
              <line x1="20" y1="8" x2="20" y2="14"/>
              <line x1="23" y1="11" x2="17" y2="11"/>
            </svg>
            <br>
            ${t('no_receivables') || 'Alacaklı cari hesap bulunmuyor'}
          </td>
        </tr>
      `;
      return;
    }

    tableBody.innerHTML = alacakliCariler.slice(0, 5).map(cari => `
      <tr class="receivable-row" data-cari-id="${cari.Id}">
        <td class="company-name" title="${cari.Unvan}">${cari.Unvan || t('unknown_company')}</td>
        <td class="amount positive">₺${cari.NetBakiye.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
        <td class="due-date">${t('contact_for_details') || 'Detay için iletişim'}</td>
        <td class="actions">
          <button class="btn btn-sm btn-secondary" onclick="viewCariDetails(${cari.Id})" title="${t('view_details') || 'Detayları görüntüle'}">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
            ${t('btn_view') || 'Görüntüle'}
          </button>
        </td>
      </tr>
    `).join('');

    // Add "show all" link if there are more than 5 receivables
    if (alacakliCariler.length > 5) {
      tableBody.innerHTML += `
        <tr class="show-all-row">
          <td colspan="4" class="text-center" style="padding: var(--space-3);">
            <button class="btn btn-sm btn-outline" onclick="navigateToAccountingAccounts()">
              ${t('view_all_receivables') || 'Tüm alacakları görüntüle'} (${alacakliCariler.length - 5} ${t('more') || 'daha'})
            </button>
          </td>
        </tr>
      `;
    }

  } catch (error) {
    console.error('Error loading receivables data:', error);
    tableBody.innerHTML = `
      <tr>
        <td colspan="4" class="text-center error-message">
          ${t('error_loading_receivables') || 'Alacaklar yüklenirken hata oluştu'}: ${error.message}
        </td>
      </tr>
    `;
  }
}

async function renderPayablesTable(tableBody, carilerData, t) {
  if (!tableBody) return;

  try {
    // If carilerData is not provided, fetch it from the API
    if (!carilerData) {
      const stats = await window.api.invoke('dashboard:getStats');
      carilerData = stats.carilerNetBakiye || [];
    }

    const borcluCariler = carilerData.filter(cari => cari.NetBakiye < 0).sort((a,b) => a.NetBakiye - b.NetBakiye);

    if (borcluCariler.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="4" class="text-center" style="padding: var(--space-4); color: var(--text-secondary);">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" style="margin-bottom: var(--space-2); opacity: 0.5;">
              <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="8.5" cy="7" r="4"/>
              <line x1="23" y1="11" x2="17" y2="11"/>
            </svg>
            <br>
            ${t('no_payables') || 'Borçlu cari hesap bulunmuyor'}
          </td>
        </tr>
      `;
      return;
    }

    tableBody.innerHTML = borcluCariler.slice(0, 5).map(cari => `
      <tr class="payable-row" data-cari-id="${cari.Id}">
        <td class="company-name" title="${cari.Unvan}">${cari.Unvan || t('unknown_company')}</td>
        <td class="amount negative">₺${Math.abs(cari.NetBakiye).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
        <td class="due-date">${t('contact_for_details') || 'Detay için iletişim'}</td>
        <td class="actions">
          <button class="btn btn-sm btn-secondary" onclick="viewCariDetails(${cari.Id})" title="${t('view_details') || 'Detayları görüntüle'}">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
            ${t('btn_view') || 'Görüntüle'}
          </button>
        </td>
      </tr>
    `).join('');

    // Add "show all" link if there are more than 5 payables
    if (borcluCariler.length > 5) {
      tableBody.innerHTML += `
        <tr class="show-all-row">
          <td colspan="4" class="text-center" style="padding: var(--space-3);">
            <button class="btn btn-sm btn-outline" onclick="navigateToAccountingAccounts()">
              ${t('view_all_payables') || 'Tüm borçları görüntüle'} (${borcluCariler.length - 5} ${t('more') || 'daha'})
            </button>
          </td>
        </tr>
      `;
    }

  } catch (error) {
    console.error('Error loading payables data:', error);
    tableBody.innerHTML = `
      <tr>
        <td colspan="4" class="text-center error-message">
          ${t('error_loading_payables') || 'Borçlar yüklenirken hata oluştu'}: ${error.message}
        </td>
      </tr>
    `;
  }
}

async function renderIncomeExpenseChart(stats, t) {
  const ctx = document.getElementById('incomeExpenseChart');
  if (!ctx) return;

  try {
    // Try to get real financial data from accounting header
    const { AccountingHeader } = await import('../components/accounting-header.js');
    const tempAccountingHeader = new AccountingHeader({});
    const financialData = await tempAccountingHeader.fetchFinancialSummary();

    // Calculate income and expenses from real data
    const incomeData = financialData.collectedMoney || 0;
    const expenseData = financialData.paidMoney || 0;

    // If no real data, use fallback values
    const totalIncome = incomeData > 0 ? incomeData : (stats.totalSales || 50000);
    const totalExpenses = expenseData > 0 ? expenseData : (stats.totalPurchases || 35000);

    // Destroy existing chart if it exists
    if (window.dashboardIncomeExpenseChart) {
      window.dashboardIncomeExpenseChart.destroy();
    }

    window.dashboardIncomeExpenseChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: [t('total_income') || 'Toplam Gelir', t('total_expense') || 'Toplam Gider'],
        datasets: [{
          label: t('income_expense_chart_title') || 'Gelir/Gider Dağılımı',
          data: [totalIncome, totalExpenses],
          backgroundColor: [
            'rgba(34, 197, 94, 0.8)', // green for income
            'rgba(239, 68, 68, 0.8)'  // red for expense
          ],
          borderColor: [
            'rgba(34, 197, 94, 1)',
            'rgba(239, 68, 68, 1)'
          ],
          borderWidth: 2,
          hoverBackgroundColor: [
            'rgba(34, 197, 94, 0.9)',
            'rgba(239, 68, 68, 0.9)'
          ],
          hoverBorderWidth: 3
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              font: {
                size: 12
              }
            }
          },
          title: {
            display: true,
            text: t('income_expense_chart_title') || 'Gelir/Gider Dağılımı',
            font: {
              size: 14,
              weight: 'bold'
            },
            padding: {
              bottom: 20
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = new Intl.NumberFormat('tr-TR', {
                  style: 'currency',
                  currency: 'TRY'
                }).format(context.parsed);
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : '0.0';
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        },
        animation: {
          animateRotate: true,
          animateScale: true,
          duration: 1000
        }
      }
    });

  } catch (error) {
    console.error('Error rendering income/expense chart:', error);
    // Fallback to simple display
    ctx.parentElement.innerHTML = `
      <div class="chart-error" style="text-align: center; padding: var(--space-6); color: var(--text-secondary);">
        <p>${t('chart_error') || 'Grafik yüklenirken hata oluştu'}</p>
        <small>${error.message}</small>
      </div>
    `;
  }
}

// Global function for viewing cari details (can be called from table buttons)
// This will be overridden by the updated function at the end of the file

// Initialize cariler table functionality
function initializeCarilerTableFunctionality(carilerData, t) {
  if (!carilerData) return;

  const alacakliCariler = carilerData.filter(cari => cari.NetBakiye > 0);
  const borcluCariler = carilerData.filter(cari => cari.NetBakiye < 0);

  // Search functionality for receivables
  const receivablesSearch = document.getElementById('receivables-search');
  const receivablesFilter = document.getElementById('receivables-filter');
  const receivablesTableBody = document.getElementById('receivables-table-body');

  if (receivablesSearch && receivablesFilter && receivablesTableBody) {
    const handleReceivablesFilter = () => {
      const searchTerm = receivablesSearch.value.toLowerCase();
      const filterValue = receivablesFilter.value;

      let filteredData = alacakliCariler.filter(cari =>
        cari.Unvan.toLowerCase().includes(searchTerm)
      );

      // Apply amount filter
      if (filterValue === 'high') {
        filteredData = filteredData.filter(cari => cari.NetBakiye > 10000);
      } else if (filterValue === 'medium') {
        filteredData = filteredData.filter(cari => cari.NetBakiye >= 1000 && cari.NetBakiye <= 10000);
      } else if (filterValue === 'low') {
        filteredData = filteredData.filter(cari => cari.NetBakiye < 1000);
      }

      renderFilteredReceivables(receivablesTableBody, filteredData, t);
    };

    receivablesSearch.addEventListener('input', handleReceivablesFilter);
    receivablesFilter.addEventListener('change', handleReceivablesFilter);
  }

  // Search functionality for payables
  const payablesSearch = document.getElementById('payables-search');
  const payablesFilter = document.getElementById('payables-filter');
  const payablesTableBody = document.getElementById('payables-table-body');

  if (payablesSearch && payablesFilter && payablesTableBody) {
    const handlePayablesFilter = () => {
      const searchTerm = payablesSearch.value.toLowerCase();
      const filterValue = payablesFilter.value;

      let filteredData = borcluCariler.filter(cari =>
        cari.Unvan.toLowerCase().includes(searchTerm)
      );

      // Apply amount filter
      if (filterValue === 'high') {
        filteredData = filteredData.filter(cari => Math.abs(cari.NetBakiye) > 10000);
      } else if (filterValue === 'medium') {
        filteredData = filteredData.filter(cari => Math.abs(cari.NetBakiye) >= 1000 && Math.abs(cari.NetBakiye) <= 10000);
      } else if (filterValue === 'low') {
        filteredData = filteredData.filter(cari => Math.abs(cari.NetBakiye) < 1000);
      }

      renderFilteredPayables(payablesTableBody, filteredData, t);
    };

    payablesSearch.addEventListener('input', handlePayablesFilter);
    payablesFilter.addEventListener('change', handlePayablesFilter);
  }

  // Sorting functionality
  initializeTableSorting();
}

function renderFilteredReceivables(tableBody, data, t) {
  if (data.length === 0) {
    tableBody.innerHTML = `<tr><td colspan="4" class="text-center">${t('no_results_found') || 'Sonuç bulunamadı'}</td></tr>`;
    return;
  }

  tableBody.innerHTML = data.map(cari => `
    <tr class="receivable-row" data-cari-id="${cari.Id}">
      <td class="company-name">${cari.Unvan || t('unknown_company')}</td>
      <td class="amount positive">₺${cari.NetBakiye.toFixed(2)}</td>
      <td class="due-date">${t('contact_for_details') || 'Detay için iletişim'}</td>
      <td class="actions">
        <button class="btn btn-sm btn-secondary" onclick="viewCariDetails(${cari.Id})">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
            <circle cx="12" cy="12" r="3"/>
          </svg>
          ${t('btn_view') || 'Görüntüle'}
        </button>
      </td>
    </tr>
  `).join('');
}

function renderFilteredPayables(tableBody, data, t) {
  if (data.length === 0) {
    tableBody.innerHTML = `<tr><td colspan="4" class="text-center">${t('no_results_found') || 'Sonuç bulunamadı'}</td></tr>`;
    return;
  }

  tableBody.innerHTML = data.map(cari => `
    <tr class="payable-row" data-cari-id="${cari.Id}">
      <td class="company-name">${cari.Unvan || t('unknown_company')}</td>
      <td class="amount negative">₺${Math.abs(cari.NetBakiye).toFixed(2)}</td>
      <td class="due-date">${t('contact_for_details') || 'Detay için iletişim'}</td>
      <td class="actions">
        <button class="btn btn-sm btn-secondary" onclick="viewCariDetails(${cari.Id})">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
            <circle cx="12" cy="12" r="3"/>
          </svg>
          ${t('btn_view') || 'Görüntüle'}
        </button>
      </td>
    </tr>
  `).join('');
}

function initializeTableSorting() {
  const sortableHeaders = document.querySelectorAll('.sortable');

  sortableHeaders.forEach(header => {
    header.addEventListener('click', () => {
      const sortType = header.dataset.sort;
      const table = header.closest('table');
      const tbody = table.querySelector('tbody');
      const rows = Array.from(tbody.querySelectorAll('tr'));

      // Toggle sort direction
      const isAscending = header.classList.contains('sort-asc');

      // Remove sort classes from all headers in this table
      table.querySelectorAll('.sortable').forEach(h => {
        h.classList.remove('sort-asc', 'sort-desc');
      });

      // Add appropriate sort class
      header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');

      // Sort rows
      rows.sort((a, b) => {
        let aValue, bValue;

        if (sortType === 'company') {
          aValue = a.querySelector('.company-name').textContent.toLowerCase();
          bValue = b.querySelector('.company-name').textContent.toLowerCase();
        } else if (sortType === 'amount') {
          aValue = parseFloat(a.querySelector('.amount').textContent.replace('₺', '').replace(',', ''));
          bValue = parseFloat(b.querySelector('.amount').textContent.replace('₺', '').replace(',', ''));
        }

        if (isAscending) {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });

      // Reorder rows in DOM
      rows.forEach(row => tbody.appendChild(row));
    });
  });
}

// Helper functions for alert handling
function getAlertTypeIcon(type) {
  const icons = {
    vaccination: '💉',
    health: '🏥',
    payment: '💰',
    breeding: '🐄',
    milk: '🥛'
  };
  return icons[type] || '⚠️';
}

window.handleAlertClick = function(alertId) {
  console.log('Alert clicked:', alertId);

  // Mark alert as read first
  const alertRow = document.querySelector(`[data-alert-id="${alertId}"]`);
  if (alertRow && alertRow.classList.contains('alert-unread')) {
    alertRow.classList.remove('alert-unread');
    alertRow.classList.add('alert-read');

    const statusBadge = alertRow.querySelector('.badge-warning');
    if (statusBadge) {
      statusBadge.classList.remove('badge-warning');
      statusBadge.classList.add('badge-success');
      statusBadge.innerHTML = '○ Okundu';
    }
  }

  // Parse alert ID to determine type and navigate accordingly
  if (alertId.startsWith('vac_') || alertId.startsWith('overdue_') || alertId.startsWith('reminder_')) {
    // Navigate to health page for vaccination-related alerts
    navigateToHealthPage();
  } else if (alertId.includes('breeding')) {
    // Navigate to breeding page
    navigateToBreedingPage();
  } else if (alertId.includes('milk')) {
    // Navigate to milk production page
    navigateToMilkPage();
  } else if (alertId.includes('payment')) {
    // Navigate to accounting page
    navigateToAccountingPage();
  } else {
    // Generic alert handling
    window.toast.info(`Alert ${alertId} clicked - navigate to details page`);
  }
};

// Navigation helper functions
function navigateToHealthPage() {
  try {
    window.location.hash = '#health';
    window.toast.success('Sağlık sayfasına yönlendiriliyor...');
  } catch (error) {
    console.error('Error navigating to health page:', error);
    window.toast.error('Sayfa yönlendirmesinde hata oluştu');
  }
}

function navigateToBreedingPage() {
  try {
    window.location.hash = '#breeding';
    window.toast.success('Üreme sayfasına yönlendiriliyor...');
  } catch (error) {
    console.error('Error navigating to breeding page:', error);
    window.toast.error('Sayfa yönlendirmesinde hata oluştu');
  }
}

function navigateToMilkPage() {
  try {
    window.location.hash = '#milk';
    window.toast.success('Süt üretimi sayfasına yönlendiriliyor...');
  } catch (error) {
    console.error('Error navigating to milk page:', error);
    window.toast.error('Sayfa yönlendirmesinde hata oluştu');
  }
}

function navigateToAccountingPage() {
  try {
    window.location.hash = '#accounting';
    window.toast.success('Muhasebe sayfasına yönlendiriliyor...');
  } catch (error) {
    console.error('Error navigating to accounting page:', error);
    window.toast.error('Sayfa yönlendirmesinde hata oluştu');
  }
}

function navigateToAccountingAccounts() {
  try {
    window.location.hash = '#accounting-accounts';
    window.toast.success('Cari hesaplar sayfasına yönlendiriliyor...');
  } catch (error) {
    console.error('Error navigating to accounting accounts page:', error);
    window.toast.error('Sayfa yönlendirmesinde hata oluştu');
  }
}

// Update the existing viewCariDetails function
window.viewCariDetails = function(cariId) {
  try {
    // Navigate to cari details page with the specific ID
    window.location.hash = `#accounting-accounts?cari=${cariId}`;
    window.toast.success('Cari hesap detayları yükleniyor...');
  } catch (error) {
    console.error('Error viewing cari details:', error);
    window.toast.error('Cari hesap detayları yüklenirken hata oluştu');
  }
};

// Standardized Accounting Accounts Page Module
// Following the new standardization guidelines

import { IconSystem } from '../utils/icon-system.js';
import { BadgeSystem } from '../utils/badge-system.js';
import { AccountingHeader } from '../components/accounting-header.js';
import { TableAnimations } from '../utils/table-animations.js';

// Standardized globals for this module
let passedI18n, passedContentArea, passedDefaultAvatarSVG;

let allAccounts = [];
let currentAccounts = [];
const itemsPerPage = 10;
let currentPage = 1;
let sortColumn = 'Id';
let sortDirection = 1; // 1 for asc, -1 for desc

// DOM element references
let accountsTableBody;
let accountModal;
let accountForm;
let accountModalTitle;
let currentEditingAccountId = null;
let accountDetailModal;
let pageInfo;
let prevButton;
let nextButton;

// Main render function following standardized pattern
export async function renderAccountingAccountsPage(contentArea, i18n, defaultAvatarSVG) {
  // Store passed parameters using standardized naming
  passedI18n = i18n;
  passedContentArea = contentArea;
  passedDefaultAvatarSVG = defaultAvatarSVG;
  const t = passedI18n.t;

  // Initialize accounting header immediately to prevent jumping
  const accountingHeader = new AccountingHeader({
    containerId: 'accounting-header',
    pageTitle: t('accounting_accounts_page_title') || 'Cari Hesaplar',
    pageIcon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
      <circle cx="9" cy="7" r="4"/>
      <path d="m22 21-3-3m0 0a5.5 5.5 0 1 0-7.78-7.78 5.5 5.5 0 0 0 7.78 7.78Z"/>
    </svg>`
  });

  // Generate page HTML using standardized structure
  contentArea.innerHTML = generatePageHTML(t);

  // Initialize accounting header immediately after HTML is created
  accountingHeader.init();

  // Initialize DOM references
  initializeDOMReferences();

  // Setup event listeners
  setupEventListeners();

  // Load initial data
  await loadInitialData();

  // Ensure modals are properly structured
  setTimeout(() => {
    if (window.reinitializeModals) {
      window.reinitializeModals();
    }

    // Debug modal structure
    if (window.debugModals) {
      console.log('🔍 Debugging account modals:');
      window.debugModals();
    }
  }, 100);
}

// Standardized HTML generation functions
function generatePageHTML(t) {
  return `
    <div class="page-container">
      <div id="accounting-header"></div>
      <div class="page-actions">
        <button id="add-account-button" class="btn btn-primary">
          ${generateAddIcon()}
          ${t('add_new_account') || 'Add New Account'}
        </button>
        <div class="filter-controls">
          ${generateFilterControls(t)}
        </div>
      </div>
      
      <table id="accounts-table" class="modern-table">
        ${generateTableHeader(t)}
        <tbody></tbody>
      </table>
      
      <div class="pagination-controls">
        ${generatePaginationControls(t)}
      </div>
      
      ${generateModal(t)}
      ${generateDetailModal(t)}
    </div>
  `;
}

function generateAddIcon() {
  return `<svg viewBox='0 0 20 20' fill='none' style="width:20px; height:20px; margin-right: 8px;">
    <circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/>
    <path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/>
  </svg>`;
}

function generateFilterControls(t) {
  // Filter controls removed - using table filters instead
  return '';
}

function generateTableHeader(t) {
  return `
    <thead>
      <tr>
        <th data-column-key="Id" class="sortable">${t('table_header_id') || 'ID'}</th>
        <th data-column-key="Unvan" class="sortable">${t('table_header_unvan') || 'Ünvan'}</th>
        <th data-column-key="Tip" class="sortable">${t('table_header_type') || 'Tip'}</th>
        <th data-column-key="Telefon" class="sortable">${t('table_header_phone') || 'Telefon'}</th>
        <th data-column-key="Email" class="sortable">${t('table_header_email') || 'Email'}</th>
        <th data-column-key="VergiNo" class="sortable">${t('table_header_tax_no') || 'Vergi No'}</th>
        <th class="col-actions">${t('table_header_actions') || 'İşlemler'}</th>
      </tr>
      <tr class="filter-row">
        <th><input type="number" id="filter-accounts-id" class="filter-input" placeholder="${t('table_header_id')}"></th>
        <th><input type="text" id="filter-accounts-unvan-table" class="filter-input" placeholder="${t('table_header_unvan')}"></th>
        <th>
          <select id="filter-accounts-tip-table" class="filter-input">
            <option value="">${t('option_all') || 'All'}</option>
            <option value="Musteri">${t('option_customer') || 'Customer'}</option>
            <option value="Tedarikci">${t('option_supplier') || 'Supplier'}</option>
            <option value="Diger">${t('option_other') || 'Other'}</option>
          </select>
        </th>
        <th><input type="text" id="filter-accounts-telefon" class="filter-input" placeholder="${t('table_header_phone')}"></th>
        <th><input type="text" id="filter-accounts-email" class="filter-input" placeholder="${t('table_header_email')}"></th>
        <th><input type="text" id="filter-accounts-vergi-no" class="filter-input" placeholder="${t('table_header_tax_no')}"></th>
        <th></th>
      </tr>
    </thead>
  `;
}

function generatePaginationControls(t) {
  return `
    <button id="prev-page-accounts" disabled>&laquo; ${t('btn_previous') || 'Previous'}</button>
    <span id="page-info-accounts">${t('page_info_template') || 'Page 1 / 1 (0 records)'}</span>
    <button id="next-page-accounts" disabled>${t('btn_next') || 'Next'} &raquo;</button>
  `;
}

function generateModal(t) {
  return `
    <div id="account-modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h3 id="account-modal-title">${t('account_modal_title_add') || 'Add New Account'}</h3>
        </div>
        <form id="account-form">
          <div class="modal-body">
          <!-- Basic Account Information Section -->
          <div class="form-section">
            <h3 class="section-title">${t('section_basic_info') || 'Temel Bilgiler'}</h3>
            <div class="form-grid">
              <div class="form-row">
                <label for="Unvan">${t('label_unvan') || 'Ünvan'}</label>
                <input type="text" name="Unvan" required />
              </div>
              <div class="form-row">
                <label for="Tip">${t('label_type') || 'Tip'}</label>
                <select name="Tip" required>
                  <option value="">${t('option_select') || 'Seçiniz...'}</option>
                  <option value="Musteri">${t('option_customer') || 'Müşteri'}</option>
                  <option value="Tedarikci">${t('option_supplier') || 'Tedarikçi'}</option>
                  <option value="Diger">${t('option_other') || 'Diğer'}</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Contact Information Section -->
          <div class="form-section">
            <h3 class="section-title">${t('section_contact_info') || 'İletişim Bilgileri'}</h3>
            <div class="form-grid">
              <div class="form-row">
                <label for="Telefon">${t('label_phone') || 'Telefon'}</label>
                <input type="tel" name="Telefon" />
              </div>
              <div class="form-row">
                <label for="Email">${t('label_email') || 'Email'}</label>
                <input type="email" name="Email" />
              </div>
              <div class="form-row full">
                <label for="Adres">${t('label_address') || 'Adres'}</label>
                <textarea name="Adres" rows="3"></textarea>
              </div>
            </div>
          </div>

          <!-- Tax Information Section -->
          <div class="form-section">
            <h3 class="section-title">${t('section_tax_info') || 'Vergi Bilgileri'}</h3>
            <div class="form-grid">
              <div class="form-row">
                <label for="VergiDairesi">${t('label_tax_office') || 'Vergi Dairesi'}</label>
                <input type="text" name="VergiDairesi" />
              </div>
              <div class="form-row">
                <label for="VergiNo">${t('label_tax_no') || 'Vergi No'}</label>
                <input type="text" name="VergiNo" />
              </div>
            </div>
          </div>

          <!-- Additional Information Section -->
          <div class="form-section">
            <h3 class="section-title">${t('section_additional_info') || 'Ek Bilgiler'}</h3>
            <div class="form-row full">
              <label for="Notlar">${t('label_notes') || 'Notlar'}</label>
              <textarea name="Notlar" rows="3"></textarea>
            </div>
          </div>

          <input type="hidden" name="Id" />
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('account-modal').classList.add('hidden')">${t('btn_cancel') || 'İptal'}</button>
              <button type="submit" class="btn btn-primary">${t('btn_save') || 'Kaydet'}</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  `;
}

function generateDetailModal(t) {
  return `
    <div id="account-detail-modal" class="modal hidden">
      <div class="modal-content account-detail-modal-content large">
        <div class="account-detail-header">
          <span class="close-btn">&times;</span>
          <div class="account-header-info">
            <div class="account-avatar">
              <div class="account-avatar-placeholder">
                <i class="fas fa-user-tie"></i>
              </div>
            </div>
            <div class="account-header-details">
              <h2 id="cari-detail-name" class="account-name-title">${t('cari_details') || 'Cari Detayları'}</h2>
              <div class="account-header-tags">
                <span id="cari-detail-type-tag" class="detail-tag tag-type">${t('type') || 'Tip'}</span>
                <span id="cari-detail-balance-tag" class="detail-tag tag-balance">₺0.00</span>
                <span id="cari-detail-status-tag" class="detail-tag tag-status">Aktif</span>
              </div>
            </div>
          </div>
        </div>

        <div class="account-detail-tabs detail-modal-tabs">
          <button class="tab-link detail-tab-link active" data-tab="cari-info-tab">
            <i class="fas fa-info-circle"></i>
            <span>${t('tab_general_info') || 'Genel Bilgiler'}</span>
          </button>
          <button class="tab-link detail-tab-link" data-tab="cari-balance-tab">
            <i class="fas fa-chart-line"></i>
            <span>${t('tab_balance_info') || 'Bakiye Bilgileri'}</span>
          </button>
          <button class="tab-link detail-tab-link" data-tab="cari-transactions-tab">
            <i class="fas fa-history"></i>
            <span>${t('tab_recent_transactions') || 'Son İşlemler'}</span>
          </button>
          <button class="tab-link detail-tab-link" data-tab="cari-stats-tab">
            <i class="fas fa-chart-pie"></i>
            <span>${t('tab_statistics') || 'İstatistikler'}</span>
          </button>
        </div>

        <div class="account-detail-body detail-modal-body">
          <div id="cari-info-tab" class="tab-content detail-tab-content active">
            <div class="info-sections detail-info-sections">
              <!-- Contact Information Section -->
              <div class="info-section">
                <div class="section-header detail-section-header">
                  <i class="fas fa-address-card"></i>
                  <h3>${t('section_contact_info') || 'İletişim Bilgileri'}</h3>
                </div>
                <div class="info-cards detail-info-cards">
                  <div class="info-card detail-info-card">
                    <div class="info-icon detail-info-icon">
                      <i class="fas fa-building"></i>
                    </div>
                    <div class="info-content">
                      <label>${t('label_unvan') || 'Ünvan'}</label>
                      <span id="detail-unvan" class="info-value">-</span>
                    </div>
                  </div>
                  <div class="info-card detail-info-card">
                    <div class="info-icon detail-info-icon">
                      <i class="fas fa-tag"></i>
                    </div>
                    <div class="info-content">
                      <label>${t('label_type') || 'Tip'}</label>
                      <span id="detail-tip" class="info-value">-</span>
                    </div>
                  </div>
                  <div class="info-card detail-info-card">
                    <div class="info-icon detail-info-icon">
                      <i class="fas fa-phone"></i>
                    </div>
                    <div class="info-content">
                      <label>${t('label_phone') || 'Telefon'}</label>
                      <span id="detail-telefon" class="info-value">-</span>
                    </div>
                  </div>
                  <div class="info-card detail-info-card">
                    <div class="info-icon detail-info-icon">
                      <i class="fas fa-envelope"></i>
                    </div>
                    <div class="info-content">
                      <label>${t('label_email') || 'Email'}</label>
                      <span id="detail-email" class="info-value">-</span>
                    </div>
                  </div>
                  <div class="info-card detail-info-card full-width">
                    <div class="info-icon detail-info-icon">
                      <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="info-content">
                      <label>${t('label_address') || 'Adres'}</label>
                      <span id="detail-adres" class="info-value">-</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Tax Information Section -->
              <div class="info-section">
                <div class="section-header detail-section-header">
                  <i class="fas fa-receipt"></i>
                  <h3>${t('section_tax_info') || 'Vergi Bilgileri'}</h3>
                </div>
                <div class="info-cards detail-info-cards">
                  <div class="info-card detail-info-card">
                    <div class="info-icon detail-info-icon">
                      <i class="fas fa-university"></i>
                    </div>
                    <div class="info-content">
                      <label>${t('label_tax_office') || 'Vergi Dairesi'}</label>
                      <span id="detail-vergi-dairesi" class="info-value">-</span>
                    </div>
                  </div>
                  <div class="info-card detail-info-card">
                    <div class="info-icon detail-info-icon">
                      <i class="fas fa-hashtag"></i>
                    </div>
                    <div class="info-content">
                      <label>${t('label_tax_no') || 'Vergi No'}</label>
                      <span id="detail-vergi-no" class="info-value">-</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Additional Notes Section -->
              <div class="info-section">
                <div class="section-header detail-section-header">
                  <i class="fas fa-sticky-note"></i>
                  <h3>${t('section_notes') || 'Notlar'}</h3>
                </div>
                <div class="notes-content detail-notes-content">
                  <p id="detail-notlar" class="notes-text">-</p>
                </div>
              </div>
            </div>
          </div>

          <div id="cari-balance-tab" class="tab-content detail-tab-content">
            <div id="balance-content" class="balance-dashboard detail-balance-dashboard">
              <div class="loading-spinner detail-loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <span>${t('loading') || 'Yükleniyor...'}</span>
              </div>
            </div>
          </div>

          <div id="cari-transactions-tab" class="tab-content detail-tab-content">
            <div id="transactions-content" class="transactions-list detail-transactions-list">
              <div class="loading-spinner detail-loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <span>${t('loading') || 'Yükleniyor...'}</span>
              </div>
            </div>
          </div>

          <div id="cari-stats-tab" class="tab-content detail-tab-content">
            <div id="stats-content" class="stats-dashboard detail-stats-dashboard">
              <div class="loading-spinner detail-loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <span>${t('loading') || 'Yükleniyor...'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

// Standardized initialization functions
function initializeDOMReferences() {
  accountsTableBody = passedContentArea.querySelector('#accounts-table tbody');
  accountModal = passedContentArea.querySelector('#account-modal');
  accountForm = passedContentArea.querySelector('#account-form');
  accountModalTitle = passedContentArea.querySelector('#account-modal-title');
  accountDetailModal = passedContentArea.querySelector('#account-detail-modal');
  pageInfo = passedContentArea.querySelector('#page-info-accounts');
  prevButton = passedContentArea.querySelector('#prev-page-accounts');
  nextButton = passedContentArea.querySelector('#next-page-accounts');
}

function setupEventListeners() {
  const t = passedI18n.t;

  // Add button
  const addButton = passedContentArea.querySelector('#add-account-button');
  addButton.addEventListener('click', () => openAccountModal());

  // Modal event listeners
  setupModalEventListeners();

  // Table sorting
  setupTableSorting();

  // Filters
  setupFilterEventListeners();

  // Pagination
  setupPaginationEventListeners();
}

function setupModalEventListeners() {
  // Main modal
  const closeBtn = accountModal.querySelector('.close-btn');
  closeBtn.onclick = closeAccountModal;
  accountModal.onclick = (e) => { if (e.target === accountModal) closeAccountModal(); };
  accountForm.onsubmit = handleFormSubmit;

  // Detail modal
  const detailCloseBtn = accountDetailModal.querySelector('.close-btn');
  detailCloseBtn.onclick = closeAccountDetailModal;
  accountDetailModal.onclick = (e) => { if (e.target === accountDetailModal) closeAccountDetailModal(); };

  // Tab switching in detail modal
  setupTabSwitching();
}

function setupTableSorting() {
  const sortableHeaders = passedContentArea.querySelectorAll('th.sortable[data-column-key]');
  sortableHeaders.forEach(header => {
    header.addEventListener('click', () => {
      const column = header.getAttribute('data-column-key');
      if (column) {
        // Clear all sorting classes
        sortableHeaders.forEach(th => {
          th.classList.remove('sorted-asc', 'sorted-desc');
        });

        // Sort the data
        sortAccountsBy(column);

        // Add visual feedback
        header.classList.add(sortDirection === 1 ? 'sorted-asc' : 'sorted-desc');

        // Re-render table
        renderTablePage();
      }
    });
  });
}

function setupFilterEventListeners() {
  const filterInputs = [
    '#filter-accounts-id', '#filter-accounts-unvan-table', '#filter-accounts-tip-table',
    '#filter-accounts-telefon', '#filter-accounts-email', '#filter-accounts-vergi-no'
  ];

  filterInputs.forEach(selector => {
    const element = passedContentArea.querySelector(selector);
    if (element) {
      element.addEventListener('input', applyFiltersAndSort);
      element.addEventListener('change', applyFiltersAndSort);
    }
  });
}

function setupPaginationEventListeners() {
  prevButton.addEventListener('click', () => {
    if (currentPage > 1) {
      currentPage--;
      renderTablePage();
    }
  });

  nextButton.addEventListener('click', () => {
    const totalPages = Math.ceil(currentAccounts.length / itemsPerPage);
    if (currentPage < totalPages) {
      currentPage++;
      renderTablePage();
    }
  });
}

function setupTabSwitching() {
  const tabLinks = accountDetailModal.querySelectorAll('.tab-link');
  tabLinks.forEach(link => {
    link.addEventListener('click', () => {
      const targetTab = link.getAttribute('data-tab');
      switchTab(link, targetTab);
    });
  });
}

async function loadInitialData() {
  try {
    allAccounts = await window.api.invoke('accounts:list');
    currentAccounts = [...allAccounts];
    applyFiltersAndSort();
  } catch (error) {
    console.error('Failed to load accounts:', error);
    const t = passedI18n.t;
    accountsTableBody.innerHTML = `<tr><td colspan="7">${t('error_loading_data') || 'Error loading data'}</td></tr>`;
  }
}

// Standardized sorting function
function sortAccountsBy(column) {
  if (sortColumn === column) {
    sortDirection *= -1;
  } else {
    sortColumn = column;
    sortDirection = 1;
  }

  currentAccounts.sort((a, b) => {
    let valA = a[column];
    let valB = b[column];

    if (valA === null || valA === undefined) valA = '';
    if (valB === null || valB === undefined) valB = '';

    if (column === 'Id') {
      valA = Number(valA);
      valB = Number(valB);
      return (valA - valB) * sortDirection;
    }

    if (typeof valA === 'string' && typeof valB === 'string') {
      return valA.localeCompare(valB, undefined, { sensitivity: 'base' }) * sortDirection;
    }

    if (valA < valB) return -1 * sortDirection;
    if (valA > valB) return 1 * sortDirection;
    return 0;
  });
}

// Standardized filtering and rendering
function applyFiltersAndSort() {
  const filters = {
    id: passedContentArea.querySelector('#filter-accounts-id')?.value || '',
    unvan: passedContentArea.querySelector('#filter-accounts-unvan-table')?.value || '',
    tip: passedContentArea.querySelector('#filter-accounts-tip-table')?.value || '',
    telefon: passedContentArea.querySelector('#filter-accounts-telefon')?.value || '',
    email: passedContentArea.querySelector('#filter-accounts-email')?.value || '',
    vergiNo: passedContentArea.querySelector('#filter-accounts-vergi-no')?.value || ''
  };

  currentAccounts = allAccounts.filter(account => {
    const matchesId = !filters.id || account.Id.toString().includes(filters.id);
    const matchesUnvan = !filters.unvan || account.Unvan?.toLowerCase().includes(filters.unvan.toLowerCase());
    const matchesTip = !filters.tip || account.Tip === filters.tip;
    const matchesTelefon = !filters.telefon || account.Telefon?.toLowerCase().includes(filters.telefon.toLowerCase());
    const matchesEmail = !filters.email || account.Email?.toLowerCase().includes(filters.email.toLowerCase());
    const matchesVergiNo = !filters.vergiNo || account.VergiNo?.toLowerCase().includes(filters.vergiNo.toLowerCase());

    return matchesId && matchesUnvan && matchesTip && matchesTelefon && matchesEmail && matchesVergiNo;
  });

  currentPage = 1;
  renderTablePage();
}

// Standardized table rendering
function renderTablePage() {
  const t = passedI18n.t;
  accountsTableBody.innerHTML = '';

  const totalPages = Math.ceil(currentAccounts.length / itemsPerPage);
  currentPage = Math.max(1, Math.min(currentPage, totalPages || 1));

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const pageItems = currentAccounts.slice(startIndex, endIndex);

  if (pageItems.length === 0) {
    accountsTableBody.innerHTML = generateEmptyState(t);
    updatePaginationInfo();
    return;
  }

  pageItems.forEach(account => {
    const row = generateTableRow(account, t);
    accountsTableBody.appendChild(row);
  });

  updatePaginationInfo();
  
  // Initialize table animations
  setTimeout(() => {
    TableAnimations.initializeTable('#accounts-table');
  }, 100);
}

function generateEmptyState(t) {
  return `<tr><td colspan="7">
    <div class="empty-state-container">
      <div class="empty-state-icon">${passedDefaultAvatarSVG ? passedDefaultAvatarSVG(48) : ''}</div>
      <p>${t('no_records_found') || 'Kayıt bulunamadı'}</p>
    </div>
  </td></tr>`;
}

function generateTableRow(account, t) {
  const row = document.createElement('tr');
  // Add clickable-row class for styling and cursor pointer
  row.classList.add('clickable-row');

  // Add accessibility attributes
  row.setAttribute('tabindex', '0');
  row.setAttribute('role', 'button');
  row.setAttribute('aria-label', `${t('view_account_details') || 'View account details'}: ${account.Unvan || account.Id}`);

  row.innerHTML = `
    <td>${account.Id || '-'}</td>
    <td>${account.Unvan || '-'}</td>
    <td>${BadgeSystem.createBusinessTypeBadge(account.Tip, { customer: t('account_type_musteri') || 'Customer', supplier: t('account_type_tedarikci') || 'Supplier' })}</td>
    <td>${account.Telefon || '-'}</td>
    <td>${account.Email || '-'}</td>
    <td>${account.VergiNo || '-'}</td>
    <td class="col-actions">
      ${IconSystem.createActionsWrapper(account.Id, { edit: true, delete: true }, { edit: t('btn_edit') || 'Edit', delete: t('btn_delete') || 'Delete' })}
    </td>
  `;

  // Add row click event listener (following animals table pattern)
  const handleRowClick = async (e) => {
    // Don't trigger row click if clicking on action buttons
    if (e.target.closest('.action-btn') || e.target.closest('.actions-wrapper')) {
      return;
    }
    await openAccountDetailModal(account);
  };

  row.addEventListener('click', handleRowClick);

  // Add keyboard accessibility
  row.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleRowClick(e);
    }
  });

  // Add event listeners to action buttons
  const editBtn = row.querySelector('.edit-btn');
  const deleteBtn = row.querySelector('.delete-btn');

  editBtn.addEventListener('click', (e) => {
    e.stopPropagation(); // Prevent row click
    openAccountModal(account);
  });

  deleteBtn.addEventListener('click', (e) => {
    e.stopPropagation(); // Prevent row click
    deleteAccount(account.Id);
  });

  return row;
}

function updatePaginationInfo() {
  const t = passedI18n.t;
  const totalPages = Math.ceil(currentAccounts.length / itemsPerPage);
  const totalRecords = currentAccounts.length;

  pageInfo.textContent = `${t('page') || 'Sayfa'} ${currentPage} / ${totalPages} (${totalRecords} ${t('records') || 'kayıt'})`;

  prevButton.disabled = currentPage <= 1;
  nextButton.disabled = currentPage >= totalPages;
}

// Standardized modal handling
function openAccountModal(accountData = null) {
  const t = passedI18n.t;
  accountForm.reset();

  if (accountData) {
    currentEditingAccountId = accountData.Id;
    accountModalTitle.textContent = t('account_modal_title_edit') || 'Account Edit';

    // Populate form fields
    accountForm.elements['Id'].value = accountData.Id;
    accountForm.elements['Unvan'].value = accountData.Unvan || '';
    accountForm.elements['Tip'].value = accountData.Tip || 'Musteri';
    accountForm.elements['Telefon'].value = accountData.Telefon || '';
    accountForm.elements['Email'].value = accountData.Email || '';
    accountForm.elements['Adres'].value = accountData.Adres || '';
    accountForm.elements['VergiDairesi'].value = accountData.VergiDairesi || '';
    accountForm.elements['VergiNo'].value = accountData.VergiNo || '';
    accountForm.elements['Notlar'].value = accountData.Notlar || '';
  } else {
    currentEditingAccountId = null;
    accountModalTitle.textContent = t('account_modal_title_add') || 'Add New Account';
  }

  accountModal.classList.remove('hidden');
}

function closeAccountModal() {
  accountModal.classList.add('hidden');
  currentEditingAccountId = null;
}

async function handleFormSubmit(e) {
  e.preventDefault();
  const t = passedI18n.t;

  const formData = new FormData(accountForm);
  const accountData = Object.fromEntries(formData.entries());

  try {
    if (currentEditingAccountId) {
      await window.api.invoke('accounts:update', { id: currentEditingAccountId, data: accountData });
      window.toast.success(t('toast_success_update'));
    } else {
      await window.api.invoke('accounts:add', accountData);
      window.toast.success(t('toast_success_save'));
    }

    closeAccountModal();
    await loadInitialData(); // Reload data
  } catch (error) {
    console.error('Failed to save account:', error);
    window.toast.error(t('error_saving_data') || 'Error saving data');
  }
}

async function deleteAccount(accountId) {
  const t = passedI18n.t;

  const confirmed = await window.toast.confirm(t('confirm_delete_account') || 'Are you sure you want to delete this account?', {
    confirmText: t('toast_confirm'),
    cancelText: t('toast_cancel')
  });

  if (!confirmed) {
    return;
  }

  try {
    await window.api.invoke('accounts:delete', accountId);
    await loadInitialData(); // Reload data
    window.toast.success(t('toast_success_delete'));
  } catch (error) {
    console.error('Failed to delete account:', error);
    window.toast.error(t('error_deleting_data') || 'Error deleting data');
  }
}

async function openAccountDetailModal(account) {
  const t = passedI18n.t;

  if (!account || typeof account !== 'object' || !account.Id) return;

  // Use the unified modal from index.html instead of generated one
  const modal = document.getElementById('cari-detail-modal');
  if (!modal) {
    console.error('Customer detail modal not found');
    return;
  }

  modal.classList.remove('hidden');

  // Update header
  const detailNameEl = modal.querySelector('#cari-detail-name');
  const detailTypeTagEl = modal.querySelector('#cari-detail-type-tag');
  const detailBalanceTagEl = modal.querySelector('#cari-detail-balance-tag');

  if (detailNameEl) detailNameEl.textContent = account.Unvan || t('unknown_account_name') || 'Unknown Account';
  if (detailTypeTagEl) {
    detailTypeTagEl.textContent = account.Tip || 'Unknown';
    // Add appropriate CSS class based on type
    const typeClass = account.Tip === 'Müşteri' ? 'tag-customer' :
                     account.Tip === 'Tedarikçi' ? 'tag-supplier' : '';
    detailTypeTagEl.className = `detail-tag ${typeClass}`;
  }
  if (detailBalanceTagEl) detailBalanceTagEl.textContent = '₺0.00'; // Will be updated by loadAccountBalance

  // Update general info tab
  updateUnifiedDetailInfo(modal, account, t);

  // Setup tab functionality
  setupUnifiedTabSystem(modal);

  // Setup close functionality
  setupUnifiedCloseSystem(modal);

  // Apply translations to modal
  applyModalTranslations(modal, t);

  // Setup account statement button
  setupAccountStatementButton(modal, account, t);

  // Load additional data for other tabs
  try {
    const details = await window.api.invoke('accounts:get-details', account.Id);
    updateUnifiedBalanceInfo(modal, details.balance || {}, t);
    updateUnifiedTransactionsInfo(modal, details.transactions || [], t);
  } catch (error) {
    console.error('Failed to load account details:', error);
  }
}

function closeAccountDetailModal() {
  const modal = document.getElementById('cari-detail-modal');
  if (modal) {
    modal.classList.add('hidden');
  }
}

// New unified functions for the standardized modal
function updateUnifiedDetailInfo(modal, account, t) {
  // Contact Information
  const fields = [
    { id: 'cari-detail-code', value: account.Id || '-' },
    { id: 'cari-detail-phone', value: account.Telefon || '-' },
    { id: 'cari-detail-email', value: account.Email || '-' },
    { id: 'cari-detail-address', value: account.Adres || '-' },
    { id: 'cari-detail-taxno', value: account.VergiNo || '-' },
    { id: 'cari-detail-taxoffice', value: account.VergiDairesi || '-' }
  ];

  fields.forEach(field => {
    const element = modal.querySelector(`#${field.id}`);
    if (element) {
      element.textContent = field.value;
    }
  });
}

function updateUnifiedBalanceInfo(modal, balance, t) {
  console.log('Balance data received:', balance); // Debug log

  const balanceFields = [
    { id: 'cari-detail-total-purchases', value: formatCurrency(balance.totalPurchases || 0) },
    { id: 'cari-detail-total-sales', value: formatCurrency(balance.totalSales || 0) },
    { id: 'cari-detail-total-payments-made', value: formatCurrency(balance.totalPurchasePayments || 0) },
    { id: 'cari-detail-total-payments-received', value: formatCurrency(balance.totalSalesPayments || 0) },
    { id: 'cari-detail-current-balance', value: formatCurrency(-(balance.netBalance || 0)) }
  ];

  balanceFields.forEach(field => {
    const element = modal.querySelector(`#${field.id}`);
    if (element) {
      element.textContent = field.value;

      // Add balance styling for current balance
      if (field.id === 'cari-detail-current-balance') {
        const balanceValue = balance.netBalance || 0;
        // Mantığı tersine çevir: pozitif bakiye = kırmızı, negatif bakiye = yeşil
        element.className = 'detail-info-value ' + (balanceValue >= 0 ? 'balance-negative' : 'balance-positive');
      }
    }
  });

  // Update header balance tag - show actual value with proper sign logic
  const headerBalanceTag = modal.querySelector('#cari-detail-balance-tag');
  if (headerBalanceTag) {
    const balanceValue = balance.netBalance || 0;
    // İşareti tersine çevir
    headerBalanceTag.textContent = formatCurrency(-balanceValue);
    // Mantığı tersine çevir: pozitif bakiye = kırmızı, negatif bakiye = yeşil
    headerBalanceTag.className = 'detail-tag ' + (balanceValue >= 0 ? 'balance-negative' : 'balance-positive');
  }
}

function updateUnifiedTransactionsInfo(modal, transactions, t) {
  console.log('Transactions data received:', transactions); // Debug log

  const tableBody = modal.querySelector('#cari-transactions-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  if (!transactions || transactions.length === 0) {
    const row = tableBody.insertRow();
    const cell = row.insertCell();
    cell.colSpan = 4;
    cell.textContent = t('no_transactions_found') || 'İşlem bulunamadı.';
    cell.style.textAlign = 'center';
    cell.style.padding = '20px';
    cell.style.color = 'var(--text-muted)';
    return;
  }

  transactions.slice(0, 10).forEach(transaction => {
    const row = tableBody.insertRow();

    // Format transaction type (backend uses 'Tip')
    let transactionType = '-';
    if (transaction.Tip) {
      transactionType = transaction.Tip;
    } else if (transaction.tip || transaction.type) {
      transactionType = transaction.tip || transaction.type;
    }

    // Format description (backend uses 'Aciklama')
    let description = transaction.Aciklama || transaction.aciklama || transaction.description || '-';

    // Format amount with proper sign (backend uses 'Tutar')
    let amount = transaction.Tutar || transaction.tutar || transaction.amount || 0;
    let formattedAmount = formatCurrency(Math.abs(amount));

    // Add color coding for positive/negative amounts
    let amountClass = amount >= 0 ? 'text-success' : 'text-danger';

    row.innerHTML = `
      <td>${formatDate(transaction.Tarih || transaction.tarih || transaction.date)}</td>
      <td>${transactionType}</td>
      <td>${description}</td>
      <td class="${amountClass}">${formattedAmount}</td>
    `;
  });
}

function setupUnifiedTabSystem(modal) {
  const tabLinks = modal.querySelectorAll('.detail-tab-link');
  const tabContents = modal.querySelectorAll('.detail-tab-content');

  function switchTab(activeTabLink) {
    tabLinks.forEach(link => link.classList.remove('active'));
    activeTabLink.classList.add('active');
    const targetTabId = activeTabLink.dataset.tab;
    tabContents.forEach(content => {
      content.classList.remove('active');
      if (content.id === targetTabId) {
        content.classList.add('active');
      }
    });
  }

  tabLinks.forEach(link => {
    link.onclick = () => switchTab(link);
  });

  // Activate the first tab by default
  if (tabLinks.length > 0) switchTab(tabLinks[0]);
}

function setupUnifiedCloseSystem(modal) {
  const closeBtn = modal.querySelector('.detail-close-btn');

  function closeModal() {
    modal.classList.add('hidden');
  }

  if (closeBtn) {
    closeBtn.onclick = closeModal;
  }

  modal.onclick = (e) => {
    if (e.target === modal) closeModal();
  };
}

// Helper functions
function formatCurrency(amount) {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY'
  }).format(amount);
}

function formatDate(dateString) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('tr-TR');
}

function applyModalTranslations(modal, t) {
  // Apply translations to all elements with data-i18n attribute
  modal.querySelectorAll('[data-i18n]').forEach(el => {
    const key = el.getAttribute('data-i18n');
    const translation = t(key);
    if (translation !== key) {
      el.textContent = translation;
    }
  });
}

// Setup account statement button functionality
function setupAccountStatementButton(modal, account, t) {
  const statementBtn = modal.querySelector('#account-statement-btn');
  if (!statementBtn) return;

  statementBtn.addEventListener('click', () => {
    openAccountStatementModal(account, t);
  });
}

// Close account statement modal
function closeStatementModal() {
  const statementModal = document.getElementById('account-statement-modal');
  if (statementModal) {
    statementModal.classList.add('hidden');
    document.body.style.overflow = ''; // Restore background scroll
  }
}

// Open account statement modal
function openAccountStatementModal(account, t) {
  const statementModal = document.getElementById('account-statement-modal');
  if (!statementModal) {
    console.error('Account statement modal not found');
    return;
  }

  // Set account info in modal title
  const modalTitle = statementModal.querySelector('#statement-account-title');
  if (modalTitle) {
    modalTitle.textContent = `${account.Unvan} - ${t('account_statement_title') || 'Hesap Ekstresi'}`;
  }

  // Setup date inputs with default values (last 30 days)
  const today = new Date();
  const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

  const startDateInput = statementModal.querySelector('#statement-start-date');
  const endDateInput = statementModal.querySelector('#statement-end-date');

  if (startDateInput) startDateInput.value = thirtyDaysAgo.toISOString().split('T')[0];
  if (endDateInput) endDateInput.value = today.toISOString().split('T')[0];

  // Setup generate button
  const generateBtn = statementModal.querySelector('#generate-statement-btn');
  if (generateBtn) {
    generateBtn.onclick = () => generateAccountStatement(account, t);
  }

  // Setup export buttons
  setupStatementExportButtons(account, t);

  // Setup close functionality
  const closeBtn = statementModal.querySelector('.statement-close-btn');
  if (closeBtn) {
    closeBtn.onclick = () => closeStatementModal();
  }

  // Close on overlay click
  statementModal.onclick = (e) => {
    if (e.target === statementModal) {
      closeStatementModal();
    }
  };

  // ESC key to close
  const handleEscKey = (e) => {
    if (e.key === 'Escape') {
      closeStatementModal();
      document.removeEventListener('keydown', handleEscKey);
    }
  };
  document.addEventListener('keydown', handleEscKey);

  // Show modal
  statementModal.classList.remove('hidden');
  document.body.style.overflow = 'hidden'; // Prevent background scroll
}

// Generate account statement
async function generateAccountStatement(account, t) {
  const statementModal = document.getElementById('account-statement-modal');
  const startDate = statementModal.querySelector('#statement-start-date').value;
  const endDate = statementModal.querySelector('#statement-end-date').value;

  if (!startDate || !endDate) {
    window.showToast('error', t('error') || 'Hata', t('please_select_date_range') || 'Lütfen tarih aralığı seçin.');
    return;
  }

  const loadingDiv = statementModal.querySelector('#statement-loading');
  const resultsDiv = statementModal.querySelector('#statement-results');
  const exportButtonsContainer = statementModal.querySelector('#statement-export-buttons');

  // Show loading state
  loadingDiv.style.display = 'flex';
  if (resultsDiv) resultsDiv.style.display = 'none';
  if (exportButtonsContainer) exportButtonsContainer.style.display = 'none';

  try {
    const statementData = await window.api.invoke('accounting:account-statement', {
      accountId: account.Id,
      startDate,
      endDate
    });

    // Update header information
    updateStatementHeader(statementData, startDate, endDate, t);

    // Update transactions table
    updateStatementTable(statementData.transactions, t);

    // Show results and hide loading
    loadingDiv.style.display = 'none';
    const resultsDiv = statementModal.querySelector('#statement-results');
    const exportButtonsContainer = statementModal.querySelector('#statement-export-buttons');

    if (resultsDiv) resultsDiv.style.display = 'block';
    if (exportButtonsContainer) exportButtonsContainer.style.display = 'flex';

    // Store data for export
    window.currentStatementData = statementData;

  } catch (error) {
    console.error('Failed to generate account statement:', error);
    loadingDiv.innerHTML = `<p style="color: red;">${t('error_generating_statement') || 'Hesap ekstresi oluşturulurken hata oluştu.'}</p>`;
    window.showToast('error', t('error') || 'Hata', t('error_generating_statement') || 'Hesap ekstresi oluşturulurken hata oluştu.');
  }
}

// Update statement header with account and summary info
function updateStatementHeader(statementData, startDate, endDate, t) {
  const statementModal = document.getElementById('account-statement-modal');

  // Account name
  const accountNameEl = statementModal.querySelector('#statement-account-name');
  if (accountNameEl) {
    accountNameEl.textContent = statementData.account.Unvan || t('unknown_account') || 'Bilinmeyen Cari';
  }

  // Account type
  const accountTypeEl = statementModal.querySelector('#statement-account-type');
  if (accountTypeEl) {
    accountTypeEl.textContent = statementData.account.Tip || t('unknown_type') || 'Bilinmeyen Tip';
  }

  // Date range
  const dateRangeEl = statementModal.querySelector('#statement-date-range');
  if (dateRangeEl) {
    const formattedStartDate = new Date(startDate).toLocaleDateString('tr-TR');
    const formattedEndDate = new Date(endDate).toLocaleDateString('tr-TR');
    dateRangeEl.textContent = `${formattedStartDate} - ${formattedEndDate}`;
  }

  // Summary amounts
  const summary = statementData.summary;

  const totalDebitEl = statementModal.querySelector('#statement-total-debit');
  if (totalDebitEl) {
    totalDebitEl.textContent = formatCurrency(summary.totalDebit || 0);
  }

  const totalCreditEl = statementModal.querySelector('#statement-total-credit');
  if (totalCreditEl) {
    totalCreditEl.textContent = formatCurrency(summary.totalCredit || 0);
  }

  const finalBalanceEl = statementModal.querySelector('#statement-final-balance');
  if (finalBalanceEl) {
    const balance = summary.finalBalance || 0;
    finalBalanceEl.textContent = formatCurrency(balance);
    finalBalanceEl.className = `amount balance ${balance >= 0 ? 'positive' : 'negative'}`;
  }
}

// Update statement transactions table
function updateStatementTable(transactions, t) {
  const tableBody = document.querySelector('#statement-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  if (!transactions || transactions.length === 0) {
    const row = tableBody.insertRow();
    const cell = row.insertCell();
    cell.colSpan = 7;
    cell.textContent = t('no_transactions_found') || 'Bu tarih aralığında işlem bulunamadı.';
    cell.style.textAlign = 'center';
    cell.style.padding = '20px';
    cell.style.color = 'var(--text-muted)';
    return;
  }

  transactions.forEach(transaction => {
    const row = tableBody.insertRow();

    // Date
    const dateCell = row.insertCell();
    dateCell.textContent = transaction.FormattedDate || formatDate(transaction.Tarih);

    // Type
    const typeCell = row.insertCell();
    typeCell.textContent = transaction.IslemTipi || '-';

    // Invoice No
    const invoiceCell = row.insertCell();
    invoiceCell.textContent = transaction.FaturaNo || '-';

    // Description
    const descCell = row.insertCell();
    const description = transaction.UrunAdi ?
      `${transaction.UrunAdi} (${transaction.Miktar || 1} x ${formatCurrency(transaction.BirimFiyat || 0)})` :
      transaction.Aciklama || '-';
    descCell.textContent = description;

    // Debit (Carinin borcu = bizim alacağımız)
    const debitCell = row.insertCell();
    debitCell.className = 'amount-cell';
    if (transaction.Alacak > 0) {
      debitCell.textContent = formatCurrency(transaction.Alacak);
      debitCell.classList.add('debit-amount');
    } else {
      debitCell.textContent = '-';
    }

    // Credit (Carinin alacağı = bizim borcumuz)
    const creditCell = row.insertCell();
    creditCell.className = 'amount-cell';
    if (transaction.Borc > 0) {
      creditCell.textContent = formatCurrency(transaction.Borc);
      creditCell.classList.add('credit-amount');
    } else {
      creditCell.textContent = '-';
    }

    // Balance (Carinin perspektifinden)
    const balanceCell = row.insertCell();
    balanceCell.className = 'amount-cell balance-amount';
    const balance = -(transaction.Bakiye || 0); // İşareti tersine çevir
    balanceCell.textContent = formatCurrency(balance);
    balanceCell.classList.add(balance >= 0 ? 'balance-positive' : 'balance-negative');
  });
}



// Setup export buttons for statement
function setupStatementExportButtons(account, t) {
  const statementModal = document.getElementById('account-statement-modal');

  // PDF Export
  const pdfBtn = statementModal.querySelector('#export-statement-pdf');
  if (pdfBtn) {
    pdfBtn.onclick = () => exportStatementToPDF(account, t);
  }

  // Excel Export
  const excelBtn = statementModal.querySelector('#export-statement-excel');
  if (excelBtn) {
    excelBtn.onclick = () => exportStatementToExcel(account, t);
  }

  // Print
  const printBtn = statementModal.querySelector('#print-statement');
  if (printBtn) {
    printBtn.onclick = () => printStatement(account, t);
  }
}

// Export statement to PDF
function exportStatementToPDF(account, t) {
  if (!window.currentStatementData) {
    window.showToast('error', t('error') || 'Hata', t('no_data_to_export') || 'Dışa aktarılacak veri bulunamadı.');
    return;
  }

  if (typeof pdfMake === 'undefined') {
    window.showToast('error', t('error') || 'Hata', t('pdf_library_not_loaded') || 'PDF kütüphanesi yüklenemedi.');
    return;
  }

  const data = window.currentStatementData;
  const startDate = document.querySelector('#statement-start-date').value;
  const endDate = document.querySelector('#statement-end-date').value;

  // Prepare table data
  const tableHeaders = [
    t('label_date') || 'Tarih',
    t('label_type') || 'İşlem Tipi',
    t('label_invoice_no') || 'Fatura No',
    t('label_description') || 'Açıklama',
    t('label_debit') || 'Borç',
    t('label_credit') || 'Alacak',
    t('label_balance') || 'Bakiye'
  ];

  const tableBody = data.transactions.map(transaction => [
    transaction.FormattedDate || formatDate(transaction.Tarih),
    transaction.IslemTipi || '-',
    transaction.FaturaNo || '-',
    transaction.UrunAdi ?
      `${transaction.UrunAdi} (${transaction.Miktar || 1} x ${formatCurrency(transaction.BirimFiyat || 0)})` :
      transaction.Aciklama || '-',
    transaction.Alacak > 0 ? formatCurrency(transaction.Alacak) : '-',
    transaction.Borc > 0 ? formatCurrency(transaction.Borc) : '-',
    formatCurrency(-(transaction.Bakiye || 0))
  ]);

  const documentDefinition = {
    content: [
      { text: `${account.Unvan} - ${t('account_statement_title') || 'Hesap Ekstresi'}`, style: 'header' },
      { text: `${t('date_range') || 'Tarih Aralığı'}: ${new Date(startDate).toLocaleDateString('tr-TR')} - ${new Date(endDate).toLocaleDateString('tr-TR')}`, style: 'subheader' },
      { text: ' ', margin: [0, 10] },
      {
        table: {
          headerRows: 1,
          widths: ['auto', 'auto', 'auto', '*', 'auto', 'auto', 'auto'],
          body: [tableHeaders, ...tableBody]
        },
        layout: 'lightHorizontalLines'
      },
      { text: ' ', margin: [0, 20] },
      {
        table: {
          widths: ['*', 'auto'],
          body: [
            [t('label_total_debit') || 'Toplam Borç', formatCurrency(data.summary.totalDebit || 0)],
            [t('label_total_credit') || 'Toplam Alacak', formatCurrency(data.summary.totalCredit || 0)],
            [t('label_final_balance') || 'Son Bakiye', formatCurrency(-(data.summary.finalBalance || 0))]
          ]
        },
        layout: 'noBorders'
      }
    ],
    styles: {
      header: { fontSize: 18, bold: true, margin: [0, 0, 0, 10] },
      subheader: { fontSize: 14, margin: [0, 0, 0, 5] }
    }
  };

  const fileName = `${account.Unvan.replace(/\s+/g, '_')}_hesap_ekstresi_${new Date().toISOString().slice(0,10)}.pdf`;
  pdfMake.createPdf(documentDefinition).download(fileName);
}

// Export statement to Excel
function exportStatementToExcel(account, t) {
  if (!window.currentStatementData) {
    window.showToast('error', t('error') || 'Hata', t('no_data_to_export') || 'Dışa aktarılacak veri bulunamadı.');
    return;
  }

  if (typeof XLSX === 'undefined') {
    window.showToast('error', t('error') || 'Hata', t('excel_library_not_loaded') || 'Excel kütüphanesi yüklenemedi.');
    return;
  }

  const data = window.currentStatementData;
  const startDate = document.querySelector('#statement-start-date').value;
  const endDate = document.querySelector('#statement-end-date').value;

  // Prepare worksheet data
  const headers = [
    t('label_date') || 'Tarih',
    t('label_type') || 'İşlem Tipi',
    t('label_invoice_no') || 'Fatura No',
    t('label_description') || 'Açıklama',
    t('label_debit') || 'Borç',
    t('label_credit') || 'Alacak',
    t('label_balance') || 'Bakiye'
  ];

  const rows = data.transactions.map(transaction => [
    transaction.FormattedDate || formatDate(transaction.Tarih),
    transaction.IslemTipi || '-',
    transaction.FaturaNo || '-',
    transaction.UrunAdi ?
      `${transaction.UrunAdi} (${transaction.Miktar || 1} x ${formatCurrency(transaction.BirimFiyat || 0)})` :
      transaction.Aciklama || '-',
    transaction.Alacak > 0 ? transaction.Alacak : 0,
    transaction.Borc > 0 ? transaction.Borc : 0,
    -(transaction.Bakiye || 0)
  ]);

  // Add summary rows
  rows.push([]);
  rows.push([t('label_total_debit') || 'Toplam Borç', '', '', '', data.summary.totalDebit || 0, '', '']);
  rows.push([t('label_total_credit') || 'Toplam Alacak', '', '', '', '', data.summary.totalCredit || 0, '']);
  rows.push([t('label_final_balance') || 'Son Bakiye', '', '', '', '', '', data.summary.finalBalance || 0]);

  const ws_data = [headers, ...rows];
  const ws = XLSX.utils.aoa_to_sheet(ws_data);

  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, t('account_statement') || 'Hesap Ekstresi');

  const fileName = `${account.Unvan.replace(/\s+/g, '_')}_hesap_ekstresi_${new Date().toISOString().slice(0,10)}.xlsx`;
  XLSX.writeFile(wb, fileName);
}

// Print statement
function printStatement(account, t) {
  if (!window.currentStatementData) {
    window.showToast('error', t('error') || 'Hata', t('no_data_to_export') || 'Dışa aktarılacak veri bulunamadı.');
    return;
  }

  const data = window.currentStatementData;
  const startDate = document.querySelector('#statement-start-date').value;
  const endDate = document.querySelector('#statement-end-date').value;

  // Create print window
  const printWindow = window.open('', '_blank');

  const printContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>${account.Unvan} - ${t('account_statement_title') || 'Hesap Ekstresi'}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 20px; }
        .header h1 { margin: 0; color: #333; }
        .header p { margin: 5px 0; color: #666; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; font-weight: bold; }
        .amount { text-align: right; }
        .summary { margin-top: 20px; }
        .summary table { width: 300px; margin-left: auto; }
        .debit { color: #ef4444; }
        .credit { color: #10b981; }
        .balance-positive { color: #10b981; }
        .balance-negative { color: #ef4444; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>${account.Unvan}</h1>
        <h2>${t('account_statement_title') || 'Hesap Ekstresi'}</h2>
        <p>${t('date_range') || 'Tarih Aralığı'}: ${new Date(startDate).toLocaleDateString('tr-TR')} - ${new Date(endDate).toLocaleDateString('tr-TR')}</p>
      </div>

      <table>
        <thead>
          <tr>
            <th>${t('label_date') || 'Tarih'}</th>
            <th>${t('label_type') || 'İşlem Tipi'}</th>
            <th>${t('label_invoice_no') || 'Fatura No'}</th>
            <th>${t('label_description') || 'Açıklama'}</th>
            <th class="amount">${t('label_debit') || 'Borç'}</th>
            <th class="amount">${t('label_credit') || 'Alacak'}</th>
            <th class="amount">${t('label_balance') || 'Bakiye'}</th>
          </tr>
        </thead>
        <tbody>
          ${data.transactions.map(transaction => `
            <tr>
              <td>${transaction.FormattedDate || formatDate(transaction.Tarih)}</td>
              <td>${transaction.IslemTipi || '-'}</td>
              <td>${transaction.FaturaNo || '-'}</td>
              <td>${transaction.UrunAdi ?
                `${transaction.UrunAdi} (${transaction.Miktar || 1} x ${formatCurrency(transaction.BirimFiyat || 0)})` :
                transaction.Aciklama || '-'}</td>
              <td class="amount ${transaction.Alacak > 0 ? 'debit' : ''}">${transaction.Alacak > 0 ? formatCurrency(transaction.Alacak) : '-'}</td>
              <td class="amount ${transaction.Borc > 0 ? 'credit' : ''}">${transaction.Borc > 0 ? formatCurrency(transaction.Borc) : '-'}</td>
              <td class="amount ${(-(transaction.Bakiye || 0)) >= 0 ? 'balance-positive' : 'balance-negative'}">${formatCurrency(-(transaction.Bakiye || 0))}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div class="summary">
        <table>
          <tr>
            <td><strong>${t('label_total_debit') || 'Toplam Borç'}:</strong></td>
            <td class="amount debit">${formatCurrency(data.summary.totalDebit || 0)}</td>
          </tr>
          <tr>
            <td><strong>${t('label_total_credit') || 'Toplam Alacak'}:</strong></td>
            <td class="amount credit">${formatCurrency(data.summary.totalCredit || 0)}</td>
          </tr>
          <tr>
            <td><strong>${t('label_final_balance') || 'Son Bakiye'}:</strong></td>
            <td class="amount ${(data.summary.finalBalance || 0) >= 0 ? 'balance-positive' : 'balance-negative'}">${formatCurrency(data.summary.finalBalance || 0)}</td>
          </tr>
        </table>
      </div>
    </body>
    </html>
  `;

  printWindow.document.write(printContent);
  printWindow.document.close();
  printWindow.focus();
  printWindow.print();
}

// Debug function for customer modal data
window.debugCustomerModal = function(accountId) {
  console.log('🔍 Customer Modal Debug for account:', accountId);

  if (accountId) {
    window.api.invoke('accounts:get-details', accountId)
      .then(details => {
        console.log('📋 Account Details Response:', details);
        console.log('💰 Balance Data:', details.balance);
        console.log('📊 Transactions Data:', details.transactions);
        console.log('📊 Transactions Count:', details.transactions?.length || 0);
      })
      .catch(error => {
        console.error('❌ Error fetching account details:', error);
      });
  }
};

// Test function for account statement modal
window.testAccountStatementModal = function() {
  console.log('🧪 Testing account statement modal...');

  const testAccount = {
    Id: 1,
    Unvan: 'Test Account',
    Tip: 'Müşteri'
  };

  const mockT = (key) => key;

  openAccountStatementModal(testAccount, mockT);
};

function updateDetailInfo(account) {
  const fields = ['unvan', 'tip', 'telefon', 'email', 'adres', 'vergi-dairesi', 'vergi-no', 'notlar'];
  const mapping = {
    'unvan': 'Unvan',
    'tip': 'Tip',
    'telefon': 'Telefon',
    'email': 'Email',
    'adres': 'Adres',
    'vergi-dairesi': 'VergiDairesi',
    'vergi-no': 'VergiNo',
    'notlar': 'Notlar'
  };

  fields.forEach(field => {
    const element = accountDetailModal.querySelector(`#detail-${field}`);
    if (element) {
      element.textContent = account[mapping[field]] || '-';
    }
  });
}

function updateBalanceInfo(balance) {
  const balanceContent = accountDetailModal.querySelector('#balance-content');
  const t = passedI18n.t;

  balanceContent.innerHTML = `
    <div class="balance-summary">
      <div class="balance-item">
        <label>${t('total_debt') || 'Toplam Borç'}:</label>
        <span class="amount debt">${balance.totalDebt || 0} ₺</span>
      </div>
      <div class="balance-item">
        <label>${t('total_credit') || 'Toplam Alacak'}:</label>
        <span class="amount credit">${balance.totalCredit || 0} ₺</span>
      </div>
      <div class="balance-item">
        <label>${t('net_balance') || 'Net Bakiye'}:</label>
        <span class="amount ${balance.netBalance >= 0 ? 'credit' : 'debt'}">${balance.netBalance || 0} ₺</span>
      </div>
    </div>
  `;
}

function updateTransactionsInfo(transactions) {
  const transactionsContent = accountDetailModal.querySelector('#transactions-content');
  const t = passedI18n.t;

  if (!transactions || transactions.length === 0) {
    transactionsContent.innerHTML = `<p>${t('no_transactions_found') || 'İşlem bulunamadı'}</p>`;
    return;
  }

  const transactionsHtml = transactions.map(transaction => `
    <div class="transaction-item">
      <div class="transaction-date">${transaction.Tarih || '-'}</div>
      <div class="transaction-type">${transaction.Tip || '-'}</div>
      <div class="transaction-amount ${transaction.Tutar >= 0 ? 'credit' : 'debt'}">${transaction.Tutar || 0} ₺</div>
    </div>
  `).join('');

  transactionsContent.innerHTML = `<div class="transactions-list">${transactionsHtml}</div>`;
}

function switchTab(tabLink, targetTab) {
  // Remove active class from all tabs and content
  accountDetailModal.querySelectorAll('.tab-link').forEach(link => link.classList.remove('active'));
  accountDetailModal.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

  // Add active class to clicked tab and corresponding content
  tabLink.classList.add('active');
  const targetContent = accountDetailModal.querySelector(`#${targetTab}`);
  if (targetContent) {
    targetContent.classList.add('active');
  }
}

// Enhanced account details display
function showAccountDetails(account) {
  // Populate modal header with account data
  document.getElementById('cari-detail-name').textContent = account.Unvan || 'Bilinmeyen Cari';

  // Update header tags
  const typeTag = document.getElementById('cari-detail-type-tag');
  const balanceTag = document.getElementById('cari-detail-balance-tag');
  const statusTag = document.getElementById('cari-detail-status-tag');

  // Set type tag
  typeTag.textContent = account.Tip || 'Bilinmeyen';

  // Set balance tag (placeholder - will be updated by loadAccountBalance)
  balanceTag.textContent = '₺0.00';

  // Set status tag
  statusTag.textContent = 'Aktif';

  // Update avatar based on type
  const avatarIcon = document.querySelector('.account-avatar-placeholder i');
  if (avatarIcon) {
    if (account.Tip === 'Musteri') {
      avatarIcon.className = 'fas fa-user-tie';
    } else if (account.Tip === 'Tedarikci') {
      avatarIcon.className = 'fas fa-truck';
    } else {
      avatarIcon.className = 'fas fa-building';
    }
  }

  // Populate info fields with enhanced display
  populateInfoField('detail-unvan', account.Unvan);
  populateInfoField('detail-tip', account.Tip);
  populateInfoField('detail-telefon', account.Telefon, formatPhone);
  populateInfoField('detail-email', account.Email, formatEmail);
  populateInfoField('detail-adres', account.Adres);
  populateInfoField('detail-vergi-dairesi', account.VergiDairesi);
  populateInfoField('detail-vergi-no', account.VergiNo);
  populateInfoField('detail-notlar', account.Notlar || 'Henüz not eklenmemiş.');

  // Show modal
  document.getElementById('account-detail-modal').classList.remove('hidden');

  // Load additional data
  loadAccountBalance(account.Id);
  loadAccountTransactions(account.Id);
  loadAccountStats(account.Id);
}

function populateInfoField(elementId, value, formatter = null) {
  const element = document.getElementById(elementId);
  if (element) {
    if (value && value.trim() !== '') {
      element.textContent = formatter ? formatter(value) : value;
      element.classList.remove('empty-value');
    } else {
      element.textContent = 'Belirtilmemiş';
      element.classList.add('empty-value');
    }
  }
}

function formatPhone(phone) {
  // Simple phone formatting
  if (phone && phone.length >= 10) {
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
  }
  return phone;
}

function formatEmail(email) {
  // Add mailto link styling
  return email;
}

// Enhanced data loading functions
function loadAccountBalance(accountId) {
  const balanceContent = document.getElementById('balance-content');
  const balanceTag = document.getElementById('cari-detail-balance-tag');

  // Simulate loading
  setTimeout(() => {
    const mockBalance = (Math.random() * 50000 - 25000).toFixed(2);
    const isPositive = parseFloat(mockBalance) >= 0;

    // Update balance tag
    balanceTag.textContent = `₺${Math.abs(mockBalance)}`;
    balanceTag.className = `detail-tag ${isPositive ? 'tag-balance' : 'tag-debt'}`;

    // Create balance dashboard
    balanceContent.innerHTML = `
      <div class="balance-summary">
        <div class="balance-card ${isPositive ? 'positive' : 'negative'}">
          <div class="balance-icon">
            <i class="fas ${isPositive ? 'fa-arrow-up' : 'fa-arrow-down'}"></i>
          </div>
          <div class="balance-info">
            <h3>Mevcut Bakiye</h3>
            <div class="balance-amount">₺${mockBalance}</div>
            <div class="balance-status">${isPositive ? 'Alacak' : 'Borç'}</div>
          </div>
        </div>
        <div class="balance-details">
          <div class="balance-item">
            <span class="label">Toplam Satış:</span>
            <span class="value">₺${(Math.random() * 100000).toFixed(2)}</span>
          </div>
          <div class="balance-item">
            <span class="label">Toplam Ödeme:</span>
            <span class="value">₺${(Math.random() * 80000).toFixed(2)}</span>
          </div>
          <div class="balance-item">
            <span class="label">Son İşlem:</span>
            <span class="value">${new Date().toLocaleDateString('tr-TR')}</span>
          </div>
        </div>
      </div>
    `;
  }, 1000);
}

function loadAccountTransactions(accountId) {
  const transactionsContent = document.getElementById('transactions-content');

  // Simulate loading
  setTimeout(() => {
    const mockTransactions = [
      { date: '2024-01-15', type: 'Satış', amount: 5000, description: 'Süt satışı' },
      { date: '2024-01-10', type: 'Ödeme', amount: -2000, description: 'Yem alımı' },
      { date: '2024-01-05', type: 'Satış', amount: 3500, description: 'Et satışı' }
    ];

    transactionsContent.innerHTML = `
      <div class="transactions-header">
        <h3>Son İşlemler</h3>
        <button class="btn btn-sm btn-outline">Tümünü Gör</button>
      </div>
      <div class="transactions-items">
        ${mockTransactions.map(transaction => `
          <div class="transaction-item">
            <div class="transaction-icon ${transaction.amount > 0 ? 'positive' : 'negative'}">
              <i class="fas ${transaction.amount > 0 ? 'fa-plus' : 'fa-minus'}"></i>
            </div>
            <div class="transaction-details">
              <div class="transaction-description">${transaction.description}</div>
              <div class="transaction-meta">${transaction.type} • ${transaction.date}</div>
            </div>
            <div class="transaction-amount ${transaction.amount > 0 ? 'positive' : 'negative'}">
              ₺${Math.abs(transaction.amount).toFixed(2)}
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }, 1500);
}

function loadAccountStats(accountId) {
  const statsContent = document.getElementById('stats-content');

  // Simulate loading
  setTimeout(() => {
    statsContent.innerHTML = `
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">${Math.floor(Math.random() * 50) + 10}</div>
            <div class="stat-label">Toplam İşlem</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-calendar"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">${Math.floor(Math.random() * 365) + 30}</div>
            <div class="stat-label">Gün Aktif</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-star"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">${(Math.random() * 2 + 3).toFixed(1)}</div>
            <div class="stat-label">Ortalama Puan</div>
          </div>
        </div>
      </div>
    `;
  }, 2000);
}

// Initialize enhanced tab functionality
function initializeAccountDetailTabs() {
  const accountDetailModal = document.getElementById('account-detail-modal');
  if (accountDetailModal) {
    // Add click event listeners to tab buttons
    accountDetailModal.querySelectorAll('.tab-link').forEach(tabButton => {
      tabButton.addEventListener('click', function() {
        const targetTab = this.getAttribute('data-tab');

        // Remove active class from all tabs and content
        accountDetailModal.querySelectorAll('.tab-link').forEach(tab => tab.classList.remove('active'));
        accountDetailModal.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // Add active class to clicked tab and corresponding content
        this.classList.add('active');
        const targetContent = accountDetailModal.querySelector(`#${targetTab}`);
        if (targetContent) {
          targetContent.classList.add('active');
        }
      });
    });
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  initializeAccountDetailTabs();
});

<!DOCTYPE html>
<html lang="tr">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SürüYönet - Giriş</title>
  <!-- FontAwesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Foundation CSS -->
  <link rel="stylesheet" href="css/01-foundation/reset.css">
  <link rel="stylesheet" href="css/01-foundation/tokens.css">
  <link rel="stylesheet" href="css/01-foundation/themes.css">
  <!-- Component CSS -->
  <link rel="stylesheet" href="css/03-components/notifications.css">
  <!-- Login styles -->
  <link rel="stylesheet" href="login-style.css">
</head>
<body>
  <div class="login-container">
    <div class="login-card">
      <!-- Logo and Title -->
      <div class="login-header">
        <div class="logo">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 2L2 7l10 5 10-5-10-5z"/>
            <path d="M2 17l10 5 10-5"/>
            <path d="M2 12l10 5 10-5"/>
            <path d="M4.14285 15.8903C4.14285 18.2137 6.14285 20.1273 8.71428 20.1273H15.2857C17.8571 20.1273 19.8571 18.2137 19.8571 15.8903" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <h1>SürüYönet</h1>
        </div>
        <p class="subtitle">Canlı Hayvan Yönetim Sistemi</p>
      </div>

      <!-- Login Form -->
      <div class="form-container">
        <div class="form-tabs">
          <button class="tab-btn active" data-tab="login">
            <i class="fas fa-sign-in-alt"></i>
            Giriş Yap
          </button>
          <button class="tab-btn" data-tab="register">
            <i class="fas fa-user-plus"></i>
            Kayıt Ol
          </button>
        </div>

        <!-- Login Form -->
        <form id="login-form" class="auth-form active">
          <div class="form-group">
            <label for="login-username">
              <i class="fas fa-user"></i>
              Kullanıcı Adı veya E-posta
            </label>
            <input type="text" id="login-username" name="kullaniciAdi" required>
          </div>

          <div class="form-group">
            <label for="login-password">
              <i class="fas fa-lock"></i>
              Şifre
            </label>
            <div class="password-input">
              <input type="password" id="login-password" name="sifre" required>
              <button type="button" class="password-toggle" data-target="login-password">
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>

          <div class="form-group">
            <div class="remember-me">
              <input type="checkbox" id="remember-me" name="beniHatirla">
              <label for="remember-me" class="checkbox-label">
                <span class="checkmark"></span>
                Beni Hatırla
              </label>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn-primary">
              <i class="fas fa-sign-in-alt"></i>
              Giriş Yap
            </button>
          </div>
        </form>

        <!-- Registration Form -->
        <form id="register-form" class="auth-form">
          <div class="form-group">
            <label for="register-fullname">
              <i class="fas fa-id-card"></i>
              Ad Soyad
            </label>
            <input type="text" id="register-fullname" name="adSoyad" required>
          </div>

          <div class="form-group">
            <label for="register-username">
              <i class="fas fa-user"></i>
              Kullanıcı Adı
            </label>
            <input type="text" id="register-username" name="kullaniciAdi" required>
          </div>

          <div class="form-group">
            <label for="register-email">
              <i class="fas fa-envelope"></i>
              E-posta
            </label>
            <input type="email" id="register-email" name="email" required>
          </div>

          <div class="form-group">
            <label for="register-password">
              <i class="fas fa-lock"></i>
              Şifre
            </label>
            <div class="password-input">
              <input type="password" id="register-password" name="sifre" required minlength="6">
              <button type="button" class="password-toggle" data-target="register-password">
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>

          <div class="form-group">
            <label for="register-password-confirm">
              <i class="fas fa-lock"></i>
              Şifre Tekrar
            </label>
            <div class="password-input">
              <input type="password" id="register-password-confirm" name="sifreTekrar" required minlength="6">
              <button type="button" class="password-toggle" data-target="register-password-confirm">
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn-primary">
              <i class="fas fa-user-plus"></i>
              Kayıt Ol
            </button>
          </div>
        </form>
      </div>

      <!-- Loading Overlay -->
      <div id="loading-overlay" class="loading-overlay hidden">
        <div class="spinner"></div>
        <p>İşlem yapılıyor...</p>
      </div>
    </div>
  </div>

  <!-- Toast Container -->
  <div id="toast-container" class="toast-container"></div>

  <!-- Scripts -->
  <script type="module" src="login.js"></script>
</body>
</html>

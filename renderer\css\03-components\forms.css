/* =================================
   Modern Form System
   ================================= */

/* =================================
   Form Container
   ================================= */
.form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-row.horizontal {
  flex-direction: row;
  align-items: center;
  gap: var(--space-3);
}

.form-row.horizontal label {
  min-width: 120px;
  margin-bottom: 0;
}

/* =================================
   Form Labels
   ================================= */
label {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-1);
}

label.required::after {
  content: ' *';
  color: var(--status-error);
}

.label-hint {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  font-weight: var(--font-normal);
  margin-top: var(--space-1);
}

/* =================================
   Form Inputs
   ================================= */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="date"],
input[type="datetime-local"],
input[type="time"],
select,
textarea {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  font-family: inherit;
  min-height: var(--input-height-base);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
input[type="date"]:focus,
input[type="datetime-local"]:focus,
input[type="time"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

input[type="text"]:disabled,
input[type="email"]:disabled,
input[type="password"]:disabled,
input[type="number"]:disabled,
input[type="tel"]:disabled,
input[type="url"]:disabled,
input[type="date"]:disabled,
input[type="datetime-local"]:disabled,
input[type="time"]:disabled,
select:disabled,
textarea:disabled {
  background: var(--bg-secondary);
  color: var(--text-tertiary);
  cursor: not-allowed;
  opacity: 0.6;
}

/* =================================
   Input Sizes
   ================================= */
.input-sm {
  padding: var(--space-2);
  font-size: var(--text-xs);
  min-height: var(--input-height-sm);
}

.input-lg {
  padding: var(--space-4);
  font-size: var(--text-base);
  min-height: var(--input-height-lg);
}

/* =================================
   Textarea
   ================================= */
textarea {
  resize: vertical;
  min-height: 80px;
  line-height: var(--leading-relaxed);
}

textarea.auto-resize {
  resize: none;
  overflow: hidden;
}

/* =================================
   Select Dropdown
   ================================= */
select {
  cursor: pointer;
  padding-right: var(--space-3);
}

/* =================================
   Checkbox & Radio
   ================================= */
input[type="checkbox"],
input[type="radio"] {
  width: 18px;
  height: 18px;
  margin-right: var(--space-2);
  accent-color: var(--interactive-primary);
  cursor: pointer;
}

.checkbox-group,
.radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.checkbox-item,
.radio-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: background var(--transition-fast);
}

.checkbox-item:hover,
.radio-item:hover {
  background: var(--interactive-secondary);
}

.checkbox-item label,
.radio-item label {
  cursor: pointer;
  margin: 0;
  font-weight: var(--font-normal);
}

/* =================================
   Input Groups
   ================================= */
.input-group {
  display: flex;
  align-items: stretch;
}

.input-group input {
  border-radius: 0;
  border-right: none;
}

.input-group input:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.input-group input:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-right: 1px solid var(--border-primary);
}

.input-group-addon {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  padding: var(--space-3);
  display: flex;
  align-items: center;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  white-space: nowrap;
}

.input-group-addon:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
  border-right: none;
}

.input-group-addon:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-left: none;
}

/* =================================
   Form Validation
   ================================= */
.form-row.error input,
.form-row.error select,
.form-row.error textarea {
  border-color: var(--status-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-row.success input,
.form-row.success select,
.form-row.success textarea {
  border-color: var(--status-success);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.error-message {
  font-size: var(--text-xs);
  color: var(--status-error);
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.success-message {
  font-size: var(--text-xs);
  color: var(--status-success);
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

/* =================================
   Form Actions
   ================================= */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
  margin-top: var(--space-6);
}

/* Form actions in modal footer */
.modal-footer .form-actions {
  padding-top: 0;
  border-top: none;
  margin-top: 0;
  width: 100%;
}

.form-actions.centered {
  justify-content: center;
}

.form-actions.space-between {
  justify-content: space-between;
}

/* =================================
   Search Input
   ================================= */
.search-input {
  position: relative;
}

.search-input input {
  padding-left: var(--space-10);
}

.search-input::before {
  content: '';
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'/%3E%3Cpath d='M21 21l-4.35-4.35'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  pointer-events: none;
  z-index: 1;
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .form-row.horizontal {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-2);
  }

  .form-row.horizontal label {
    min-width: auto;
  }

  .form-actions {
    flex-direction: column;
    gap: var(--space-2);
  }

  .form-actions.space-between {
    flex-direction: column;
  }

  .input-group {
    flex-direction: column;
  }

  .input-group input,
  .input-group-addon {
    border-radius: var(--radius-lg) !important;
    border: 1px solid var(--border-primary) !important;
  }
}

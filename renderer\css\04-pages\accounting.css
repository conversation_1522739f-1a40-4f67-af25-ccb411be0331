/* =================================
   Accounting Pages Styles
   ================================= */

/* =================================
   Account Detail Modal Styles
   ================================= */
.account-detail-modal-content {
  max-width: 1200px;
  width: 95vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.account-detail-header {
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  position: relative;
}

.account-header-info {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  flex: 1;
}

.account-avatar {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-full);
  background: var(--interactive-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: var(--text-xl);
  flex-shrink: 0;
}

.account-header-details h2 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.account-header-tags {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.detail-tag {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.detail-tag.tag-type {
  background: var(--info-50);
  color: var(--info-700);
}

.detail-tag.tag-balance {
  background: var(--success-50);
  color: var(--success-700);
}

.detail-tag.tag-status {
  background: var(--warning-50);
  color: var(--warning-700);
}

/* Detail Modal Tabs */
.detail-modal-tabs {
  display: flex;
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
  overflow-x: auto;
}

.detail-tab-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.detail-tab-link:hover {
  color: var(--text-primary);
  background: var(--interactive-secondary);
}

.detail-tab-link.active {
  color: var(--interactive-primary);
  border-bottom-color: var(--interactive-primary);
  background: var(--interactive-secondary);
}

.detail-tab-link i {
  font-size: var(--text-sm);
}

/* Detail Modal Body */
.detail-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-6);
}

.detail-tab-content {
  display: none;
}

.detail-tab-content.active {
  display: block;
}

/* Detail Info Sections */
.detail-info-sections {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.detail-section-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.detail-section-header i {
  color: var(--interactive-primary);
  font-size: var(--text-lg);
}

.detail-section-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.detail-info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

.detail-info-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: all var(--transition-fast);
}

.detail-info-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.detail-info-card.full-width {
  grid-column: 1 / -1;
}

.detail-info-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  background: var(--interactive-secondary);
  color: var(--interactive-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.detail-info-card .info-content {
  flex: 1;
}

.detail-info-card .info-content label {
  display: block;
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-info-card .info-value {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

/* Detail Notes */
.detail-notes-content {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
}

.notes-text {
  margin: 0;
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* Detail Loading Spinner */
.detail-loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  color: var(--text-secondary);
}

.detail-loading-spinner i {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-3);
}

/* Detail Balance Dashboard */
.detail-balance-dashboard {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* Detail Transactions List */
.detail-transactions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

/* Detail Stats Dashboard */
.detail-stats-dashboard {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* =================================
   Accounting Header
   ================================= */

/* Accounting Header Section Container */
.accounting-header-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.accounting-page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.accounting-page-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.accounting-page-title svg {
  width: 24px;
  height: 24px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

#accounting-header {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

/* Accounting Cards Grid */
.accounting-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

/* Individual Accounting Card */
.accounting-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.accounting-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--border-secondary);
}

/* Card Icon */
.accounting-card .card-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: var(--primary-50);
  color: var(--primary-600);
}

.accounting-card .card-icon svg {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

/* Card Content */
.accounting-card .card-content {
  flex: 1;
  min-width: 0;
}

.accounting-card .card-title {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.accounting-card .card-value {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  line-height: var(--leading-tight);
}

.accounting-card .card-subtitle {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  line-height: var(--leading-tight);
}

/* Card Value Colors */
.accounting-card .card-value.positive {
  color: var(--success-600);
}

.accounting-card .card-value.negative {
  color: var(--error-600);
}

.accounting-card .card-value.neutral {
  color: var(--text-primary);
}

/* Specific Card Types */
.unpaid-debt-card .card-icon {
  background: var(--error-50);
  color: var(--error-600);
}

.uncollected-receivable-card .card-icon {
  background: var(--warning-50);
  color: var(--warning-600);
}

.paid-money-card .card-icon {
  background: var(--success-50);
  color: var(--success-600);
}

.collected-money-card .card-icon {
  background: var(--info-50);
  color: var(--info-600);
}

.cash-balance-card .card-icon {
  background: var(--primary-50);
  color: var(--primary-600);
}

.net-financial-position-card .card-icon {
  background: var(--purple-50);
  color: var(--purple-600);
}

.accounting-header-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
  text-align: center;
}

.accounting-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.accounting-metric-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  text-align: center;
  transition: all var(--transition-fast);
}

.accounting-metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
  border-color: var(--border-secondary);
}

.accounting-metric-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-2);
}

.accounting-metric-value {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--leading-tight);
}

.accounting-metric-change {
  font-size: var(--text-xs);
  margin-top: var(--space-1);
}

.accounting-metric-change.positive {
  color: var(--status-success);
}

.accounting-metric-change.negative {
  color: var(--status-error);
}

/* Metric Card Variants */
.accounting-metric-card.receivables .accounting-metric-value {
  color: var(--success-600);
}

.accounting-metric-card.payables .accounting-metric-value {
  color: var(--error-600);
}

.accounting-metric-card.cash .accounting-metric-value {
  color: var(--info-600);
}

.accounting-metric-card.net .accounting-metric-value {
  color: var(--primary-600);
}

/* =================================
   Page Actions
   ================================= */
.page-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  gap: var(--space-4);
  flex-wrap: wrap;
}

.filter-controls {
  display: flex;
  gap: var(--space-3);
  align-items: center;
  flex-wrap: wrap;
}

.filter-controls input,
.filter-controls select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  min-width: 150px;
}

.filter-controls input:focus,
.filter-controls select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* =================================
   Products Controls
   ================================= */
#products-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  gap: var(--space-4);
  flex-wrap: wrap;
}

#add-product-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* =================================
   Pagination Controls
   ================================= */
.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) var(--space-6);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.pagination-info {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.pagination-buttons {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.pagination-buttons button {
  padding: var(--space-2) var(--space-3);
  background: var(--interactive-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--text-sm);
}

.pagination-buttons button:hover:not(:disabled) {
  background: var(--interactive-secondary-hover);
  border-color: var(--border-secondary);
}

.pagination-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: var(--font-medium);
  padding: 0 var(--space-3);
}

/* =================================
   Account Detail Modal
   ================================= */
#account-detail-modal .account-detail-modal-content {
  max-width: 900px;
  width: 95vw;
  max-height: 90vh;
  background: var(--modal-bg);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* =================================
   Account Statement Modal Styles
   ================================= */
.statement-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  padding: var(--space-4);
  box-sizing: border-box;
}

.statement-modal-overlay.show,
.statement-modal-overlay:not(.hidden) {
  opacity: 1;
  visibility: visible;
}

.statement-modal-overlay.hidden {
  display: none !important;
}

.statement-modal {
  background: var(--modal-bg);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 1000px;
  width: 95vw;
  max-height: calc(100vh - 2 * var(--space-4));
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.95);
  transition: transform var(--transition-base);
}

.statement-modal-overlay.show .statement-modal,
.statement-modal-overlay:not(.hidden) .statement-modal {
  transform: scale(1);
}

.statement-modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-elevated);
  flex-shrink: 0;
}

.statement-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.statement-title i {
  color: var(--interactive-primary);
}

.statement-close-btn {
  background: none;
  border: none;
  font-size: var(--text-xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.statement-close-btn:hover {
  background: var(--interactive-secondary);
  color: var(--text-primary);
}

.statement-controls {
  padding: var(--space-4) var(--space-6);
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
}

.date-inputs {
  display: flex;
  align-items: end;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.date-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.date-group label {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.date-input {
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  min-width: 150px;
}

.date-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.generate-btn {
  padding: var(--space-3) var(--space-4);
  background: var(--interactive-primary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  height: fit-content;
}

.generate-btn:hover {
  background: var(--interactive-primary-hover);
  transform: translateY(-1px);
}

.statement-main-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--space-6);
  background: var(--modal-bg);
  min-height: 0;
}

.statement-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  text-align: center;
  color: var(--text-secondary);
}

.statement-empty-state .empty-icon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.statement-results {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.statement-table-container {
  background: var(--content-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  overflow: hidden;
}

.statement-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.statement-table th,
.statement-table td {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.statement-table th {
  background: var(--bg-elevated);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.statement-table td {
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.statement-table tbody tr:hover {
  background: var(--interactive-secondary);
}

.statement-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-elevated);
  flex-shrink: 0;
}

.export-buttons {
  display: flex;
  justify-content: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.export-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--content-bg);
  color: var(--text-primary);
}

.export-btn:hover {
  background: var(--interactive-secondary);
  transform: translateY(-1px);
}

.export-btn.pdf-btn:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.export-btn.excel-btn:hover {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.export-btn.print-btn:hover {
  background: var(--interactive-primary);
  color: white;
  border-color: var(--interactive-primary);
}

.account-detail-header {
  padding: var(--space-6);
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  position: relative;
}

.account-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--primary-50);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--border-primary);
}

.account-avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--primary-50);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--border-primary);
}

.account-avatar-placeholder svg,
.account-avatar svg {
  width: 40px;
  height: 40px;
}

.account-header-info {
  flex: 1;
}

.account-name-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.account-header-details {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--space-3) 0;
}

.account-header-tags {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.detail-tag {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
}

.detail-tag.tag-type {
  background: var(--primary-50);
  color: var(--primary-600);
  border: 1px solid var(--primary-200);
}

.detail-tag.tag-balance {
  background: var(--success-50);
  color: var(--success-600);
  border: 1px solid var(--success-200);
}

.detail-tag.tag-balance.negative {
  background: var(--error-50);
  color: var(--error-600);
  border: 1px solid var(--error-200);
}

/* =================================
   Reports Output
   ================================= */
#report-output-area {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-top: var(--space-6);
  box-shadow: var(--shadow-sm);
  min-height: 400px;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: var(--space-4);
}

.report-table th,
.report-table td {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.report-table th {
  background: var(--table-header-bg);
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.report-table td {
  font-size: var(--text-sm);
  color: var(--text-primary);
}

.report-table tr:hover {
  background: var(--table-row-hover);
}

.report-summary {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-top: var(--space-4);
}

.report-summary h4 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.report-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.report-summary-item {
  text-align: center;
}

.report-summary-label {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.report-summary-value {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.report-summary-value.positive {
  color: var(--status-success);
}

.report-summary-value.negative {
  color: var(--status-error);
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  #accounting-header {
    padding: var(--space-4);
  }

  .accounting-metrics {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }

  .accounting-metric-card {
    padding: var(--space-3);
  }

  .accounting-metric-value {
    font-size: var(--text-lg);
  }

  .page-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .filter-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .filter-controls input,
  .filter-controls select {
    min-width: 120px;
  }

  .pagination-controls {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }

  .account-detail-header {
    padding: var(--space-4);
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }

  .account-avatar,
  .account-avatar-placeholder {
    width: 60px;
    height: 60px;
  }

  .account-avatar svg,
  .account-avatar-placeholder svg {
    width: 30px;
    height: 30px;
  }

  .account-name-title {
    font-size: var(--text-xl);
  }

  #report-output-area {
    padding: var(--space-4);
  }

  .report-summary-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .report-table {
    font-size: var(--text-xs);
  }

  .report-table th,
  .report-table td {
    padding: var(--space-2) var(--space-3);
  }
}

const sqlite3 = require('sqlite3').verbose();
const path = require('path'); // path might still be needed by createGenericCrudHandlers if it were more complex

let db = null;
let dbPathInternal = null; // To store the path for potential re-use if needed, e.g. by settings

// Function to initialize the database connection
function initDb(appDbPath) {
  if (db) {
    console.log('Database already initialized.');
    return db;
  }

  dbPathInternal = appDbPath;
  db = new sqlite3.Database(dbPathInternal, (err) => {
    if (err) {
      console.error('Failed to connect to the database:', err.message);
      // Propagate the error or handle it as critical
      throw err;
    } else {
      console.log('Connected to the SQLite database from database.js.');
      db.run('PRAGMA foreign_keys = ON;', foreignKeyErr => {
        if (foreignKeyErr) {
          console.error("Could not enable foreign key support:", foreignKeyErr.message);
        } else {
          console.log("Foreign key support is enabled from database.js.");
        }
      });
      
      // Initialize database schema
      try {
        const { initializeSchema } = require('../../db/init.js');
console.log('About to call initializeSchema...');
initializeSchema(db, () => {
          console.log('Database schema initialized successfully.');
        });
      } catch (initErr) {
        console.error('Error initializing database schema:', initErr.message);
      }
    }
  });
  return db;
}

// Function to get the initialized DB instance
function getDb() {
  if (!db) {
    // This should ideally not happen if initDb is called correctly on app startup
    console.error('Database not initialized. Call initDb first.');
    // Attempt to initialize with a default path as a fallback, though not ideal.
    // Or throw an error. For now, logging error.
    // initDb(path.join(__dirname, '..', '..', 'db', 'livestock.db')); // Fallback, adjust path if main.js is not root
    throw new Error('Database not initialized. Call initDb in main.js first.');
  }
  return db;
}

// Function to get the database path (might be needed by settings handlers)
function getDbPath() {
    if (!dbPathInternal) {
        throw new Error('Database path not set. Call initDb first.');
    }
    return dbPathInternal;
}

// Generic CRUD Handler Factory with Profile Support
function createGenericCrudHandlers(tableName, columns, options = {}) {
  const currentDb = getDb(); // Ensure DB is available
  const { hasHayvanId = true, booleanFields = [], requiresProfile = true } = options;

  // Include ProfilId in columns if required
  const baseColumns = requiresProfile ? ['ProfilId'] : [];
  const allColumns = hasHayvanId ? [...baseColumns, 'HayvanId', ...columns] : [...baseColumns, ...columns];
  const placeholders = allColumns.map(() => '?').join(', ');
  const updatePlaceholders = columns.map(col => `${col}=?`).join(', ');

  const handlers = {};

  // Helper function to get current profile
  const getCurrentProfile = () => {
    try {
      const authHandlers = require('../ipc-handlers/auth-handlers.js');
      return authHandlers.getCurrentProfile();
    } catch (error) {
      console.error('Error getting current profile:', error);
      return null;
    }
  };

  if (hasHayvanId) {
    handlers.listByHayvanId = async (event, hayvanId) => {
      return new Promise((resolve, reject) => {
        if (requiresProfile) {
          const currentProfile = getCurrentProfile();
          if (!currentProfile) {
            reject(new Error('Aktif profil bulunamadı'));
            return;
          }
          currentDb.all(`SELECT * FROM ${tableName} WHERE HayvanId = ? AND ProfilId = ? ORDER BY Id DESC`, [hayvanId, currentProfile.id], (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          });
        } else {
          currentDb.all(`SELECT * FROM ${tableName} WHERE HayvanId = ? ORDER BY Id DESC`, [hayvanId], (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          });
        }
      });
    };
  } else {
    handlers.listAll = async () => { // For tables not linked to HayvanId, if any
      return new Promise((resolve, reject) => {
        if (requiresProfile) {
          const currentProfile = getCurrentProfile();
          if (!currentProfile) {
            reject(new Error('Aktif profil bulunamadı'));
            return;
          }
          currentDb.all(`SELECT * FROM ${tableName} WHERE ProfilId = ? ORDER BY Id DESC`, [currentProfile.id], (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          });
        } else {
          currentDb.all(`SELECT * FROM ${tableName} ORDER BY Id DESC`, [], (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          });
        }
      });
    };
  }

  handlers.add = async (event, data) => {
    return new Promise((resolve, reject) => {
      if (requiresProfile) {
        const currentProfile = getCurrentProfile();
        if (!currentProfile) {
          reject(new Error('Aktif profil bulunamadı'));
          return;
        }
        // Add profile ID to data
        data.ProfilId = currentProfile.id;
      }

      const values = allColumns.map(col => {
        if (booleanFields.includes(col)) return data[col] === 'Var' || data[col] === 1 || data[col] === true ? 1 : 0;
        return data[col];
      });

      const stmt = currentDb.prepare(`INSERT INTO ${tableName} (${allColumns.join(', ')}) VALUES (${placeholders})`);
      stmt.run(values, function(err) {
        stmt.finalize();
        if (err) reject(err);
        else resolve({ id: this.lastID });
      });
    });
  };

  handlers.update = async (event, data) => {
    return new Promise((resolve, reject) => {
      if (requiresProfile) {
        const currentProfile = getCurrentProfile();
        if (!currentProfile) {
          reject(new Error('Aktif profil bulunamadı'));
          return;
        }

        const values = columns.map(col => {
          if (booleanFields.includes(col)) return data[col] === 'Var' || data[col] === 1 || data[col] === true ? 1 : 0;
          return data[col];
        });
        values.push(data.Id);
        values.push(currentProfile.id);
        const stmt = currentDb.prepare(`UPDATE ${tableName} SET ${updatePlaceholders} WHERE Id=? AND ProfilId=?`);
        stmt.run(values, function(err) {
          stmt.finalize();
          if (err) reject(err);
          else resolve({ changes: this.changes });
        });
      } else {
        const values = columns.map(col => {
          if (booleanFields.includes(col)) return data[col] === 'Var' || data[col] === 1 || data[col] === true ? 1 : 0;
          return data[col];
        });
        values.push(data.Id);
        const stmt = currentDb.prepare(`UPDATE ${tableName} SET ${updatePlaceholders} WHERE Id=?`);
        stmt.run(values, function(err) {
          stmt.finalize();
          if (err) reject(err);
          else resolve({ changes: this.changes });
        });
      }
    });
  };

  handlers.delete = async (event, id) => {
    return new Promise((resolve, reject) => {
      if (requiresProfile) {
        const currentProfile = getCurrentProfile();
        if (!currentProfile) {
          reject(new Error('Aktif profil bulunamadı'));
          return;
        }
        currentDb.run(`DELETE FROM ${tableName} WHERE Id=? AND ProfilId=?`, [id, currentProfile.id], function(err) {
          if (err) reject(err);
          else resolve({ changes: this.changes });
        });
      } else {
        currentDb.run(`DELETE FROM ${tableName} WHERE Id=?`, [id], function(err) {
          if (err) reject(err);
          else resolve({ changes: this.changes });
        });
      }
    });
  };
  return handlers;
}

module.exports = {
  initDb,
  getDb,
  getDbPath,
  createGenericCrudHandlers
};

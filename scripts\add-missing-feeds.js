const sqlite3 = require('sqlite3');
const path = require('path');

// Veritabanı bağlantısını başlat
const dbPath = path.join(__dirname, '..', 'livestock.db');
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('<PERSON>eri<PERSON><PERSON><PERSON>na bağlanırken hata:', err.message);
        process.exit(1);
    }
    console.log('SQLite veritabanına bağlandı');
});

// <PERSON><PERSON><PERSON>
const missingFeeds = [
    {
        ProfilId: 1,
        Yem<PERSON><PERSON>: '<PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON>)',
        <PERSON><PERSON><PERSON><PERSON><PERSON>: '<PERSON><PERSON><PERSON><PERSON>',
        <PERSON>ciklama: '<PERSON><PERSON><PERSON> kaynağı tahıl yemi',
        KuruMadde: 87.0,
        HamProtein: 9.0,
        RDP: 65.0,
        RUP: 35.0,
        MetabolikEnerji: 13.2,
        NEL: 2.06,
        <PERSON><PERSON>ag: 3.8,
        <PERSON><PERSON><PERSON>: 72.0,
        <PERSON><PERSON>: 2.5,
        <PERSON><PERSON>: 1.3,
        N<PERSON>: 9.5,
        <PERSON><PERSON>: 2.8,
        <PERSON><PERSON><PERSON><PERSON><PERSON>: 6.7,
        <PERSON><PERSON><PERSON><PERSON>: 2.2,
        <PERSON><PERSON><PERSON>: 0.6,
        <PERSON><PERSON><PERSON><PERSON><PERSON>: 0.03,
        <PERSON><PERSON><PERSON><PERSON>: 0.28,
        <PERSON><PERSON>zyum: 0.12,
        Potasyum: 0.37,
        Sodyum: 0.02,
        Klorur: 0.05,
        sulfur: 0.10,
        Demir: 30.0,
        Cinko: 22.0,
        Bakir: 3.0,
        Manganez: 5.0,
        Selenyum: 0.15,
        <PERSON>yot: 0.05,
        Kobalt: 0.02,
        <PERSON>libden: 0.6,
        VitaminA: 0.0,
        VitaminD: 0.0,
        VitaminE: 15.0,
        VitaminK: 0.5,
        Thiamin: 3.8,
        Riboflavin: 1.2,
        Niacin: 27.0,
        Biotin: 0.06,
        Folat: 0.3,
        Cobalamin: 0.0,
        Tanen: 0.0,
        Fitat: 0.28,
        Saponin: 0.0,
        Oksalat: 0.01,
        BirimFiyat: 8.50
    },
    {
        ProfilId: 1,
        YemAdi: 'Arpa (Dane)',
        YemTipi: 'Tahıl',
        Aciklama: 'Enerji kaynağı tahıl yemi',
        KuruMadde: 89.0,
        HamProtein: 11.5,
        RDP: 70.0,
        RUP: 30.0,
        MetabolikEnerji: 12.5,
        NEL: 1.95,
        HamYag: 2.1,
        Nisasta: 58.0,
        Seker: 2.0,
        Kul: 2.4,
        NDF: 18.0,
        ADF: 5.5,
        Hemiseluloz: 12.5,
        Seluloz: 4.5,
        Lignin: 1.0,
        Kalsiyum: 0.05,
        Fosfor: 0.34,
        Magnezyum: 0.12,
        Potasyum: 0.47,
        Sodyum: 0.02,
        Klorur: 0.12,
        sulfur: 0.15,
        Demir: 35.0,
        Cinko: 25.0,
        Bakir: 5.0,
        Manganez: 16.0,
        Selenyum: 0.10,
        Iyot: 0.05,
        Kobalt: 0.02,
        Molibden: 0.5,
        VitaminA: 0.0,
        VitaminD: 0.0,
        VitaminE: 12.0,
        VitaminK: 0.5,
        Thiamin: 4.5,
        Riboflavin: 1.8,
        Niacin: 52.0,
        Biotin: 0.22,
        Folat: 0.5,
        Cobalamin: 0.0,
        Tanen: 0.15,
        Fitat: 0.38,
        Saponin: 0.0,
        Oksalat: 0.01,
        BirimFiyat: 7.80
    }
];

// Veritabanına yem ekleme fonksiyonu
function addFeed(feed) {
    return new Promise((resolve, reject) => {
        const sql = `
            INSERT INTO Yemler (
                ProfilId, YemAdi, YemTipi, Aciklama,
                KuruMadde, HamProtein, RDP, RUP, MetabolikEnerji, NEL, HamYag,
                Nisasta, Seker, Kul,
                NDF, ADF, Hemiseluloz, Seluloz, Lignin,
                Kalsiyum, Fosfor, Magnezyum, Potasyum, Sodyum, Klorur, sulfur,
                Demir, Cinko, Bakir, Manganez, Selenyum, Iyot, Kobalt, Molibden,
                VitaminA, VitaminD, VitaminE, VitaminK, Thiamin, Riboflavin, Niacin, Biotin, Folat, Cobalamin,
                Tanen, Fitat, Saponin, Oksalat,
                BirimFiyat
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const values = [
            feed.ProfilId, feed.YemAdi, feed.YemTipi, feed.Aciklama,
            feed.KuruMadde, feed.HamProtein, feed.RDP, feed.RUP, feed.MetabolikEnerji, feed.NEL, feed.HamYag,
            feed.Nisasta, feed.Seker, feed.Kul,
            feed.NDF, feed.ADF, feed.Hemiseluloz, feed.Seluloz, feed.Lignin,
            feed.Kalsiyum, feed.Fosfor, feed.Magnezyum, feed.Potasyum, feed.Sodyum, feed.Klorur, feed.sulfur,
            feed.Demir, feed.Cinko, feed.Bakir, feed.Manganez, feed.Selenyum, feed.Iyot, feed.Kobalt, feed.Molibden,
            feed.VitaminA, feed.VitaminD, feed.VitaminE, feed.VitaminK, feed.Thiamin, feed.Riboflavin, feed.Niacin, feed.Biotin, feed.Folat, feed.Cobalamin,
            feed.Tanen, feed.Fitat, feed.Saponin, feed.Oksalat,
            feed.BirimFiyat
        ];

        db.run(sql, values, function(err) {
            if (err) {
                reject(err);
            } else {
                resolve(this.lastID);
            }
        });
    });
}

// Eksik yemleri ekle
async function addMissingFeeds() {
    try {
        console.log('Eksik yemler ekleniyor...');
        
        for (let i = 0; i < missingFeeds.length; i++) {
            const feed = missingFeeds[i];
            try {
                const id = await addFeed(feed);
                console.log(`✓ ${feed.YemAdi} eklendi (ID: ${id})`);
            } catch (error) {
                console.error(`✗ ${feed.YemAdi} eklenirken hata:`, error.message);
            }
        }
        
        console.log('\nEksik yemler başarıyla eklendi!');
        db.close();
    } catch (error) {
        console.error('Genel hata:', error);
        db.close();
    }
}

// Scripti çalıştır
addMissingFeeds();

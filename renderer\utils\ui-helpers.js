export function calculateAge(birthDateString, t) { // t is i18n.t
  if (!birthDateString) return '';
  const birthDate = new Date(birthDateString);
  if (isNaN(birthDate.getTime())) return ''; // Invalid date check

  const today = new Date();
  let years = today.getFullYear() - birthDate.getFullYear();
  let months = today.getMonth() - birthDate.getMonth();

  if (months < 0 || (months === 0 && today.getDate() < birthDate.getDate())) {
    years--;
    months = (months + 12) % 12;
  }

  if (years > 0) {
    return `${years} ${t('year_unit')} ${months > 0 ? months + ' ' + t('month_unit') : ''}`.trim();
  } else if (months > 0) {
    return `${months} ${t('month_unit')}`;
  } else {
    const days = Math.floor((today - birthDate) / (1000 * 60 * 60 * 24));
    return `${days} ${t('day_unit')}`;
  }
}

export function defaultAvatarSVG(size = 28) { // Default size 28, can be overridden
  // Sade, modern bir inek başı SVG ikonu
  return `<svg width='${size}' height='${size}' viewBox='0 0 28 28' fill='none' xmlns='http://www.w3.org/2000/svg'>
    <circle cx='14' cy='14' r='14' fill='#e0e7ef'/>
    <ellipse cx='14' cy='21' rx='8.5' ry='4' fill='#b6c3d1'/>
    <g>
      <ellipse cx='14' cy='13' rx='6' ry='5' fill='#fff'/>
      <ellipse cx='10.5' cy='12' rx='2' ry='1.2' fill='#b6c3d1' opacity='0.7'/>
      <ellipse cx='17.5' cy='12' rx='2' ry='1.2' fill='#b6c3d1' opacity='0.7'/>
      <ellipse cx='12' cy='14.5' rx='1.1' ry='1.5' fill='#222c37'/>
      <ellipse cx='16' cy='14.5' rx='1.1' ry='1.5' fill='#222c37'/>
      <ellipse cx='14' cy='17.5' rx='1.5' ry='0.7' fill='#b6c3d1'/>
      <ellipse cx='8.5' cy='10.5' rx='1.2' ry='2.2' fill='#b6c3d1'/>
      <ellipse cx='19.5' cy='10.5' rx='1.2' ry='2.2' fill='#b6c3d1'/>
    </g>
  </svg>`;
}

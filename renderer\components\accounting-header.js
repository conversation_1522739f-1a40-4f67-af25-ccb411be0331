/**
 * Accounting Header Component
 * Displays financial overview cards for accounting pages
 */

export class AccountingHeader {
  constructor(options = {}) {
    this.containerId = options.containerId || 'accounting-header';
    this.onRefresh = options.onRefresh || (() => {});
    this.pageTitle = options.pageTitle || 'Muhasebe Yönetimi';
    this.pageIcon = options.pageIcon || this.getDefaultIcon();
  }

  /**
   * Get default accounting icon
   */
  getDefaultIcon() {
    return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M12 2L2 7l10 5 10-5-10-5z"/>
      <path d="m2 17 10 5 10-5"/>
      <path d="m2 12 10 5 10-5"/>
    </svg>`;
  }

  /**
   * Initialize the accounting header
   */
  async init() {
    this.render(); // Render synchronously to prevent jumping
    // Load data asynchronously without blocking
    this.loadData().catch(error => {
      console.error('Error loading accounting header data:', error);
      this.showError();
    });
  }

  /**
   * Render the header HTML structure
   */
  render() {
    const container = document.getElementById(this.containerId);
    if (!container) {
      console.warn(`Accounting header container with ID '${this.containerId}' not found`);
      return;
    }

    container.innerHTML = `
      <div class="accounting-header-section">
        <div class="accounting-page-header">
          <h2 class="accounting-page-title">
            ${this.pageIcon}
            ${this.pageTitle}
          </h2>
        </div>
      </div>
      <div class="accounting-header">
        <div class="accounting-cards">
          <div class="accounting-card unpaid-debt-card">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="12" y1="8" x2="12" y2="16"/>
                <line x1="8" y1="12" x2="16" y2="12"/>
              </svg>
            </div>
            <div class="card-content">
              <div class="card-title">Ödenmemiş Borç</div>
              <div class="card-value" id="unpaid-debt">₺0.00</div>
              <div class="card-subtitle">Toplam Borç</div>
            </div>
          </div>

          <div class="accounting-card uncollected-receivable-card">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
              </svg>
            </div>
            <div class="card-content">
              <div class="card-title">Tahsil Edilmemiş</div>
              <div class="card-value" id="uncollected-receivable">₺0.00</div>
              <div class="card-subtitle">Toplam Alacak</div>
            </div>
          </div>

          <div class="accounting-card paid-money-card">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
                <path d="m9 14 2 2 4-4"/>
              </svg>
            </div>
            <div class="card-content">
              <div class="card-title">Ödenmiş Para</div>
              <div class="card-value" id="paid-money">₺0.00</div>
              <div class="card-subtitle">Nakit Dahil</div>
            </div>
          </div>

          <div class="accounting-card collected-money-card">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 1v22M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                <circle cx="18" cy="6" r="3"/>
                <path d="m16 8 2 2 4-4"/>
              </svg>
            </div>
            <div class="card-content">
              <div class="card-title">Tahsil Edilmiş</div>
              <div class="card-value" id="collected-money">₺0.00</div>
              <div class="card-subtitle">Nakit Dahil</div>
            </div>
          </div>

          <div class="accounting-card cash-balance-card">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                <line x1="1" y1="10" x2="23" y2="10"/>
                <circle cx="8" cy="14" r="2"/>
                <path d="m15 11 1 1 3-3"/>
              </svg>
            </div>
            <div class="card-content">
              <div class="card-title">Kasa Net Bakiye</div>
              <div class="card-value" id="cash-net-balance">₺0.00</div>
              <div class="card-subtitle">Toplam Bakiye</div>
            </div>
          </div>

          <div class="accounting-card net-financial-position-card">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                <path d="m2 17 10 5 10-5"/>
                <path d="m2 12 10 5 10-5"/>
              </svg>
            </div>
            <div class="card-content">
              <div class="card-title">Net Finansal Durum</div>
              <div class="card-value" id="net-financial-position">₺0.00</div>
              <div class="card-subtitle">Toplam Alacak - Toplam Borç</div>
            </div>
          </div>
        </div>
      </div>
      </div>
    `;
  }

  /**
   * Load and display financial data
   */
  async loadData() {
    try {
      const data = await this.fetchFinancialSummary();
      this.updateCards(data);
    } catch (error) {
      console.error('Error loading accounting header data:', error);
      this.showError();
    }
  }

  /**
   * Fetch financial summary data from backend
   */
  async fetchFinancialSummary() {
    try {
      // Get all purchases and sales data
      const [purchases, sales, payments] = await Promise.all([
        window.api.invoke('purchases:list'),
        window.api.invoke('sales:list'),
        window.api.invoke('payments:list')
      ]);

      // Calculate totals from purchases and sales
      const totalPurchases = purchases.reduce((sum, p) => sum + (p.ToplamTutar || (p.Miktar * p.BirimFiyat)), 0);
      const totalSales = sales.reduce((sum, s) => sum + (s.ToplamTutar || (s.Miktar * s.BirimFiyat)), 0);

      // Calculate total paid money from purchases (OdenenTutar already includes all payments)
      const totalPaidMoney = purchases.reduce((sum, p) => sum + (p.OdenenTutar || 0), 0);

      // Calculate total collected money from sales (OdenenTutar already includes all payments)
      const totalCollectedMoney = sales.reduce((sum, s) => sum + (s.OdenenTutar || 0), 0);

      // Calculate unpaid amounts (what we still owe)
      const unpaidDebt = totalPurchases - totalPaidMoney;

      // Calculate uncollected amounts (what we're still owed)
      const uncollectedReceivable = totalSales - totalCollectedMoney;

      // Net cash balance (what we collected minus what we paid)
      const netCashBalance = totalCollectedMoney - totalPaidMoney;

      // Net financial position (total receivables - total debts)
      const netFinancialPosition = (uncollectedReceivable + totalCollectedMoney) - (unpaidDebt + totalPaidMoney);

      return {
        unpaidDebt: Math.max(0, unpaidDebt), // Don't show negative debt
        uncollectedReceivable: Math.max(0, uncollectedReceivable), // Don't show negative receivable
        paidMoney: totalPaidMoney,
        collectedMoney: totalCollectedMoney,
        cashNetBalance: netCashBalance,
        netFinancialPosition: netFinancialPosition
      };
    } catch (error) {
      console.error('Error fetching financial data:', error);
      return {
        unpaidDebt: 0,
        uncollectedReceivable: 0,
        paidMoney: 0,
        collectedMoney: 0,
        cashNetBalance: 0
      };
    }
  }



  /**
   * Update card values with new data
   */
  updateCards(data) {
    const unpaidDebtElement = document.getElementById('unpaid-debt');
    const uncollectedReceivableElement = document.getElementById('uncollected-receivable');
    const paidMoneyElement = document.getElementById('paid-money');
    const collectedMoneyElement = document.getElementById('collected-money');
    const cashNetBalanceElement = document.getElementById('cash-net-balance');
    const netFinancialPositionElement = document.getElementById('net-financial-position');

    if (unpaidDebtElement) {
      unpaidDebtElement.textContent = this.formatCurrency(data.unpaidDebt);
      unpaidDebtElement.className = `card-value ${data.unpaidDebt > 0 ? 'negative' : 'neutral'}`;
    }

    if (uncollectedReceivableElement) {
      uncollectedReceivableElement.textContent = this.formatCurrency(data.uncollectedReceivable);
      uncollectedReceivableElement.className = `card-value ${data.uncollectedReceivable > 0 ? 'positive' : 'neutral'}`;
    }

    if (paidMoneyElement) {
      paidMoneyElement.textContent = this.formatCurrency(data.paidMoney);
      paidMoneyElement.className = `card-value ${data.paidMoney > 0 ? 'negative' : 'neutral'}`;
    }

    if (collectedMoneyElement) {
      collectedMoneyElement.textContent = this.formatCurrency(data.collectedMoney);
      collectedMoneyElement.className = `card-value ${data.collectedMoney > 0 ? 'positive' : 'neutral'}`;
    }

    if (cashNetBalanceElement) {
      cashNetBalanceElement.textContent = this.formatCurrency(data.cashNetBalance);
      cashNetBalanceElement.className = `card-value ${data.cashNetBalance >= 0 ? 'positive' : 'negative'}`;
    }

    if (netFinancialPositionElement) {
      netFinancialPositionElement.textContent = this.formatCurrency(data.netFinancialPosition);
      netFinancialPositionElement.className = `card-value ${data.netFinancialPosition >= 0 ? 'positive' : 'negative'}`;
    }
  }

  /**
   * Show error state
   */
  showError() {
    const unpaidDebtElement = document.getElementById('unpaid-debt');
    const uncollectedReceivableElement = document.getElementById('uncollected-receivable');
    const paidMoneyElement = document.getElementById('paid-money');
    const collectedMoneyElement = document.getElementById('collected-money');
    const cashNetBalanceElement = document.getElementById('cash-net-balance');
    const netFinancialPositionElement = document.getElementById('net-financial-position');

    [unpaidDebtElement, uncollectedReceivableElement, paidMoneyElement, collectedMoneyElement, cashNetBalanceElement, netFinancialPositionElement].forEach(el => {
      if (el) {
        el.innerHTML = 'Hata';
        el.className = 'card-value error';
      }
    });
  }

  /**
   * Format currency values
   */
  formatCurrency(amount) {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Refresh the header data
   */
  async refresh() {
    await this.loadData();
  }
}

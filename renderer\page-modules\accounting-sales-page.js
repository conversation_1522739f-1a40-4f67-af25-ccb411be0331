// Standardized Accounting Sales Page Module
// Following the new standardization guidelines

import { IconSystem } from '../utils/icon-system.js';
import { BadgeSystem } from '../utils/badge-system.js';
import { RecentActivities, formatActivityDate, truncateText } from '../components/recent-activities.js';
import { AccountingHeader } from '../components/accounting-header.js';
import { TableAnimations } from '../utils/table-animations.js';

// Standardized globals for this module
let passedI18n, passedContentArea, passedDefaultAvatarSVG;

let allSales = [];
let currentSales = [];
const itemsPerPage = 10;
let currentPage = 1;
let sortColumn = 'Tarih'; // Default sort column
let sortDirection = -1; // Default sort direction (desc for date)

let productsCache = [];
let accountsCache = [];

// DOM element references
let salesTableBody;
let saleModal;
let saleForm;
let saleModalTitle;
let currentEditingSaleId = null;
let pageInfo;
let prevButton;
let nextButton;
let salesRecentActivities;

function sortSalesList(list, column, direction) {
  return [...list].sort((a, b) => {
    let valA = a[column];
    let valB = b[column];

    if (valA === null || valA === undefined) valA = '';
    if (valB === null || valB === undefined) valB = '';

    if (column === 'Tarih' || column === 'VadeTarihi') {
      valA = new Date(valA);
      valB = new Date(valB);
      return (valA - valB) * direction;
    }
    if (['Id', 'Miktar', 'BirimFiyat', 'ToplamTutar', 'OdenenTutar', 'KalanTutar'].includes(column)) {
      valA = Number(valA);
      valB = Number(valB);
      return (valA - valB) * direction;
    }
    if (typeof valA === 'string' && typeof valB === 'string') {
      return valA.localeCompare(valB, undefined, { sensitivity: 'base' }) * direction;
    }
    if (valA < valB) return -1 * direction;
    if (valA > valB) return 1 * direction;
    return 0;
  });
}

export async function renderAccountingSalesPage(contentArea, i18n, defaultAvatarSVG) {
  // Store passed parameters using standardized naming
  passedI18n = i18n;
  passedContentArea = contentArea;
  passedDefaultAvatarSVG = defaultAvatarSVG;
  const t = passedI18n.t;

  // Initialize accounting header immediately to prevent jumping
  const accountingHeader = new AccountingHeader({
    containerId: 'accounting-header',
    pageTitle: t('accounting_sales_page_title') || 'Satışlar',
    pageIcon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M3 3h18l-1 13H4L3 3z"/>
      <path d="M16 16a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7 16a1 1 0 1 1 2 0 1 1 0 0 1-2 0z"/>
    </svg>`
  });

  contentArea.innerHTML = `
    <div id="accounting-header"></div>
    <div id="sales-controls">
      <button id="add-sale-button" class="btn btn-primary">
        <svg viewBox='0 0 20 20' fill='none' style="width:20px; height:20px; margin-right: 8px;"><circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/><path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/></svg>
        ${t('add_new_sale') || 'Add New Sale'}
      </button><br></br>
    </div>
    <table id="sales-table" class="modern-table">
      <thead>
        <tr>
          <th data-column-key="Id" class="sortable" title="${t('table_header_id') || 'ID'}">${t('table_header_id_short') || 'ID'}</th>
          <th data-column-key="CariUnvan" class="sortable" title="${t('table_header_account') || 'Cari Hesap'}">${t('table_header_account_short') || 'Cari'}</th>
          <th data-column-key="UrunAdi" class="sortable" title="${t('table_header_product') || 'Ürün'}">${t('table_header_product_short') || 'Ürün'}</th>
          <th data-column-key="Tarih" class="sortable" title="${t('table_header_date') || 'Tarih'}">${t('table_header_date_short') || 'Tarih'}</th>
          <th data-column-key="FaturaNo" class="sortable" title="${t('table_header_invoice_no') || 'Fatura Numarası'}">${t('table_header_invoice_no_short') || 'Fatura'}</th>
          <th data-column-key="Miktar" class="sortable" title="${t('table_header_quantity') || 'Miktar'}">${t('table_header_quantity_short') || 'Adet'}</th>
          <th data-column-key="BirimFiyat" class="sortable" title="${t('table_header_unit_price') || 'Birim Fiyat'}">${t('table_header_unit_price_short') || 'B.Fiyat'}</th>
          <th data-column-key="ToplamTutar" class="sortable" title="${t('table_header_total_amount') || 'Toplam Tutar'}">${t('table_header_total_amount_short') || 'Toplam'}</th>
          <th data-column-key="TahsilEdilen" class="sortable" title="${t('table_header_collected_amount') || 'Tahsil Edilen'}">${t('table_header_collected_amount_short') || 'Tahsil'}</th>
          <th data-column-key="KalanAlacak" class="sortable" title="${t('table_header_remaining_receivable') || 'Kalan Alacak'}">${t('table_header_remaining_receivable_short') || 'K.Alacak'}</th>
          <th data-column-key="OdemeDurumu" class="sortable" title="${t('table_header_payment_type') || 'Ödeme Tipi'}">${t('table_header_payment_type_short') || 'Ö.Tipi'}</th>
          <th data-column-key="IslemTamamlandi" class="sortable" title="${t('table_header_transaction_status') || 'İşlem Durumu'}">${t('table_header_transaction_status_short') || 'Durum'}</th>
          <th class="col-actions" title="${t('table_header_actions') || 'İşlemler'}">${t('table_header_actions_short') || 'İşlem'}</th>
        </tr>
        <tr class="filter-row">
          <th><input type="number" id="filter-satislar-id" class="filter-input" placeholder="${t('satislar_col_id')}"></th>
          <th><input type="text" id="filter-satislar-cari" class="filter-input" placeholder="${t('satislar_col_cari')}"></th>
          <th><input type="text" id="filter-satislar-urun" class="filter-input" placeholder="${t('satislar_col_urun')}"></th>
          <th>
            <input type="date" id="filter-satislar-tarih-start" class="filter-input">
            <input type="date" id="filter-satislar-tarih-end" class="filter-input">
          </th>
          <th><input type="text" id="filter-satislar-fatura" class="filter-input" placeholder="${t('satislar_col_fatura_no')}"></th>
          <th></th> <!-- Miktar -->
          <th></th> <!-- Birim Fiyat -->
          <th></th> <!-- Toplam Tutar -->
          <th></th> <!-- Ödenen Tutar -->
          <th></th> <!-- Kalan Tutar -->
          <th>
            <select id="filter-satislar-odeme-durumu" class="filter-input">
              <option value="">${t('option_all')}</option>
              <option value="Pesin">${t('payment_type_pesin') || 'Peşin'}</option>
              <option value="Vadeli">${t('payment_type_vadeli') || 'Vadeli'}</option>
            </select>
          </th>
          <th>
            <select id="filter-satislar-islem-durumu" class="filter-input">
              <option value="">${t('option_all')}</option>
              <option value="paid">${t('transaction_status_paid') || 'Tahsil Edildi'}</option>
              <option value="partially_paid">${t('transaction_status_partially_paid') || 'Kısmi Tahsil Edildi'}</option>
              <option value="unpaid">${t('transaction_status_unpaid') || 'Tahsil Edilmedi'}</option>
            </select>
          </th>
          <th></th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>

    <div id="satislar-pagination-controls" class="pagination-controls">
      <button id="prev-page-satislar" disabled>&laquo; ${t('btn_previous') || 'Previous'}</button>
      <span id="page-info-satislar">Page 1 of 1</span>
      <button id="next-page-satislar" disabled>&raquo; ${t('btn_next') || 'Next'}</button>
    </div>

    <div id="sales-recent-activities"></div>

    <div id="sale-modal" class="modal hidden">
      <div class="modal-content large">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="sale-modal-title">${t('sale_modal_title_add') || 'Add New Sale'}</h2>
        </div>
        <form id="sale-form">
          <div class="modal-body">
          <!-- Sale Information Section -->
          <div class="form-section">
            <h3 class="section-title">${t('section_sale_info') || 'Satış Bilgileri'}</h3>
            <div class="form-grid">
              <div class="form-row">
                <label for="satis-cari">${t('satislar_col_cari')}</label>
                <select name="CariId" id="satis-cari" required></select>
              </div>
              <div class="form-row">
                <label for="satis-urun">${t('satislar_col_urun')}</label>
                <select name="UrunId" id="satis-urun" required></select>
              </div>
              <div class="form-row">
                <label for="satis-tarih">${t('satislar_col_tarih')}</label>
                <input type="date" name="Tarih" id="satis-tarih" required>
              </div>
              <div class="form-row">
                <label for="satis-fatura-no">${t('satislar_col_fatura_no')}</label>
                <input type="text" name="FaturaNo" id="satis-fatura-no">
              </div>
            </div>
          </div>

          <!-- Quantity and Pricing Section -->
          <div class="form-section">
            <h3 class="section-title">${t('section_quantity_pricing') || 'Miktar ve Fiyat'}</h3>
            <div class="form-grid">
              <div class="form-row">
                <label for="satis-miktar">${t('satislar_col_miktar')}</label>
                <input type="number" step="0.01" name="Miktar" id="satis-miktar" required min="0">
              </div>
              <div class="form-row">
                <label for="satis-birim-fiyat">${t('satislar_col_birim_fiyat')}</label>
                <input type="number" step="0.01" name="BirimFiyat" id="satis-birim-fiyat" required min="0">
              </div>
              <div class="form-row">
                <label for="satis-toplam-tutar">${t('label_total_amount') || 'Toplam Tutar'}</label>
                <input type="number" step="0.01" name="ToplamTutar" id="satis-toplam-tutar" readonly>
              </div>
            </div>
          </div>

          <!-- Payment Information Section -->
          <div class="form-section">
            <h3 class="section-title">${t('section_payment_info') || 'Ödeme Bilgileri'}</h3>
            <div class="form-grid">
              <div class="form-row">
                <label for="satis-odeme-durumu">${t('satislar_col_odeme_durumu')}</label>
                <select name="OdemeDurumu" id="satis-odeme-durumu" onchange="toggleDueDate(this.value)">
                  <option value="Pesin">${t('payment_type_pesin')}</option>
                  <option value="Vadeli">${t('payment_type_vadeli')}</option>
                </select>
              </div>
              <div class="form-row due-date-field" style="display: none;">
                <label for="satis-vade-tarihi">${t('satislar_col_vade_tarihi') || 'Vade Tarihi'}</label>
                <input type="date" name="VadeTarihi" id="satis-vade-tarihi">
              </div>
            </div>
          </div>

          <!-- Additional Information Section -->
          <div class="form-section">
            <h3 class="section-title">${t('section_additional_info') || 'Ek Bilgiler'}</h3>
            <div class="form-row full">
              <label for="satis-notlar">${t('satislar_col_notlar') || 'Notlar'}</label>
              <textarea name="Notlar" id="satis-notlar" rows="3"></textarea>
            </div>
          </div>

          <input type="hidden" name="Id">
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('sale-modal').classList.add('hidden')">${t('btn_cancel') || 'İptal'}</button>
              <button type="submit" class="btn btn-primary">${t('btn_save')}</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  `;

  // Initialize accounting header immediately after HTML is created
  accountingHeader.init();

  salesTableBody = contentArea.querySelector('#sales-table tbody');
  saleModal = contentArea.querySelector('#sale-modal');
  saleForm = contentArea.querySelector('#sale-form');
  saleModalTitle = contentArea.querySelector('#sale-modal-title');

  pageInfo = contentArea.querySelector('#page-info-satislar');
  prevButton = contentArea.querySelector('#prev-page-satislar');
  nextButton = contentArea.querySelector('#next-page-satislar');

  const addSaleButton = contentArea.querySelector('#add-sale-button');
  addSaleButton.addEventListener('click', () => openSaleModal(null));

  const closeBtn = saleModal.querySelector('.close-btn');
  closeBtn.addEventListener('click', () => saleModal.classList.add('hidden'));
  saleModal.addEventListener('click', (e) => {
    if (e.target === saleModal) saleModal.classList.add('hidden');
  });

  await Promise.all([loadUrunlerForSatisSelect(), loadCarilerForSatisSelect()]);

  saleForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    const formData = new FormData(saleForm);
    const saleData = Object.fromEntries(formData.entries());
    saleData.CariId = parseInt(saleData.CariId);
    saleData.UrunId = parseInt(saleData.UrunId);
    saleData.Miktar = parseFloat(saleData.Miktar);
    saleData.BirimFiyat = parseFloat(saleData.BirimFiyat);
    if (saleData.Tarih) saleData.Tarih = new Date(saleData.Tarih).toISOString().split('T')[0];
    if (saleData.VadeTarihi) saleData.VadeTarihi = new Date(saleData.VadeTarihi).toISOString().split('T')[0];

    try {
      if (currentEditingSaleId) {
        await window.api.invoke('sales:update', { id: currentEditingSaleId, data: saleData });
        window.toast.success(t('toast_success_update'));
      } else {
        await window.api.invoke('sales:add', saleData);
        window.toast.success(t('toast_success_save'));
      }
      saleModal.classList.add('hidden');
      allSales = await window.api.invoke('sales:list');
      applyFiltersAndSortSales();
      // Refresh recent activities
      if (salesRecentActivities) {
        salesRecentActivities.refresh();
      }
    } catch (error) {
      console.error('Satış save/update error:', error);
      window.toast.error(t('error_generic_api', { error: error.message || 'Unknown error' }));
    }
  });

  allSales = await window.api.invoke('sales:list');
  currentSales = [...allSales];

  // Setup sorting event listeners
  const sortableHeaders = contentArea.querySelectorAll('#sales-table thead th[data-column-key]');
  sortableHeaders.forEach(th => {
    const columnKey = th.dataset.columnKey;
    if (!columnKey) return;

    th.classList.add('sortable');
    th.addEventListener('click', () => {
      if (sortColumn === columnKey) {
        sortDirection *= -1;
      } else {
        sortColumn = columnKey;
        sortDirection = 1;
      }
      // Clear all sorting classes
      sortableHeaders.forEach(headerTh => {
        headerTh.classList.remove('sorted-asc', 'sorted-desc');
      });

      // Add sorting class to current header
      th.classList.add(sortDirection === 1 ? 'sorted-asc' : 'sorted-desc');

      applyFiltersAndSortSales();
    });
  });

  applyFiltersAndSortSales();

  const filterInputs = [
    '#filter-satislar-id', '#filter-satislar-cari', '#filter-satislar-urun',
    '#filter-satislar-tarih-start', '#filter-satislar-tarih-end',
    '#filter-satislar-fatura', '#filter-satislar-odeme-durumu', '#filter-satislar-islem-durumu'
  ];
  filterInputs.forEach(selector => {
    const inputElement = contentArea.querySelector(selector);
    if (inputElement) {
      inputElement.addEventListener('input', applyFiltersAndSortSales);
      if (inputElement.tagName === 'SELECT' || inputElement.type === 'date') {
        inputElement.addEventListener('change', applyFiltersAndSortSales);
      }
    }
  });

  prevButton.addEventListener('click', () => {
    if (currentPage > 1) {
      currentPage--;
      renderSalesTablePage();
      updateSalesPaginationControls();
    }
  });
  nextButton.addEventListener('click', () => {
    const totalPages = Math.ceil(currentSales.length / itemsPerPage);
    if (currentPage < totalPages) {
      currentPage++;
      renderSalesTablePage();
      updateSalesPaginationControls();
    }
  });

  // Initialize Recent Activities
  salesRecentActivities = new RecentActivities({
    containerId: 'sales-recent-activities',
    title: t('recent_sales_activities') || 'Recent Sales Activities',
    maxItems: 5,
    onViewAll: () => {
      // Scroll to main table
      document.getElementById('satislar-table').scrollIntoView({ behavior: 'smooth' });
    },
    getRecords: async () => {
      try {
        const sales = await window.api.invoke('sales:list');
        return sales.sort((a, b) => new Date(b.Tarih) - new Date(a.Tarih));
      } catch (error) {
        console.error('Error fetching sales records:', error);
        return [];
      }
    },
    formatRecord: (record) => {
      const amount = record.ToplamTutar || (record.Miktar * record.BirimFiyat);
      return {
        title: truncateText(record.CariUnvan || 'Unknown Account', 25),
        subtitle: truncateText(record.UrunAdi || 'Unknown Product', 30),
        date: formatActivityDate(record.Tarih),
        amount: amount ? `₺${amount.toFixed(2)}` : null,
        badge: BadgeSystem.createPaymentMethodBadge(record.OdemeDurumu, {
          cash: t('payment_type_cash'),
          credit: t('payment_type_credit')
        })
      };
    },
    emptyMessage: t('no_recent_sales_activities') || 'No recent sales activities'
  });



  // Initialize recent activities
  salesRecentActivities.init();

  // Ensure modals are properly structured
  setTimeout(() => {
    if (window.reinitializeModals) {
      window.reinitializeModals();
    }
  }, 100);
}

function applyFiltersAndSortSales() {
  const t = passedI18n.t;
  let filtered = [...allSales];

  const idFilter = document.getElementById('filter-satislar-id')?.value;
  const cariFilter = document.getElementById('filter-satislar-cari')?.value.trim().toLowerCase();
  const urunFilter = document.getElementById('filter-satislar-urun')?.value.trim().toLowerCase();
  const tarihStartFilter = document.getElementById('filter-satislar-tarih-start')?.value;
  const tarihEndFilter = document.getElementById('filter-satislar-tarih-end')?.value;
  const faturaFilter = document.getElementById('filter-satislar-fatura')?.value.trim().toLowerCase();
  const odemeDurumuFilter = document.getElementById('filter-satislar-odeme-durumu')?.value;
  const islemDurumuFilter = document.getElementById('filter-satislar-islem-durumu')?.value;

  if (idFilter) filtered = filtered.filter(s => s.Id == idFilter);
  if (cariFilter) filtered = filtered.filter(s => (s.CariUnvan || '').toLowerCase().includes(cariFilter));
  if (urunFilter) filtered = filtered.filter(s => (s.UrunAdi || '').toLowerCase().includes(urunFilter));
  if (tarihStartFilter) filtered = filtered.filter(s => new Date(s.Tarih) >= new Date(tarihStartFilter));
  if (tarihEndFilter) filtered = filtered.filter(s => new Date(s.Tarih) <= new Date(tarihEndFilter));
  if (faturaFilter) filtered = filtered.filter(s => (s.FaturaNo || '').toLowerCase().includes(faturaFilter));
  if (odemeDurumuFilter) filtered = filtered.filter(s => s.OdemeDurumu === odemeDurumuFilter);
  if (islemDurumuFilter) {
    filtered = filtered.filter(s => {
      const status = getSatisIslemDurumu(s, t); // Helper to determine status string
      if (islemDurumuFilter === 'paid') return status === (t('transaction_status_paid') || 'Tahsil Edildi');
      if (islemDurumuFilter === 'partially_paid') return status === (t('transaction_status_partially_paid') || 'Kısmi Tahsil Edildi');
      if (islemDurumuFilter === 'unpaid') return status === (t('transaction_status_unpaid') || 'Tahsil Edilmedi');
      return false;
    });
  }

  if (sortColumn) {
    filtered = sortSalesList(filtered, sortColumn, sortDirection);
  }

  currentSales = filtered;
  currentPage = 1;
  renderSalesTablePage();
  updateSalesPaginationControls();
}

function renderSalesTablePage() {
  const t = passedI18n.t;
  salesTableBody.innerHTML = '';

  const totalPages = Math.ceil(currentSales.length / itemsPerPage);
  currentPage = Math.max(1, Math.min(currentPage, totalPages || 1));

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const pageItems = currentSales.slice(startIndex, endIndex);

  if (pageItems.length === 0) {
    salesTableBody.innerHTML = `<tr><td colspan="13"><div class="empty-state-container"><div class="empty-state-icon">${passedDefaultAvatarSVG ? passedDefaultAvatarSVG(48) : ''}</div><p>${t('no_records_found')}</p></div></td></tr>`;
    return;
  }

  pageItems.forEach(satis => {
    const row = salesTableBody.insertRow();
    const toplamTutar = typeof satis.ToplamTutar === 'number' ? satis.ToplamTutar.toFixed(2) : 'N/A';
    const odenenTutar = typeof satis.OdenenTutar === 'number' ? satis.OdenenTutar.toFixed(2) : '0.00';
    const kalanTutar = typeof satis.KalanTutar === 'number' ? satis.KalanTutar.toFixed(2) : toplamTutar;

    // Using new badge system instead of old badge functions

    row.innerHTML = `
      <td class="cell-satislar-id">${satis.Id}</td>
      <td class="cell-satislar-cari">${satis.CariUnvan || (accountsCache.find(c=>c.Id === satis.CariId)?.Unvan) || t('unknown')}</td>
      <td class="cell-satislar-urun">${satis.UrunAdi || (productsCache.find(u=>u.Id === satis.UrunId)?.Ad) || t('unknown')}</td>
      <td class="cell-satislar-tarih">${satis.Tarih ? new Date(satis.Tarih).toLocaleDateString() : ''}</td>
      <td class="cell-satislar-fatura">${satis.FaturaNo || ''}</td>
      <td class="cell-satislar-miktar">${satis.Miktar || ''}</td>
      <td class="cell-satislar-birimfiyat">${satis.BirimFiyat ? satis.BirimFiyat.toFixed(2) : ''}</td>
      <td class="cell-satislar-toplamtutar">${toplamTutar}</td>
      <td class="cell-satislar-odenentutar">${odenenTutar}</td>
      <td class="cell-satislar-kalantutar">${kalanTutar}</td>
      <td class="cell-satislar-odemedurumu">${BadgeSystem.createPaymentMethodBadge(satis.OdemeDurumu, { cash: t('payment_type_cash'), credit: t('payment_type_credit') })}</td>
      <td class="cell-satislar-islemdurumu">${BadgeSystem.createTransactionStatusBadge(satis, { paid: t('transaction_status_paid'), unpaid: t('transaction_status_unpaid'), partiallyPaid: t('transaction_status_partially_paid') })}</td>
      <td class="col-actions actions">
        ${IconSystem.createActionsWrapper(satis.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
      </td>
    `;
    row.querySelector('.edit-btn').addEventListener('click', () => openSaleModal(satis));
    row.querySelector('.delete-btn').addEventListener('click', () => deleteSale(satis.Id));
  });
  
  // Initialize table animations
  setTimeout(() => {
    TableAnimations.initializeTable('#sales-table');
  }, 100);
}

function updateSalesPaginationControls() {
  const t = passedI18n.t;
  const totalItems = currentSales.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
  pageInfo.textContent = t('page_info_text', { currentPage: currentPage, totalPages, totalItems }) || `Page ${currentPage} of ${totalPages} (${totalItems} items)`;
  prevButton.disabled = currentPage === 1;
  nextButton.disabled = currentPage === totalPages;
}

async function loadUrunlerForSatisSelect() {
  const t = passedI18n.t;
  try {
    productsCache = await window.api.invoke('products:list');
    const productSelect = saleForm.elements['UrunId'];
    if (productSelect) {
      productSelect.innerHTML = `<option value="">${t('option_select_product') || 'Ürün Seçiniz'}</option>`;
      productsCache.forEach(urun => {
        const option = document.createElement('option');
        option.value = urun.Id;
        option.textContent = urun.Ad;
        productSelect.appendChild(option);
      });
    }
  } catch (error) {
    console.error('Error loading urunler for satis modal:', error);
  }
}

async function loadCarilerForSatisSelect() {
  const t = passedI18n.t;
  try {
    accountsCache = await window.api.invoke('accounts:list');
    const accountSelect = saleForm.elements['CariId'];
    if (accountSelect) {
      accountSelect.innerHTML = `<option value="">${t('option_select_cari') || 'Cari Seçiniz'}</option>`;
      const musteriler = accountsCache.filter(c => c.Tip === 'Musteri');
      (musteriler.length > 0 ? musteriler : accountsCache).forEach(cari => {
        const option = document.createElement('option');
        option.value = cari.Id;
        option.textContent = cari.Unvan;
        accountSelect.appendChild(option);
      });
    }
  } catch (error) {
    console.error('Error loading cariler for satis modal:', error);
  }
}

function getSatisOdemeDurumuBadge(satis, t) {
    let badgeClass = 'badge';
    let badgeText = t('payment_type_unknown') || 'Bilinmiyor';
    if (satis.OdemeDurumu === 'Pesin') {
        badgeClass = 'badge badge-pesin';
        badgeText = t('payment_type_pesin') || 'Peşin';
    } else if (satis.OdemeDurumu === 'Vadeli') {
        badgeClass = 'badge badge-vadeli';
        badgeText = t('payment_type_vadeli') || 'Vadeli';
    }
    return { badgeClass, badgeText };
}

function getSatisIslemDurumu(satis, t) { // For filter logic
    if (satis.IslemTamamlandi || (satis.ToplamTutar > 0 && satis.KalanTutar <= 0)) {
        return t('transaction_status_paid') || 'Tahsil Edildi';
    } else if (satis.OdenenTutar > 0 && satis.KalanTutar > 0) {
        return t('transaction_status_partially_paid') || 'Kısmi Tahsil Edildi';
    } else {
        return t('transaction_status_unpaid') || 'Tahsil Edilmedi';
    }
}

function getSatisIslemDurumuBadge(satis, t) {
    let badgeClass = 'badge badge-pasif';
    let badgeText = t('transaction_status_unpaid') || 'Tahsil Edilmedi';

    if (satis.IslemTamamlandi || (satis.ToplamTutar > 0 && satis.KalanTutar <= 0)) {
        badgeClass = 'badge badge-aktif';
        badgeText = t('transaction_status_paid') || 'Tahsil Edildi';
    } else if (satis.OdenenTutar > 0 && satis.KalanTutar > 0) {
        badgeClass = 'badge badge-vadeli';
        badgeText = t('transaction_status_partially_paid') || 'Kısmi Tahsil Edildi';
    }
    return { badgeClass, badgeText };
}

function openSaleModal(saleData = null) {
  const t = passedI18n.t;
  saleForm.reset();
  if (saleForm.elements['UrunId'].options.length <= 1 && productsCache.length > 0) {
      productsCache.forEach(product => {
        if (saleForm.elements['UrunId'].querySelector(`option[value="${product.Id}"]`)) return;
        const option = document.createElement('option');
        option.value = product.Id;
        option.textContent = product.Ad;
        saleForm.elements['UrunId'].appendChild(option);
      });
  }
  if (saleForm.elements['CariId'].options.length <= 1 && accountsCache.length > 0) {
      const customers = accountsCache.filter(c => c.Tip === 'Musteri');
      (customers.length > 0 ? customers : accountsCache).forEach(account => {
        if (saleForm.elements['CariId'].querySelector(`option[value="${account.Id}"]`)) return;
        const option = document.createElement('option');
        option.value = account.Id;
        option.textContent = account.Unvan;
        saleForm.elements['CariId'].appendChild(option);
      });
  }

  if (saleData) {
    currentEditingSaleId = saleData.Id;
    saleModalTitle.textContent = t('sale_modal_title_edit') || 'Edit Sale';
    saleForm.elements['Id'].value = saleData.Id;
    saleForm.elements['CariId'].value = saleData.CariId;
    saleForm.elements['UrunId'].value = saleData.UrunId;
    saleForm.elements['Tarih'].value = saleData.Tarih ? new Date(saleData.Tarih).toISOString().split('T')[0] : '';
    saleForm.elements['FaturaNo'].value = saleData.FaturaNo || '';
    saleForm.elements['Miktar'].value = saleData.Miktar || '';
    saleForm.elements['BirimFiyat'].value = saleData.BirimFiyat || '';
    saleForm.elements['OdemeDurumu'].value = saleData.OdemeDurumu || 'Pesin';
    saleForm.elements['VadeTarihi'].value = saleData.VadeTarihi ? new Date(saleData.VadeTarihi).toISOString().split('T')[0] : '';
    saleForm.elements['Notlar'].value = saleData.Notlar || '';
  } else {
    currentEditingSaleId = null;
    saleModalTitle.textContent = t('sale_modal_title_add') || 'Add New Sale';
    saleForm.elements['Id'].value = '';
    saleForm.elements['Tarih'].valueAsDate = new Date();
    saleForm.elements['OdemeDurumu'].value = 'Pesin';
  }
  saleModal.classList.remove('hidden');
}

async function deleteSale(id) {
  const t = passedI18n.t;
  const confirmed = await window.toast.confirm(t('confirm_delete_sale', { saleId: id }), {
    confirmText: t('toast_confirm'),
    cancelText: t('toast_cancel')
  });

  if (confirmed) {
    try {
      await window.api.invoke('sales:delete', id);
      allSales = await window.api.invoke('sales:list');
      applyFiltersAndSortSales();
      window.toast.success(t('toast_success_delete'));
    } catch (error) {
      console.error('Satış delete error:', error);
      window.toast.error(t('error_generic_api', { error: error.message || 'Unknown error' }));
    }
  }
}



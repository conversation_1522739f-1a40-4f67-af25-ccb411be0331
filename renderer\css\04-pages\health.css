/* =================================
   Health Page Styles
   ================================= */

/* =================================
   Health Quick Actions Section
   ================================= */
.health-quick-actions-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.health-quick-actions-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.health-page-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.health-page-title svg {
  width: 28px;
  height: 28px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

.health-quick-actions {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.health-quick-actions .btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.health-quick-actions .btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* =================================
   Health Navigation Section
   ================================= */
.health-navigation-section {
  margin-bottom: var(--space-6);
}

/* When animal selection bar is visible, reduce bottom margin */
.health-navigation-section:has(.animal-selection-bar[style*="block"]) {
  margin-bottom: var(--space-4);
}

/* =================================
   Animal Selection Bar
   ================================= */
.animal-selection-bar {
  background: var(--primary-50);
  border: 1px solid var(--border-primary);
  border-top: 1px solid var(--primary-200);
  border-radius: var(--radius-lg);
  padding: var(--space-4) var(--space-6);
  margin-top: var(--space-3);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.animal-selection-bar-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  max-width: 600px;
}



.animal-selection-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--primary-700);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.animal-selection-info svg {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.animal-selection-bar select {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--primary-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--bg-elevated);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  min-width: 200px;
}

.animal-selection-bar select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.animal-selection-bar select:hover {
  border-color: var(--primary-400);
}

/* =================================
   Health Navigation Tabs
   ================================= */
.health-nav-tabs,
#health-tabs.modern-tabs {
  display: flex;
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-1);
  margin-bottom: var(--space-6);
  overflow-x: auto;
  box-shadow: var(--shadow-sm);
  gap: var(--space-1);
}

.health-nav-tab,
.health-tab {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: var(--radius-lg);
  white-space: nowrap;
  font-size: var(--text-sm);
  min-width: 140px;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  justify-content: center;
}

.health-nav-tab:hover,
.health-tab:hover {
  color: var(--text-primary);
  background: var(--interactive-secondary);
  transform: translateY(-1px);
}

.health-nav-tab.active,
.health-tab.active {
  color: var(--text-inverse);
  background: var(--interactive-primary);
  box-shadow: var(--shadow-sm);
}

.health-tab svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.health-tab span {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

/* =================================
   Health Content Area
   ================================= */
.health-content,
#health-content {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  min-height: 500px;
  transition: all var(--transition-fast);
}

.health-content:hover,
#health-content:hover {
  box-shadow: var(--shadow-md);
}

.health-content-section {
  display: none;
}

.health-content-section.active {
  display: block;
}

/* Empty message styling */
#health-content .empty-message {
  text-align: center;
  padding: var(--space-12) var(--space-6);
  color: var(--text-tertiary);
}

#health-content .empty-message svg {
  margin-bottom: var(--space-4);
  opacity: 0.7;
}

/* Animal selection card */
.animal-selection-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.animal-selection-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.animal-selection-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
}

.animal-selection-header svg {
  width: 20px;
  height: 20px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

.animal-selection-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.animal-selection-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.animal-selection-content select {
  width: 100%;
  max-width: 400px;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.animal-selection-content select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.animal-selection-content select:hover {
  border-color: var(--border-secondary);
}

/* =================================
   Health Action Cards
   ================================= */
.health-actions-grid,
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

/* Legacy support for old quick action buttons */
.quick-action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.quick-action-btn:hover {
  background: var(--interactive-secondary);
  border-color: var(--border-focus);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.quick-action-btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
  margin-top: var(--space-6);
}

/* Animal Selection Controls */
.animal-selection-controls {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.animal-selection-controls .btn {
  font-size: var(--text-xs);
  padding: var(--space-1) var(--space-3);
}

/* Schedule Actions */
.schedule-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: center;
  margin-top: var(--space-4);
}

.schedule-actions .btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.schedule-actions .btn svg {
  width: 16px;
  height: 16px;
}

/* Reminder Actions */
.reminder-actions {
  display: flex;
  gap: var(--space-2);
  justify-content: flex-end;
}

.reminder-actions .btn {
  font-size: var(--text-xs);
  padding: var(--space-1) var(--space-2);
}

.reminder-actions .btn svg {
  width: 14px;
  height: 14px;
}

.health-action-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
}

.health-action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--interactive-primary);
  color: var(--text-primary);
  text-decoration: none;
}

.health-action-card .icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  background: var(--primary-50);
  color: var(--interactive-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.health-action-card .icon svg {
  width: 24px;
  height: 24px;
}

.health-action-card .content h3 {
  margin: 0 0 var(--space-1) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.health-action-card .content p {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* Health Action Card Variants */
.health-action-card.vaccination .icon {
  background: var(--success-50);
  color: var(--success-600);
}

.health-action-card.treatment .icon {
  background: var(--warning-50);
  color: var(--warning-600);
}

.health-action-card.checkup .icon {
  background: var(--info-50);
  color: var(--info-600);
}

.health-action-card.emergency .icon {
  background: var(--error-50);
  color: var(--error-600);
}

/* =================================
   Health Tables
   ================================= */
.health-table-container {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--space-6);
}



.health-table-header {
  padding: var(--space-4) var(--space-6);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.health-table-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.health-table-actions {
  display: flex;
  gap: var(--space-2);
}

/* =================================
   Health Calendar
   ================================= */
.health-calendar {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.health-calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
}

.health-calendar-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.health-calendar-nav {
  display: flex;
  gap: var(--space-2);
}

.health-calendar-nav button {
  padding: var(--space-2);
  background: var(--interactive-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.health-calendar-nav button:hover {
  background: var(--interactive-secondary-hover);
  border-color: var(--border-secondary);
}

.health-calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--space-1);
}

.health-calendar-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.health-calendar-day:hover {
  background: var(--interactive-secondary);
  border-color: var(--border-secondary);
}

.health-calendar-day.today {
  background: var(--primary-50);
  border-color: var(--interactive-primary);
  color: var(--interactive-primary);
  font-weight: var(--font-semibold);
}

.health-calendar-day.has-events::after {
  content: '';
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 6px;
  height: 6px;
  background: var(--status-warning);
  border-radius: 50%;
}

/* =================================
   Health Stats
   ================================= */
.health-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.health-stat-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  text-align: center;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.health-stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.health-stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  background: var(--primary-50);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-3);
}

.health-stat-icon svg {
  width: 24px;
  height: 24px;
}

.health-stat-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  line-height: var(--leading-tight);
}

.health-stat-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin: 0;
}

/* Health Stat Variants */
.health-stat-card.vaccinations .health-stat-icon {
  background: var(--success-50);
  color: var(--success-600);
}

.health-stat-card.treatments .health-stat-icon {
  background: var(--warning-50);
  color: var(--warning-600);
}

.health-stat-card.checkups .health-stat-icon {
  background: var(--info-50);
  color: var(--info-600);
}

.health-stat-card.alerts .health-stat-icon {
  background: var(--error-50);
  color: var(--error-600);
}

/* =================================
   Vaccination Schedule
   ================================= */
.vaccination-schedule-container {
  width: 100%;
}

.schedule-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.overview-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.overview-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.overview-card .card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.overview-card .card-icon svg {
  width: 24px;
  height: 24px;
}

.overview-card.overdue .card-icon {
  background: var(--error-50);
  color: var(--error-600);
}

.overview-card.upcoming .card-icon {
  background: var(--warning-50);
  color: var(--warning-600);
}

.overview-card.completed .card-icon {
  background: var(--success-50);
  color: var(--success-600);
}

.overview-card .card-content {
  flex: 1;
}

.overview-card .card-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  line-height: var(--leading-tight);
}

.overview-card .card-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
}

/* =================================
   Vaccination Management
   ================================= */
.vaccination-management-container {
  width: 100%;
}

.management-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.management-section h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

/* Quick animal filter */
#quick-animal-filter {
  width: 200px;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: var(--input-bg);
  color: var(--text-primary);
}

#quick-animal-filter:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 1024px) {
  .health-quick-actions-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }

  .health-quick-actions {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .health-quick-actions-section {
    padding: var(--space-4);
  }

  .health-page-title {
    font-size: var(--text-xl);
  }

  .health-page-title svg {
    width: 24px;
    height: 24px;
  }

  .health-quick-actions {
    flex-direction: column;
    width: 100%;
  }

  .health-quick-actions .btn {
    justify-content: center;
    width: 100%;
  }

  .health-nav-tabs {
    flex-direction: column;
    gap: var(--space-2);
  }

  .health-tab {
    min-width: auto;
    justify-content: flex-start;
    padding: var(--space-3);
  }

  .health-tab span {
    font-size: var(--text-xs);
  }

  .animal-selection-bar {
    padding: var(--space-3) var(--space-4);
  }

  .animal-selection-bar-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
    max-width: none;
  }

  .animal-selection-bar select {
    width: 100%;
    min-width: auto;
  }

  .health-content,
  #health-content {
    padding: var(--space-4);
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .health-quick-actions-section {
    padding: var(--space-3);
  }

  .health-page-title {
    font-size: var(--text-lg);
  }

  .health-quick-actions .btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }

  .health-quick-actions .btn svg {
    width: 14px;
    height: 14px;
  }

  .health-nav-tabs {
    padding: var(--space-2);
  }

  .health-tab {
    padding: var(--space-2);
    font-size: var(--text-xs);
  }

  .health-tab svg {
    width: 14px;
    height: 14px;
  }

  .animal-selection-bar {
    padding: var(--space-2) var(--space-3);
  }

  .animal-selection-info {
    font-size: var(--text-xs);
  }

  .animal-selection-info svg {
    width: 16px;
    height: 16px;
  }
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 768px) {
  .health-nav-tabs {
    flex-direction: column;
    gap: var(--space-1);
  }

  .health-nav-tab {
    min-width: auto;
    text-align: center;
  }

  .health-content {
    padding: var(--space-4);
  }

  .health-actions-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .health-action-card {
    padding: var(--space-4);
    gap: var(--space-3);
  }

  .health-action-card .icon {
    width: 40px;
    height: 40px;
  }

  .health-action-card .icon svg {
    width: 20px;
    height: 20px;
  }

  .health-calendar {
    padding: var(--space-4);
  }

  .health-calendar-header {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }

  .health-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }

  .health-stat-card {
    padding: var(--space-4);
  }

  .health-stat-icon {
    width: 40px;
    height: 40px;
    margin-bottom: var(--space-2);
  }

  .health-stat-icon svg {
    width: 20px;
    height: 20px;
  }

  .health-stat-value {
    font-size: var(--text-xl);
  }
}

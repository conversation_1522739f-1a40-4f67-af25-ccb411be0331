/**
 * Centralized Badge System for Livestock Management Application
 * Provides standardized badge creation with consistent styling and semantic meaning
 */

export class BadgeSystem {
  /**
   * Badge types with semantic meaning
   */
  static TYPES = {
    // Status badges
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    PENDING: 'pending',
    COMPLETED: 'completed',
    
    // Animal status badges
    PREGNANT: 'pregnant',
    EMPTY: 'empty',
    MILKING: 'milking',
    DRY: 'dry',
    
    // Business type badges
    CUSTOMER: 'customer',
    SUPPLIER: 'supplier',
    
    // Transaction badges
    PURCHASE: 'purchase',
    SALE: 'sale',
    PAID: 'paid',
    UNPAID: 'unpaid',
    PARTIAL: 'partial',
    
    // Payment method badges
    CASH: 'cash',
    CREDIT: 'vadeli',
    
    // Milk yield badges
    MILK_HIGH: 'milk-high',
    MILK_LOW: 'milk-low',
    
    // General purpose
    INFO: 'info',
    NEUTRAL: 'neutral',
    DEFAULT: 'default'
  };

  /**
   * Create a standardized badge
   * @param {string} text - Badge text content
   * @param {string} type - Badge type from BadgeSystem.TYPES
   * @param {Object} options - Additional options
   * @returns {string} Badge HTML string
   */
  static createBadge(text, type = BadgeSystem.TYPES.DEFAULT, options = {}) {
    const className = options.className || 'badge';
    const extraClasses = options.extraClasses || '';
    const title = options.title || '';
    
    return `<span class="${className} badge-${type} ${extraClasses}" ${title ? `title="${title}"` : ''}>
      ${text}
    </span>`;
  }

  /**
   * Create an animal status badge
   * @param {string} status - Animal status (Gebe, Bos, Sagimda, Kuru, etc.)
   * @param {Object} translations - Translation object
   * @returns {string} Badge HTML string
   */
  static createAnimalStatusBadge(status, translations = {}) {
    const statusMap = {
      'Gebe': { type: 'success', text: translations.pregnant || 'Pregnant' },
      'Bos': { type: 'error', text: translations.empty || 'Empty' },
      'Sagimda': { type: 'success', text: translations.milking || 'Milking' },
      'Sağmal': { type: 'success', text: translations.milking || 'Milking' },
      'Kuru': { type: 'info', text: translations.dry || 'Dry' }
    };

    const config = statusMap[status] || { type: 'secondary', text: status };
    return BadgeSystem.createBadge(config.text, config.type, { className: 'table-badge' });
  }

  /**
   * Create a business type badge
   * @param {string} type - Business type (Musteri, Tedarikci, etc.)
   * @param {Object} translations - Translation object
   * @returns {string} Badge HTML string
   */
  static createBusinessTypeBadge(type, translations = {}) {
    const typeMap = {
      'Musteri': { type: BadgeSystem.TYPES.CUSTOMER, text: translations.customer || 'Customer' },
      'Tedarikci': { type: BadgeSystem.TYPES.SUPPLIER, text: translations.supplier || 'Supplier' }
    };

    const config = typeMap[type] || { type: BadgeSystem.TYPES.DEFAULT, text: type };
    return BadgeSystem.createBadge(config.text, config.type, { className: 'status-badge' });
  }

  /**
   * Create a transaction status badge
   * @param {Object} transaction - Transaction object with status information
   * @param {Object} translations - Translation object
   * @returns {string} Badge HTML string
   */
  static createTransactionStatusBadge(transaction, translations = {}) {
    if (!transaction) {
      return BadgeSystem.createBadge('N/A', BadgeSystem.TYPES.DEFAULT, { className: 'status-badge' });
    }

    let type, text;

    if (transaction.IslemTamamlandi || (transaction.ToplamTutar > 0 && transaction.KalanTutar <= 0)) {
      type = BadgeSystem.TYPES.PAID;
      text = translations.paid || 'Ödendi';
    } else if (transaction.OdenenTutar > 0 && transaction.KalanTutar > 0) {
      type = BadgeSystem.TYPES.PARTIAL;
      text = translations.partiallyPaid || 'Kısmi Ödendi';
    } else {
      type = BadgeSystem.TYPES.UNPAID;
      text = translations.unpaid || 'Ödenmedi';
    }

    return BadgeSystem.createBadge(text, type, { className: 'status-badge' });
  }

  /**
   * Create a payment method badge
   * @param {string} method - Payment method
   * @param {Object} translations - Translation object
   * @returns {string} Badge HTML string
   */
  static createPaymentMethodBadge(method, translations = {}) {
    const methodMap = {
      'Pesin': { type: BadgeSystem.TYPES.CASH, text: translations.cash || 'Peşin' },
      'Vadeli': { type: BadgeSystem.TYPES.CREDIT, text: translations.credit || 'Vadeli' }
    };

    const config = methodMap[method] || { type: BadgeSystem.TYPES.DEFAULT, text: method || 'Bilinmiyor' };
    return BadgeSystem.createBadge(config.text, config.type, { className: 'status-badge' });
  }

  /**
   * Create a milk yield badge based on amount
   * @param {number} amount - Milk yield amount
   * @param {number} threshold - Threshold for high/low classification (default: 20)
   * @param {Object} translations - Translation object
   * @returns {string} Badge HTML string
   */
  static createMilkYieldBadge(amount, threshold = 30, translations = {}) {
    if (!amount || amount <= 0) {
      return BadgeSystem.createBadge(translations.noMilk || 'No Milk', 'error', { className: 'table-badge' });
    }

    const type = amount >= threshold ? 'success' : 'warning';
    const text = `${amount.toFixed(1)} L`;

    return BadgeSystem.createBadge(text, type, {
      className: 'table-badge',
      title: `${translations.dailyMilk || 'Daily Milk'}: ${text}`
    });
  }

  /**
   * Create an active/inactive status badge
   * @param {boolean} isActive - Whether the item is active
   * @param {Object} translations - Translation object
   * @returns {string} Badge HTML string
   */
  static createActiveStatusBadge(isActive, translations = {}) {
    const text = isActive ? (translations.active || 'Active') : (translations.inactive || 'Inactive');
    const type = isActive ? 'success' : 'error';
    return BadgeSystem.createBadge(text, type, { className: 'table-badge' });
  }

  /**
   * Create a generic status badge with automatic type detection
   * @param {string} status - Status string
   * @param {Object} translations - Translation object
   * @returns {string} Badge HTML string
   */
  static createGenericStatusBadge(status, translations = {}) {
    // Try to detect the appropriate badge type based on status value
    const lowerStatus = status.toLowerCase();

    if (['active', 'aktif', 'paid', 'ödendi'].includes(lowerStatus)) {
      return BadgeSystem.createBadge(status, BadgeSystem.TYPES.ACTIVE, { className: 'status-badge' });
    }

    if (['inactive', 'pasif', 'unpaid', 'ödenmedi'].includes(lowerStatus)) {
      return BadgeSystem.createBadge(status, BadgeSystem.TYPES.INACTIVE, { className: 'status-badge' });
    }

    if (['pending', 'beklemede', 'partial', 'kısmi'].includes(lowerStatus)) {
      return BadgeSystem.createBadge(status, BadgeSystem.TYPES.PENDING, { className: 'status-badge' });
    }

    // Default to neutral badge
    return BadgeSystem.createBadge(status, BadgeSystem.TYPES.NEUTRAL, { className: 'status-badge' });
  }
}

/**
 * Legacy support - export individual badge functions for backward compatibility
 */
export const createBadge = BadgeSystem.createBadge;
export const createAnimalStatusBadge = BadgeSystem.createAnimalStatusBadge;
export const createBusinessTypeBadge = BadgeSystem.createBusinessTypeBadge;
export const createTransactionStatusBadge = BadgeSystem.createTransactionStatusBadge;

const { ipcMain } = require('electron');
const { getDb } = require('../services/database.js');

// Helper function to get current profile
function getCurrentProfile() {
  try {
    const authHandlers = require('./auth-handlers.js');
    return authHandlers.getCurrentProfile();
  } catch (error) {
    console.error('Error getting current profile:', error);
    return null;
  }
}

ipcMain.handle('reports:milk-yield-summary', async (event, { animalId, startDate, endDate }) => {
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    // animalId şimdilik kullanılmıyor, tüm hayvanlar için özet varsayalım.
    // Daha sonra HayvanId'ye göre filtreleme eklenebilir.
    // Veya LaktasyonId'ye göre de düşünülebilir. Şimdilik genel bir özet.

    // <PERSON><PERSON> sorgu, her hayvan için belirtilen tarih aralığındaki toplam süt miktarını
    // ve ortalama günlük süt miktarını hesaplar.
    // Laktasyonlar ve SagimVerileri tablolarını birleştirir.
    // Sonra Hayvanlar tablosu ile birleştirerek hayvanın küpe numarasını alır.
    const query = `
      SELECT
        H.KupeNo AS HayvanKupeNo,
        SUM(SV.Miktar) AS ToplamSut,
        AVG(SV.Miktar) AS OrtalamaGunlukSut,
        COUNT(DISTINCT SV.Tarih) AS SagimYapilanGunSayisi
      FROM SagimVerileri SV
      JOIN Laktasyonlar L ON SV.LaktasyonId = L.Id
      JOIN Hayvanlar H ON L.HayvanId = H.Id
      WHERE SV.Tarih BETWEEN ? AND ? AND H.ProfilId = ?
      GROUP BY H.Id, H.KupeNo
      ORDER BY H.KupeNo;
    `;
    // Eğer belirli bir hayvan için isteniyorsa:
    // const query = `
    //   SELECT
    //     H.KupeNo AS HayvanKupeNo,
    //     SUM(SV.Miktar) AS ToplamSut,
    //     AVG(SV.Miktar) AS OrtalamaGunlukSut,
    //     COUNT(DISTINCT SV.Tarih) AS SagimYapilanGunSayisi
    //   FROM SagimVerileri SV
    //   JOIN Laktasyonlar L ON SV.LaktasyonId = L.Id
    //   JOIN Hayvanlar H ON L.HayvanId = H.Id
    //   WHERE H.Id = ? AND SV.Tarih BETWEEN ? AND ?
    //   GROUP BY H.Id, H.KupeNo;
    // `;
    // getDb().all(query, [animalId, startDate, endDate], (err, rows) => { ... });


    getDb().all(query, [startDate, endDate, currentProfile.id], (err, rows) => {
      if (err) {
        console.error("Error fetching milk yield summary:", err);
        reject(err);
      } else {
        // Ortalama günlük sütü daha doğru hesaplamak için: ToplamSut / SagimYapilanGunSayisi
        const processedRows = rows.map(row => ({
          ...row,
          OrtalamaGunlukSut: row.SagimYapilanGunSayisi > 0 ? (row.ToplamSut / row.SagimYapilanGunSayisi) : 0
        }));
        resolve(processedRows);
      }
    });
  });
});

ipcMain.handle('reports:animal-list', async (event, filters = {}) => {
  // filters objesi { AktifMi: true/false, Tur: 'Sığır', Irk: 'Holstein' } gibi olabilir.
  // Şimdilik basit bir tüm hayvan listesi.
  return new Promise((resolve, reject) => {
    const currentProfile = getCurrentProfile();
    if (!currentProfile) {
      reject(new Error('Aktif profil bulunamadı'));
      return;
    }

    let query = 'SELECT Id, KupeNo, Isim, Tur, Irk, Cinsiyet, DogumTarihi FROM Hayvanlar WHERE ProfilId = ?';
    const queryParams = [currentProfile.id];

    // Örnek filtreleme (ileride eklenebilir)
    // if (filters.AktifMi !== undefined) {
    //   query += ' AND AktifMi = ?';
    //   queryParams.push(filters.AktifMi);
    // }

    query += ' ORDER BY KupeNo';

    getDb().all(query, queryParams, (err, rows) => {
      if (err) {
        console.error("Error fetching animal list for report:", err);
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
});


console.log('Reports IPC handlers registered.');

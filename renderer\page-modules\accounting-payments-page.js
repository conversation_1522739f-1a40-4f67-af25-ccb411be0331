import { IconSystem } from '../utils/icon-system.js';
import { BadgeSystem } from '../utils/badge-system.js';
import { RecentActivities, formatActivityDate, truncateText } from '../components/recent-activities.js';
import { AccountingHeader } from '../components/accounting-header.js';
import { TableAnimations } from '../utils/table-animations.js';

// Globals for this module
let passedI18nOdemeler;
let passedDefaultAvatarSVGOdemeler;

let allPayments = [];
let currentPayments = [];
const itemsPerPage = 10;
let currentPage = 1;
let sortColumn = 'OdemeTarihi'; // Default sort column
let sortDirection = -1; // Default sort direction (desc for date)

// Turkish variable names for compatibility
let allOdemeler = [];
let currentOdemeler = [];
const itemsPerPageOdemeler = 10;
let currentPageOdemeler = 1;
let sortColumnOdemeler = 'OdemeTarihi';
let sortDirectionOdemeler = -1;

let alislarCacheOdemeler = [];
let satislarCacheOdemeler = [];
let paymentsRecentActivities;

// Helper function to synchronize English and Turkish variables
function syncPaymentsData() {
  allOdemeler = [...allPayments];
  currentOdemeler = [...currentPayments];
  currentPageOdemeler = currentPage;
  sortColumnOdemeler = sortColumn;
  sortDirectionOdemeler = sortDirection;
}

// To be assigned in renderMuhasebeOdemelerPage
let odemelerTableBody;
let odemeModal;
let odemeForm;
let odemeModalTitle;
let currentEditingOdemeId = null;
let pageInfoOdemeler;
let prevButtonOdemeler;
let nextButtonOdemeler;

function sortOdemelerList(list, column, direction) {
  return [...list].sort((a, b) => {
    let valA = a[column];
    let valB = b[column];

    if (valA === null || valA === undefined) valA = '';
    if (valB === null || valB === undefined) valB = '';

    if (column === 'OdemeTarihi') {
      valA = new Date(valA);
      valB = new Date(valB);
      return (valA - valB) * direction;
    }
    if (column === 'Tutar' || column === 'Id') {
      valA = Number(valA);
      valB = Number(valB);
      return (valA - valB) * direction;
    }
    if (typeof valA === 'string' && typeof valB === 'string') {
      return valA.localeCompare(valB, undefined, { sensitivity: 'base' }) * direction;
    }
    if (valA < valB) return -1 * direction;
    if (valA > valB) return 1 * direction;
    return 0;
  });
}

export async function renderAccountingPaymentsPage(contentArea, i18n, defaultAvatarSVG) {
  passedI18nOdemeler = i18n;
  passedDefaultAvatarSVGOdemeler = defaultAvatarSVG;
  const t = passedI18nOdemeler.t;

  // Initialize accounting header immediately to prevent jumping
  const accountingHeader = new AccountingHeader({
    containerId: 'accounting-header',
    pageTitle: t('accounting_payments_page_title') || 'Ödemeler/Tahsilatlar',
    pageIcon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
      <line x1="1" y1="10" x2="23" y2="10"/>
      <circle cx="8" cy="14" r="2"/>
      <path d="m15 11 1 1 3-3"/>
    </svg>`
  });

  contentArea.innerHTML = `
    <div id="accounting-header"></div>
    <div id="odemeler-controls">
      <button id="add-odeme-button" class="btn btn-primary">
        <svg viewBox='0 0 20 20' fill='none' style="width:20px; height:20px; margin-right: 8px;"><circle cx='10' cy='10' r='9' stroke='currentColor' stroke-width='1.5'/><path d='M10 6v8M6 10h8' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'/></svg>
        ${t('muhasebe_odemeler_page_title_add_button') || 'Yeni Ödeme/Tahsilat'}
      </button><br></br>
    </div>
    <table id="payments-table" class="modern-table">
      <thead>
        <tr>
          <th data-column-key="Id" class="sortable">${t('table_header_id') || 'ID'}</th>
          <th data-column-key="IslemTipi" class="sortable">${t('table_header_transaction_type') || 'Transaction Type'}</th>
          <th data-column-key="FaturaNo" class="sortable">${t('table_header_invoice_no') || 'Invoice No'}</th>
          <th data-column-key="CariUnvan" class="sortable">${t('table_header_account') || 'Account'}</th>
          <th data-column-key="OdemeTarihi" class="sortable">${t('table_header_payment_date') || 'Payment Date'}</th>
          <th data-column-key="Tutar" class="sortable">${t('table_header_amount') || 'Amount'}</th>
          <th data-column-key="Yontem" class="sortable">${t('table_header_method') || 'Method'}</th>
          <th class="col-actions">${t('table_header_actions') || 'Actions'}</th>
        </tr>
        <tr class="filter-row">
          <th><input type="number" id="filter-odemeler-id" class="filter-input" placeholder="${t('odemeler_col_id')}"></th>
          <th>
            <select id="filter-odemeler-islem-tipi" class="filter-input">
              <option value="">${t('option_all')}</option>
              <option value="Alis">${t('transaction_type_alis') || 'Alış Ödemesi'}</option>
              <option value="Satis">${t('transaction_type_satis') || 'Satış Tahsilatı'}</option>
            </select>
          </th>
          <th><input type="text" id="filter-odemeler-fatura-no" class="filter-input" placeholder="${t('odemeler_col_fatura_no')}"></th>
          <th><input type="text" id="filter-odemeler-cari-unvan" class="filter-input" placeholder="${t('odemeler_col_cari_unvan')}"></th>
          <th>
            <input type="date" id="filter-odemeler-tarih-start" class="filter-input">
            <input type="date" id="filter-odemeler-tarih-end" class="filter-input">
          </th>
          <th><input type="number" id="filter-odemeler-tutar" class="filter-input" placeholder="${t('odemeler_col_tutar')}"></th>
          <th>
            <select id="filter-odemeler-yontem" class="filter-input">
                <option value="">${t('option_all')}</option>
                <option value="Nakit">${t('payment_method_nakit') || 'Nakit'}</option>
                <option value="Banka">${t('payment_method_banka') || 'Banka'}</option>
                <option value="Kart">${t('payment_method_kart') || 'Kart'}</option>
                <option value="Cek">${t('payment_method_cek') || 'Çek'}</option>
                <option value="Senet">${t('payment_method_senet') || 'Senet'}</option>
                <option value="Diger">${t('payment_method_diger') || 'Diğer'}</option>
            </select>
          </th>
          <th></th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>

    <div id="odemeler-pagination-controls" class="pagination-controls">
      <button id="prev-page-odemeler" disabled>&laquo; ${t('btn_previous') || 'Previous'}</button>
      <span id="page-info-odemeler">Page 1 of 1</span>
      <button id="next-page-odemeler" disabled>&raquo; ${t('btn_next') || 'Next'}</button>
    </div>

    <div id="payments-recent-activities"></div>

    <div id="odeme-modal" class="modal hidden">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close-btn">&times;</span>
          <h2 id="odeme-modal-title">${t('odeme_modal_title_add')}</h2>
        </div>
        <form id="odeme-form">
          <div class="modal-body">
          <input type="hidden" name="Id">
          <div>
            <label for="odeme-islem-tipi">${t('odemeler_col_islem_tipi')}:</label>
            <select name="IslemTipi" id="odeme-islem-tipi" required>
              <option value="">${t('option_select_type')}</option>
              <option value="Alis">${t('transaction_type_alis')}</option>
              <option value="Satis">${t('transaction_type_satis')}</option>
            </select>
          </div>
          <div>
            <label for="odeme-islem-id">${t('odemeler_col_fatura_no')}:</label>
            <select name="IslemId" id="odeme-islem-id" required></select>
          </div>
          <div>
            <label for="odeme-tarihi">${t('odemeler_col_odeme_tarihi')}:</label>
            <input type="date" name="OdemeTarihi" id="odeme-tarihi" required>
          </div>
          <div>
            <label for="odeme-tutar">${t('odemeler_col_tutar')}:</label>
            <input type="number" step="any" name="Tutar" id="odeme-tutar" required>
          </div>
          <div>
            <label for="odeme-yontem">${t('odemeler_col_yontem')}:</label>
            <select name="Yontem" id="odeme-yontem" required>
                <option value="Nakit">${t('payment_method_nakit') || 'Nakit'}</option>
                <option value="Banka">${t('payment_method_banka') || 'Banka'}</option>
                <option value="Kart">${t('payment_method_kart') || 'Kart'}</option>
                <option value="Cek">${t('payment_method_cek') || 'Çek'}</option>
                <option value="Senet">${t('payment_method_senet') || 'Senet'}</option>
                <option value="Diger">${t('payment_method_diger') || 'Diğer'}</option>
            </select>
          </div>
          <div>
            <label for="odeme-notlar">${t('odemeler_col_notlar') || 'Notlar'}:</label>
            <textarea name="Notlar" id="odeme-notlar"></textarea>
          </div>
          </div>
          <div class="modal-footer">
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('odeme-modal').classList.add('hidden')">${t('btn_cancel') || 'İptal'}</button>
              <button type="submit" class="btn btn-primary">${t('btn_save')}</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  `;

  // Initialize accounting header immediately after HTML is created
  accountingHeader.init();

  odemelerTableBody = contentArea.querySelector('#payments-table tbody');
  odemeModal = contentArea.querySelector('#odeme-modal');
  odemeForm = contentArea.querySelector('#odeme-form');
  odemeModalTitle = contentArea.querySelector('#odeme-modal-title');

  pageInfoOdemeler = contentArea.querySelector('#page-info-odemeler');
  prevButtonOdemeler = contentArea.querySelector('#prev-page-odemeler');
  nextButtonOdemeler = contentArea.querySelector('#next-page-odemeler');

  const addOdemeButton = contentArea.querySelector('#add-odeme-button');
  addOdemeButton.addEventListener('click', () => openOdemeModal(null));

  const closeBtn = odemeModal.querySelector('.close-btn');
  closeBtn.addEventListener('click', () => odemeModal.classList.add('hidden'));
  odemeModal.addEventListener('click', (e) => {
    if (e.target === odemeModal) odemeModal.classList.add('hidden');
  });

  // Load caches for modal dropdowns
  await Promise.all([loadAlislarForSelectOdemeler(), loadSatislarForSelectOdemeler()]);

  const islemTipiSelectModal = odemeForm.elements['IslemTipi'];
  const islemIdSelectModal = odemeForm.elements['IslemId'];
  islemTipiSelectModal.addEventListener('change', (event) => {
    populateIslemIdSelectOdemeler(event.target.value, islemIdSelectModal);
  });

  odemeForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    const formData = new FormData(odemeForm);
    const odemeData = Object.fromEntries(formData.entries());
    odemeData.IslemId = parseInt(odemeData.IslemId);
    odemeData.Tutar = parseFloat(odemeData.Tutar);
    odemeData.OdemeTarihi = new Date(odemeData.OdemeTarihi).toISOString().split('T')[0];

    try {
      if (currentEditingOdemeId) {
        await window.api.invoke('payments:update', { id: currentEditingOdemeId, data: odemeData });
        window.toast.success(t('toast_success_update'));
      } else {
        await window.api.invoke('payments:add', odemeData);
        window.toast.success(t('toast_success_save'));
      }
      odemeModal.classList.add('hidden');
      allPayments = await window.api.invoke('payments:list'); // Re-fetch all
      currentPayments = [...allPayments];
      syncPaymentsData();
      applyFiltersAndSortPayments();
      // Refresh recent activities
      if (paymentsRecentActivities) {
        paymentsRecentActivities.refresh();
      }
    } catch (error) {
      console.error('Ödeme save/update error:', error);
      window.toast.error(t('error_generic_api', { error: error.message || 'Unknown error' }));
    }
  });

  // Initial data load
  allPayments = await window.api.invoke('payments:list');
  currentPayments = [...allPayments];
  syncPaymentsData();

  // Setup sorting event listeners
  const sortableHeaders = contentArea.querySelectorAll('#payments-table thead th[data-column-key]');
  sortableHeaders.forEach(th => {
    const columnKey = th.dataset.columnKey;
    if (!columnKey) return;

    th.classList.add('sortable');
    th.addEventListener('click', () => {
      if (sortColumn === columnKey) {
        sortDirection *= -1;
      } else {
        sortColumn = columnKey;
        sortDirection = 1;
      }
      // Clear all sorting classes
      sortableHeaders.forEach(headerTh => {
        headerTh.classList.remove('sorted-asc', 'sorted-desc');
      });

      // Add sorting class to current header
      th.classList.add(sortDirection === 1 ? 'sorted-asc' : 'sorted-desc');

      applyFiltersAndSortPayments();
    });
  });

  applyFiltersAndSortPayments();

  // Event listeners for filters
  const filterInputs = [
    '#filter-odemeler-id', '#filter-odemeler-islem-tipi', '#filter-odemeler-fatura-no',
    '#filter-odemeler-cari-unvan', '#filter-odemeler-tarih-start', '#filter-odemeler-tarih-end',
    '#filter-odemeler-tutar', '#filter-odemeler-yontem'
  ];
  filterInputs.forEach(selector => {
    const inputElement = contentArea.querySelector(selector);
    if (inputElement) {
      inputElement.addEventListener('input', applyFiltersAndSortPayments);
      if (inputElement.tagName === 'SELECT' || inputElement.type === 'date') {
         inputElement.addEventListener('change', applyFiltersAndSortPayments);
      }
    }
  });

  // Event listeners for pagination
  prevButtonOdemeler.addEventListener('click', () => {
    if (currentPageOdemeler > 1) {
      currentPageOdemeler--;
      renderOdemelerTablePage();
      updateOdemelerPaginationControls();
    }
  });
  nextButtonOdemeler.addEventListener('click', () => {
    const totalPages = Math.ceil(currentOdemeler.length / itemsPerPageOdemeler);
    if (currentPageOdemeler < totalPages) {
      currentPageOdemeler++;
      renderOdemelerTablePage();
      updateOdemelerPaginationControls();
    }
  });

  // Event listeners for sorting
  const ths = contentArea.querySelectorAll('#odemeler-table thead th');
  const columnKeysOdemeler = ['Id', 'IslemTipi', 'FaturaNo', 'CariUnvan', 'OdemeTarihi', 'Tutar', 'Yontem', null];
  ths.forEach((th, idx) => {
    const columnKey = columnKeysOdemeler[idx];
    if (!columnKey) return;

    th.classList.add('sortable');
    th.addEventListener('click', () => {
      if (sortColumnOdemeler === columnKey) {
        sortDirectionOdemeler *= -1;
      } else {
        sortColumnOdemeler = columnKey;
        sortDirectionOdemeler = 1;
      }
      // Clear all sorting classes and arrows
      ths.forEach(headerTh => {
        headerTh.classList.remove('sorted-asc', 'sorted-desc');
        headerTh.querySelector('.sort-arrow')?.remove();
      });

      // Add sorting class to current header
      th.classList.add(sortDirectionOdemeler === 1 ? 'sorted-asc' : 'sorted-desc');
      applyFiltersAndSortOdemeler();
    });
  });

  // Initialize Recent Activities
  paymentsRecentActivities = new RecentActivities({
    containerId: 'payments-recent-activities',
    title: t('recent_payment_activities') || 'Recent Payment Activities',
    maxItems: 5,
    onViewAll: () => {
      // Scroll to main table
      document.getElementById('odemeler-table').scrollIntoView({ behavior: 'smooth' });
    },
    getRecords: async () => {
      try {
        const payments = await window.api.invoke('payments:list');
        return payments.sort((a, b) => new Date(b.OdemeTarihi) - new Date(a.OdemeTarihi));
      } catch (error) {
        console.error('Error fetching payment records:', error);
        return [];
      }
    },
    formatRecord: (record) => {
      return {
        title: truncateText(record.CariUnvan || 'Unknown Account', 25),
        subtitle: record.IslemTipi ? t(`transaction_type_${record.IslemTipi.toLowerCase()}`) : 'Payment',
        date: formatActivityDate(record.OdemeTarihi),
        amount: record.Tutar ? `₺${record.Tutar.toFixed(2)}` : null
      };
    },
    emptyMessage: t('no_recent_payment_activities') || 'No recent payment activities'
  });



  // Initialize recent activities
  paymentsRecentActivities.init();

  // Ensure modals are properly structured
  setTimeout(() => {
    if (window.reinitializeModals) {
      window.reinitializeModals();
    }
  }, 100);
}

function applyFiltersAndSortOdemeler() {
  const t = passedI18nOdemeler.t;
  let filtered = [...allOdemeler];

  const idFilter = document.getElementById('filter-odemeler-id')?.value;
  const islemTipiFilter = document.getElementById('filter-odemeler-islem-tipi')?.value;
  const faturaNoFilter = document.getElementById('filter-odemeler-fatura-no')?.value.trim().toLowerCase();
  const cariUnvanFilter = document.getElementById('filter-odemeler-cari-unvan')?.value.trim().toLowerCase();
  const tarihStartFilter = document.getElementById('filter-odemeler-tarih-start')?.value;
  const tarihEndFilter = document.getElementById('filter-odemeler-tarih-end')?.value;
  const tutarFilter = document.getElementById('filter-odemeler-tutar')?.value;
  const yontemFilter = document.getElementById('filter-odemeler-yontem')?.value;

  if (idFilter) filtered = filtered.filter(o => o.Id == idFilter);
  if (islemTipiFilter) filtered = filtered.filter(o => o.IslemTipi === islemTipiFilter);
  if (faturaNoFilter) filtered = filtered.filter(o => (o.FaturaNo || '').toLowerCase().includes(faturaNoFilter));
  if (cariUnvanFilter) filtered = filtered.filter(o => (o.CariUnvan || '').toLowerCase().includes(cariUnvanFilter));
  if (tarihStartFilter) filtered = filtered.filter(o => new Date(o.OdemeTarihi) >= new Date(tarihStartFilter));
  if (tarihEndFilter) filtered = filtered.filter(o => new Date(o.OdemeTarihi) <= new Date(tarihEndFilter));
  if (tutarFilter) filtered = filtered.filter(o => o.Tutar == parseFloat(tutarFilter));
  if (yontemFilter) filtered = filtered.filter(o => o.Yontem === yontemFilter);

  if (sortColumnOdemeler) {
    filtered = sortOdemelerList(filtered, sortColumnOdemeler, sortDirectionOdemeler);
  }

  currentOdemeler = filtered;
  currentPageOdemeler = 1;
  renderOdemelerTablePage();
  updateOdemelerPaginationControls();
}

function renderOdemelerTablePage() {
  const t = passedI18nOdemeler.t;
  odemelerTableBody.innerHTML = '';

  const totalPages = Math.ceil(currentOdemeler.length / itemsPerPageOdemeler);
  currentPageOdemeler = Math.max(1, Math.min(currentPageOdemeler, totalPages || 1));

  const startIndex = (currentPageOdemeler - 1) * itemsPerPageOdemeler;
  const endIndex = startIndex + itemsPerPageOdemeler;
  const pageItems = currentOdemeler.slice(startIndex, endIndex);

  if (pageItems.length === 0) {
    odemelerTableBody.innerHTML = `<tr><td colspan="8"><div class="empty-state-container"><div class="empty-state-icon">${passedDefaultAvatarSVGOdemeler ? passedDefaultAvatarSVGOdemeler(48) : ''}</div><p>${t('no_records_found')}</p></div></td></tr>`;
    return;
  }

  pageItems.forEach(odeme => {
    const row = odemelerTableBody.insertRow();
    row.innerHTML = `
      <td class="cell-odemeler-id">${odeme.Id}</td>
      <td class="cell-odemeler-islemtipi">${t('transaction_type_' + (odeme.IslemTipi || 'unknown').toLowerCase())}</td>
      <td class="cell-odemeler-faturano">${odeme.FaturaNo || t('invoice_id_placeholder', {id: odeme.IslemId})}</td>
      <td class="cell-odemeler-cariunvan">${odeme.CariUnvan || t('unknown')}</td>
      <td class="cell-odemeler-odemetarihi">${odeme.OdemeTarihi ? new Date(odeme.OdemeTarihi).toLocaleDateString() : ''}</td>
      <td class="cell-odemeler-tutar">${odeme.Tutar ? odeme.Tutar.toFixed(2) : ''}</td>
      <td class="cell-odemeler-yontem">${odeme.Yontem || ''}</td>
      <td class="col-actions actions">
        ${IconSystem.createActionsWrapper(odeme.Id, { edit: true, delete: true }, { edit: t('btn_edit'), delete: t('btn_delete') })}
      </td>
    `;
    row.querySelector('.edit-btn').addEventListener('click', () => openOdemeModal(odeme));
    row.querySelector('.delete-btn').addEventListener('click', () => deleteOdeme(odeme.Id));
  });
  
  // Initialize table animations
  setTimeout(() => {
    TableAnimations.initializeTable('#payments-table');
  }, 100);
}

function updateOdemelerPaginationControls() {
  const t = passedI18nOdemeler.t;
  const totalItems = currentOdemeler.length;
  const totalPages = Math.ceil(totalItems / itemsPerPageOdemeler) || 1;
  pageInfoOdemeler.textContent = t('page_info_text', { currentPage: currentPageOdemeler, totalPages, totalItems }) || `Page ${currentPageOdemeler} of ${totalPages} (${totalItems} items)`;
  prevButtonOdemeler.disabled = currentPageOdemeler === 1;
  nextButtonOdemeler.disabled = currentPageOdemeler === totalPages;
}

async function loadAlislarForSelectOdemeler() {
  try {
    alislarCacheOdemeler = await window.api.invoke('purchases:list'); // Fetch all for now
  } catch (error) {
    console.error('Error loading alislar for odemeler modal:', error);
    alislarCacheOdemeler = []; // Ensure it's an array
  }
}

async function loadSatislarForSelectOdemeler() {
  try {
    satislarCacheOdemeler = await window.api.invoke('sales:list'); // Fetch all for now
  } catch (error) {
    console.error('Error loading satislar for odemeler modal:', error);
    satislarCacheOdemeler = []; // Ensure it's an array
  }
}

function populateIslemIdSelectOdemeler(islemTipi, islemIdSelectElement) {
  const t = passedI18nOdemeler.t;
  islemIdSelectElement.innerHTML = `<option value="">${t('option_select_invoice')}</option>`;
  let sourceCache = [];
  if (islemTipi === 'Alis') {
    sourceCache = alislarCacheOdemeler.filter(item => !item.IslemTamamlandi && (typeof item.KalanTutar !== 'number' || item.KalanTutar > 0.005 || item.KalanTutar === null));
  } else if (islemTipi === 'Satis') {
    sourceCache = satislarCacheOdemeler.filter(item => !item.IslemTamamlandi && (typeof item.KalanTutar !== 'number' || item.KalanTutar > 0.005 || item.KalanTutar === null));
  }

  if (sourceCache.length === 0 && (islemTipi === 'Alis' || islemTipi === 'Satis')) {
      islemIdSelectElement.innerHTML = `<option value="">${t('option_no_payable_receivable_found')}</option>`;
  } else {
    sourceCache.forEach(item => {
      const kalanTutar = typeof item.KalanTutar === 'number' ? item.KalanTutar.toFixed(2) : (item.ToplamTutar || 0).toFixed(2);
      const option = document.createElement('option');
      option.value = item.Id;
      option.textContent = `${item.FaturaNo || t('invoice_id_placeholder', {id: item.Id})} (${item.CariUnvan || item.Id}) - ${t('remaining_amount')}: ${kalanTutar} - ${item.Tarih ? new Date(item.Tarih).toLocaleDateString() : ''}`;
      islemIdSelectElement.appendChild(option);
    });
  }
}

function openOdemeModal(odemeData = null) {
  const t = passedI18nOdemeler.t;
  odemeForm.reset();
  const islemTipiSelect = odemeForm.elements['IslemTipi'];
  const islemIdSelect = odemeForm.elements['IslemId'];

  if (odemeData) {
    currentEditingOdemeId = odemeData.Id;
    odemeModalTitle.textContent = t('odeme_modal_title_edit');
    odemeForm.elements['Id'].value = odemeData.Id;
    islemTipiSelect.value = odemeData.IslemTipi;
    populateIslemIdSelectOdemeler(odemeData.IslemTipi, islemIdSelect);
    islemIdSelect.value = odemeData.IslemId;
    odemeForm.elements['OdemeTarihi'].value = odemeData.OdemeTarihi ? new Date(odemeData.OdemeTarihi).toISOString().split('T')[0] : '';
    odemeForm.elements['Tutar'].value = odemeData.Tutar || '';
    odemeForm.elements['Yontem'].value = odemeData.Yontem || 'Nakit';
    odemeForm.elements['Notlar'].value = odemeData.Notlar || '';
    islemTipiSelect.disabled = true;
    islemIdSelect.disabled = true;
  } else {
    currentEditingOdemeId = null;
    odemeModalTitle.textContent = t('odeme_modal_title_add');
    odemeForm.elements['Id'].value = '';
    islemTipiSelect.value = ''; // Default to empty
    populateIslemIdSelectOdemeler('', islemIdSelect); // Clear/default populate
    odemeForm.elements['OdemeTarihi'].valueAsDate = new Date();
    odemeForm.elements['Yontem'].value = 'Nakit'; // Default
    islemTipiSelect.disabled = false;
    islemIdSelect.disabled = false;
  }
  odemeModal.classList.remove('hidden');
}

async function deleteOdeme(id) {
  const t = passedI18nOdemeler.t;
  const confirmed = await window.toast.confirm(t('confirm_delete_payment', { paymentId: id }), {
    confirmText: t('toast_confirm'),
    cancelText: t('toast_cancel')
  });

  if (confirmed) {
    try {
      await window.api.invoke('payments:delete', id);
      allPayments = await window.api.invoke('payments:list'); // Re-fetch all
      currentPayments = [...allPayments];
      syncPaymentsData();
      applyFiltersAndSortPayments();
      window.toast.success(t('toast_success_delete'));
    } catch (error) {
      console.error('Ödeme delete error:', error);
      window.toast.error(t('error_generic_api', { error: error.message || 'Unknown error' }));
    }
  }
}

function applyFiltersAndSortPayments() {
  const t = passedI18nOdemeler.t;
  let filtered = [...allPayments];

  // Apply filters
  const idFilter = document.getElementById('filter-odemeler-id')?.value;
  const islemTipiFilter = document.getElementById('filter-odemeler-islem-tipi')?.value;
  const faturaNoFilter = document.getElementById('filter-odemeler-fatura-no')?.value.trim().toLowerCase();
  const cariUnvanFilter = document.getElementById('filter-odemeler-cari-unvan')?.value.trim().toLowerCase();
  const tarihStartFilter = document.getElementById('filter-odemeler-tarih-start')?.value;
  const tarihEndFilter = document.getElementById('filter-odemeler-tarih-end')?.value;
  const tutarFilter = document.getElementById('filter-odemeler-tutar')?.value;
  const yontemFilter = document.getElementById('filter-odemeler-yontem')?.value;

  if (idFilter) filtered = filtered.filter(p => p.Id == idFilter);
  if (islemTipiFilter) filtered = filtered.filter(p => p.IslemTipi === islemTipiFilter);
  if (faturaNoFilter) filtered = filtered.filter(p => (p.FaturaNo || '').toLowerCase().includes(faturaNoFilter));
  if (cariUnvanFilter) filtered = filtered.filter(p => (p.CariUnvan || '').toLowerCase().includes(cariUnvanFilter));
  if (tarihStartFilter) filtered = filtered.filter(p => new Date(p.OdemeTarihi) >= new Date(tarihStartFilter));
  if (tarihEndFilter) filtered = filtered.filter(p => new Date(p.OdemeTarihi) <= new Date(tarihEndFilter));
  if (tutarFilter) filtered = filtered.filter(p => p.Tutar == tutarFilter);
  if (yontemFilter) filtered = filtered.filter(p => p.Yontem === yontemFilter);

  // Apply sorting
  if (sortColumn) {
    filtered.sort((a, b) => {
      let valA = a[sortColumn];
      let valB = b[sortColumn];

      if (valA === null || valA === undefined) valA = '';
      if (valB === null || valB === undefined) valB = '';

      // Handle different data types
      if (sortColumn === 'Id' || sortColumn === 'Tutar') {
        valA = Number(valA) || 0;
        valB = Number(valB) || 0;
        return (valA - valB) * sortDirection;
      }

      if (sortColumn === 'OdemeTarihi') {
        valA = new Date(valA);
        valB = new Date(valB);
        return (valA - valB) * sortDirection;
      }

      // String comparison
      if (typeof valA === 'string' && typeof valB === 'string') {
        return valA.localeCompare(valB, undefined, { sensitivity: 'base' }) * sortDirection;
      }

      if (valA < valB) return -1 * sortDirection;
      if (valA > valB) return 1 * sortDirection;
      return 0;
    });
  }

  currentPayments = filtered;
  syncPaymentsData();

  // Call the existing render function that actually works
  if (typeof renderOdemelerTablePage === 'function') {
    renderOdemelerTablePage();
  } else {
    renderPaymentsTable();
  }
}

function renderPaymentsTable() {
  const t = passedI18nOdemeler.t;
  const tbody = document.querySelector('#payments-table tbody');
  if (!tbody) return;

  tbody.innerHTML = '';

  if (currentPayments.length === 0) {
    tbody.innerHTML = `
      <tr>
        <td colspan="8" style="text-align: center; padding: 20px; color: #666;">
          ${t('no_payments_found') || 'No payments found'}
        </td>
      </tr>
    `;
    return;
  }

  currentPayments.forEach(payment => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${payment.Id}</td>
      <td>${payment.IslemTipi === 'Alis' ? (t('transaction_type_purchase') || 'Purchase Payment') : (t('transaction_type_sale') || 'Sale Collection')}</td>
      <td>${payment.FaturaNo || ''}</td>
      <td>${payment.CariUnvan || ''}</td>
      <td>${payment.OdemeTarihi ? new Date(payment.OdemeTarihi).toLocaleDateString() : ''}</td>
      <td>${payment.Tutar ? Number(payment.Tutar).toFixed(2) : ''}</td>
      <td>${payment.Yontem || ''}</td>
      <td class="col-actions">
        <button class="btn btn-sm btn-secondary" onclick="openOdemeModal(${JSON.stringify(payment).replace(/"/g, '&quot;')})">${t('btn_edit') || 'Edit'}</button>
        <button class="btn btn-sm btn-danger" onclick="deleteOdeme(${payment.Id})">${t('btn_delete') || 'Delete'}</button>
      </td>
    `;
    tbody.appendChild(row);
  });
}

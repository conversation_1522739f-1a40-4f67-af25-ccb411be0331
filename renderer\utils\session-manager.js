/**
 * Session Manager
 * Handles user session tracking, automatic logout, and profile context persistence
 */

class SessionManager {
  constructor() {
    this.currentUser = null;
    this.currentProfile = null;
    this.sessionTimeout = 30 * 60 * 1000; // 30 minutes in milliseconds
    this.timeoutId = null;
    this.lastActivity = Date.now();
    this.isAuthenticated = false;
    
    this.init();
  }

  async init() {
    // Check if user is already authenticated
    await this.checkAuthenticationStatus();
    
    // Set up activity tracking
    this.setupActivityTracking();
    
    // Start session timeout monitoring
    this.startSessionMonitoring();
  }

  async checkAuthenticationStatus() {
    try {
      const session = await window.api.invoke('auth:getCurrentSession');
      
      if (session.isAuthenticated && session.user && session.profile) {
        this.currentUser = session.user;
        this.currentProfile = session.profile;
        this.isAuthenticated = true;
        this.lastActivity = Date.now();
        
        // Store session data in localStorage for persistence
        this.saveSessionToStorage();
        
        return true;
      } else {
        this.clearSession();
        return false;
      }
    } catch (error) {
      console.error('Error checking authentication status:', error);
      this.clearSession();
      return false;
    }
  }

  setupActivityTracking() {
    // Track user activity to reset session timeout
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    activityEvents.forEach(event => {
      document.addEventListener(event, () => {
        this.updateLastActivity();
      }, true);
    });
  }

  updateLastActivity() {
    this.lastActivity = Date.now();
    
    // Reset session timeout
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
    
    if (this.isAuthenticated) {
      this.startSessionTimeout();
    }
  }

  startSessionMonitoring() {
    // Check session status every minute
    setInterval(() => {
      if (this.isAuthenticated) {
        this.checkSessionValidity();
      }
    }, 60000); // 1 minute
  }

  startSessionTimeout() {
    this.timeoutId = setTimeout(() => {
      this.handleSessionTimeout();
    }, this.sessionTimeout);
  }

  async checkSessionValidity() {
    try {
      const isValid = await window.api.invoke('auth:isAuthenticated');
      
      if (!isValid) {
        this.handleSessionExpired();
      }
    } catch (error) {
      console.error('Error checking session validity:', error);
      this.handleSessionExpired();
    }
  }

  handleSessionTimeout() {
    this.showTimeoutWarning();
  }

  showTimeoutWarning() {
    // Show a warning dialog before logging out
    const timeLeft = 5 * 60 * 1000; // 5 minutes warning
    
    if (window.toast) {
      window.toast.warning(
        'Oturumunuz yakında sona erecek. Devam etmek için herhangi bir tuşa basın.',
        {
          duration: 10000,
          actions: [
            {
              text: 'Oturumu Uzat',
              action: () => {
                this.extendSession();
              }
            },
            {
              text: 'Çıkış Yap',
              action: () => {
                this.logout();
              }
            }
          ]
        }
      );
    }

    // Auto logout after warning period
    setTimeout(() => {
      if (this.isAuthenticated) {
        this.logout();
      }
    }, timeLeft);
  }

  extendSession() {
    this.updateLastActivity();
    if (window.toast) {
      window.toast.success('Oturum uzatıldı');
    }
  }

  handleSessionExpired() {
    if (window.toast) {
      window.toast.error('Oturumunuz sona erdi. Lütfen tekrar giriş yapın.');
    }
    this.logout();
  }

  async logout(clearRemembered = false) {
    try {
      await window.api.invoke('auth:logout', clearRemembered);

      // Clear remembered credentials if requested
      if (clearRemembered) {
        localStorage.removeItem('livestock_remembered_user');
      }
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      this.clearSession();
      this.redirectToLogin();
    }
  }

  clearSession() {
    this.currentUser = null;
    this.currentProfile = null;
    this.isAuthenticated = false;
    this.lastActivity = null;
    
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    
    // Clear session data from localStorage
    this.clearSessionFromStorage();
  }

  saveSessionToStorage() {
    const sessionData = {
      user: this.currentUser,
      profile: this.currentProfile,
      lastActivity: this.lastActivity,
      timestamp: Date.now()
    };
    
    localStorage.setItem('livestock_session', JSON.stringify(sessionData));
  }

  clearSessionFromStorage() {
    localStorage.removeItem('livestock_session');
  }

  loadSessionFromStorage() {
    try {
      const sessionData = localStorage.getItem('livestock_session');
      if (sessionData) {
        const parsed = JSON.parse(sessionData);
        
        // Check if session is not too old (max 1 day)
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours
        if (Date.now() - parsed.timestamp < maxAge) {
          return parsed;
        }
      }
    } catch (error) {
      console.error('Error loading session from storage:', error);
    }
    
    return null;
  }

  redirectToLogin() {
    // Redirect to login page
    window.location.href = 'login.html';
  }

  // Public methods for getting session information
  getCurrentUser() {
    return this.currentUser;
  }

  getCurrentProfile() {
    return this.currentProfile;
  }

  isUserAuthenticated() {
    return this.isAuthenticated;
  }

  async switchProfile(profileId) {
    try {
      const result = await window.api.invoke('profiles:switch', profileId);
      
      if (result.profile) {
        this.currentProfile = result.profile;
        this.saveSessionToStorage();
        
        // Notify other parts of the application about profile change
        this.notifyProfileChange();
        
        return result;
      }
    } catch (error) {
      console.error('Error switching profile:', error);
      throw error;
    }
  }

  notifyProfileChange() {
    // Dispatch custom event for profile change
    const event = new CustomEvent('profileChanged', {
      detail: {
        profile: this.currentProfile
      }
    });
    
    document.dispatchEvent(event);
  }

  // Method to refresh session data
  async refreshSession() {
    return await this.checkAuthenticationStatus();
  }

  // Method to get session info for display
  getSessionInfo() {
    return {
      user: this.currentUser,
      profile: this.currentProfile,
      isAuthenticated: this.isAuthenticated,
      lastActivity: this.lastActivity
    };
  }
}

// Create global session manager instance
window.sessionManager = new SessionManager();

export default SessionManager;

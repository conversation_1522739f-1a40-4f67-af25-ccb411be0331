/**
 * Login Page JavaScript
 * Handles user authentication, registration, and form interactions
 */

// Import toast system
import { toastSystem } from './utils/toast-system.js';

class LoginManager {
  constructor() {
    this.currentTab = 'login';
    this.isLoading = false;
    this.toast = toastSystem;

    this.init();
  }

  init() {
    this.bindEvents();
    this.initPasswordToggles();
    this.initFormValidation();
    this.loadRememberedCredentials();
  }

  bindEvents() {
    // Tab switching
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tab = e.currentTarget.dataset.tab;
        this.switchTab(tab);
      });
    });

    // Form submissions
    document.getElementById('login-form').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleLogin();
    });

    document.getElementById('register-form').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleRegister();
    });
  }

  initPasswordToggles() {
    document.querySelectorAll('.password-toggle').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const targetId = e.currentTarget.dataset.target;
        const input = document.getElementById(targetId);
        const icon = e.currentTarget.querySelector('i');

        if (input.type === 'password') {
          input.type = 'text';
          icon.className = 'fas fa-eye-slash';
        } else {
          input.type = 'password';
          icon.className = 'fas fa-eye';
        }
      });
    });
  }

  initFormValidation() {
    // Real-time validation for registration form
    const registerForm = document.getElementById('register-form');
    const inputs = registerForm.querySelectorAll('input');

    inputs.forEach(input => {
      input.addEventListener('blur', () => this.validateField(input));
      input.addEventListener('input', () => this.clearValidation(input));
    });

    // Password confirmation validation
    const passwordConfirm = document.getElementById('register-password-confirm');
    passwordConfirm.addEventListener('input', () => this.validatePasswordMatch());
  }

  switchTab(tab) {
    if (this.isLoading) return;

    this.currentTab = tab;

    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.tab === tab);
    });

    // Update forms
    document.querySelectorAll('.auth-form').forEach(form => {
      form.classList.toggle('active', form.id === `${tab}-form`);
    });

    // Clear any validation messages
    this.clearAllValidation();
  }

  validateField(input) {
    const value = input.value.trim();
    let isValid = true;
    let message = '';

    switch (input.name) {
      case 'adSoyad':
        if (value.length < 2) {
          isValid = false;
          message = 'Ad soyad en az 2 karakter olmalıdır';
        }
        break;

      case 'kullaniciAdi':
        if (value.length < 3) {
          isValid = false;
          message = 'Kullanıcı adı en az 3 karakter olmalıdır';
        } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
          isValid = false;
          message = 'Kullanıcı adı sadece harf, rakam ve alt çizgi içerebilir';
        }
        break;

      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          isValid = false;
          message = 'Geçerli bir e-posta adresi giriniz';
        }
        break;

      case 'sifre':
        if (value.length < 6) {
          isValid = false;
          message = 'Şifre en az 6 karakter olmalıdır';
        }
        break;
    }

    this.showValidation(input, isValid, message);
    return isValid;
  }

  validatePasswordMatch() {
    const password = document.getElementById('register-password').value;
    const passwordConfirm = document.getElementById('register-password-confirm').value;
    const input = document.getElementById('register-password-confirm');

    if (passwordConfirm && password !== passwordConfirm) {
      this.showValidation(input, false, 'Şifreler eşleşmiyor');
      return false;
    } else if (passwordConfirm) {
      this.showValidation(input, true, '');
      return true;
    }
    return true;
  }

  showValidation(input, isValid, message) {
    input.classList.toggle('invalid', !isValid);
    input.classList.toggle('valid', isValid && input.value.trim());

    let validationMsg = input.parentNode.querySelector('.validation-message');
    if (!validationMsg) {
      validationMsg = document.createElement('div');
      validationMsg.className = 'validation-message';
      input.parentNode.appendChild(validationMsg);
    }

    validationMsg.textContent = message;
    validationMsg.classList.toggle('show', !isValid && message);
  }

  clearValidation(input) {
    input.classList.remove('invalid', 'valid');
    const validationMsg = input.parentNode.querySelector('.validation-message');
    if (validationMsg) {
      validationMsg.classList.remove('show');
    }
  }

  clearAllValidation() {
    document.querySelectorAll('input').forEach(input => {
      this.clearValidation(input);
    });
  }

  async handleLogin() {
    if (this.isLoading) return;

    const form = document.getElementById('login-form');
    const formData = new FormData(form);
    const credentials = Object.fromEntries(formData);

    // Basic validation
    if (!credentials.kullaniciAdi || !credentials.sifre) {
      this.showToast('error', 'Hata', 'Lütfen tüm alanları doldurunuz');
      return;
    }

    this.setLoading(true, 'Giriş yapılıyor...');

    try {
      const result = await window.api.invoke('auth:login', credentials);

      // Handle "Remember Me" functionality
      if (credentials.beniHatirla) {
        this.saveCredentials(credentials.kullaniciAdi);
      } else {
        this.clearSavedCredentials();
      }

      // Update loading message
      this.updateLoadingMessage('Giriş başarılı! Yönlendiriliyor...');

      // Show success message
      this.showToast('success', 'Başarılı', result.message);

      // Redirect to main application after short delay
      setTimeout(() => {
        window.location.href = 'index.html';
      }, 1500);

    } catch (error) {
      console.error('Login error:', error);
      this.showToast('error', 'Giriş Hatası', error.message || 'Giriş yapılırken bir hata oluştu');
      this.setLoading(false);
    }
  }

  async handleRegister() {
    if (this.isLoading) return;

    const form = document.getElementById('register-form');
    const formData = new FormData(form);
    const userData = Object.fromEntries(formData);

    // Validate all fields
    const inputs = form.querySelectorAll('input');
    let isValid = true;

    inputs.forEach(input => {
      if (!this.validateField(input)) {
        isValid = false;
      }
    });

    // Check password match
    if (!this.validatePasswordMatch()) {
      isValid = false;
    }

    if (!isValid) {
      this.showToast('error', 'Doğrulama Hatası', 'Lütfen form hatalarını düzeltiniz');
      return;
    }

    // Check if passwords match
    if (userData.sifre !== userData.sifreTekrar) {
      this.showToast('error', 'Hata', 'Şifreler eşleşmiyor');
      return;
    }

    this.setLoading(true, 'Hesap oluşturuluyor...');

    try {
      const result = await window.api.invoke('auth:register', userData);

      // Update loading message
      this.updateLoadingMessage('Hesap başarıyla oluşturuldu!');

      this.showToast('success', 'Başarılı', result.message);

      // Switch to login tab after successful registration
      setTimeout(() => {
        this.setLoading(false);
        this.switchTab('login');
        form.reset();
        this.clearAllValidation();
      }, 1500);

    } catch (error) {
      console.error('Registration error:', error);
      this.showToast('error', 'Kayıt Hatası', error.message || 'Kayıt olurken bir hata oluştu');
      this.setLoading(false);
    }
  }

  setLoading(loading, message = 'İşlem yapılıyor...') {
    this.isLoading = loading;
    const overlay = document.getElementById('loading-overlay');
    const buttons = document.querySelectorAll('button[type="submit"]');
    const loadingText = overlay.querySelector('p');

    if (loadingText) {
      loadingText.textContent = message;
    }

    overlay.classList.toggle('hidden', !loading);
    buttons.forEach(btn => {
      btn.disabled = loading;
    });
  }

  updateLoadingMessage(message) {
    const overlay = document.getElementById('loading-overlay');
    const loadingText = overlay.querySelector('p');
    if (loadingText) {
      loadingText.textContent = message;
    }
  }

  showToast(type, title, message) {
    // Use the new toast system
    this.toast[type](message);
  }

  // Remember Me functionality
  saveCredentials(username) {
    try {
      const credentials = {
        username: username,
        timestamp: Date.now(),
        rememberMe: true
      };
      localStorage.setItem('livestock_remembered_user', JSON.stringify(credentials));
    } catch (error) {
      console.error('Error saving credentials:', error);
    }
  }

  clearSavedCredentials() {
    try {
      localStorage.removeItem('livestock_remembered_user');
    } catch (error) {
      console.error('Error clearing credentials:', error);
    }
  }

  loadRememberedCredentials() {
    try {
      const saved = localStorage.getItem('livestock_remembered_user');
      if (saved) {
        const credentials = JSON.parse(saved);

        // Check if credentials are not too old (max 30 days)
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
        if (Date.now() - credentials.timestamp < maxAge) {
          // Fill in the username
          const usernameInput = document.getElementById('login-username');
          const rememberCheckbox = document.getElementById('remember-me');

          if (usernameInput && rememberCheckbox) {
            usernameInput.value = credentials.username;
            rememberCheckbox.checked = true;

            // Focus on password field for convenience
            const passwordInput = document.getElementById('login-password');
            if (passwordInput) {
              passwordInput.focus();
            }
          }
        } else {
          // Credentials are too old, remove them
          this.clearSavedCredentials();
        }
      }
    } catch (error) {
      console.error('Error loading remembered credentials:', error);
      this.clearSavedCredentials();
    }
  }
}

// Initialize login manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new LoginManager();
});

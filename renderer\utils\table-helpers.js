// Ortak tablo yardımcı fonksiyonları - filtreleme ve sıralama

// Generic sıralama fonksiyonu
export function sortList(list, column, direction) {
  return [...list].sort((a, b) => {
    let valA = a[column];
    let valB = b[column];
    
    // Null/undefined değerleri sona koy
    if (valA === null || valA === undefined) return 1;
    if (valB === null || valB === undefined) return -1;
    
    // Tarih sütunları için
    if (column.includes('Tarih') || column.includes('Date')) {
      valA = new Date(valA);
      valB = new Date(valB);
    } else if (typeof valA === 'string' && valA.match(/^\d{4}-\d{2}-\d{2}$/)) {
      valA = new Date(valA);
      valB = new Date(valB);
    } else if (typeof valA === 'number' && typeof valB === 'number') {
      // Pure numeric - do nothing, already numbers
    } else if (typeof valA === 'boolean' && typeof valB === 'boolean') {
      // Boolean - do nothing
    } else {
      // String comparison
      valA = (valA || '').toString().toLowerCase();
      valB = (valB || '').toString().toLowerCase();
    }
    
    if (valA < valB) return -1 * direction;
    if (valA > valB) return 1 * direction;
    return 0;
  });
}

// Generic filtreleme fonksiyonu
export function applyFilters(list, filters) {
  let filtered = [...list];
  
  Object.keys(filters).forEach(key => {
    const filterValue = filters[key];
    if (filterValue !== null && filterValue !== undefined && filterValue !== '') {
      filtered = filtered.filter(item => {
        const itemValue = item[key];
        if (itemValue === null || itemValue === undefined) return false;
        
        // Exact match için
        if (typeof filterValue === 'boolean' || typeof filterValue === 'number') {
          return itemValue === filterValue;
        }
        
        // String contains için
        return itemValue.toString().toLowerCase().includes(filterValue.toString().toLowerCase());
      });
    }
  });
  
  return filtered;
}

// Tablo başlığına sıralama functionality ekleme
export function addSortingToHeaders(container, columnKeys, onSort) {
  const headers = container.querySelectorAll('thead th.sortable');
  
  headers.forEach((th, idx) => {
    const sortableColumnKey = columnKeys[idx];
    if (!sortableColumnKey) return;
    
    th.style.cursor = 'pointer';
    th.addEventListener('click', () => {
      onSort(sortableColumnKey, th, headers);
    });
  });
}

// Sıralama görsel göstergelerini güncelleme - Modern CSS-based system
export function updateSortIndicators(activeHeader, direction, allHeaders) {
  allHeaders.forEach(headerTh => {
    headerTh.classList.remove('sorted-asc', 'sorted-desc');
  });

  // Add modern CSS-based sorting class
  activeHeader.classList.add(direction === 1 ? 'sorted-asc' : 'sorted-desc');
  // The visual indicator is now handled by CSS ::after pseudo-element
}

// Filtre inputları oluşturma
export function createFilterRow(tableContainer, filterConfig, onFilterChange) {
  const thead = tableContainer.querySelector('thead');
  const filterRow = document.createElement('tr');
  filterRow.className = 'filter-row';
  
  filterConfig.forEach(config => {
    const th = document.createElement('th');
    
    if (config.type === 'text') {
      th.innerHTML = `<input type="text" class="filter-input" placeholder="${config.placeholder}" data-column="${config.column}" />`;
    } else if (config.type === 'select') {
      let optionsHtml = `<option value="">${config.allText || 'Tümü'}</option>`;
      config.options.forEach(option => {
        optionsHtml += `<option value="${option.value}">${option.text}</option>`;
      });
      th.innerHTML = `<select class="filter-input" data-column="${config.column}">${optionsHtml}</select>`;
    } else if (config.type === 'number') {
      th.innerHTML = `<input type="number" class="filter-input" placeholder="${config.placeholder}" data-column="${config.column}" min="${config.min || 0}" ${config.step ? `step="${config.step}"` : ''} />`;
    } else if (config.type === 'empty') {
      th.innerHTML = '';
    }
    
    filterRow.appendChild(th);
  });
  
  thead.appendChild(filterRow);
  
  // Event listeners ekle
  filterRow.querySelectorAll('.filter-input').forEach(input => {
    input.addEventListener('input', onFilterChange);
    input.addEventListener('change', onFilterChange);
  });
}

// Filtre değerlerini alma
export function getFilterValues(container) {
  const filters = {};
  container.querySelectorAll('.filter-input').forEach(input => {
    const column = input.dataset.column;
    const value = input.value.trim();
    if (value) {
      filters[column] = input.type === 'number' ? parseFloat(value) : value;
    }
  });
  return filters;
}

// Sayfalama hesaplamaları
export function calculatePagination(items, currentPage, itemsPerPage) {
  const totalItems = items.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages || 1));
  
  const startIndex = (validCurrentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const pageItems = items.slice(startIndex, endIndex);
  
  return {
    pageItems,
    totalPages,
    currentPage: validCurrentPage,
    totalItems,
    startIndex,
    endIndex
  };
}

// Sayfalama kontrolleri güncelleme
export function updatePaginationControls(paginationContainer, pagination, onPageChange, t) {
  const prevButton = paginationContainer.querySelector('#prev-page, .prev-page');
  const nextButton = paginationContainer.querySelector('#next-page, .next-page');
  const pageInfo = paginationContainer.querySelector('#page-info, .page-info');
  
  if (pageInfo) {
    pageInfo.textContent = t && t('page_info_text') 
      ? t('page_info_text', pagination)
      : `Page ${pagination.currentPage} of ${pagination.totalPages} (${pagination.totalItems} items)`;
  }
  
  if (prevButton) {
    prevButton.disabled = pagination.currentPage === 1;
    prevButton.onclick = () => {
      if (pagination.currentPage > 1) {
        onPageChange(pagination.currentPage - 1);
      }
    };
  }
  
  if (nextButton) {
    nextButton.disabled = pagination.currentPage === pagination.totalPages || pagination.totalPages === 0;
    nextButton.onclick = () => {
      if (pagination.currentPage < pagination.totalPages) {
        onPageChange(pagination.currentPage + 1);
      }
    };
  }
}

// Boş durum gösterimi
export function renderEmptyState(container, colCount, message, icon) {
  const defaultIcon = `<svg xmlns='http://www.w3.org/2000/svg' width='48' height='48' fill='none' viewBox='0 0 48 48'>
    <circle cx='24' cy='24' r='24' fill='#e0e7ef'/>
    <ellipse cx='24' cy='22' rx='11' ry='10' fill='#fff'/>
    <ellipse cx='17' cy='19' rx='3' ry='2' fill='#b6c3d1' opacity='0.7'/>
    <ellipse cx='31' cy='19' rx='3' ry='2' fill='#b6c3d1' opacity='0.7'/>
    <ellipse cx='20' cy='26' rx='2' ry='3' fill='#222c37'/>
    <ellipse cx='28' cy='26' rx='2' ry='3' fill='#222c37'/>
    <ellipse cx='24' cy='32' rx='4' ry='2' fill='#b6c3d1'/>
  </svg>`;

  container.innerHTML = `
    <tr>
      <td colspan="${colCount}">
        <div class="empty-state-container">
          <div class="empty-state-icon">${icon || defaultIcon}</div>
          <p>${message}</p>
        </div>
      </td>
    </tr>
  `;
}

// Compact Table Optimization Utilities
export class CompactTableHelpers {
  /**
   * Create a table cell with truncation and tooltip support
   * @param {string} content - Cell content
   * @param {string} className - CSS class name
   * @param {Object} options - Additional options
   * @returns {string} HTML string
   */
  static createCompactCell(content, className = '', options = {}) {
    const { align = 'left', maxLength = 0, preserveBadges = false } = options;

    // Don't truncate if content contains badges or is already short
    if (preserveBadges && (content.includes('badge') || content.includes('<'))) {
      return `<td class="${className} ${align !== 'left' ? `cell-text-${align}` : ''}">${content}</td>`;
    }

    let displayContent = content;
    let titleAttr = '';

    // Auto-truncate text content if too long
    if (maxLength > 0 && content && typeof content === 'string' && content.length > maxLength) {
      displayContent = content.substring(0, maxLength) + '...';
      titleAttr = `title="${content.replace(/"/g, '&quot;')}"`;
    }

    const alignClass = align !== 'left' ? `cell-text-${align}` : '';
    const fullClassName = [className, alignClass].filter(Boolean).join(' ');

    return `<td class="${fullClassName}" ${titleAttr}>${displayContent}</td>`;
  }

  /**
   * Add tooltips to truncated cells after table render
   * @param {HTMLElement} tableContainer - Table container element
   */
  static addTooltipsToTruncatedCells(tableContainer) {
    const cells = tableContainer.querySelectorAll('td');
    cells.forEach(cell => {
      const content = cell.textContent.trim();
      if (content.endsWith('...') && !cell.hasAttribute('title')) {
        // Try to get original content from data attribute or estimate
        const originalContent = cell.dataset.originalContent || content.replace('...', '');
        if (originalContent !== content) {
          cell.setAttribute('title', originalContent);
        }
      }
    });
  }

  /**
   * Optimize table for compact display
   * @param {HTMLElement} table - Table element
   * @param {Object} options - Optimization options
   */
  static optimizeTableForCompactDisplay(table, options = {}) {
    const { preserveBadges = true, addTooltips = true } = options;

    // Add compact class
    table.classList.add('compact-optimized');

    // Ensure badges don't get truncated
    if (preserveBadges) {
      const badgeCells = table.querySelectorAll('td .badge, td .status-badge, td .table-badge');
      badgeCells.forEach(badge => {
        badge.parentElement.style.overflow = 'visible';
      });
    }

    // Add tooltips for truncated content
    if (addTooltips) {
      this.addTooltipsToTruncatedCells(table);
    }
  }
}

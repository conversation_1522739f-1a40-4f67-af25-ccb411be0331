const { ipcMain, dialog, app } = require('electron');
const fs = require('fs');
const path = require('path'); // Required for defaultPath in dialogs
const { getDbPath } = require('../services/database.js'); // To get the current db path

ipcMain.handle('settings:backup-db', async () => {
  const currentDbPath = getDbPath();
  const { filePath } = await dialog.showSaveDialog({
    title: 'Veritabanını Yedekle',
    defaultPath: `livestock_backup_${Date.now()}.db`, // Corrected defaultPath
    filters: [{ name: 'SQLite DB', extensions: ['db'] }]
  });

  if (filePath) {
    try {
      fs.copyFileSync(currentDbPath, filePath);
      return { success: true, path: filePath };
    } catch (err) {
      console.error('Yedekleme hatası:', err);
      return { success: false, error: err.message };
    }
  }
  return { success: false, error: '<PERSON><PERSON>lem iptal edildi.' };
});

ipcMain.handle('settings:restore-db', async () => {
  const currentDbPath = getDbPath();
  const { filePaths } = await dialog.showOpenDialog({
    title: 'Veritabanını Geri Yükle',
    filters: [{ name: 'SQLite DB', extensions: ['db'] }],
    properties: ['openFile']
  });

  if (filePaths && filePaths.length > 0) {
    const backupPath = filePaths[0];
    try {
      // It's crucial that the db connection in database.js is closed BEFORE this copy happens.
      // However, the current design has a persistent DB connection.
      // For a restore, it's often best to close the current connection, copy the file, then relaunch.
      // The relaunch will re-initialize the DB from the new file.
      // We can't directly close the shared `db` instance from here without more complex app-level state management.
      // The current implementation in main.js for app.quit() handles db.close().
      // A relaunch is a good way to ensure the new DB is loaded cleanly.

      fs.copyFileSync(backupPath, currentDbPath);

      // Relaunch the app
      app.relaunch();
      app.quit(); // Quit current instance
      return { success: true }; // This might not be received if app quits too fast
    } catch (err) {
      console.error('Geri yükleme hatası:', err);
      return { success: false, error: err.message };
    }
  }
  return { success: false, error: 'İşlem iptal edildi.' };
});

ipcMain.handle('settings:get-app-version', () => {
  return app.getVersion();
});

console.log('Settings IPC handlers registered.');

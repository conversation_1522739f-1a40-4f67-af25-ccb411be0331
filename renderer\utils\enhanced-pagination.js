/**
 * Enhanced Pagination System
 * Provides comprehensive pagination functionality with flexible page size options
 * and advanced navigation controls
 */

export class EnhancedPagination {
  /**
   * Calculate pagination information
   * @param {Array} data - Data array to paginate
   * @param {number} currentPage - Current page number (1-based)
   * @param {number} itemsPerPage - Items per page
   * @returns {Object} Pagination information
   */
  static calculatePagination(data, currentPage, itemsPerPage) {
    const totalItems = data.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
    const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
    
    const startIndex = (validCurrentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
    const pageItems = data.slice(startIndex, endIndex);
    
    const hasNextPage = validCurrentPage < totalPages;
    const hasPreviousPage = validCurrentPage > 1;
    
    return {
      pageItems,
      totalPages,
      currentPage: validCurrentPage,
      totalItems,
      startIndex,
      endIndex,
      itemsPerPage,
      hasNextPage,
      hasPreviousPage,
      startItem: totalItems > 0 ? startIndex + 1 : 0,
      endItem: endIndex,
      isFirstPage: validCurrentPage === 1,
      isLastPage: validCurrentPage === totalPages
    };
  }

  /**
   * Generate page numbers for pagination controls
   * @param {number} currentPage - Current page number
   * @param {number} totalPages - Total number of pages
   * @param {number} maxVisible - Maximum visible page numbers
   * @returns {Array} Array of page numbers to display
   */
  static generatePageNumbers(currentPage, totalPages, maxVisible = 5) {
    if (totalPages <= maxVisible) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const pages = [];
    const halfVisible = Math.floor(maxVisible / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, startPage + maxVisible - 1);
    
    // Adjust if we're near the end
    if (endPage - startPage + 1 < maxVisible) {
      startPage = Math.max(1, endPage - maxVisible + 1);
    }
    
    // Add ellipsis and first page if needed
    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push('...');
      }
    }
    
    // Add visible page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // Add ellipsis and last page if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push('...');
      }
      pages.push(totalPages);
    }
    
    return pages;
  }

  /**
   * Create pagination controls HTML
   * @param {Object} paginationInfo - Pagination information object
   * @param {Object} options - Options for pagination controls
   * @returns {string} HTML string for pagination controls
   */
  static createPaginationHTML(paginationInfo, options = {}) {
    const {
      showInfo = true,
      showPageNumbers = true,
      showItemsPerPage = true,
      showFirstLast = true,
      maxVisiblePages = 5,
      itemsPerPageOptions = [5, 10, 25, 50, 100],
      labels = {
        previous: 'Previous',
        next: 'Next',
        first: 'First',
        last: 'Last',
        itemsPerPage: 'Items per page:',
        showing: 'Showing',
        of: 'of',
        items: 'items'
      }
    } = options;

    const {
      currentPage,
      totalPages,
      totalItems,
      startItem,
      endItem,
      itemsPerPage,
      hasNextPage,
      hasPreviousPage,
      isFirstPage,
      isLastPage
    } = paginationInfo;

    let html = '<div class="pagination-container">';

    // Pagination info
    if (showInfo) {
      html += `
        <div class="pagination-info">
          <span class="page-info">
            ${labels.showing} ${startItem}-${endItem} ${labels.of} ${totalItems} ${labels.items}
          </span>
        </div>
      `;
    }

    // Pagination controls
    html += '<div class="pagination-controls">';

    // First page button
    if (showFirstLast && totalPages > 1) {
      html += `
        <button class="btn btn-secondary first-page-btn" ${isFirstPage ? 'disabled' : ''} data-page="1">
          ${labels.first}
        </button>
      `;
    }

    // Previous button
    html += `
      <button class="btn btn-secondary prev-page-btn" ${!hasPreviousPage ? 'disabled' : ''} data-page="${currentPage - 1}">
        ${labels.previous}
      </button>
    `;

    // Page numbers
    if (showPageNumbers && totalPages > 1) {
      const pageNumbers = EnhancedPagination.generatePageNumbers(currentPage, totalPages, maxVisiblePages);
      
      html += '<div class="page-numbers">';
      pageNumbers.forEach(page => {
        if (page === '...') {
          html += '<span class="page-ellipsis">...</span>';
        } else {
          const isActive = page === currentPage;
          html += `
            <button class="btn page-number-btn ${isActive ? 'btn-primary' : 'btn-secondary'}" 
                    ${isActive ? 'disabled' : ''} data-page="${page}">
              ${page}
            </button>
          `;
        }
      });
      html += '</div>';
    } else if (totalPages > 1) {
      html += `<span class="page-info-simple">Page ${currentPage} of ${totalPages}</span>`;
    }

    // Next button
    html += `
      <button class="btn btn-secondary next-page-btn" ${!hasNextPage ? 'disabled' : ''} data-page="${currentPage + 1}">
        ${labels.next}
      </button>
    `;

    // Last page button
    if (showFirstLast && totalPages > 1) {
      html += `
        <button class="btn btn-secondary last-page-btn" ${isLastPage ? 'disabled' : ''} data-page="${totalPages}">
          ${labels.last}
        </button>
      `;
    }

    html += '</div>'; // Close pagination-controls

    // Items per page selector
    if (showItemsPerPage) {
      html += `
        <div class="items-per-page">
          <label>${labels.itemsPerPage}</label>
          <select class="items-per-page-select">
      `;
      
      itemsPerPageOptions.forEach(option => {
        const selected = option === itemsPerPage ? 'selected' : '';
        html += `<option value="${option}" ${selected}>${option}</option>`;
      });
      
      html += `
          </select>
        </div>
      `;
    }

    html += '</div>'; // Close pagination-container

    return html;
  }

  /**
   * Attach event listeners to pagination controls
   * @param {HTMLElement} container - Container element with pagination controls
   * @param {Function} onPageChange - Callback for page changes
   * @param {Function} onItemsPerPageChange - Callback for items per page changes
   */
  static attachPaginationListeners(container, onPageChange, onItemsPerPageChange) {
    // Page navigation buttons
    container.addEventListener('click', (e) => {
      const button = e.target.closest('[data-page]');
      if (button && !button.disabled) {
        const page = parseInt(button.dataset.page);
        if (!isNaN(page) && onPageChange) {
          onPageChange(page);
        }
      }
    });

    // Items per page selector
    const itemsSelect = container.querySelector('.items-per-page-select');
    if (itemsSelect && onItemsPerPageChange) {
      itemsSelect.addEventListener('change', (e) => {
        const itemsPerPage = parseInt(e.target.value);
        if (!isNaN(itemsPerPage)) {
          onItemsPerPageChange(itemsPerPage);
        }
      });
    }
  }

  /**
   * Update pagination controls with new information
   * @param {HTMLElement} container - Container element with pagination controls
   * @param {Object} paginationInfo - New pagination information
   * @param {Object} options - Options for pagination controls
   */
  static updatePaginationControls(container, paginationInfo, options = {}) {
    const paginationContainer = container.querySelector('.pagination-container');
    if (paginationContainer) {
      const newHTML = EnhancedPagination.createPaginationHTML(paginationInfo, options);
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = newHTML;
      const newPaginationContainer = tempDiv.firstElementChild;
      
      paginationContainer.replaceWith(newPaginationContainer);
    }
  }

  /**
   * Get pagination state from URL parameters
   * @param {URLSearchParams} searchParams - URL search parameters
   * @returns {Object} Pagination state
   */
  static getPaginationFromURL(searchParams) {
    const page = parseInt(searchParams.get('page')) || 1;
    const itemsPerPage = parseInt(searchParams.get('itemsPerPage')) || 10;
    
    return {
      currentPage: Math.max(1, page),
      itemsPerPage: Math.max(1, itemsPerPage)
    };
  }

  /**
   * Update URL with pagination state
   * @param {number} currentPage - Current page number
   * @param {number} itemsPerPage - Items per page
   * @param {boolean} replaceState - Whether to replace or push state
   */
  static updateURL(currentPage, itemsPerPage, replaceState = true) {
    const url = new URL(window.location);
    url.searchParams.set('page', currentPage.toString());
    url.searchParams.set('itemsPerPage', itemsPerPage.toString());
    
    if (replaceState) {
      window.history.replaceState({}, '', url);
    } else {
      window.history.pushState({}, '', url);
    }
  }

  /**
   * Create a simple pagination summary text
   * @param {Object} paginationInfo - Pagination information
   * @returns {string} Summary text
   */
  static createSummaryText(paginationInfo) {
    const { startItem, endItem, totalItems } = paginationInfo;
    
    if (totalItems === 0) {
      return 'No items found';
    }
    
    if (totalItems === 1) {
      return '1 item';
    }
    
    if (startItem === endItem) {
      return `Item ${startItem} of ${totalItems}`;
    }
    
    return `Showing ${startItem}-${endItem} of ${totalItems} items`;
  }

  /**
   * Validate pagination parameters
   * @param {number} currentPage - Current page number
   * @param {number} itemsPerPage - Items per page
   * @param {number} totalItems - Total number of items
   * @returns {Object} Validated parameters
   */
  static validatePaginationParams(currentPage, itemsPerPage, totalItems) {
    const validItemsPerPage = Math.max(1, Math.min(1000, itemsPerPage)); // Limit to reasonable range
    const totalPages = Math.ceil(totalItems / validItemsPerPage) || 1;
    const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
    
    return {
      currentPage: validCurrentPage,
      itemsPerPage: validItemsPerPage,
      totalPages
    };
  }

  /**
   * Create a pagination configuration object
   * @param {Object} options - Configuration options
   * @returns {Object} Pagination configuration
   */
  static createConfig(options = {}) {
    return {
      itemsPerPage: options.itemsPerPage || 10,
      maxVisiblePages: options.maxVisiblePages || 5,
      showInfo: options.showInfo !== false,
      showPageNumbers: options.showPageNumbers !== false,
      showItemsPerPage: options.showItemsPerPage !== false,
      showFirstLast: options.showFirstLast !== false,
      itemsPerPageOptions: options.itemsPerPageOptions || [5, 10, 25, 50, 100],
      labels: {
        previous: 'Previous',
        next: 'Next',
        first: 'First',
        last: 'Last',
        itemsPerPage: 'Items per page:',
        showing: 'Showing',
        of: 'of',
        items: 'items',
        ...options.labels
      }
    };
  }
}

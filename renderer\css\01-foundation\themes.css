/* =================================
   Theme System
   Semantic color tokens for light and dark themes
   ================================= */

/* =================================
   Light Theme (Default)
   ================================= */
:root,
[data-theme="light"] {
  /* Background Colors */
  --bg-primary: var(--gray-50);
  --bg-secondary: var(
  --gray-100);
  --bg-tertiary: white;
  --bg-elevated: white;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors */
  --text-primary: var(
  --gray-900);
  --text-secondary: var(
  --gray-700);
  --text-tertiary: var(
  --gray-500);
  --text-inverse: white;
  --text-link: var(
  --primary-600);
  --text-link-hover: var(
  --primary-700);

  /* Border Colors */
  --border-primary: var(
  --gray-200);
  --border-secondary: var(
  --gray-300);
  --border-focus: var(
  --primary-500);
  --border-error: var(
  --error-500);
  --border-success: var(
  --success-500);

  /* Interactive Colors */
  --interactive-primary: var(
  --primary-600);
  --interactive-primary-hover: var(
  --primary-700);
  --interactive-primary-active: var(
  --primary-800);
  --interactive-secondary: var(
  --gray-100);
  --interactive-secondary-hover: var(
  --gray-200);
  --interactive-secondary-active: var(
  --gray-300);

  /* Status Colors */
  --status-success: var(
  --success-600);
  --status-success-bg: var(--success-50);
  --status-warning: var(
  --warning-600);
  --status-warning-bg: var(
  --warning-50);
  --status-error: var(
  --error-600);
  --status-error-bg: var(
  --error-50);
  --status-info: var(
  --info-600);
  --status-info-bg: var(
  --info-50);

  /* Component Specific */
  --sidebar-bg: white;
  --sidebar-border: var(
  --border-primary);
  --sidebar-item-hover: var(
  --gray-100);
  --sidebar-item-active: var(
  --primary-50);
  --sidebar-item-active-text: var(
  --primary-700);

  --table-header-bg: var(
  --gray-50);
  --table-row-hover: var(
  --gray-50);
  --table-row-selected: var(
  --primary-50);

  --modal-backdrop: rgba(0, 0, 0, 0.5);
  --modal-bg: white;

  --input-bg: white;
  --input-border: var(
  --border-primary);
  --input-border-focus: var(
  --border-focus);

  /* Scrollbar */
  --scrollbar-thumb: var(
  --gray-300);
  --scrollbar-thumb-hover: var(
  --gray-400);

  /* Focus */
  --focus-color: var(
  --primary-500);

  /* Shadows */
  --shadow-color: rgb(0 0 0 / 0.1);
}

/* =================================
   Dark Theme
   ================================= */
[data-theme="dark"] {
  /* Background Colors */
  --bg-primary: #020617; /* gray-950 */
  --bg-secondary: #0f172a; /* gray-900 */
  --bg-tertiary: #1e293b; /* gray-800 */
  --bg-elevated: #1e293b; /* gray-800 */
  --bg-overlay: rgba(0, 0, 0, 0.7);

  /* Text Colors */
  --text-primary: #f1f5f9; /* gray-100 */
  --text-secondary: #cbd5e1; /* gray-300 */
  --text-tertiary: #64748b; /* gray-500 */
  --text-inverse: #0f172a; /* gray-900 */
  --text-link: var(--primary-400);
  --text-link-hover: var(--primary-300);

  /* Border Colors */
  --border-primary: #334155; /* gray-700 */
  --border-secondary: #475569; /* gray-600 */
  --border-focus: #60a5fa; /* primary-400 */
  --border-error: var(--error-500);
  --border-success: var(--success-500);

  /* Interactive Colors */
  --interactive-primary: var(--primary-500);
  --interactive-primary-hover: var(--primary-400);
  --interactive-primary-active: var(--primary-300);
  --interactive-secondary: var(--gray-700);
  --interactive-secondary-hover: var(--gray-600);
  --interactive-secondary-active: var(--gray-500);

  /* Status Colors */
  --status-success: var(--success-500);
  --status-success-bg: rgba(34, 197, 94, 0.1);
  --status-warning: var(--warning-500);
  --status-warning-bg: rgba(245, 158, 11, 0.1);
  --status-error: var(--error-500);
  --status-error-bg: rgba(239, 68, 68, 0.1);
  --status-info: var(--info-500);
  --status-info-bg: rgba(6, 182, 212, 0.1);

  /* Component Specific */
  --sidebar-bg: var(--gray-900);
  --sidebar-border: var(--border-primary);
  --sidebar-item-hover: var(--gray-800);
  --sidebar-item-active: rgba(59, 130, 246, 0.1);
  --sidebar-item-active-text: var(--primary-400);

  --table-header-bg: var(--gray-800);
  --table-row-hover: var(--gray-800);
  --table-row-selected: rgba(59, 130, 246, 0.1);

  --modal-backdrop: rgba(0, 0, 0, 0.8);
  --modal-bg: var(--gray-800);

  --input-bg: var(--gray-700);
  --input-border: var(--border-primary);
  --input-border-focus: var(--border-focus);

  /* Scrollbar */
  --scrollbar-thumb: var(--gray-600);
  --scrollbar-thumb-hover: var(--gray-500);

  /* Focus */
  --focus-color: var(--primary-400);

  /* Shadows */
  --shadow-color: rgb(0 0 0 / 0.3);
}

/* =================================
   System Theme Preference
   ================================= */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    /* Apply dark theme if no explicit theme is set */
    --bg-primary: var(--gray-950);
    --bg-secondary: var(--gray-900);
    --bg-tertiary: var(--gray-800);
    --bg-elevated: var(--gray-800);
    --bg-overlay: rgba(0, 0, 0, 0.7);

    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-500);
    --text-inverse: var(--gray-900);
    --text-link: var(--primary-400);
    --text-link-hover: var(--primary-300);

    --border-primary: var(--gray-700);
    --border-secondary: var(--gray-600);
    --border-focus: var(--primary-400);

    --interactive-primary: var(--primary-500);
    --interactive-primary-hover: var(--primary-400);
    --interactive-secondary: var(--gray-700);
    --interactive-secondary-hover: var(--gray-600);

    --sidebar-bg: var(--gray-900);
    --sidebar-item-hover: var(--gray-800);
    --sidebar-item-active: rgba(59, 130, 246, 0.1);
    --sidebar-item-active-text: var(--primary-400);

    --table-header-bg: var(--gray-800);
    --table-row-hover: var(--gray-800);

    --modal-backdrop: rgba(0, 0, 0, 0.8);
    --modal-bg: var(--gray-800);

    --input-bg: var(--gray-700);
    --scrollbar-thumb: var(--gray-600);
    --focus-color: var(--primary-400);
    --shadow-color: rgb(0 0 0 / 0.3);
  }
}

/* =================================
   High Contrast Mode
   ================================= */
@media (prefers-contrast: high) {
  :root {
    --border-primary: currentColor;
    --border-secondary: currentColor;
    --text-tertiary: var(--text-secondary);
  }
}

/* =================================
   Theme Transition
   ================================= */
* {
  transition: 
    background-color var(--transition-base),
    border-color var(--transition-base),
    color var(--transition-base),
    box-shadow var(--transition-base);
}

/* Disable transitions for theme switching */
[data-theme-switching] * {
  transition: none !important;
}



/* =================================
   Dark Theme Component Overrides
   ================================= */
[data-theme="dark"] {
  /* Animal Selection Bar */
  --primary-50: rgba(59, 130, 246, 0.1);
  --primary-100: rgba(59, 130, 246, 0.2);
  --primary-200: rgba(59, 130, 246, 0.3);
  --primary-300: rgba(59, 130, 246, 0.4);
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;

  /* Cards and elevated surfaces */
  --card-bg: var(--gray-800);
  --card-border: var(--gray-700);

  /* Form inputs */
  --input-bg: var(--gray-700);
  --input-border: var(--gray-600);
  --input-border-focus: var(--primary-400);

  /* Buttons */
  --btn-primary-bg: var(--primary-600);
  --btn-primary-hover: var(--primary-500);
  --btn-secondary-bg: var(--gray-700);
  --btn-secondary-hover: var(--gray-600);

  /* Tables */
  --table-bg: var(--gray-800);
  --table-border: var(--gray-700);
  --table-header-bg: var(--gray-750);
  --table-row-hover: var(--gray-750);

  /* Modals */
  --modal-bg: var(--gray-800);
  --modal-backdrop: rgba(0, 0, 0, 0.8);
  --modal-border: var(--gray-700);

  /* Navigation */
  --nav-bg: var(--gray-900);
  --nav-border: var(--gray-700);
  --nav-item-hover: var(--gray-800);
  --nav-item-active: rgba(59, 130, 246, 0.2);
}

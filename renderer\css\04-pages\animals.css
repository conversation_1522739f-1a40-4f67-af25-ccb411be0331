/* =================================
   Animals Page Styles
   ================================= */

/* =================================
   Animals Header Section
   ================================= */
.animals-header-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.animals-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.animals-page-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.animals-page-title svg {
  width: 28px;
  height: 28px;
  color: var(--interactive-primary);
  flex-shrink: 0;
}

.animals-quick-actions {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.animals-quick-actions .btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.animals-quick-actions .btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* =================================
   Page Controls
   ================================= */
#animal-table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  gap: var(--space-4);
  flex-wrap: wrap;
}

#animal-table-controls .btn-add {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: var(--interactive-primary);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: var(--font-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

#animal-table-controls .btn-add:hover {
  background: var(--interactive-primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

#animal-table-controls .btn-add svg {
  width: 20px;
  height: 20px;
}

/* =================================
   Animals Table Specific
   ================================= */
#animals-table {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

#animals-table .filter-row {
  background: var(--bg-secondary);
}

#animals-table .filter-row th {
  padding: var(--space-2) var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

#animals-table .filter-row input,
#animals-table .filter-row select {
  width: 100%;
  padding: var(--space-1) var(--space-2);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  background: var(--input-bg);
  color: var(--text-primary);
}

#animals-table .filter-row input:focus,
#animals-table .filter-row select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* =================================
   Animal Avatar System
   ================================= */
.animal-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--border-primary);
  background: var(--bg-elevated);
  transition: all var(--transition-fast);
  min-width: 40px;
  min-height: 40px;
}

.animal-avatar:hover {
  transform: scale(1.1);
  border-color: var(--interactive-primary);
  box-shadow: var(--shadow-md);
}

.animal-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-50);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--border-primary);
  transition: all var(--transition-fast);
  min-width: 40px;
  min-height: 40px;
}

.animal-avatar-placeholder:hover {
  transform: scale(1.1);
  background: var(--primary-100);
  border-color: var(--interactive-primary);
  box-shadow: var(--shadow-md);
}

.animal-avatar-placeholder svg {
  width: 20px;
  height: 20px;
}

/* =================================
   Clickable Rows
   ================================= */
#animals-table .clickable-row {
  cursor: pointer;
  transition: all var(--transition-fast);
}

#animals-table .clickable-row:hover {
  background: var(--table-row-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

#animals-table .clickable-row:active {
  transform: translateY(0);
}

/* =================================
   Animal Detail Modal
   ================================= */
#animal-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

#animal-detail-modal.hidden {
  display: none !important;
}

#animal-detail-modal .animal-detail-modal-content {
  max-width: 900px;
  width: 95vw;
  max-height: 90vh;
  background: var(--modal-bg);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.animal-detail-header {
  padding: var(--space-6);
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  position: relative;
}

.animal-detail-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: none;
  border: none;
  font-size: var(--text-xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.animal-detail-close:hover {
  background: var(--interactive-secondary);
  color: var(--text-primary);
}

.animal-detail-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--border-primary);
  background: var(--bg-elevated);
}

.animal-detail-avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--primary-50);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--border-primary);
}

.animal-detail-avatar-placeholder svg {
  width: 40px;
  height: 40px;
}

.animal-detail-avatar-placeholder i {
  font-size: 40px;
}

/* Detail avatar support */
.detail-avatar {
  position: relative;
  width: 80px;
  height: 80px;
}

.detail-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--border-primary);
}

.detail-avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--primary-50);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--border-primary);
}

.detail-avatar-placeholder i {
  font-size: 40px;
}

.animal-detail-info {
  flex: 1;
}

.animal-detail-name {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.animal-detail-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--space-3) 0;
}

.animal-detail-badges {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

/* Detail modal header support */
.detail-modal-header {
  padding: var(--space-6);
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
  position: relative;
}

.detail-close-btn {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: none;
  border: none;
  font-size: var(--text-xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-close-btn:hover {
  background: var(--interactive-secondary);
  color: var(--text-primary);
}

.detail-header-info {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.detail-header-content {
  flex: 1;
}

.detail-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.detail-tags {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
  margin-bottom: var(--space-3);
}

.detail-tag {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  background: var(--primary-50);
  color: var(--primary-600);
  border: 1px solid var(--primary-200);
}

.detail-badges {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.detail-badges .badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
}

/* =================================
   Animal Detail Tabs
   ================================= */
.animal-detail-tabs {
  display: flex;
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
  overflow-x: auto;
}

.animal-detail-tab {
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-bottom: 2px solid transparent;
  white-space: nowrap;
  font-size: var(--text-sm);
}

.animal-detail-tab:hover {
  color: var(--text-primary);
  background: var(--interactive-secondary);
}

.animal-detail-tab.active {
  color: var(--interactive-primary);
  border-bottom-color: var(--interactive-primary);
  background: var(--primary-50);
}

.animal-detail-tab i {
  font-size: 16px;
  margin-right: var(--space-2);
}

/* Detail tab link support */
.detail-tab-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-bottom: 2px solid transparent;
  white-space: nowrap;
  font-size: var(--text-sm);
}

.detail-tab-link:hover {
  color: var(--text-primary);
  background: var(--interactive-secondary);
}

.detail-tab-link.active {
  color: var(--interactive-primary);
  border-bottom-color: var(--interactive-primary);
  background: var(--primary-50);
}

.detail-tab-link i {
  font-size: 16px;
}

.animal-detail-body {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-primary);
}

.animal-detail-content {
  padding: var(--space-6);
  display: none;
}

.animal-detail-content.active {
  display: block;
}

/* =================================
   Animal Info Sections
   ================================= */
.animal-info-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.animal-info-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
}

.animal-info-section h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-primary);
}

.animal-info-grid {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.animal-info-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-primary);
}

.animal-info-item:last-child {
  border-bottom: none;
}

.animal-info-icon {
  width: 20px;
  height: 20px;
  color: var(--text-tertiary);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.animal-info-icon i {
  font-size: 16px;
}

/* FontAwesome icon support */
.detail-info-icon {
  width: 24px;
  height: 24px;
  color: var(--primary-600);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-info-icon i {
  font-size: 18px;
}

.detail-section-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-primary);
}

.detail-section-header i {
  font-size: 20px;
  color: var(--primary-600);
}

.detail-section-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.detail-info-cards {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.detail-info-card {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.detail-info-card:hover {
  background: var(--primary-50);
  border-color: var(--interactive-primary);
}

.animal-info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.animal-info-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.animal-info-value {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

.animal-info-value.empty {
  color: var(--text-tertiary);
  font-style: italic;
}

/* =================================
   Responsive Design
   ================================= */
@media (max-width: 1024px) {
  .animals-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }

  .animals-quick-actions {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .animals-header-section {
    padding: var(--space-4);
  }

  .animals-page-title {
    font-size: var(--text-xl);
  }

  .animals-page-title svg {
    width: 24px;
    height: 24px;
  }

  .animals-quick-actions {
    flex-direction: column;
    width: 100%;
  }

  .animals-quick-actions .btn {
    justify-content: center;
    width: 100%;
  }

  #animal-table-controls {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .animal-detail-header {
    padding: var(--space-4);
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }

  .animal-detail-avatar,
  .animal-detail-avatar-placeholder {
    width: 60px;
    height: 60px;
  }

  .animal-detail-avatar-placeholder svg {
    width: 30px;
    height: 30px;
  }

  .animal-detail-name {
    font-size: var(--text-xl);
  }

  .animal-detail-content {
    padding: var(--space-4);
  }

  .animal-info-sections {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .animal-info-grid {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .animal-avatar,
  .animal-avatar-placeholder {
    width: 32px;
    height: 32px;
  }

  .animal-avatar-placeholder svg {
    width: 16px;
    height: 16px;
  }
}

@media (max-width: 480px) {
  .animals-header-section {
    padding: var(--space-3);
  }

  .animals-page-title {
    font-size: var(--text-lg);
  }

  .animals-quick-actions .btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }

  .animals-quick-actions .btn svg {
    width: 14px;
    height: 14px;
  }
}

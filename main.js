const { app, BrowserWindow, ipc<PERSON>ain, dialog } = require('electron');
const path = require('path');
// const sqlite3 = require('sqlite3').verbose(); // No longer directly used here
const fs = require('fs');
const { initDb, getDb } = require('./main-process/services/database.js');

// --- DATABASE SETUP ---
// Define dbPath relative to resources in production or project root in development
const dbPath = app.isPackaged
  ? path.join(process.resourcesPath, 'db', 'livestock.db')
  : path.join(__dirname, 'db', 'livestock.db');

// Initialize the database
try {
  initDb(dbPath);
} catch (err) {
  console.error("CRITICAL: Database initialization failed. Application might not work correctly.", err);
  // Optionally, show a dialog to the user and quit
  // dialog.showErrorBox('Database Error', 'Failed to initialize the database. The application will now close.');
  // app.quit();
}

// Load IPC Handlers
require('./main-process/ipc-handlers/auth-handlers.js'); // Authentication handlers
require('./main-process/ipc-handlers/animal-handlers.js');
require('./main-process/ipc-handlers/health-handlers.js');
require('./main-process/ipc-handlers/reproduction-handlers.js');
require('./main-process/ipc-handlers/milk-handlers.js');
require('./main-process/ipc-handlers/dashboard-handlers.js');
require('./main-process/ipc-handlers/settings-handlers.js');
require('./main-process/ipc-handlers/reports-handlers.js'); // Yeni rapor handler'ları
require('./main-process/ipc-handlers/validation-handlers.js'); // İş kuralları validation handler'ları
require('./main-process/ipc-handlers/feed-ration-handlers.js'); // Yem ve rasyon handler'ları

const { setupAccountingHandlers } = require('./main-process/ipc-handlers/accounting-handlers.js');

// Setup Accounting IPC Handlers
setupAccountingHandlers();

// Gracefully close the database connection when the app quits
app.on('quit', () => {
  const dbInstance = getDb(); // Get the initialized instance
  if (dbInstance) {
    dbInstance.close((err) => {
      if (err) {
        console.error('Error closing the database:', err.message);
      } else {
        console.log('Database connection closed.');
      }
    });
  }
});

async function createWindow() {
  const win = new BrowserWindow({
    width: 1440,
    height: 900,
    minWidth: 1440,
    minHeight: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false
    }
  });

  // Check if user is authenticated by trying to get current session
  try {
    const { getCurrentUser } = require('./main-process/ipc-handlers/auth-handlers.js');
    const currentUser = getCurrentUser();

    if (currentUser) {
      // User is authenticated, load main application
      win.loadFile(path.join(__dirname, 'renderer', 'index.html'));
    } else {
      // User is not authenticated, load login page
      win.loadFile(path.join(__dirname, 'renderer', 'login.html'));
    }
  } catch (error) {
    // If there's an error checking authentication, default to login page
    console.log('No active session, loading login page');
    win.loadFile(path.join(__dirname, 'renderer', 'login.html'));
  }
}

app.whenReady().then(() => {
  createWindow();
  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// ipcMain.handle('hayvanlar:get-details', ...) MOVED to main-process/ipc-handlers/animal-handlers.js

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// Database schema is now initialized in database.js service

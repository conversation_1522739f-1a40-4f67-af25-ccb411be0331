/* Login Page Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', 'Inter', Arial, sans-serif;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 450px;
  position: relative;
}

.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.login-header {
  text-align: center;
  padding: 40px 30px 30px;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
}

.logo svg {
  color: white;
}

.logo h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
}

.subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

.form-container {
  padding: 30px;
}

.form-tabs {
  display: flex;
  margin-bottom: 30px;
  border-radius: 8px;
  background: #f8f9fa;
  padding: 4px;
}

.tab-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tab-btn.active {
  background: white;
  color: #2563eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
  color: #495057;
}

.auth-form {
  display: none;
}

.auth-form.active {
  display: block;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
}

.form-group label i {
  width: 16px;
  color: #6c757d;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: white;
}

.form-group input:focus {
  outline: none;
  border-color: #2563eb;
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: #495057;
}

/* Remember Me Checkbox */
.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.remember-me input[type="checkbox"] {
  display: none;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
  user-select: none;
  transition: color 0.2s ease;
}

.checkbox-label:hover {
  color: #2563eb;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
  background: white;
}

.checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.remember-me input[type="checkbox"]:checked + .checkbox-label .checkmark {
  background: #2563eb;
  border-color: #2563eb;
}

.remember-me input[type="checkbox"]:checked + .checkbox-label .checkmark::after {
  opacity: 1;
}

.remember-me input[type="checkbox"]:checked + .checkbox-label {
  color: #2563eb;
}

.form-actions {
  margin-top: 30px;
}

.btn-primary {
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading Overlay - Modern Design */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  border-radius: 16px;
  z-index: 1000;
}

.loading-overlay.hidden {
  display: none;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(37, 99, 235, 0.1);
  border-top: 4px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
    border-top-color: #2563eb;
  }
  25% {
    border-top-color: #1d4ed8;
  }
  50% {
    transform: rotate(180deg);
    border-top-color: #2563eb;
  }
  75% {
    border-top-color: #1d4ed8;
  }
  100% {
    transform: rotate(360deg);
    border-top-color: #2563eb;
  }
}

.loading-overlay p {
  color: #495057;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  margin: 0;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Toast Styles */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.toast {
  background: white;
  border-radius: 8px;
  padding: 16px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 300px;
  max-width: 400px;
  transform: translateX(100%);
  animation: slideIn 0.3s ease forwards;
}

.toast.success {
  border-left: 4px solid #28a745;
}

.toast.error {
  border-left: 4px solid #dc3545;
}

.toast.warning {
  border-left: 4px solid #ffc107;
}

.toast.info {
  border-left: 4px solid #17a2b8;
}

.toast-icon {
  font-size: 18px;
}

.toast.success .toast-icon {
  color: #28a745;
}

.toast.error .toast-icon {
  color: #dc3545;
}

.toast.warning .toast-icon {
  color: #ffc107;
}

.toast.info .toast-icon {
  color: #17a2b8;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 2px;
}

.toast-message {
  font-size: 13px;
  color: #6c757d;
}

.toast-close {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.toast-close:hover {
  color: #495057;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Input validation styles */
.form-group input.invalid {
  border-color: #dc3545;
  background-color: #fff5f5;
}

.form-group input.valid {
  border-color: #28a745;
}

.validation-message {
  font-size: 12px;
  color: #dc3545;
  margin-top: 4px;
  display: none;
}

.validation-message.show {
  display: block;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    max-width: 100%;
    margin: 0;
  }

  .login-card {
    border-radius: 0;
    min-height: 100vh;
  }

  .login-header {
    padding: 60px 30px 40px;
  }

  .form-container {
    padding: 30px 20px;
  }

  .toast {
    min-width: auto;
    max-width: calc(100vw - 40px);
  }

  /* Mobile checkbox adjustments */
  .remember-me {
    justify-content: center;
    margin-top: 12px;
  }

  .checkbox-label {
    font-size: 15px;
  }

  .checkmark {
    width: 20px;
    height: 20px;
  }

  .checkmark::after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 9px;
  }
}

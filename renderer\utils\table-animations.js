/**
 * Standardized Table Animation System
 * Provides consistent animation behavior across all tables in the application
 */

export class TableAnimations {
  /**
   * Initialize table with standard animations
   * @param {HTMLElement|string} tableElement - Table element or selector
   * @param {Object} options - Animation options
   */
  static initializeTable(tableElement, options = {}) {
    const table = typeof tableElement === 'string' 
      ? document.querySelector(tableElement) 
      : tableElement;
    
    if (!table) {
      console.warn('Table element not found for animation initialization');
      return;
    }

    const defaults = {
      enableInitialAnimation: true,
      enableRowStagger: true,
      animationDuration: 600,
      staggerDelay: 50,
      respectReducedMotion: true
    };

    const config = { ...defaults, ...options };

    // Check for reduced motion preference
    if (config.respectReducedMotion && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      table.classList.add('no-animations');
      return;
    }

    // Add animation classes
    if (config.enableInitialAnimation) {
      table.classList.add('table-animated');
    }

    // Set custom animation duration if provided
    if (config.animationDuration !== 600) {
      table.style.setProperty('--table-animation-duration', `${config.animationDuration}ms`);
    }

    // Set custom stagger delay if provided
    if (config.staggerDelay !== 50) {
      table.style.setProperty('--table-stagger-delay', `${config.staggerDelay}ms`);
    }
  }

  /**
   * Animate table content refresh
   * @param {HTMLElement|string} tableElement - Table element or selector
   * @param {Function} updateCallback - Function to update table content
   */
  static async refreshTable(tableElement, updateCallback) {
    const table = typeof tableElement === 'string' 
      ? document.querySelector(tableElement) 
      : tableElement;
    
    if (!table) {
      console.warn('Table element not found for refresh animation');
      return;
    }

    // Check for reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      if (updateCallback) updateCallback();
      return;
    }

    const container = table.closest('.table-container') || table.parentElement;
    
    // Add updating class to prevent conflicting animations
    container.classList.add('table-updating');
    
    // Execute the update
    if (updateCallback) {
      await updateCallback();
    }
    
    // Small delay to ensure DOM is updated
    setTimeout(() => {
      container.classList.remove('table-updating');
      container.classList.add('table-refresh');
      
      // Remove refresh class after animation completes
      setTimeout(() => {
        container.classList.remove('table-refresh');
      }, 600);
    }, 50);
  }

  /**
   * Add a new row with animation
   * @param {HTMLElement} tableBody - Table body element
   * @param {HTMLElement} newRow - New row element to add
   * @param {number} position - Position to insert (default: end)
   */
  static addRowAnimated(tableBody, newRow, position = -1) {
    if (!tableBody || !newRow) {
      console.warn('Table body or new row element not found');
      return;
    }

    // Check for reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      if (position === -1) {
        tableBody.appendChild(newRow);
      } else {
        tableBody.insertBefore(newRow, tableBody.children[position]);
      }
      return;
    }

    // Set initial state for animation
    newRow.style.opacity = '0';
    newRow.style.transform = 'translateY(10px)';
    newRow.style.transition = 'all 0.4s ease-out';

    // Insert the row
    if (position === -1) {
      tableBody.appendChild(newRow);
    } else {
      tableBody.insertBefore(newRow, tableBody.children[position]);
    }

    // Trigger animation
    requestAnimationFrame(() => {
      newRow.style.opacity = '1';
      newRow.style.transform = 'translateY(0)';
      
      // Clean up styles after animation
      setTimeout(() => {
        newRow.style.removeProperty('opacity');
        newRow.style.removeProperty('transform');
        newRow.style.removeProperty('transition');
      }, 400);
    });
  }

  /**
   * Remove a row with animation
   * @param {HTMLElement} row - Row element to remove
   * @param {Function} callback - Callback after animation completes
   */
  static removeRowAnimated(row, callback) {
    if (!row) {
      console.warn('Row element not found for removal animation');
      return;
    }

    // Check for reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      row.remove();
      if (callback) callback();
      return;
    }

    // Set animation styles
    row.style.transition = 'all 0.3s ease-out';
    row.style.opacity = '0';
    row.style.transform = 'translateX(-20px)';
    row.style.maxHeight = row.offsetHeight + 'px';
    
    setTimeout(() => {
      row.style.maxHeight = '0';
      row.style.paddingTop = '0';
      row.style.paddingBottom = '0';
      row.style.marginTop = '0';
      row.style.marginBottom = '0';
      
      setTimeout(() => {
        row.remove();
        if (callback) callback();
      }, 300);
    }, 100);
  }

  /**
   * Show loading state for table
   * @param {HTMLElement|string} tableElement - Table element or selector
   */
  static showLoading(tableElement) {
    const table = typeof tableElement === 'string' 
      ? document.querySelector(tableElement) 
      : tableElement;
    
    if (!table) return;

    const container = table.closest('.table-container') || table.parentElement;
    container.classList.add('table-loading');
  }

  /**
   * Hide loading state for table
   * @param {HTMLElement|string} tableElement - Table element or selector
   */
  static hideLoading(tableElement) {
    const table = typeof tableElement === 'string' 
      ? document.querySelector(tableElement) 
      : tableElement;
    
    if (!table) return;

    const container = table.closest('.table-container') || table.parentElement;
    container.classList.remove('table-loading');
  }

  /**
   * Reset all animations for a table
   * @param {HTMLElement|string} tableElement - Table element or selector
   */
  static resetAnimations(tableElement) {
    const table = typeof tableElement === 'string' 
      ? document.querySelector(tableElement) 
      : tableElement;
    
    if (!table) return;

    const container = table.closest('.table-container') || table.parentElement;
    
    // Remove all animation classes
    container.classList.remove('table-updating', 'table-refresh', 'table-loading');
    table.classList.remove('table-animated', 'no-animations');
    
    // Reset custom properties
    table.style.removeProperty('--table-animation-duration');
    table.style.removeProperty('--table-stagger-delay');
  }
}

// Export default for convenience
export default TableAnimations;
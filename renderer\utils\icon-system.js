/**
 * Centralized Icon System for Livestock Management Application
 * Provides standardized SVG icons with consistent sizing and styling
 */

export class IconSystem {
  /**
   * Standard icon sizes
   */
  static SIZES = {
    SMALL: 14,
    MEDIUM: 16,
    LARGE: 18,
    XLARGE: 20
  };

  /**
   * Standard colors for icons
   */
  static COLORS = {
    PRIMARY: '#2563eb',
    SECONDARY: '#6b7280',
    SUCCESS: '#22c55e',
    WARNING: '#f59e0b',
    DANGER: '#ef4444',
    MUTED: '#a1a1aa'
  };

  /**
   * Get edit icon SVG
   * @param {number} size - Icon size in pixels (default: 18)
   * @param {string} color - Icon color (default: primary)
   * @returns {string} SVG string
   */
  static getEditIcon(size = IconSystem.SIZES.MEDIUM, color = IconSystem.COLORS.PRIMARY) {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" class="icon-edit">
      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
    </svg>`;
  }

  /**
   * Get delete icon SVG
   * @param {number} size - Icon size in pixels (default: 18)
   * @param {string} color - Icon color (default: danger)
   * @returns {string} SVG string
   */
  static getDeleteIcon(size = IconSystem.SIZES.MEDIUM, color = IconSystem.COLORS.DANGER) {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" class="icon-delete">
      <path d="M3 6h18" />
      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
      <path d="M10 11v6" />
      <path d="M14 11v6" />
    </svg>`;
  }

  /**
   * Get view/eye icon SVG
   * @param {number} size - Icon size in pixels (default: 18)
   * @param {string} color - Icon color (default: secondary)
   * @returns {string} SVG string
   */
  static getViewIcon(size = IconSystem.SIZES.MEDIUM, color = IconSystem.COLORS.SECONDARY) {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" class="icon-view">
      <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
      <circle cx="12" cy="12" r="3" />
    </svg>`;
  }

  /**
   * Get complete/check icon SVG
   * @param {number} size - Icon size in pixels (default: 18)
   * @param {string} color - Icon color (default: success)
   * @returns {string} SVG string
   */
  static getCompleteIcon(size = IconSystem.SIZES.MEDIUM, color = IconSystem.COLORS.SUCCESS) {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" class="icon-complete">
      <path d="M20 6 9 17l-5-5" />
    </svg>`;
  }

  /**
   * Get sort icon SVG (unsorted state)
   * @param {number} size - Icon size in pixels (default: 16)
   * @param {string} color - Icon color (default: muted)
   * @returns {string} SVG string
   */
  static getSortIcon(size = IconSystem.SIZES.SMALL, column = null, currentColumn = null, direction = null) {
    const color = column === currentColumn ? IconSystem.COLORS.PRIMARY : IconSystem.COLORS.MUTED;
    
    if (column === currentColumn) {
      return direction === 1 ? this.getSortAscIcon(size, color) : this.getSortDescIcon(size, color);
    }
    return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-sort">
      <path d="M8 9l4-4 4 4"></path>
      <path d="M16 15l-4 4-4-4"></path>
    </svg>`;
  }

  /**
   * Get sort ascending icon SVG
   * @param {number} size - Icon size in pixels (default: 16)
   * @param {string} color - Icon color (default: primary)
   * @returns {string} SVG string
   */
  static getSortAscIcon(size = IconSystem.SIZES.SMALL, color = IconSystem.COLORS.PRIMARY) {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" class="icon-sort-asc">
      <path d="M8 18l4-4 4 4"></path>
      <path d="M8 6l4-4 4 4"></path>
    </svg>`;
  }

  /**
   * Get sort descending icon SVG
   * @param {number} size - Icon size in pixels (default: 16)
   * @param {string} color - Icon color (default: primary)
   * @returns {string} SVG string
   */
  static getSortDescIcon(size = IconSystem.SIZES.SMALL, color = IconSystem.COLORS.PRIMARY) {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" class="icon-sort-desc">
      <path d="M8 18l4-4 4 4"></path>
      <path d="M8 6l4-4 4 4"></path>
    </svg>`;
  }

  /**
   * Create standardized action button HTML
   * @param {string} type - Button type: 'edit', 'delete', 'view', 'complete'
   * @param {string} id - Data ID for the button
   * @param {string} title - Button title/tooltip
   * @param {Object} options - Additional options
   * @returns {string} Button HTML string
   */
  static createActionButton(type, id, title, options = {}) {
    const size = options.size || IconSystem.SIZES.MEDIUM;
    const className = `action-btn ${type}-btn`;

    let icon;
    switch (type) {
      case 'edit':
        icon = IconSystem.getEditIcon(size);
        break;
      case 'delete':
        icon = IconSystem.getDeleteIcon(size);
        break;
      case 'view':
        icon = IconSystem.getViewIcon(size);
        break;
      case 'complete':
        icon = IconSystem.getCompleteIcon(size);
        break;
      default:
        icon = '';
    }

    return `<button class="${className}" data-action="${type}" data-id="${id}" title="${title}">
      ${icon}
    </button>`;
  }

  /**
   * Create standardized actions wrapper HTML
   * @param {string} id - Data ID for the actions
   * @param {Object} actions - Actions configuration
   * @param {Object} translations - Translation object
   * @returns {string} Actions wrapper HTML
   */
  static createActionsWrapper(id, actions = {}, translations = {}) {
    const buttons = [];
    
    if (actions.edit !== false) {
      buttons.push(IconSystem.createActionButton('edit', id, translations.edit || 'Edit'));
    }
    
    if (actions.delete !== false) {
      buttons.push(IconSystem.createActionButton('delete', id, translations.delete || 'Delete'));
    }
    
    if (actions.view) {
      buttons.push(IconSystem.createActionButton('view', id, translations.view || 'View'));
    }

    return `<div class="actions-wrapper">
      ${buttons.join('')}
    </div>`;
  }
}

/**
 * Legacy support - export individual icon functions for backward compatibility
 */
export const getEditIcon = IconSystem.getEditIcon;
export const getDeleteIcon = IconSystem.getDeleteIcon;
export const getViewIcon = IconSystem.getViewIcon;
export const createActionButton = IconSystem.createActionButton;
export const createActionsWrapper = IconSystem.createActionsWrapper;

/**
 * Enhanced Filtering System
 * Provides comprehensive filtering functionality for tables with support for multiple filter types
 * and advanced filtering operations
 */

export class EnhancedFiltering {
  /**
   * Apply filters to a dataset
   * @param {Array} data - Array of objects to filter
   * @param {Object} filters - Filter criteria object
   * @param {Array} columns - Column configuration array
   * @returns {Array} Filtered data
   */
  static applyFilters(data, filters, columns = []) {
    if (!filters || Object.keys(filters).length === 0) {
      return [...data];
    }

    return data.filter(item => {
      return Object.keys(filters).every(columnKey => {
        const filterValue = filters[columnKey];
        if (filterValue === null || filterValue === undefined || filterValue === '') {
          return true; // No filter applied for this column
        }

        const column = columns.find(col => col.key === columnKey);
        const itemValue = EnhancedFiltering.getNestedValue(item, columnKey);

        return EnhancedFiltering.matchesFilter(itemValue, filterValue, column);
      });
    });
  }

  /**
   * Check if a value matches a filter criteria
   * @param {*} itemValue - Value from the data item
   * @param {*} filterValue - Filter criteria value
   * @param {Object} column - Column configuration
   * @returns {boolean} True if value matches filter
   */
  static matchesFilter(itemValue, filterValue, column = {}) {
    // Handle null/undefined item values
    if (itemValue === null || itemValue === undefined) {
      return false;
    }

    const filterType = column.filterType || 'text';
    const caseSensitive = column.filterCaseSensitive === true;

    switch (filterType) {
      case 'exact':
        return EnhancedFiltering.exactMatch(itemValue, filterValue, caseSensitive);
      
      case 'select':
        return EnhancedFiltering.selectMatch(itemValue, filterValue);
      
      case 'number':
        return EnhancedFiltering.numberMatch(itemValue, filterValue, column);
      
      case 'date':
        return EnhancedFiltering.dateMatch(itemValue, filterValue, column);
      
      case 'range':
        return EnhancedFiltering.rangeMatch(itemValue, filterValue, column);
      
      case 'boolean':
        return EnhancedFiltering.booleanMatch(itemValue, filterValue);
      
      case 'text':
      default:
        return EnhancedFiltering.textMatch(itemValue, filterValue, caseSensitive);
    }
  }

  /**
   * Text-based filtering (contains)
   * @param {*} itemValue - Item value
   * @param {string} filterValue - Filter value
   * @param {boolean} caseSensitive - Case sensitive matching
   * @returns {boolean} Match result
   */
  static textMatch(itemValue, filterValue, caseSensitive = false) {
    const itemStr = String(itemValue);
    const filterStr = String(filterValue);

    if (caseSensitive) {
      return itemStr.includes(filterStr);
    } else {
      return itemStr.toLowerCase().includes(filterStr.toLowerCase());
    }
  }

  /**
   * Exact match filtering
   * @param {*} itemValue - Item value
   * @param {*} filterValue - Filter value
   * @param {boolean} caseSensitive - Case sensitive matching
   * @returns {boolean} Match result
   */
  static exactMatch(itemValue, filterValue, caseSensitive = false) {
    const itemStr = String(itemValue);
    const filterStr = String(filterValue);

    if (caseSensitive) {
      return itemStr === filterStr;
    } else {
      return itemStr.toLowerCase() === filterStr.toLowerCase();
    }
  }

  /**
   * Select/dropdown filtering
   * @param {*} itemValue - Item value
   * @param {*} filterValue - Filter value
   * @returns {boolean} Match result
   */
  static selectMatch(itemValue, filterValue) {
    return String(itemValue) === String(filterValue);
  }

  /**
   * Number-based filtering with comparison operators
   * @param {*} itemValue - Item value
   * @param {*} filterValue - Filter value (can include operators)
   * @param {Object} column - Column configuration
   * @returns {boolean} Match result
   */
  static numberMatch(itemValue, filterValue, column = {}) {
    const itemNum = Number(itemValue);
    if (isNaN(itemNum)) return false;

    // Handle operator-based filtering (e.g., ">10", "<=5", "=100")
    const filterStr = String(filterValue).trim();
    const operatorMatch = filterStr.match(/^(>=|<=|>|<|=)?(.+)$/);
    
    if (operatorMatch) {
      const operator = operatorMatch[1] || '=';
      const filterNum = Number(operatorMatch[2]);
      
      if (isNaN(filterNum)) return false;

      switch (operator) {
        case '>': return itemNum > filterNum;
        case '>=': return itemNum >= filterNum;
        case '<': return itemNum < filterNum;
        case '<=': return itemNum <= filterNum;
        case '=': 
        default: return Math.abs(itemNum - filterNum) < 0.0001; // Handle floating point precision
      }
    }

    return false;
  }

  /**
   * Date-based filtering
   * @param {*} itemValue - Item value
   * @param {*} filterValue - Filter value
   * @param {Object} column - Column configuration
   * @returns {boolean} Match result
   */
  static dateMatch(itemValue, filterValue, column = {}) {
    const itemDate = new Date(itemValue);
    const filterDate = new Date(filterValue);

    if (isNaN(itemDate.getTime()) || isNaN(filterDate.getTime())) {
      return false;
    }

    const dateComparison = column.dateComparison || 'exact';

    switch (dateComparison) {
      case 'exact':
        return itemDate.toDateString() === filterDate.toDateString();
      
      case 'after':
        return itemDate > filterDate;
      
      case 'before':
        return itemDate < filterDate;
      
      case 'on-or-after':
        return itemDate >= filterDate;
      
      case 'on-or-before':
        return itemDate <= filterDate;
      
      default:
        return itemDate.toDateString() === filterDate.toDateString();
    }
  }

  /**
   * Range-based filtering
   * @param {*} itemValue - Item value
   * @param {Object} filterValue - Filter value with min/max properties
   * @param {Object} column - Column configuration
   * @returns {boolean} Match result
   */
  static rangeMatch(itemValue, filterValue, column = {}) {
    const itemNum = Number(itemValue);
    if (isNaN(itemNum)) return false;

    const { min, max } = filterValue;
    
    if (min !== undefined && min !== null && min !== '') {
      if (itemNum < Number(min)) return false;
    }
    
    if (max !== undefined && max !== null && max !== '') {
      if (itemNum > Number(max)) return false;
    }

    return true;
  }

  /**
   * Boolean filtering
   * @param {*} itemValue - Item value
   * @param {*} filterValue - Filter value
   * @returns {boolean} Match result
   */
  static booleanMatch(itemValue, filterValue) {
    const itemBool = Boolean(itemValue);
    const filterBool = Boolean(filterValue);
    return itemBool === filterBool;
  }

  /**
   * Get nested value from object using dot notation
   * @param {Object} obj - Object to extract value from
   * @param {string} path - Dot-separated path to the value
   * @returns {*} The value at the specified path
   */
  static getNestedValue(obj, path) {
    if (!obj || !path) return undefined;
    
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Create a debounced filter function to improve performance
   * @param {Function} filterFunction - Function to debounce
   * @param {number} delay - Delay in milliseconds
   * @returns {Function} Debounced function
   */
  static debounceFilter(filterFunction, delay = 300) {
    let timeoutId;
    
    return function(...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => filterFunction.apply(this, args), delay);
    };
  }

  /**
   * Create filter options from data for select filters
   * @param {Array} data - Data array
   * @param {string} columnKey - Column key to extract values from
   * @param {Object} options - Additional options
   * @returns {Array} Array of filter options
   */
  static createFilterOptions(data, columnKey, options = {}) {
    const {
      includeEmpty = true,
      emptyLabel = 'All',
      sortOptions = true,
      maxOptions = 100
    } = options;

    // Extract unique values
    const uniqueValues = new Set();
    
    data.forEach(item => {
      const value = EnhancedFiltering.getNestedValue(item, columnKey);
      if (value !== null && value !== undefined && value !== '') {
        uniqueValues.add(value);
      }
    });

    // Convert to options array
    let optionsArray = Array.from(uniqueValues).map(value => ({
      value: String(value),
      label: String(value)
    }));

    // Sort options if requested
    if (sortOptions) {
      optionsArray.sort((a, b) => a.label.localeCompare(b.label));
    }

    // Limit options if specified
    if (maxOptions && optionsArray.length > maxOptions) {
      optionsArray = optionsArray.slice(0, maxOptions);
    }

    // Add empty option if requested
    if (includeEmpty) {
      optionsArray.unshift({ value: '', label: emptyLabel });
    }

    return optionsArray;
  }

  /**
   * Validate filter value based on column configuration
   * @param {*} filterValue - Filter value to validate
   * @param {Object} column - Column configuration
   * @returns {Object} Validation result with isValid and message
   */
  static validateFilter(filterValue, column = {}) {
    const filterType = column.filterType || 'text';

    switch (filterType) {
      case 'number':
        if (filterValue && isNaN(Number(filterValue))) {
          return { isValid: false, message: 'Please enter a valid number' };
        }
        break;
      
      case 'date':
        if (filterValue && isNaN(new Date(filterValue).getTime())) {
          return { isValid: false, message: 'Please enter a valid date' };
        }
        break;
      
      case 'range':
        if (filterValue && typeof filterValue === 'object') {
          const { min, max } = filterValue;
          if (min && isNaN(Number(min))) {
            return { isValid: false, message: 'Minimum value must be a number' };
          }
          if (max && isNaN(Number(max))) {
            return { isValid: false, message: 'Maximum value must be a number' };
          }
          if (min && max && Number(min) > Number(max)) {
            return { isValid: false, message: 'Minimum value cannot be greater than maximum' };
          }
        }
        break;
    }

    return { isValid: true, message: '' };
  }

  /**
   * Clear all filters
   * @param {HTMLElement} container - Container element with filter inputs
   */
  static clearAllFilters(container) {
    const filterInputs = container.querySelectorAll('.filter-input');
    filterInputs.forEach(input => {
      if (input.type === 'checkbox') {
        input.checked = false;
      } else {
        input.value = '';
      }
    });
  }

  /**
   * Get current filter values from DOM
   * @param {HTMLElement} container - Container element with filter inputs
   * @returns {Object} Current filter values
   */
  static getCurrentFilters(container) {
    const filters = {};
    const filterInputs = container.querySelectorAll('.filter-input');
    
    filterInputs.forEach(input => {
      const column = input.dataset.column;
      if (column) {
        if (input.type === 'checkbox') {
          filters[column] = input.checked;
        } else if (input.value !== '') {
          filters[column] = input.value;
        }
      }
    });

    return filters;
  }
}
